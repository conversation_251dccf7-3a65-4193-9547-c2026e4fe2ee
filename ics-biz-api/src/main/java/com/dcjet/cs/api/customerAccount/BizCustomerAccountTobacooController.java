package com.dcjet.cs.api.customerAccount;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.customerAccount.*;
import com.dcjet.cs.customerAccount.service.BizCustomerAccountTobacooService;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@RestController
@RequestMapping("v1/bizCustomerAccountTobacoo")
@Api(tags = "接口")
public class BizCustomerAccountTobacooController extends BaseController {
    @Resource
    private BizCustomerAccountTobacooService bizCustomerAccountTobacooService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizCustomerAccountTobacooParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizCustomerAccountTobacooDto>> getListPaged(@RequestBody BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizCustomerAccountTobacooDto>> paged = bizCustomerAccountTobacooService.getListPaged(bizCustomerAccountTobacooParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizCustomerAccountTobacooParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizCustomerAccountTobacooDto> insert(@Valid @RequestBody BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        ResultObject<BizCustomerAccountTobacooDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizCustomerAccountTobacooDto bizCustomerAccountTobacooDto = bizCustomerAccountTobacooService.insert(bizCustomerAccountTobacooParam, userInfo);
        if (bizCustomerAccountTobacooDto != null) {
            resultObject.setData(bizCustomerAccountTobacooDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizCustomerAccountTobacooParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizCustomerAccountTobacooDto> update(@PathVariable String sid, @Valid @RequestBody BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        bizCustomerAccountTobacooParam.setSid(sid);
        BizCustomerAccountTobacooDto bizCustomerAccountTobacooDto = bizCustomerAccountTobacooService.update(bizCustomerAccountTobacooParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizCustomerAccountTobacooDto != null) {
            resultObject.setData(bizCustomerAccountTobacooDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizCustomerAccountTobacooService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizCustomerAccountTobacooExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizCustomerAccountTobacooDto> bizCustomerAccountTobacooDtos = bizCustomerAccountTobacooService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizCustomerAccountTobacooDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizCustomerAccountTobacooDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizCustomerAccountTobacooDto> list) {
        for(BizCustomerAccountTobacooDto item : list) {
        }
    }
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountTobacooService.sendApproval(sid, userInfo);
    }
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizCustomerAccountTobacooParam param, UserInfoToken userInfo) {
        return bizCustomerAccountTobacooService.confirmStatus(param, userInfo);
    }
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountTobacooService.invalidate(sid, userInfo);
    }
    @ApiOperation("退单接口")
    @PostMapping("back/{sid}")
    public ResultObject back(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountTobacooService.back(sid, userInfo);
    }
    @ApiOperation("打印结算单")
    @PostMapping("/print")
    public ResponseEntity printSummary(@RequestBody BizCustomerAccountTobacooParam param, UserInfoToken userInfo) throws Exception {
        return bizCustomerAccountTobacooService.print(param, userInfo);
    }
    @ApiOperation("获取流水号")
    @PostMapping("getSerialNo")
    public ResultObject getSerialNo(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取流水号成功");
        String serialNo = bizCustomerAccountTobacooService.getSerialNo(userInfo);
        resultObject.setData(serialNo);
        return resultObject;
    }
    @ApiOperation("获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        return bizCustomerAccountTobacooService.getSupplierList(params,userInfo);
    }
    @ApiOperation("获取制单人列表信息")
    @PostMapping("/getCreateUserList")
    public ResultObject getCreateUserList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        return bizCustomerAccountTobacooService.getCreateUserList(params,userInfo);
    }
    @ApiOperation("分页查询接口")
    @PostMapping("/getForeignContractHeadList")
    public ResultObject<List<ForeignContractHeadDto>> getForeignContractHeadList(@RequestBody ForeignContractHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadDto>> paged = bizCustomerAccountTobacooService.getForeignContractHeadList(param, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("从外商合同新增")
    @PostMapping("/insertByContract")
    public ResultObject insertByShipping(@RequestBody BizCustomerAccountTobacooParam param, UserInfoToken userInfo) {
        return bizCustomerAccountTobacooService.insertByContract(param, userInfo);
    }

}
