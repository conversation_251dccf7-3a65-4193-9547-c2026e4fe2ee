package com.dcjet.cs.api.aeo;

import com.dcjet.cs.aeo.service.BizFieldMarkingService;
import com.dcjet.cs.dto.aeo.BizFieldMarkingDto;
import com.dcjet.cs.dto.aeo.BizFieldMarkingParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 表单字段标记信息Controller
 *
 * <AUTHOR>
 * @date 2024
 */
@RestController
@RequestMapping("v1/bizFieldMarking")
@Api(tags = "AEO管理-表单字段标记信息接口")
public class BizFieldMarkingController extends BaseController {
    
    @Resource
    private BizFieldMarkingService bizFieldMarkingService;

    /**
     * 查询字段标记信息列表
     *
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("查询字段标记信息列表")
    @PostMapping("/selectList")
    public ResultObject<List<BizFieldMarkingDto>> selectList(
            @RequestBody BizFieldMarkingParam param, 
            UserInfoToken userInfo) {
        List<BizFieldMarkingDto> result = bizFieldMarkingService.selectList(param, userInfo);
        return ResultObject.createInstance(true, "查询成功", result);
    }

    /**
     * 根据sid和formType查询字段标记信息
     *
     * @param sid
     * @param formType
     * @param userInfo
     * @return
     */
    @ApiOperation("根据sid和formType查询字段标记信息")
    @GetMapping("/getBySidAndFormType")
    public ResultObject<BizFieldMarkingDto> getBySidAndFormType(
            @ApiParam(value = "表单记录ID", required = true) @RequestParam String sid,
            @ApiParam(value = "表单类型", required = false) @RequestParam(required = false, defaultValue = "default") String formType,
            UserInfoToken userInfo) {
        BizFieldMarkingDto result = bizFieldMarkingService.getBySidAndFormType(sid, formType, userInfo);
        return ResultObject.createInstance(true, "查询成功", result);
    }

    /**
     * 新增字段标记信息
     *
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("新增字段标记信息")
    @PostMapping("/insert")
    public ResultObject<BizFieldMarkingDto> insert(
            @Valid @RequestBody BizFieldMarkingParam param, 
            UserInfoToken userInfo) {
        BizFieldMarkingDto result = bizFieldMarkingService.insert(param, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, result);
    }

    /**
     * 更新字段标记信息
     *
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("更新字段标记信息")
    @PostMapping("/update")
    public ResultObject<BizFieldMarkingDto> update(
            @Valid @RequestBody BizFieldMarkingParam param, 
            UserInfoToken userInfo) {
        BizFieldMarkingDto result = bizFieldMarkingService.update(param, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, result);
    }

    /**
     * 保存或更新字段标记信息
     *
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("保存或更新字段标记信息")
    @PostMapping("/saveOrUpdate")
    public ResultObject<BizFieldMarkingDto> saveOrUpdate(
            @Valid @RequestBody BizFieldMarkingParam param, 
            UserInfoToken userInfo) {
        BizFieldMarkingDto result = bizFieldMarkingService.saveOrUpdate(param, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, result);
    }

    /**
     * 删除字段标记信息
     *
     * @param id
     * @param userInfo
     * @return
     */
    @ApiOperation("删除字段标记信息")
    @DeleteMapping("/delete/{id}")
    public ResultObject<Void> delete(
            @ApiParam(value = "主键ID", required = true) @PathVariable String id,
            UserInfoToken userInfo) {
        bizFieldMarkingService.delete(id, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }
}