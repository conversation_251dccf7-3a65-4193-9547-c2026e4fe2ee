package com.dcjet.cs.api.equipment;

import com.dcjet.cs.dto.equipment.*;
import com.dcjet.cs.equipment.service.ForeignContractListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/equipment/foreignContract/body")
@Api(tags = "国营贸易进口烟机设备-外商合同表体")
public class ForeignContractListController {
    @Resource
    private ForeignContractListService foreignContractListService;

    /**
     * 获取新增表体物料信息
     *
     * @param userInfo 用户信息
     * @return 物料信息
     */
    @ApiOperation("获取新增表体物料信息")
    @GetMapping("/material")
    public ResultObject<List<ForeignContractAddBodyMaterialDto>> getMaterialInfo(UserInfoToken<?> userInfo) {
        List<ForeignContractAddBodyMaterialDto> materialInfoList = this.foreignContractListService.getMaterialInfoForAddBody(userInfo);
        return ResultObject.createInstance(true, "查询成功", materialInfoList);
    }

    /**
     * 获取分页列表
     *
     * @param param     外商合同表体参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 外商合同表体分页列表
     */
    @ApiOperation("获取外商合同表体分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication("/tobacco/equipment/foreignContract")
    public ResultObject<List<ForeignContractListDto>> getListPaged(@RequestBody ForeignContractListParam param,
                                                                   PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.foreignContractListService.getListPaged(param, pageParam, userInfo);
    }

    /**
     * 获取表体汇总数据
     *
     * @param param    外商合同表体参数
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("获取表体汇总数据")
    @PostMapping("/summary")
    public ResultObject<ForeignContractListSummaryDto> getListSummary(@RequestBody ForeignContractListParam param,
                                                                      UserInfoToken<?> userInfo) {
        ForeignContractListSummaryDto listSummaryDto = this.foreignContractListService
                .getListSummary(param, userInfo);
        return ResultObject.createInstance(true, "查询成功", listSummaryDto);
    }

    /**
     * 新增表体
     *
     * @param addBodyParam 新增表体参数
     * @return 响应数据
     */
    @ApiOperation("新增表体")
    @PostMapping
    public ResultObject<?> addBody(@RequestBody ForeignContractAddBodyParam addBodyParam, UserInfoToken<?> userInfo) {
        this.foreignContractListService.addBody(addBodyParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
    }

    /**
     * 修改外商合同表体
     *
     * @param id       主键
     * @param param    外商合同表体参数
     * @param userInfo 用户信息
     * @return 外商合同表体传输模型
     */
    @ApiOperation("修改外商合同表体")
    @PutMapping("/{id}")
    public ResultObject<?> update(@PathVariable String id, @RequestBody @Valid ForeignContractListParam param,
                                  UserInfoToken<?> userInfo) {
        param.setId(id);
        ForeignContractListDto foreignContractListDto = this.foreignContractListService.updateBody(param, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, foreignContractListDto);
    }

    /**
     * 删除外商合同表体
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除外商合同表体")
    @DeleteMapping("/{ids}")
    public ResultObject<?> delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        this.foreignContractListService.delete(ids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }
}