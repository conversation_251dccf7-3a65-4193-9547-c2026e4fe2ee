package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadExportParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractListParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractWithDetailParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractWithDetailDto;
import com.dcjet.cs.auxiliaryMaterials.service.BizINonStateAuxmatAggrContractHeadService;
import com.dcjet.cs.dto.dec.BizIWarehouseReceiptHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@RestController
@RequestMapping("v1/bizINonStateAuxmatAggrContractHead")
@Api(tags = "接口")
public class BizINonStateAuxmatAggrContractHeadController extends BaseController {
    @Resource
    private BizINonStateAuxmatAggrContractHeadService bizINonStateAuxmatAggrContractHeadService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> getListPaged(@RequestBody BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> paged = bizINonStateAuxmatAggrContractHeadService.getListPaged(bizINonStateAuxmatAggrContractHeadParam, pageParam);
        return paged;
    }
    /**
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizINonStateAuxmatAggrContractHeadDto> insert(@Valid @RequestBody BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        ResultObject<BizINonStateAuxmatAggrContractHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizINonStateAuxmatAggrContractHeadDto bizINonStateAuxmatAggrContractHeadDto = bizINonStateAuxmatAggrContractHeadService.insert(bizINonStateAuxmatAggrContractHeadParam, userInfo);
        if (bizINonStateAuxmatAggrContractHeadDto != null) {
            resultObject.setData(bizINonStateAuxmatAggrContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizINonStateAuxmatAggrContractHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        bizINonStateAuxmatAggrContractHeadParam.setId(sid);
        BizINonStateAuxmatAggrContractHeadDto bizINonStateAuxmatAggrContractHeadDto = bizINonStateAuxmatAggrContractHeadService.update(bizINonStateAuxmatAggrContractHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizINonStateAuxmatAggrContractHeadDto != null) {
            resultObject.setData(bizINonStateAuxmatAggrContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizINonStateAuxmatAggrContractHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizINonStateAuxmatAggrContractHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizINonStateAuxmatAggrContractHeadDto> bizINonStateAuxmatAggrContractHeadDtos = bizINonStateAuxmatAggrContractHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizINonStateAuxmatAggrContractHeadDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizINonStateAuxmatAggrContractHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizINonStateAuxmatAggrContractHeadDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        for(BizINonStateAuxmatAggrContractHeadDto item : list) {

            // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
            Map<String, String> bizMerchantMap = bizINonStateAuxmatAggrContractHeadService.createMerchantMap(bizMerchants);
            // 转换业务类型
            if (StringUtils.isNotBlank(item.getBusinessType())){
                item.setBusinessType(item.getBusinessType() + " " + CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(item.getBusinessType()));
            }
            // 转换订单数据状态
            if (StringUtils.isNotBlank(item.getStatus())){
                item.setStatus(item.getStatus() + " " + CommonEnum.STATE_ENUM.getValue(item.getStatus()));
            }
            //转换审核状态
            if (StringUtils.isNotBlank(item.getApprStatus())) {
                item.setApprStatus(CommonEnum.OrderApprStatusEnum.getValue(item.getApprStatus()));
            }
            if (StringUtils.isNotBlank(item.getSupplier())){
                item.setSupplier(bizINonStateAuxmatAggrContractHeadService.getMerchantNameSafely(bizMerchantMap, item.getSupplier()));
            }
            //转换国内委托人
            if (StringUtils.isNotBlank(item.getDomesticPrincipal())){
                item.setDomesticPrincipal(bizINonStateAuxmatAggrContractHeadService.getMerchantNameSafely(bizMerchantMap, item.getDomesticPrincipal()));
            }


        }
    }

    /**
     * 确认数据状态接口
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm/{sid}")
    public ResultObject confirmStatus(@PathVariable String sid, UserInfoToken userInfo) {
        return bizINonStateAuxmatAggrContractHeadService.confirmStatus(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizINonStateAuxmatAggrContractHeadService.sendApproval(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizINonStateAuxmatAggrContractHeadService.invalidate(sid, userInfo);
    }

    /**
     * 校验是否存在 同一个合同号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("校验是否存在 同一个合同号是否存在未作废的数据")
    @PostMapping("/checkContractIdNotCancel")
    public ResultObject checkContractIdNotCancel(@RequestBody BizINonStateAuxmatAggrContractHeadParam params, UserInfoToken userInfo) {
        return bizINonStateAuxmatAggrContractHeadService.checkContractIdNotCancel(params, userInfo);
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("版本复制")
    @PostMapping("/copyVersion")
    public ResultObject copyVersion(@RequestBody BizINonStateAuxmatAggrContractHeadParam params, UserInfoToken userInfo) {
        return bizINonStateAuxmatAggrContractHeadService.copyVersion(params, userInfo);
    }

    /**
     * 校验单行表体数据
     * @param detail 表体数据
     * @param userInfo 用户信息
     * @return 返回校验结果
     */
    @ApiOperation("校验单行表体数据")
    @PostMapping("/validateDetailData")
    public ResultObject<String> validateDetailData(@Valid @RequestBody BizINonStateAuxmatAggrContractListParam detail, UserInfoToken userInfo) {
        try {
            String errorMessage = bizINonStateAuxmatAggrContractHeadService.validateSingleDetailData(detail);

            if (StringUtils.isBlank(errorMessage)) {
                return ResultObject.createInstance(true, "校验通过", "");
            } else {
                return ResultObject.createInstance(false, errorMessage);
            }
        } catch (Exception e) {
            return ResultObject.createInstance(false, "校验异常: " + e.getMessage(), "");
        }
    }

    /**
     * 新增表头和表体数据
     * @param bizINonStateAuxmatAggrContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("新增表头和表体数据")
    @PostMapping("/insertAggrContractWithDetails")
    public ResultObject<BizINonStateAuxmatAggrContractWithDetailDto> insertAggrContractWithDetails(@Valid @RequestBody BizINonStateAuxmatAggrContractWithDetailParam bizINonStateAuxmatAggrContractWithDetailParam, UserInfoToken userInfo) {
        ResultObject<BizINonStateAuxmatAggrContractWithDetailDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        try {
            BizINonStateAuxmatAggrContractWithDetailDto bizINonStateAuxmatAggrContractWithDetailDto = bizINonStateAuxmatAggrContractHeadService.insertAggrContractWithDetails(bizINonStateAuxmatAggrContractWithDetailParam, userInfo);
            if (bizINonStateAuxmatAggrContractWithDetailDto != null) {
                resultObject.setData(bizINonStateAuxmatAggrContractWithDetailDto);
            } else {
                resultObject.setSuccess(false);
                resultObject.setMessage(ResultObject.INSERT_FAIL);
            }
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage(e.getMessage());
        }
        return resultObject;
    }

    /**
     * 修改表头和表体数据
     * @param bizINonStateAuxmatAggrContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("修改表头和表体数据")
    @PostMapping("/updateAggrContractWithDetails")
    public ResultObject<BizINonStateAuxmatAggrContractWithDetailDto> updateAggrContractWithDetails(@Valid @RequestBody BizINonStateAuxmatAggrContractWithDetailParam bizINonStateAuxmatAggrContractWithDetailParam, UserInfoToken userInfo) {
        ResultObject<BizINonStateAuxmatAggrContractWithDetailDto> resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        try {
            BizINonStateAuxmatAggrContractWithDetailDto bizINonStateAuxmatAggrContractWithDetailDto = bizINonStateAuxmatAggrContractHeadService.updateAggrContractWithDetails(bizINonStateAuxmatAggrContractWithDetailParam, userInfo);
            if (bizINonStateAuxmatAggrContractWithDetailDto != null) {
                resultObject.setData(bizINonStateAuxmatAggrContractWithDetailDto);
            } else {
                resultObject.setSuccess(false);
                resultObject.setMessage(ResultObject.UPDATE_FAIL);
            }
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage(e.getMessage());
        }
        return resultObject;
    }
    @ApiOperation("分页查询接口")
    @PostMapping("/listToCustomerAccount")
    public ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> getListPagedToCustomerAccount(@RequestBody BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> paged = bizINonStateAuxmatAggrContractHeadService.getListPagedToCustomerAccount(bizINonStateAuxmatAggrContractHeadParam, pageParam);
        return paged;
    }

    @ApiOperation(value = "协议会签单")
    @PostMapping("printAgreement")
    public ResponseEntity printAgreement(@RequestBody BizINonStateAuxmatAggrContractHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("协议会签单") + param.getType();
        String exportFileName = bizINonStateAuxmatAggrContractHeadService.printAgreement("协议会签单", param,userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        if(".pdf".equals(param.getType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    @ApiOperation(value = "协议会签单")
    @PostMapping("printContract")
    public ResponseEntity printContract(@RequestBody BizINonStateAuxmatAggrContractHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("合同会签单") + param.getType();
        String exportFileName = bizINonStateAuxmatAggrContractHeadService.printAgreement("合同会签单", param,userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        if(".pdf".equals(param.getType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }


    /**
     * 划款通知
     * @param bizINonStateAuxmatAggrContractHead
     * @param userInfo
     * @return
     */
    @ApiOperation("划款通知")
    @PostMapping("handlerTransferNotice")
    public ResultObject handlerTransferNotice(@RequestBody BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead, UserInfoToken userInfo) {
        return bizINonStateAuxmatAggrContractHeadService.handlerTransferNotice(bizINonStateAuxmatAggrContractHead, userInfo);
    }

    /**
     * 划款通知保存
     * @param bizINonStateAuxmatAggrContractHead
     * @param userInfo
     * @return
     */
    @ApiOperation("划款通知保存")
    @PostMapping("saveTransferNotice")
    public ResultObject saveTransferNotice(@RequestBody BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead, UserInfoToken userInfo) {
        return bizINonStateAuxmatAggrContractHeadService.saveTransferNotice(bizINonStateAuxmatAggrContractHead, userInfo);
    }
    /**
     * 划款通知保存校验
     * @param bizINonStateAuxmatAggrContractHead
     * @param
     * @return
     */
    @ApiOperation("划款通知保存校验")
    @PostMapping("checkTransferNotice")
    public ResultObject checkTransferNotice(@RequestBody BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead) {
        return bizINonStateAuxmatAggrContractHeadService.checkTransferNotice(bizINonStateAuxmatAggrContractHead);
    }
    /**
     * 划款通知保存
     * @param bizINonStateAuxmatAggrContractHead
     * @param userInfo
     * @return
     */
    @ApiOperation("划款通知保存打印")
    @PostMapping("saveTransferNoticePrint")
    public ResponseEntity saveTransferNoticePrint(@RequestBody BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead, UserInfoToken userInfo) throws Exception {
        return bizINonStateAuxmatAggrContractHeadService.saveTransferNoticePrint(bizINonStateAuxmatAggrContractHead, userInfo);
    }
}
