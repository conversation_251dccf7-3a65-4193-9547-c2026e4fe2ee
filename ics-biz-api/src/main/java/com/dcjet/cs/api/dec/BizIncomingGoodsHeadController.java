package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.model.BizIncomingGoodsHeadList;
import com.dcjet.cs.dec.service.BizIncomingGoodsHeadService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import com.dcjet.cs.dto.dec.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;

/**
* TBizIncomingGoodsHeadController层
*
* <AUTHOR>
* @date 2025-05-22 15:28:59
*/
@RestController
@RequestMapping("v1/bizIncomingGoodsHead")
@Api(tags = "进货管理-表头数据接口")
public class BizIncomingGoodsHeadController {

    @Resource
    private BizIncomingGoodsHeadService tBizIncomingGoodsHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进货管理-表头数据数据
     * @param tBizIncomingGoodsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("list")
    public ResultObject<List<BizIncomingGoodsHeadDto>> getListPaged(@RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsHeadDto>> paged = tBizIncomingGoodsHeadService.getListPaged(tBizIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param tBizIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizIncomingGoodsHeadDto> insert(@Valid @RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIncomingGoodsHeadDto tBizIncomingGoodsHeadDto = tBizIncomingGoodsHeadService.insert(tBizIncomingGoodsHeadParam, userInfo);
        if (tBizIncomingGoodsHeadDto != null) {
            resultObject.setData(tBizIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param tBizIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/{id}")
    public ResultObject<BizIncomingGoodsHeadDto> update(@PathVariable String id, @Valid @RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        tBizIncomingGoodsHeadParam.setId(id);
        BizIncomingGoodsHeadDto tBizIncomingGoodsHeadDto = tBizIncomingGoodsHeadService.update(tBizIncomingGoodsHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (tBizIncomingGoodsHeadDto != null) {
            resultObject.setData(tBizIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		tBizIncomingGoodsHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIncomingGoodsHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIncomingGoodsHeadList> bizIncomingGoodsHeadLists = tBizIncomingGoodsHeadService.selectAllNew(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIncomingGoodsHeadLists);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIncomingGoodsHeadLists);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIncomingGoodsHeadList> list) {
        for(BizIncomingGoodsHeadList item : list) {
            // 处理进货单据状态
            if (StringUtils.isNotBlank(item.getDataState())) {
                if ("0".equals(item.getDataState())) {
                    item.setDataState("0 编制");
                }else if ("1".equals(item.getDataState())) {
                    item.setDataState("1 确认");
                }else if ("2".equals(item.getDataState())) {
                    item.setDataState("2 作废");
                }
            }

            if (StringUtils.isNotBlank(item.getSellStatus())) {
                if ("0".equals(item.getSellStatus())) {
                    item.setSellStatus("0 编制");
                }else if ("1".equals(item.getSellStatus())) {
                    item.setSellStatus("1 确认");
                }else if ("2".equals(item.getSellStatus())) {
                    item.setSellStatus("2 作废");
                }else if ("-1".equals(item.getSellStatus())) {
                    item.setSellStatus("-1 未生成");
                }
            }

            // 处理供应商
            // 不处理

        }
    }



    /**
     * 进货管理-获取供应商列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getSupplierList(params,userInfo);
    }


    /**
     * 进货管理-获取港口列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取供应商列表信息")
    @PostMapping("/getPortList")
    public ResultObject getPortList(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getPortList(params,userInfo);
    }



    /**
     * 进货管理-获取币制信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取币制信息")
    @PostMapping("/getCurrList")
    public ResultObject getCurrList(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getCurrList(params,userInfo);
    }



    /**
     * 进货管理-获取价格条款
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取价格条款")
    @PostMapping("/getPriceTermList")
    public ResultObject getPriceTermList(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getPriceTermList(params,userInfo);
    }


    /**
     * 进货管理-获取单位信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取单位信息")
    @PostMapping("/getUnitList")
    public ResultObject getUnitList(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getUnitList(params,userInfo);
    }


    /**
     * 确定 进货信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("确定进货信息")
    @PostMapping("/confirmIncomingGoods")
    public ResultObject<BizIncomingGoodsHeadDto> confirmIncomingGoods(@Valid @RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.confirmIncomingGoods(params,userInfo);
    }


    @ApiOperation("获取可提取的合同信息")
    @PostMapping("/getExtractContractInfo")
    public ResultObject<List<BizIAuxmatForContractHeadDto>> getExtractContractInfo(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getExtractContractInfo(params,userInfo);
    }



    @ApiOperation("提取合同")
    @PostMapping("/extractContract")
    public ResultObject extractContract(@RequestBody BizIAuxmatForContractHeadParam params,UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.extractContract(params,userInfo);
    }


    @ApiOperation("根据表头ID获取表头信息")
    @PostMapping("/getHeadInfoById")
    public ResultObject<BizIncomingGoodsHeadDto> getHeadInfoById(@RequestBody BizIncomingGoodsHeadParam params,UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getHeadInfoById(params,userInfo);
    }



    /**
     * 分页获取进货管理-表头数据数据
     * @param tBizIncomingGoodsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("listNew")
    public ResultObject<List<BizIncomingGoodsHeadList>> getListPagedNew(@RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsHeadList>> paged = tBizIncomingGoodsHeadService.getListPagedNew(tBizIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("获取进货信息供应商去重汇总")
    @PostMapping("/getSupplierListDistinct")
    public ResultObject<List<Map<String,String>>> getSupplierListDistinct(@RequestBody BizIAuxmatForContractHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getSupplierListDistinct(params,userInfo);
    }



    @ApiOperation("作废数据")
    @PostMapping("/cancel")
    public ResultObject cancel(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.cancel(params,userInfo);
    }



    @ApiOperation("退单")
    @PostMapping("/returnOrder")
    public ResultObject returnOrder(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.returnOrder(params,userInfo);
    }


    @ApiOperation("红冲")
    @PostMapping("/redFlush")
    public ResultObject redFlush(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.redFlush(params,userInfo);
    }


    /**
     * 压缩包打印
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("压缩包打印")
    @PostMapping("/generateTB")
    public ResponseEntity licenceViewZip(@RequestBody GenerateTBParams param, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.generateTB(param,userInfo);
    }


    /**
     * 获取投保险险别数据
     */
    @ApiOperation("获取投保险险别数据")
    @PostMapping("/getInsuranceCategoryList")
    public ResultObject<List<Map<String,String>>> getInsuranceCategoryList(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getInsuranceCategoryList(params,userInfo);
    }


}
