package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizExportGoodsListService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListDto;
import com.dcjet.cs.dto.dec.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizExportGoodsListController层
*
* <AUTHOR>
* @date 2025-07-07 13:22:20
*/
@RestController
@RequestMapping("v1/bizExportGoodsList")
@Api(tags = "第9条线-非国营贸易出口辅料-出货信息表体（商品信息）接口")
public class BizExportGoodsListController{

    @Resource
    private BizExportGoodsListService bizExportGoodsListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取第9条线-非国营贸易出口辅料-出货信息表体（商品信息）数据
     * @param bizExportGoodsListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取第9条线-非国营贸易出口辅料-出货信息表体（商品信息）数据")
    @PostMapping("list")
    public ResultObject<List<BizExportGoodsListDto>> getListPaged(@RequestBody BizExportGoodsListParam bizExportGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizExportGoodsListDto>> paged = bizExportGoodsListService.getListPaged(bizExportGoodsListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizExportGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizExportGoodsListDto> insert(@Valid @RequestBody BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizExportGoodsListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizExportGoodsListDto bizExportGoodsListDto = bizExportGoodsListService.insert(bizExportGoodsListParam, userInfo);
        if (bizExportGoodsListDto != null) {
            resultObject.setData(bizExportGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizExportGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizExportGoodsListDto> update(@PathVariable String sid, @Valid @RequestBody BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        bizExportGoodsListParam.setId(sid);
        BizExportGoodsListDto bizExportGoodsListDto = bizExportGoodsListService.update(bizExportGoodsListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizExportGoodsListDto != null) {
            resultObject.setData(bizExportGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizExportGoodsListService.delete(sids, userInfo);
        return resultObject;
    }



    @ApiOperation("根据ID获取出货信息表体")
    @GetMapping("/getListById/{id}")
    public ResultObject<BizExportGoodsListDto> getListById(@PathVariable String id, UserInfoToken userInfo) {
        return bizExportGoodsListService.getListById(id, userInfo);
    }

    @ApiOperation("修改数量/单价")
    @PostMapping("/updateNumOrPrice")
    public ResultObject<BizExportGoodsListDto> updateNumOrPrice(@RequestBody @Valid BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.updateNumOrPrice(bizExportGoodsListParam, userInfo);
    }

    @ApiOperation("修改起始箱号")
    @PostMapping("/updateStartBoxNo")
    public ResultObject<BizExportGoodsListDto> updateStartBoxNo(@RequestBody @Valid BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.updateStartBoxNo(bizExportGoodsListParam, userInfo);
    }


    @ApiOperation("修改终止箱号")
    @PostMapping("/updateEndBoxNo")
    public ResultObject<BizExportGoodsListDto> updateEndBoxNo(@RequestBody @Valid BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.updateEndBoxNo(bizExportGoodsListParam, userInfo);
    }


    @ApiOperation("修改毛重")
    @PostMapping("/updateGrossWeight")
    public ResultObject<BizExportGoodsListDto> updateGrossWeight(@RequestBody @Valid BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.updateGrossWeight(bizExportGoodsListParam, userInfo);
    }

    @ApiOperation("修改净重")
    @PostMapping("/updateNetWeight")
    public ResultObject<BizExportGoodsListDto> updateNetWeight(@RequestBody @Valid BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.updateNetWeight(bizExportGoodsListParam, userInfo);
    }

    @ApiOperation("修改长/高/宽")
    @PostMapping("/updateGoodsLengthHeightWidth")
    public ResultObject<BizExportGoodsListDto> updateGoodsLengthHeightWidth(@RequestBody @Valid BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.updateGoodsLengthHeightWidth(bizExportGoodsListParam, userInfo);
    }


    @ApiOperation("修改数量（卷）")
    @PostMapping("/updateQtyJ")
    public ResultObject<BizExportGoodsListDto> updateQtyJ(@RequestBody @Valid BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.updateQtyJ(bizExportGoodsListParam, userInfo);
    }



    @ApiOperation("获取表体汇总信息")
    @PostMapping("/getSummary/{parentId}")
    public ResultObject<BizExportGoodsListSummaryDto> getSummary(@PathVariable String parentId, UserInfoToken userInfo) {
        return bizExportGoodsListService.getSummary(parentId, userInfo);
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizExportGoodsListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizExportGoodsListDto> bizExportGoodsListDtos = bizExportGoodsListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizExportGoodsListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizExportGoodsListDtos);
    }


    /**
     * 获取可以提取的外商合同信息
     */
    @ApiOperation("获取可提取外商合同信息")
    @PostMapping("/getExtractContractList")
    public ResultObject getExtractContractList(@RequestBody BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsListService.getExtractContractList(param, userInfo);
    }





    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizExportGoodsListDto> list) {
        for(BizExportGoodsListDto item : list) {
        
        }
    }



    @ApiOperation("根据合同号获取外商合同表体数据")
    @PostMapping("/getListByContractNo")
    public ResultObject<List<BizENonStateAuxmatAggrContractListDto>> getListByContractNo(@RequestBody BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        return bizExportGoodsListService.getListByContractNo(bizExportGoodsListBoxParam, userInfo);
    }



    @ApiOperation("提取外商合同表体数据")
    @PostMapping("/extractContractList")
    public ResultObject extractContractList(@RequestBody ExtractContractListParam param, UserInfoToken userInfo) {
        return bizExportGoodsListService.extractContractList(param, userInfo);
    }


}
