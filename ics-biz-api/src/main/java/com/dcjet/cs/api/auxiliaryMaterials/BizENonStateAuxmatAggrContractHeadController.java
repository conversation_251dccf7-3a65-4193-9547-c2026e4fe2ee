package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractHeadParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractHeadExportParam;
import com.dcjet.cs.auxiliaryMaterials.service.BizENonStateAuxmatAggrContractHeadService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@RestController
@RequestMapping("v1/bizENonStateAuxmatAggrContractHead")
@Api(tags = "接口")
public class BizENonStateAuxmatAggrContractHeadController extends BaseController {
    @Resource
    private BizENonStateAuxmatAggrContractHeadService bizENonStateAuxmatAggrContractHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizENonStateAuxmatAggrContractHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> getListPaged(@RequestBody BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> paged = bizENonStateAuxmatAggrContractHeadService.getListPaged(bizENonStateAuxmatAggrContractHeadParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizENonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizENonStateAuxmatAggrContractHeadDto> insert(@Valid @RequestBody BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        ResultObject<BizENonStateAuxmatAggrContractHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizENonStateAuxmatAggrContractHeadDto bizENonStateAuxmatAggrContractHeadDto = bizENonStateAuxmatAggrContractHeadService.insert(bizENonStateAuxmatAggrContractHeadParam, userInfo);
        if (bizENonStateAuxmatAggrContractHeadDto != null) {
            resultObject.setData(bizENonStateAuxmatAggrContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizENonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizENonStateAuxmatAggrContractHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        bizENonStateAuxmatAggrContractHeadParam.setSid(sid);
        BizENonStateAuxmatAggrContractHeadDto bizENonStateAuxmatAggrContractHeadDto = bizENonStateAuxmatAggrContractHeadService.update(bizENonStateAuxmatAggrContractHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizENonStateAuxmatAggrContractHeadDto != null) {
            resultObject.setData(bizENonStateAuxmatAggrContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizENonStateAuxmatAggrContractHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizENonStateAuxmatAggrContractHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizENonStateAuxmatAggrContractHeadDto> bizENonStateAuxmatAggrContractHeadDtos = bizENonStateAuxmatAggrContractHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizENonStateAuxmatAggrContractHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizENonStateAuxmatAggrContractHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizENonStateAuxmatAggrContractHeadDto> list) {
        for(BizENonStateAuxmatAggrContractHeadDto item : list) {
        }
    }

    @ApiOperation("分页查询接口")
    @PostMapping("listToCustomerAccount")
    public ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> getListPagedToCustomerAccount(@RequestBody BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> paged = bizENonStateAuxmatAggrContractHeadService.getListPagedToCustomerAccount(bizENonStateAuxmatAggrContractHeadParam, pageParam,userInfo);
        return paged;
    }
}
