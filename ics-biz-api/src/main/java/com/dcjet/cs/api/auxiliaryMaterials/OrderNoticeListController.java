package com.dcjet.cs.api.auxiliaryMaterials;

import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListParam;
import com.dcjet.cs.auxiliaryMaterials.service.OrderNoticeListService;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListSummaryDto;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/importAuxMat/orderNoticeList")
@Api(tags = "国营贸易进口辅料-订货通知表体")
public class OrderNoticeListController extends BaseController {
    @Resource
    private OrderNoticeListService orderNoticeListService;

    /**
     * 获取分页列表
     *
     * @param orderNoticeListParam 订货通知表体参数
     * @param pageParam            分页参数
     * @param userInfo             用户信息
     * @return 订货通知表体分页列表
     */
    @ApiOperation("获取订货通知表体分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication("/tobacco/auxiliaryMaterials/orderNotice")
    public ResultObject<List<OrderNoticeListDto>> getListPaged(@RequestBody OrderNoticeListParam orderNoticeListParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.orderNoticeListService.getListPaged(orderNoticeListParam, pageParam, userInfo);
    }

    /**
     * 获取表体汇总数据
     *
     * @param orderNoticeListParam 订货通知表体参数
     * @param userInfo             用户信息
     * @return 响应数据
     */
    @ApiOperation("获取表体汇总数据")
    @PostMapping("/summary")
    public ResultObject<OrderNoticeListSummaryDto> getListSummary(@RequestBody OrderNoticeListParam orderNoticeListParam
            , UserInfoToken<?> userInfo) {
        OrderNoticeListSummaryDto listSummaryDto = this.orderNoticeListService.getListSummary(orderNoticeListParam, userInfo);
        return ResultObject.createInstance(true, "查询成功", listSummaryDto);
    }

    /**
     * 修改订货通知表体
     *
     * @param id                   主键
     * @param orderNoticeListParam 订货通知表体参数
     * @param userInfo             用户信息
     * @return 订货通知传输模型
     */
    @ApiOperation("修改订货通知表体")
    @PutMapping("/{id}")
    public ResultObject<?> update(@PathVariable String id, @RequestBody @Valid OrderNoticeListParam orderNoticeListParam
            , UserInfoToken<?> userInfo) {
        orderNoticeListParam.setId(id);
        OrderNoticeListDto orderNoticeListDto = this.orderNoticeListService.update(orderNoticeListParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, orderNoticeListDto);
    }

    /**
     * 删除订货通知表体
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除订货通知表体")
    @DeleteMapping("/{ids}")
    public ResultObject<?> delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        this.orderNoticeListService.delete(ids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }
}