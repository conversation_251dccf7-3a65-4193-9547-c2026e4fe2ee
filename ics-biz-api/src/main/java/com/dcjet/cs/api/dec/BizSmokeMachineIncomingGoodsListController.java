package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizSmokeCommonService;
import com.dcjet.cs.dec.service.BizSmokeMachineIncomingGoodsListService;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListExportParam;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;

/**
* BizSmokeMachineIncomingGoodsListController层
*
* <AUTHOR>
* @date 2025-07-03 16:10:01
*/
@RestController
@RequestMapping("v1/bizSmokeMachineIncomingGoodsList")
@Api(tags = "进过管理-表体列表接口")
public class BizSmokeMachineIncomingGoodsListController{

    @Resource
    private BizSmokeMachineIncomingGoodsListService bizSmokeMachineIncomingGoodsListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;


    @Resource
    private BizSmokeCommonService bizSmokeCommonService;
 
    /**
     * 分页获取进过管理-表体列表数据
     * @param bizSmokeMachineIncomingGoodsListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进过管理-表体列表数据")
    @PostMapping("list")
    public ResultObject<List<BizSmokeMachineIncomingGoodsListDto>> getListPaged(@RequestBody BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizSmokeMachineIncomingGoodsListDto>> paged = bizSmokeMachineIncomingGoodsListService.getListPaged(bizSmokeMachineIncomingGoodsListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizSmokeMachineIncomingGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("insert")
    public ResultObject<BizSmokeMachineIncomingGoodsListDto> insert(@Valid @RequestBody BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizSmokeMachineIncomingGoodsListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizSmokeMachineIncomingGoodsListDto bizSmokeMachineIncomingGoodsListDto = bizSmokeMachineIncomingGoodsListService.insert(bizSmokeMachineIncomingGoodsListParam, userInfo);
        if (bizSmokeMachineIncomingGoodsListDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizSmokeMachineIncomingGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizSmokeMachineIncomingGoodsListDto> update(@PathVariable String sid, @Valid @RequestBody BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        bizSmokeMachineIncomingGoodsListParam.setId(sid);
        BizSmokeMachineIncomingGoodsListDto bizSmokeMachineIncomingGoodsListDto = bizSmokeMachineIncomingGoodsListService.update(bizSmokeMachineIncomingGoodsListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizSmokeMachineIncomingGoodsListDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    @ApiOperation("更新表体数量")
    @PostMapping("/updateQuantity")
    public ResultObject updateQuantity(@RequestBody @Valid BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsListService.updateQuantity(bizSmokeMachineIncomingGoodsListParam, userInfo);
    }


    @ApiOperation("更新表体单价")
    @PostMapping("/updateUnitPrice")
    public ResultObject updateUnitPrice(@RequestBody @Valid BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsListService.updateUnitPrice(bizSmokeMachineIncomingGoodsListParam, userInfo);
    }


    @ApiOperation("更新表体通用字段不涉及联动参数")
    @PostMapping("/updateCommonFiled")
    public ResultObject updateCommonFiled(@RequestBody @Valid BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsListService.updateCommonFiled(bizSmokeMachineIncomingGoodsListParam, userInfo);
    }

    @ApiOperation("根据Sid获取表体的信息")
    @PostMapping("/getBizSmokeMachineIncomingGoodsListById/{id}")
    public ResultObject getBizSmokeMachineIncomingGoodsListById(@PathVariable String id,@RequestBody BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsListService.getBizSmokeMachineIncomingGoodsListById(id,bizSmokeMachineIncomingGoodsListParam, userInfo);
    }




    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizSmokeMachineIncomingGoodsListService.delete(sids, userInfo);
        return resultObject;
    }



    @ApiOperation("获取通用当前表体数据")
    @PostMapping("/getCommonKeyValueList")
    public ResultObject getCommonKeyValueList(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeCommonService.getListCommonKeyValueList(userInfo, Arrays.asList(BizSmokeCommonService.BizTypeEnum.UNIT));
    }



    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizSmokeMachineIncomingGoodsListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizSmokeMachineIncomingGoodsListDto> bizSmokeMachineIncomingGoodsListDtos = bizSmokeMachineIncomingGoodsListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizSmokeMachineIncomingGoodsListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizSmokeMachineIncomingGoodsListDtos);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizSmokeMachineIncomingGoodsListDto> list) {
        for(BizSmokeMachineIncomingGoodsListDto item : list) {

        }
    }


}
