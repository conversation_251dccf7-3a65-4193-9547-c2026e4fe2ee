package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizBpAnalyseOrderListBoxBoxService;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxBoxDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxBoxExportParam;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxBoxParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizBpAnalyseOrderListBoxBoxController层
*
* <AUTHOR>
* @date 2025-08-05 11:08:41
*/
@RestController
@RequestMapping("v1/bizBpAnalyseOrderListBoxBox")
@Api(tags = "（第7条线）出料加工进口薄片-装箱列表-子表的子表接口")
public class BizBpAnalyseOrderListBoxBoxController{

    @Resource
    private BizBpAnalyseOrderListBoxBoxService bizBpAnalyseOrderListBoxBoxService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取（第7条线）出料加工进口薄片-装箱列表-子表的子表数据
     * @param bizBpAnalyseOrderListBoxBoxParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取（第7条线）出料加工进口薄片-装箱列表-子表的子表数据")
    @PostMapping("list")
    public ResultObject<List<BizBpAnalyseOrderListBoxBoxDto>> getListPaged(@RequestBody BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizBpAnalyseOrderListBoxBoxDto>> paged = bizBpAnalyseOrderListBoxBoxService.getListPaged(bizBpAnalyseOrderListBoxBoxParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizBpAnalyseOrderListBoxBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizBpAnalyseOrderListBoxBoxDto> insert(@Valid @RequestBody BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam, UserInfoToken userInfo) {
        ResultObject<BizBpAnalyseOrderListBoxBoxDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizBpAnalyseOrderListBoxBoxDto bizBpAnalyseOrderListBoxBoxDto = bizBpAnalyseOrderListBoxBoxService.insert(bizBpAnalyseOrderListBoxBoxParam, userInfo);
        if (bizBpAnalyseOrderListBoxBoxDto != null) {
            resultObject.setData(bizBpAnalyseOrderListBoxBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizBpAnalyseOrderListBoxBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizBpAnalyseOrderListBoxBoxDto> update(@PathVariable String sid, @Valid @RequestBody BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam, UserInfoToken userInfo) {
        bizBpAnalyseOrderListBoxBoxParam.setId(sid);
        BizBpAnalyseOrderListBoxBoxDto bizBpAnalyseOrderListBoxBoxDto = bizBpAnalyseOrderListBoxBoxService.update(bizBpAnalyseOrderListBoxBoxParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizBpAnalyseOrderListBoxBoxDto != null) {
            resultObject.setData(bizBpAnalyseOrderListBoxBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizBpAnalyseOrderListBoxBoxService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizBpAnalyseOrderListBoxBoxExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizBpAnalyseOrderListBoxBoxDto> bizBpAnalyseOrderListBoxBoxDtos = bizBpAnalyseOrderListBoxBoxService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizBpAnalyseOrderListBoxBoxDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizBpAnalyseOrderListBoxBoxDtos);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizBpAnalyseOrderListBoxBoxDto> list) {
        for(BizBpAnalyseOrderListBoxBoxDto item : list) {
        
        }
    }


}
