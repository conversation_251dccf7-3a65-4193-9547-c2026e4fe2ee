package com.dcjet.cs.api.equipment;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanHeadDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanHeadParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanHeadExportParam;
import com.dcjet.cs.equipment.service.BizIEquipmentPlanHeadService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@RestController
@RequestMapping("v1/bizIEquipmentPlanHead")
@Api(tags = "接口")
public class BizIEquipmentPlanHeadController extends BaseController {
    @Resource
    private BizIEquipmentPlanHeadService bizIEquipmentPlanHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIEquipmentPlanHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIEquipmentPlanHeadDto>> getListPaged(@RequestBody BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIEquipmentPlanHeadDto>> paged = bizIEquipmentPlanHeadService.getListPaged(bizIEquipmentPlanHeadParam, pageParam);
        return paged;
    }
    /**
     * @param bizIEquipmentPlanHeadParam
     * @param userInfo
     * @return
     */

    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIEquipmentPlanHeadDto> insert(@Valid @RequestBody BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIEquipmentPlanHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIEquipmentPlanHeadDto bizIEquipmentPlanHeadDto = bizIEquipmentPlanHeadService.insert(bizIEquipmentPlanHeadParam, userInfo);
        if (bizIEquipmentPlanHeadDto != null) {
            resultObject.setData(bizIEquipmentPlanHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIEquipmentPlanHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIEquipmentPlanHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, UserInfoToken userInfo) {
        bizIEquipmentPlanHeadParam.setId(sid);
        BizIEquipmentPlanHeadDto bizIEquipmentPlanHeadDto = bizIEquipmentPlanHeadService.update(bizIEquipmentPlanHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentPlanHeadDto != null) {
            resultObject.setData(bizIEquipmentPlanHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIEquipmentPlanHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIEquipmentPlanHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIEquipmentPlanHeadDto> bizIEquipmentPlanHeadDtos = bizIEquipmentPlanHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIEquipmentPlanHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIEquipmentPlanHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIEquipmentPlanHeadDto> list) {
        for(BizIEquipmentPlanHeadDto item : list) {
        }
    }

    /**
     * 确认数据状态接口
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm/{sid}")
    public ResultObject confirmStatus(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIEquipmentPlanHeadService.confirmStatus(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("发送审批接口")
    @PostMapping("sendAudit/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIEquipmentPlanHeadService.sendApproval(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIEquipmentPlanHeadService.invalidate(sid, userInfo);
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("版本复制")
    @PostMapping("/copyVersion")
    public ResultObject copyVersion(@RequestBody BizIEquipmentPlanHeadParam params, UserInfoToken userInfo) {
        return bizIEquipmentPlanHeadService.copyVersion(params, userInfo);
    }


    /**
     * 获取预警设置信息
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("获取预警设置信息")
    @GetMapping("warningSettings/{sid}")
    public ResultObject getWarningSettings(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIEquipmentPlanHeadService.getWarningSettings(sid, userInfo);
    }
    /**
     * 修改预警设置
     * @param params
     * @param userInfo
     * @return
     */
    @ApiOperation("修改预警设置")
    @PostMapping("warningSettings")
    public ResultObject updateWarningSetting(@RequestBody BizIEquipmentPlanHeadParam params,  UserInfoToken userInfo) {
        return bizIEquipmentPlanHeadService.updateWarningSetting(params, userInfo);
    }

    /**
     * 获取预警设置信息
     * @param userInfo
     * @return
     */
    @ApiOperation("获取预警设置信息")
    @PostMapping("getWaringPlanList")
    public ResultObject getWaringPlanList(UserInfoToken userInfo) {
        return bizIEquipmentPlanHeadService.getWaringPlanList(userInfo);
    }
}
