package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.service.BizIOrderHeadService;
import com.dcjet.cs.dec.service.BizIOrderHeadService;
import com.dcjet.cs.dec.service.BizISellHeadService;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIPurchaseListParam;
import com.dcjet.cs.dto.dec.BizISellHeadDto;
import com.dcjet.cs.dto.dec.BizISellHeadParam;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
* BizISellHeadController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:58
*/
@RestController
@RequestMapping("v1/BizISellHead")
@Api(tags = "进口管理-进货信息表头接口")
public class BizISellHeadController {

    @Resource
    private BizISellHeadService BizISellHeadService;
    @Resource
    private BizIOrderHeadService bizIOrderHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进口管理-进货信息表头数据
     * @param BizISellHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-进货信息表头数据")
    @PostMapping("list")
    public ResultObject<List<BizISellHeadDto>> getListPaged(@RequestBody BizISellHeadParam BizISellHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizISellHeadDto>> paged = BizISellHeadService.getListPaged(BizISellHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param sid
     * @param BizISellHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizISellHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizISellHeadParam BizISellHeadParam, UserInfoToken userInfo) {
        BizISellHeadParam.setSid(sid);
        BizISellHeadDto BizISellHeadDto = BizISellHeadService.update(BizISellHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (BizISellHeadDto != null) {
            resultObject.setData(BizISellHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param BizISellHeadParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    @ApiOperation("获取进货信息表头数据 根据订单表头sid")
    @PostMapping("/getSellHeadByOrderSid")
    public ResultObject <BizISellHeadDto> getPurchaseHeadByOrderSid(@RequestBody BizISellHeadParam BizISellHeadParam, UserInfoToken userInfo) {
        return BizISellHeadService.getPurchaseHeadByOrderSid(BizISellHeadParam, userInfo);
    }

                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizISellHeadDto> list) {
        for(BizISellHeadDto item : list) {
        
        }
    }

    //退单
    @PostMapping("chargeback/{sid}")
    public ResultObject chargeback(@PathVariable String sid,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("退单成功"));
        BizISellHeadService.chargeback(sid,userInfo);
        return resultObject;
    }
    //出货退单
    @PostMapping("chargebackIncoming/{sid}")
    public ResultObject chargebackIncoming(@PathVariable String sid,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("退单成功"));
        BizISellHeadService.chargebackIncoming(sid,userInfo);
        return resultObject;
    }


    /**
     * 确认
     * @param bizIOrderHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("confirm")
    public ResultObject confirmOrderHead(@RequestBody BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIOrderHeadService.confirmOrderHead(bizIOrderHeadParam, userInfo);
        return resultObject;
    }

    //出货
    @PostMapping("confirmIncoming")
    public ResultObject confirmIncoming(@RequestBody BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIOrderHeadService.confirmIncoming(bizIOrderHeadParam, userInfo);
        return resultObject;
    }

    @PostMapping("confirmReceipt")
    public ResultObject confirmReceiptHead(@RequestBody BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIOrderHeadService.confirmReceiptHead(bizIOrderHeadParam, userInfo);
        return resultObject;
    }

    //刷新表体
    @PostMapping("confirmRefreshList")
    public ResultObject confirmRefreshList(@RequestBody BizIOrderHeadParam bizIOrderHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIOrderHeadService.confirmRefreshList(bizIOrderHeadParam, userInfo);
        return resultObject;
    }

}
