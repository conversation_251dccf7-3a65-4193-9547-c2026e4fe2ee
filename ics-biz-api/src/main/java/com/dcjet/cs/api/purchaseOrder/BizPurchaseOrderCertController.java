package com.dcjet.cs.api.purchaseOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderCertDto;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderCertParam;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderCertDto;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderCertParam;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderCertExportParam;
import com.dcjet.cs.purchaseOrder.service.BizPurchaseOrderCertService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@RestController
@RequestMapping("v1/bizPurchaseOrderCert")
@Api(tags = "接口")
public class BizPurchaseOrderCertController extends BaseController {
    @Resource
    private BizPurchaseOrderCertService bizPurchaseOrderCertService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizPurchaseOrderCertParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizPurchaseOrderCertDto>> getListPaged(@RequestBody BizPurchaseOrderCertParam bizPurchaseOrderCertParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizPurchaseOrderCertDto>> paged = bizPurchaseOrderCertService.getListPaged(bizPurchaseOrderCertParam, pageParam);
        return paged;
    }
    /**
     * @param bizPurchaseOrderCertParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizPurchaseOrderCertDto> insert(@Valid @RequestBody BizPurchaseOrderCertParam bizPurchaseOrderCertParam, UserInfoToken userInfo) {
        ResultObject<BizPurchaseOrderCertDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizPurchaseOrderCertDto bizPurchaseOrderCertDto = bizPurchaseOrderCertService.insert(bizPurchaseOrderCertParam, userInfo);
        if (bizPurchaseOrderCertDto != null) {
            resultObject.setData(bizPurchaseOrderCertDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizPurchaseOrderCertParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizPurchaseOrderCertDto> update(@PathVariable String sid, @Valid @RequestBody BizPurchaseOrderCertParam bizPurchaseOrderCertParam, UserInfoToken userInfo) {
        bizPurchaseOrderCertParam.setSid(sid);
        BizPurchaseOrderCertDto bizPurchaseOrderCertDto = bizPurchaseOrderCertService.update(bizPurchaseOrderCertParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizPurchaseOrderCertDto != null) {
            resultObject.setData(bizPurchaseOrderCertDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizPurchaseOrderCertService.delete(sids, userInfo);
        return resultObject;
    }

    @ApiOperation("获取根据head_sid")
    @PostMapping("/getPurchaseOrderCertByHeadSid")
    public ResultObject <BizPurchaseOrderCertDto> getPurchaseOrderCertByHeadSid(@RequestBody BizPurchaseOrderCertParam param, UserInfoToken userInfo) {
        return bizPurchaseOrderCertService.getPurchaseOrderCertByHeadSid(param, userInfo);
    }
}
