package com.dcjet.cs.api.dec;


import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dec.service.BizExportCommonService;
import com.dcjet.cs.dec.service.BizExportGoodsHeadService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractHeadDto;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizExportGoodsHeadController层
*
* <AUTHOR>
* @date 2025-07-07 11:18:39
*/
@RestController
@RequestMapping("v1/bizExportGoodsHead")
@Api(tags = "第9条线-非国营贸易出口辅料-出货信息表头接口")
public class BizExportGoodsHeadController{

    @Resource
    private BizExportGoodsHeadService bizExportGoodsHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;

    @Resource
    private BizExportCommonService exportCommonService;
 
    /**
     * 分页获取第9条线-非国营贸易出口辅料-出货信息表头数据
     * @param bizExportGoodsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取第9条线-非国营贸易出口辅料-出货信息表头数据")
    @PostMapping("list")
    public ResultObject<List<BizExportGoodsHeadDto>> getListPaged(@RequestBody BizExportGoodsHeadParam bizExportGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizExportGoodsHeadDto>> paged = bizExportGoodsHeadService.getListPaged(bizExportGoodsHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizExportGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizExportGoodsHeadDto> insert(@Valid @RequestBody BizExportGoodsHeadParam bizExportGoodsHeadParam, UserInfoToken userInfo) {
        ResultObject<BizExportGoodsHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizExportGoodsHeadDto bizExportGoodsHeadDto = bizExportGoodsHeadService.insert(bizExportGoodsHeadParam, userInfo);
        if (bizExportGoodsHeadDto != null) {
            resultObject.setData(bizExportGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizExportGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizExportGoodsHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizExportGoodsHeadParam bizExportGoodsHeadParam, UserInfoToken userInfo) {
        bizExportGoodsHeadParam.setId(sid);
        BizExportGoodsHeadDto bizExportGoodsHeadDto = bizExportGoodsHeadService.update(bizExportGoodsHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizExportGoodsHeadDto != null) {
            resultObject.setData(bizExportGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    @ApiOperation("修改准运与到货")
    @PutMapping("/updateApprovedShipmentArrival/{sid}")
    public ResultObject<BizExportGoodsHeadDto> updateApprovedShipmentArrival(@PathVariable String sid, @Valid @RequestBody BizExportGoodsHeadParam bizExportGoodsHeadParam, UserInfoToken userInfo) {
        bizExportGoodsHeadParam.setId(sid);
        BizExportGoodsHeadDto bizExportGoodsHeadDto = bizExportGoodsHeadService.updateApprovedShipmentArrival(bizExportGoodsHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizExportGoodsHeadDto != null) {
            resultObject.setData(bizExportGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    @ApiOperation("作废数据")
    @PutMapping("/cancel/{id}")
    public ResultObject<BizExportGoodsHeadDto> cancel(@PathVariable String id, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.cancel(id, userInfo);
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizExportGoodsHeadService.delete(sids, userInfo);
        return resultObject;
    }


    @ApiOperation("校验出货单号是否存在")
    @PostMapping("/checkExportNo")
    public ResultObject checkExportNo(@RequestBody BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.checkExportNo(param, userInfo);
    }


    @ApiOperation("获取常用下拉列表")
    @PostMapping("/getCommonKeyValueList")
    public ResultObject<BizExportGoodsHeadCommonDto> getCommonKeyValueList(@RequestBody BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.getCommonKeyValueList(param, userInfo);
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizExportGoodsHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizExportGoodsHeadDto> bizExportGoodsHeadDtos = bizExportGoodsHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizExportGoodsHeadDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizExportGoodsHeadDtos);
    }


    @ApiOperation("确认数据")
    @PostMapping("/confirm")
    public ResultObject confirm(@RequestBody BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.confirm(param, userInfo);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizExportGoodsHeadDto> list, UserInfoToken userInfo) {
        // 获取全部客户/港口数据 进行转换
        List<Map<String, String>> customerList = exportCommonService.getExportHeadCustomerList(userInfo.getCompany());
        List<Map<String, String>> supplierList = exportCommonService.getExportHeadSupplierList(userInfo.getCompany());
        List<Map<String, String>> portList = exportCommonService.getPortList(userInfo.getCompany());
        List<Map<String, String>> currList = exportCommonService.getCurrList(userInfo.getCompany());

        for (BizExportGoodsHeadDto item : list) {
            // 转换客户信息
            if (StringUtils.isNotBlank(item.getCustomer())) {
                for (Map<String, String> customer : customerList) {
                    if (customer.get("value").equals(item.getCustomer())) {
                        item.setCustomer(item.getCustomer() + " " + customer.get("label"));
                    }
                }
            }
            // 转换供应商信息
            if (StringUtils.isNotBlank(item.getSupplier())) {
                for (Map<String, String> supplier : supplierList) {
                    if (supplier.get("value").equals(item.getSupplier())) {
                        item.setSupplier(item.getSupplier() + " " + supplier.get("label"));
                    }
                }
            }
            // 获取港口信息
            if (StringUtils.isNotBlank(item.getPortOfDestination())) {
                for (Map<String, String> port : portList) {
                    if (port.get("value").equals(item.getPortOfDestination())) {
                        item.setPortOfDestination(item.getPortOfDestination() + " " + port.get("label"));
                    }
                }
            }
            // 转换币制
            if (StringUtils.isNotBlank(item.getCurrency())) {
                for (Map<String, String> curr : currList) {
                    if (curr.get("value").equals(item.getCurrency())) {
                        item.setCurrency(item.getCurrency() + " " + curr.get("label"));
                    }
                }
            }
            // 出货单据状态
            if (StringUtils.isNotBlank(item.getDataState())) {
                item.setDataState( CommonEnum.OrderStatusEnum.getValue(item.getDataState()));
            }
            // 外销发票状态
            if (StringUtils.isNotBlank(item.getInvoiceDataState())) {
                item.setInvoiceDataState(CommonEnum.OrderStatusEnum.getValue(item.getInvoiceDataState()));
            }
            // 发送财务系统  0 否 1 是
            if (StringUtils.isNotBlank(item.getInvoiceSendFinance())) {
                item.setInvoiceSendFinance(item.getInvoiceSendFinance() + " " + CommonEnum.ZEO_NO_ONE_YES.getValue(item.getInvoiceSendFinance()));
            }
            // 是否冲红
            if (StringUtils.isNotBlank(item.getInvoiceIsRedFlush())) {
                item.setInvoiceIsRedFlush(item.getInvoiceIsRedFlush() + " " + CommonEnum.ZEO_NO_ONE_YES.getValue(item.getInvoiceIsRedFlush()));
            }
        }
    }




    @PostMapping("/sendAudit")
    @ApiOperation("第3条线 -> 烟机设备 -> 进货单发送您内审")
    public ResultObject sendAudit(@RequestBody BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.sendAudit(param, userInfo);
    }





    @PostMapping("audit")
    @ApiOperation("第3条线 -> 烟机设备 -> 内审通过")
    public ResultObject audit(@RequestBody BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.audit(param, userInfo);
    }




    @ApiOperation("第3条线 -> 烟机设备 -> 内审退回")
    @PostMapping("reject")
    public ResultObject reject(@RequestBody BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.reject(param, userInfo);
    }



    @ApiOperation("第3条线 -> 烟机设备 -> 查询内审记录")
    @PostMapping("aeoList")
    public ResultObject<List<BizExportGoodsHeadDto>> getAeoListPaged(@RequestBody BizExportGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        param.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizExportGoodsHeadDto>> paged = bizExportGoodsHeadService.getAeoListPaged(param, pageParam, userInfo);
        return paged;
    }

    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("listToCustomerAccount")
    public ResultObject<List<BizExportGoodsHeadDto>> getListPagedToCustomerAccount(@RequestBody BizExportGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizExportGoodsHeadDto>> paged = bizExportGoodsHeadService.getListPagedToCustomerAccount(param, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("第9条线 -> 出口管理 -> 获取可提取的外商合同")
    @PostMapping("/getExtractContractList")
    public ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> getExtractContractList(@RequestBody BizExportGoodsHeadParam params, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.getExtractContractList(params,userInfo);
    }




    @ApiOperation("第9条线 -> 出口管理 -> 获取可提取的外商合同")
    @PostMapping("/extractContract")
    public ResultObject extractContract(@RequestBody BizExportGoodsHeadParam params, UserInfoToken userInfo) {
        return bizExportGoodsHeadService.extractContract(params,userInfo);
    }


}
