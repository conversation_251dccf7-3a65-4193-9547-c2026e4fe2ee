package com.dcjet.cs.api.equipment;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.equipment.BizIEquipmentContainerInfoDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentContainerInfoParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentContainerInfoExportParam;
import com.dcjet.cs.equipment.service.BizIEquipmentContainerInfoService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@RestController
@RequestMapping("v1/bizIEquipmentContainerInfo")
@Api(tags = "接口")
public class BizIEquipmentContainerInfoController extends BaseController {
    @Resource
    private BizIEquipmentContainerInfoService bizIEquipmentContainerInfoService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIEquipmentContainerInfoParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIEquipmentContainerInfoDto>> getListPaged(@RequestBody BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIEquipmentContainerInfoDto>> paged = bizIEquipmentContainerInfoService.getListPaged(bizIEquipmentContainerInfoParam, pageParam);
        return paged;
    }
    /**
     * @param bizIEquipmentContainerInfoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIEquipmentContainerInfoDto> insert(@Valid @RequestBody BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, UserInfoToken userInfo) {
        ResultObject<BizIEquipmentContainerInfoDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIEquipmentContainerInfoDto bizIEquipmentContainerInfoDto = bizIEquipmentContainerInfoService.insert(bizIEquipmentContainerInfoParam, userInfo);
        if (bizIEquipmentContainerInfoDto != null) {
            resultObject.setData(bizIEquipmentContainerInfoDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIEquipmentContainerInfoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIEquipmentContainerInfoDto> update(@PathVariable String sid, @Valid @RequestBody BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, UserInfoToken userInfo) {
        bizIEquipmentContainerInfoParam.setId(sid);
        BizIEquipmentContainerInfoDto bizIEquipmentContainerInfoDto = bizIEquipmentContainerInfoService.update(bizIEquipmentContainerInfoParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentContainerInfoDto != null) {
            resultObject.setData(bizIEquipmentContainerInfoDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    /**
     * @param bizIEquipmentContainerInfoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("updateAll")
    public ResultObject<List<BizIEquipmentContainerInfoDto>> updateAll(@Valid @RequestBody List<BizIEquipmentContainerInfoParam> bizIEquipmentContainerInfoParam, UserInfoToken userInfo) {
        List<BizIEquipmentContainerInfoDto> bizIEquipmentContainerInfoDto = bizIEquipmentContainerInfoService.updateAll(bizIEquipmentContainerInfoParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentContainerInfoDto != null) {
            resultObject.setData(bizIEquipmentContainerInfoDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIEquipmentContainerInfoService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param headId
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("deleteByNotify/{headId}")
    public ResultObject deleteByNotify(@PathVariable String headId, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIEquipmentContainerInfoService.deleteByNotify(headId, userInfo);
        return resultObject;
    }
}
