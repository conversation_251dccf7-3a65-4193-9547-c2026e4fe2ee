package com.dcjet.cs.api.approvalFlow;

import com.dcjet.cs.approvalFlow.service.impl.ApprovalFlowCommonService;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 审批流通用控制器
 *
 * <AUTHOR>
 * @date 2024
 */
@RestController
@RequestMapping("v1/approvalFlow")
@Api(tags = "审批流管理-审批流通用接口")
public class ApprovalFlowController extends BaseController {

    @Resource
    private ApprovalFlowCommonService approvalFlowCommonService;

    /**
     * 发送审核
     *
     * @param approvalFlowParam 审批流参数
     * @param userInfo 用户信息
     * @return 操作结果
     * @throws InterruptedException 中断异常
     */
    @ApiOperation("发送审核")
    @PostMapping("/sendAudit")
    public ResultObject sendAudit(@Valid @RequestBody ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) throws InterruptedException {
        return approvalFlowCommonService.sendAudit(approvalFlowParam, userInfo);
    }

    /**
     * 审核通过
     *
     * @param approvalFlowParam 审批流参数
     * @param userInfo 用户信息
     * @return 操作结果
     * @throws InterruptedException 中断异常
     */
    @ApiOperation("审核通过")
    @PostMapping("/audit")
    public ResultObject audit(@Valid @RequestBody ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) throws InterruptedException {
        return approvalFlowCommonService.audit(approvalFlowParam, userInfo);
    }

    /**
     * 审核退回
     *
     * @param approvalFlowParam 审批流参数
     * @param userInfo 用户信息
     * @return 操作结果
     */
    @ApiOperation("审核退回")
    @PostMapping("/reject")
    public ResultObject reject(@Valid @RequestBody ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        return approvalFlowCommonService.reject(approvalFlowParam, userInfo);
    }
}