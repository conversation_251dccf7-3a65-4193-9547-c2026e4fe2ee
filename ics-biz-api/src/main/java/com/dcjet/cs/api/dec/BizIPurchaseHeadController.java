package com.dcjet.cs.api.dec;

import com.dcjet.cs.dto.dec.BizIPurchaseHeadDto;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadParam;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadExportParam;
import com.dcjet.cs.dec.service.BizIPurchaseHeadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizIPurchaseHeadController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:58
*/
@RestController
@RequestMapping("v1/bizIPurchaseHead")
@Api(tags = "进口管理-进货信息表头接口")
public class BizIPurchaseHeadController{

    @Resource
    private BizIPurchaseHeadService bizIPurchaseHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进口管理-进货信息表头数据
     * @param bizIPurchaseHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-进货信息表头数据")
    @PostMapping("list")
    public ResultObject<List<BizIPurchaseHeadDto>> getListPaged(@RequestBody BizIPurchaseHeadParam bizIPurchaseHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIPurchaseHeadDto>> paged = bizIPurchaseHeadService.getListPaged(bizIPurchaseHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIPurchaseHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIPurchaseHeadDto> insert(@Valid @RequestBody BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIPurchaseHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIPurchaseHeadDto bizIPurchaseHeadDto = bizIPurchaseHeadService.insert(bizIPurchaseHeadParam, userInfo);
        if (bizIPurchaseHeadDto != null) {
            resultObject.setData(bizIPurchaseHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIPurchaseHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIPurchaseHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        bizIPurchaseHeadParam.setSid(sid);
        BizIPurchaseHeadDto bizIPurchaseHeadDto = bizIPurchaseHeadService.update(bizIPurchaseHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIPurchaseHeadDto != null) {
            resultObject.setData(bizIPurchaseHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    @ApiOperation("修改接口")
    @PutMapping("updateEntry/{sid}")
    public ResultObject<BizIPurchaseHeadDto> updateEntryInfo(@PathVariable String sid, @RequestBody BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        bizIPurchaseHeadParam.setSid(sid);
        BizIPurchaseHeadDto bizIPurchaseHeadDto = bizIPurchaseHeadService.updateEntryInfo(bizIPurchaseHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIPurchaseHeadDto != null) {
            resultObject.setData(bizIPurchaseHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIPurchaseHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIPurchaseHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIPurchaseHeadDto> bizIPurchaseHeadDtos = bizIPurchaseHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIPurchaseHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIPurchaseHeadDtos);
    }


    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param bizIPurchaseHeadParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    @ApiOperation("获取进货信息表头数据 根据订单表头sid")
    @PostMapping("/getPurchaseHeadByOrderSid")
    public ResultObject <BizIPurchaseHeadDto> getPurchaseHeadByOrderSid(@RequestBody BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        return bizIPurchaseHeadService.getPurchaseHeadByOrderSid(bizIPurchaseHeadParam, userInfo);
    }

                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIPurchaseHeadDto> list) {
        for(BizIPurchaseHeadDto item : list) {
        
        }
    }


}
