package com.dcjet.cs.api.deliveryOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountParam;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountSummaryParam;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountTobacooParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.service.BizDeliveryOrderHeadService;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadDto;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizDeliveryOrderHead")
@Api(tags = "接口")
public class BizDeliveryOrderHeadController extends BaseController {
    @Resource
    private BizDeliveryOrderHeadService bizDeliveryOrderHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizDeliveryOrderHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizDeliveryOrderHeadDto>> getListPaged(@RequestBody BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizDeliveryOrderHeadDto>> paged = bizDeliveryOrderHeadService.getListPaged(bizDeliveryOrderHeadParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizDeliveryOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizDeliveryOrderHeadDto> insert(@Valid @RequestBody BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizDeliveryOrderHeadDto bizDeliveryOrderHeadDto = bizDeliveryOrderHeadService.insert(bizDeliveryOrderHeadParam, userInfo);
        if (bizDeliveryOrderHeadDto != null) {
            resultObject.setData(bizDeliveryOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizDeliveryOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizDeliveryOrderHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, UserInfoToken userInfo) {
        bizDeliveryOrderHeadParam.setSid(sid);
        BizDeliveryOrderHeadDto bizDeliveryOrderHeadDto = bizDeliveryOrderHeadService.update(bizDeliveryOrderHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizDeliveryOrderHeadDto != null) {
            resultObject.setData(bizDeliveryOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizDeliveryOrderHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizDeliveryOrderHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizDeliveryOrderHeadDto> bizDeliveryOrderHeadDtos = bizDeliveryOrderHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizDeliveryOrderHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizDeliveryOrderHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizDeliveryOrderHeadDto> list) {
        for(BizDeliveryOrderHeadDto item : list) {
        }
    }
    @ApiOperation("获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.getSupplierList(params,userInfo);
    }
    @ApiOperation("获取制单人列表信息")
    @PostMapping("/getCreateUserList")
    public ResultObject getCreateUserList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.getCreateUserList(params,userInfo);
    }
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.sendApproval(sid, userInfo);
    }
    @ApiOperation("退单接口")
    @PostMapping("back/{sid}")
    public ResultObject back(@PathVariable String sid, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.back(sid, userInfo);
    }
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizDeliveryOrderHeadParam param, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.confirmStatus(param, userInfo);
    }
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.invalidate(sid, userInfo);
    }
    @ApiOperation("打印")
    @PostMapping("/printLink")
    public ResponseEntity printLink(@RequestBody BizDeliveryOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        return bizDeliveryOrderHeadService.printLink(param, userInfo);
    }
    @ApiOperation("打印")
    @PostMapping("/printIns")
    public ResponseEntity printIns(@RequestBody BizDeliveryOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        return bizDeliveryOrderHeadService.printIns(param, userInfo);
    }
    @ApiOperation("获取根据head_sid")
    @PostMapping("/getDeliveryOrderSid")
    public ResultObject <BizDeliveryOrderHeadDto> getDeliveryOrderSid(@RequestBody BizDeliveryOrderHeadParam param, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.getDeliveryOrderSid(param, userInfo);
    }
    @ApiOperation("发送报关")
    @PostMapping("/sendEntry")
    public ResultObject sendEntry(@RequestBody BizDeliveryOrderHeadParam params, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.sendEntry(params,userInfo);
    }
    @ApiOperation("从合同新增")
    @PostMapping("insertByContract")
    public ResultObject insertByContract(@RequestBody BizDeliveryOrderHeadParam params, UserInfoToken userInfo) {
        return bizDeliveryOrderHeadService.insertByContract(params, userInfo);
    }
    @ApiOperation("分页获取")
    @PostMapping("listInDeliveryOrderHead")
    public ResultObject<List<SevenForeignContractHeadDto>> listInDeliveryOrderHead(@RequestBody BizDeliveryOrderHeadParam params, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<SevenForeignContractHeadDto>> paged = bizDeliveryOrderHeadService.listInDeliveryOrderHead(params, pageParam,userInfo);
        return paged;
    }
}
