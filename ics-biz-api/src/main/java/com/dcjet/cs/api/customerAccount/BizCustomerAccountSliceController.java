package com.dcjet.cs.api.customerAccount;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.customerAccount.*;
import com.dcjet.cs.customerAccount.service.BizCustomerAccountSliceService;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderHeadParam;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadDto;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-28
 */
@RestController
@RequestMapping("v1/bizCustomerAccountSlice")
@Api(tags = "接口")
public class BizCustomerAccountSliceController extends BaseController {
    @Resource
    private BizCustomerAccountSliceService bizCustomerAccountSliceService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    /**
     * @param bizCustomerAccountSliceParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizCustomerAccountSliceDto>> getListPaged(@RequestBody BizCustomerAccountSliceParam bizCustomerAccountSliceParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizCustomerAccountSliceDto>> paged = bizCustomerAccountSliceService.getListPaged(bizCustomerAccountSliceParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizCustomerAccountSliceParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizCustomerAccountSliceDto> insert(@Valid @RequestBody BizCustomerAccountSliceParam bizCustomerAccountSliceParam, UserInfoToken userInfo) {
        ResultObject<BizCustomerAccountSliceDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizCustomerAccountSliceDto bizCustomerAccountSliceDto = bizCustomerAccountSliceService.insert(bizCustomerAccountSliceParam, userInfo);
        if (bizCustomerAccountSliceDto != null) {
            resultObject.setData(bizCustomerAccountSliceDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizCustomerAccountSliceParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizCustomerAccountSliceDto> update(@PathVariable String sid, @Valid @RequestBody BizCustomerAccountSliceParam bizCustomerAccountSliceParam, UserInfoToken userInfo) {
        bizCustomerAccountSliceParam.setSid(sid);
        BizCustomerAccountSliceDto bizCustomerAccountSliceDto = bizCustomerAccountSliceService.update(bizCustomerAccountSliceParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizCustomerAccountSliceDto != null) {
            resultObject.setData(bizCustomerAccountSliceDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizCustomerAccountSliceService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizCustomerAccountSliceExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizCustomerAccountSliceDto> bizCustomerAccountSliceDtos = bizCustomerAccountSliceService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizCustomerAccountSliceDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizCustomerAccountSliceDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizCustomerAccountSliceDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);
        for(BizCustomerAccountSliceDto item : list) {
            item.setBusinessType(item.getBusinessType() + " " + CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(item.getBusinessType()));
            String customerNameCn = getMerchantNameSafely(bizMerchantMap, item.getCustomer());
            item.setCustomer(item.getCustomer() + " " + customerNameCn);
            item.setCurrI(pCodeHolder.getValue(PCodeType.CURR, item.getCurrI()));
            item.setCurrE(pCodeHolder.getValue(PCodeType.CURR, item.getCurrE()));
            if(StringUtils.isNotBlank(item.getSendFinance())){
                if("0".equals(item.getSendFinance())){
                    item.setSendFinance("0 是");
                }
                if("1".equals(item.getSendFinance())){
                    item.setSendFinance("1 否");
                }
            }
            item.setStatus(CommonEnum.OrderStatusEnum.getValue(item.getStatus()));
        }
    }
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }

    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.sendApproval(sid, userInfo);
    }
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizCustomerAccountSliceParam param, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.confirmStatus(param, userInfo);
    }
    @ApiOperation("退单接口")
    @PostMapping("back/{sid}")
    public ResultObject back(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.back(sid, userInfo);
    }
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.invalidate(sid, userInfo);
    }
    @ApiOperation("红冲接口")
    @PostMapping("redFlush/{sid}")
    public ResultObject redFlush(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.redFlush(sid, userInfo);
    }

    @ApiOperation("校验是否存在 同一个订单号是否存在未作废的数据")
    @PostMapping("/checkIdNotCancel")
    public ResultObject checkPlanIdNotCancel(@RequestBody BizCustomerAccountSliceParam param, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.checkIdNotCancel(param,userInfo);
    }
//    @ApiOperation("版本复制")
//    @PostMapping("/copyVersion")
//    public ResultObject copyVersion(@RequestBody BizCustomerAccountSliceParam param, UserInfoToken userInfo) {
//        return bizCustomerAccountSliceService.copyVersion(param,userInfo);
//    }

    @ApiOperation("打印客户结算单")
    @PostMapping("/print")
    public ResponseEntity print(@RequestBody BizCustomerAccountSliceParam param, UserInfoToken userInfo) throws Exception {
        return bizCustomerAccountSliceService.print(param, userInfo);
    }

    @ApiOperation("获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(BizCustomerAccountSliceParam params, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.getSupplierList(params,userInfo);
    }
    @ApiOperation("获取制单人列表信息")
    @PostMapping("/getCreateUserList")
    public ResultObject getCreateUserList(BizCustomerAccountSliceParam params, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.getCreateUserList(params,userInfo);
    }
    @ApiOperation("分页获取")
    @PostMapping("/contractListToCustomerAccount")
    public ResultObject<List<SevenForeignContractHeadDto>> contractListToCustomerAccount(@RequestBody BizCustomerAccountSliceParam params, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<SevenForeignContractHeadDto>> paged = bizCustomerAccountSliceService.listInDeliveryOrderHead(params, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("从外商合同新增")
    @PostMapping("/insertByContract")
    public ResultObject insertByContract(@RequestBody BizCustomerAccountSliceParam param, UserInfoToken userInfo) {
        return bizCustomerAccountSliceService.insertByContract(param, userInfo);
    }
}
