package com.dcjet.cs.api.deliveryOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.service.BizDeliveryOrderInsuranceInfoService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizDeliveryOrderInsuranceInfo")
@Api(tags = "接口")
public class BizDeliveryOrderInsuranceInfoController extends BaseController {
    @Resource
    private BizDeliveryOrderInsuranceInfoService bizDeliveryOrderInsuranceInfoService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizDeliveryOrderInsuranceInfoParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizDeliveryOrderInsuranceInfoDto>> getListPaged(@RequestBody BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizDeliveryOrderInsuranceInfoDto>> paged = bizDeliveryOrderInsuranceInfoService.getListPaged(bizDeliveryOrderInsuranceInfoParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizDeliveryOrderInsuranceInfoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizDeliveryOrderInsuranceInfoDto> insert(@Valid @RequestBody BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderInsuranceInfoDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizDeliveryOrderInsuranceInfoDto bizDeliveryOrderInsuranceInfoDto = bizDeliveryOrderInsuranceInfoService.insert(bizDeliveryOrderInsuranceInfoParam, userInfo);
        if (bizDeliveryOrderInsuranceInfoDto != null) {
            resultObject.setData(bizDeliveryOrderInsuranceInfoDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizDeliveryOrderInsuranceInfoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizDeliveryOrderInsuranceInfoDto> update(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, UserInfoToken userInfo) {
        bizDeliveryOrderInsuranceInfoParam.setSid(sid);
        BizDeliveryOrderInsuranceInfoDto bizDeliveryOrderInsuranceInfoDto = bizDeliveryOrderInsuranceInfoService.update(bizDeliveryOrderInsuranceInfoParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizDeliveryOrderInsuranceInfoDto != null) {
            resultObject.setData(bizDeliveryOrderInsuranceInfoDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizDeliveryOrderInsuranceInfoService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizDeliveryOrderInsuranceInfoExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizDeliveryOrderInsuranceInfoDto> bizDeliveryOrderInsuranceInfoDtos = bizDeliveryOrderInsuranceInfoService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizDeliveryOrderInsuranceInfoDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizDeliveryOrderInsuranceInfoDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizDeliveryOrderInsuranceInfoDto> list) {
        for(BizDeliveryOrderInsuranceInfoDto item : list) {
        }
    }
    @ApiOperation("获取根据head_sid")
    @PostMapping("/getDeliveryOrderInsuranceInfoByHeadSid")
    public ResultObject <BizDeliveryOrderInsuranceInfoDto> getDeliveryOrderInsuranceInfoByHeadSid(@RequestBody BizDeliveryOrderInsuranceInfoParam param, UserInfoToken userInfo) {
        return bizDeliveryOrderInsuranceInfoService.getDeliveryOrderInsuranceInfoByHeadSid(param, userInfo);
    }
    @ApiOperation("获取根据head_sid")
    @PostMapping("/getExchangeRate/{curr}")
    public ResultObject getExchangeRate(@PathVariable String curr,UserInfoToken userInfo) {
        return bizDeliveryOrderInsuranceInfoService.getExchangeRate(curr,userInfo);
    }
}
