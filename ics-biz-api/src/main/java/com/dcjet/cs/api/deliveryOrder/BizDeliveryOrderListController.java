package com.dcjet.cs.api.deliveryOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.service.BizDeliveryOrderListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizDeliveryOrderList")
@Api(tags = "接口")
public class BizDeliveryOrderListController extends BaseController {
    @Resource
    private BizDeliveryOrderListService bizDeliveryOrderListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizDeliveryOrderListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizDeliveryOrderListDto>> getListPaged(@RequestBody BizDeliveryOrderListParam bizDeliveryOrderListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizDeliveryOrderListDto>> paged = bizDeliveryOrderListService.getListPaged(bizDeliveryOrderListParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizDeliveryOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizDeliveryOrderListDto> insert(@Valid @RequestBody BizDeliveryOrderListParam bizDeliveryOrderListParam, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizDeliveryOrderListDto bizDeliveryOrderListDto = bizDeliveryOrderListService.insert(bizDeliveryOrderListParam, userInfo);
        if (bizDeliveryOrderListDto != null) {
            resultObject.setData(bizDeliveryOrderListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizDeliveryOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizDeliveryOrderListDto> update(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderListParam bizDeliveryOrderListParam, UserInfoToken userInfo) {
        bizDeliveryOrderListParam.setSid(sid);
        BizDeliveryOrderListDto bizDeliveryOrderListDto = bizDeliveryOrderListService.update(bizDeliveryOrderListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizDeliveryOrderListDto != null) {
            resultObject.setData(bizDeliveryOrderListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizDeliveryOrderListService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizDeliveryOrderListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizDeliveryOrderListDto> bizDeliveryOrderListDtos = bizDeliveryOrderListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizDeliveryOrderListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizDeliveryOrderListDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizDeliveryOrderListDto> list) {
        for(BizDeliveryOrderListDto item : list) {
        }
    }
    @ApiOperation("获取根据sid")
    @PostMapping("/getDeliveryOrderListBySid/{sid}")
    public ResultObject <BizDeliveryOrderListDto> getDeliveryOrderListBySid(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderListParam param, UserInfoToken userInfo) {
        param.setSid(sid);
        return bizDeliveryOrderListService.getDeliveryOrderListBySid(param, userInfo);
    }
}
