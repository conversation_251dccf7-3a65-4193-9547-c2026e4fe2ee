package com.dcjet.cs.api.warehouse;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.warehouse.BizStoreIListDto;
import com.dcjet.cs.dto.warehouse.BizStoreIListParam;
import com.dcjet.cs.dto.warehouse.BizStoreIListExportParam;
import com.dcjet.cs.warehouse.service.BizStoreIListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@RestController
@RequestMapping("v1/bizStoreIList")
@Api(tags = "接口")
public class BizStoreIListController extends BaseController {
    @Resource
    private BizStoreIListService bizStoreIListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizStoreIListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizStoreIListDto>> getListPaged(@RequestBody BizStoreIListParam bizStoreIListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizStoreIListDto>> paged = bizStoreIListService.getListPaged(bizStoreIListParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizStoreIListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizStoreIListDto> insert(@Valid @RequestBody BizStoreIListParam bizStoreIListParam, UserInfoToken userInfo) {
        ResultObject<BizStoreIListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizStoreIListDto bizStoreIListDto = bizStoreIListService.insert(bizStoreIListParam, userInfo);
        if (bizStoreIListDto != null) {
            resultObject.setData(bizStoreIListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizStoreIListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizStoreIListDto> update(@PathVariable String sid, @Valid @RequestBody BizStoreIListParam bizStoreIListParam, UserInfoToken userInfo) {
        bizStoreIListParam.setSid(sid);
        BizStoreIListDto bizStoreIListDto = bizStoreIListService.update(bizStoreIListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizStoreIListDto != null) {
            resultObject.setData(bizStoreIListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizStoreIListService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizStoreIListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizStoreIListDto> bizStoreIListDtos = bizStoreIListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizStoreIListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizStoreIListDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizStoreIListDto> list) {
        for(BizStoreIListDto item : list) {
        }
    }
    @PostMapping("/getListBySid/{sid}")
    public ResultObject getListBySid(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreIListService.getListBySid(sid,userInfo);
    }
}
