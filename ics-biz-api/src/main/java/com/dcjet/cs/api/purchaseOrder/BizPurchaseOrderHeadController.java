package com.dcjet.cs.api.purchaseOrder;
import com.dcjet.cs.baseInfoCustomerParams.service.BaseInfoCustomerParamsService;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountTobacooParam;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderHeadDto;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderHeadParam;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderHeadDto;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderHeadParam;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderHeadExportParam;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadParam;
import com.dcjet.cs.purchaseOrder.service.BizPurchaseOrderHeadService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.Optional;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@RestController
@RequestMapping("v1/bizPurchaseOrderHead")
@Api(tags = "接口")
public class BizPurchaseOrderHeadController extends BaseController {
    @Resource
    private BizPurchaseOrderHeadService bizPurchaseOrderHeadService;
    @Resource
    private BaseInfoCustomerParamsService baseInfoCustomerParamsService;

    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizPurchaseOrderHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizPurchaseOrderHeadDto>> getListPaged(@RequestBody BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizPurchaseOrderHeadDto>> paged = bizPurchaseOrderHeadService.getListPaged(bizPurchaseOrderHeadParam, pageParam);
        return paged;
    }
    /**
     * @param bizPurchaseOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizPurchaseOrderHeadDto> insert(@Valid @RequestBody BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, UserInfoToken userInfo) {
        ResultObject<BizPurchaseOrderHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizPurchaseOrderHeadDto bizPurchaseOrderHeadDto = bizPurchaseOrderHeadService.insert(bizPurchaseOrderHeadParam, userInfo);
        if (bizPurchaseOrderHeadDto != null) {
            resultObject.setData(bizPurchaseOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizPurchaseOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizPurchaseOrderHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, UserInfoToken userInfo) {
        bizPurchaseOrderHeadParam.setSid(sid);
        BizPurchaseOrderHeadDto bizPurchaseOrderHeadDto = bizPurchaseOrderHeadService.update(bizPurchaseOrderHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizPurchaseOrderHeadDto != null) {
            resultObject.setData(bizPurchaseOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizPurchaseOrderHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizPurchaseOrderHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizPurchaseOrderHeadDto> bizPurchaseOrderHeadDtos = bizPurchaseOrderHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizPurchaseOrderHeadDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizPurchaseOrderHeadDtos);
    }

    @ApiOperation("获取制单人列表信息")
    @PostMapping("/getCreateUserList")
    public ResultObject getCreateUserList(BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.getCreateUserList(params,userInfo);
    }
    @ApiOperation("获取根据head_sid")
    @PostMapping("/getPurchaseOrderSid")
    public ResultObject <BizPurchaseOrderHeadDto> getPurchaseOrderSid(@RequestBody BizPurchaseOrderHeadParam param, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.getPurchaseOrderSid(param, userInfo);
    }

    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizPurchaseOrderHeadParam param, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.confirmStatus(param, userInfo);
    }

    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.invalidate(sid, userInfo);
    }

    @ApiOperation("打印")
    @PostMapping("/print")
    public ResponseEntity print(@RequestBody BizPurchaseOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        return bizPurchaseOrderHeadService.print(param, userInfo);
    }

    @ApiOperation("发送报关")
    @PostMapping("/sendEntry")
    public ResultObject sendEntry(@RequestBody BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.sendEntry(params,userInfo);
    }

    @ApiOperation("新增数据")
    @PostMapping("/listInPurchaseOrderHead")
    public ResultObject listInPurchaseOrderHead(@RequestBody BizPurchaseOrderHeadParam params, PageParam pageParam, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.listInPurchaseOrderHead(params,pageParam,userInfo);
    }

    @ApiOperation("外商合同新增保存")
    @PostMapping("insertByContract")
    public ResultObject insertByContract(@RequestBody BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.insertByContract(params, userInfo);
    }

    @ApiOperation("外商合同新增保存")
    @PostMapping("insertListByContract")
    public ResultObject insertListByContract(@RequestBody BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        return bizPurchaseOrderHeadService.insertListByContract(params, userInfo);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizPurchaseOrderHeadDto> list, UserInfoToken userInfo) {
        for(BizPurchaseOrderHeadDto item : list) {
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
            if(!ObjectUtils.isEmpty(bizMerchants)){
                Optional<BizMerchant> first = bizMerchants.stream().filter(x -> x.getMerchantCode().equals(item.getSupplier())).findFirst();
                first.ifPresent(merchant -> item.setSupplier(item.getSupplier()+" "+merchant.getMerchantNameCn()));
            }
            if(!ObjectUtils.isEmpty(item.getPortOfDestination())) {
                String portOfDestination = baseInfoCustomerParamsService.getNameByCode(item.getPortOfDestination(), "PORT", item.getBusinessType());
                item.setPortOfDestination(portOfDestination);
            }
            if(!ObjectUtils.isEmpty(item.getStatus())) {
                if("1".equals(item.getStatus())) {
                    item.setStatus("确认");
                } else if("2".equals(item.getStatus())) {
                    item.setStatus("作废");
                } else if("0".equals(item.getStatus())){
                    item.setStatus("编制");
                }
            }
        }
    }
}
