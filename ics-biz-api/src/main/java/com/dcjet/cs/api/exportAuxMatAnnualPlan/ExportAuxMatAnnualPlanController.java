package com.dcjet.cs.api.exportAuxMatAnnualPlan;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanDto;
import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanOptionsDto;
import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanParam;
import com.dcjet.cs.exportAuxMatAnnualPlan.service.ExportAuxMatAnnualPlanService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/v1/exportAuxMatAnnualPlan")
@Api(tags = "出口辅料年度计划")
public class ExportAuxMatAnnualPlanController extends BaseController {
    @Resource
    private ExportAuxMatAnnualPlanService exportAuxMatAnnualPlanService;
    @Resource
    private ExcelService excelService;

    /**
     * 获取选项
     *
     * @param userInfo 用户信息
     * @return 选项配置
     */
    @ApiOperation("获取选项")
    @GetMapping("/options")
    public ResultObject<?> getOptions(UserInfoToken<?> userInfo) {
        ExportAuxMatAnnualPlanOptionsDto optionsDto = this.exportAuxMatAnnualPlanService.options(userInfo);
        return ResultObject.createInstance(true, "查询成功", optionsDto);
    }

    /**
     * 获取分页列表
     *
     * @param annualPlanParam 年度计划
     * @param pageParam       分页参数
     * @param userInfo        用户信息
     * @return 出口辅料年度计划分页列表
     */
    @ApiOperation("获取出口辅料年度计划分页列表")
    @PostMapping("/list")
    public ResultObject<List<ExportAuxMatAnnualPlanDto>> getListPaged(@RequestBody ExportAuxMatAnnualPlanParam annualPlanParam,
                                                                      PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.exportAuxMatAnnualPlanService.getListPaged(annualPlanParam, pageParam, userInfo);
    }


    /**
     * 新增年度计划
     *
     * @param annualPlanParam 年度计划参数
     * @param userInfo        用户信息
     * @return 城市传输模型
     */
    @ApiOperation("新增年度计划")
    @PostMapping
    public ResultObject<?> insert(@Valid @RequestBody ExportAuxMatAnnualPlanParam annualPlanParam, UserInfoToken<?> userInfo) {
        ExportAuxMatAnnualPlanDto auxMatAnnualPlanDto = this.exportAuxMatAnnualPlanService.insert(annualPlanParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, auxMatAnnualPlanDto);
    }

    /**
     * 修改年度计划
     *
     * @param id              主键
     * @param annualPlanParam 年度计划参数
     * @param userInfo        用户信息
     * @return 城市传输模型
     */
    @ApiOperation("修改年度计划")
    @PutMapping("/{id}")
    public ResultObject<?> update(@PathVariable String id, @Valid @RequestBody ExportAuxMatAnnualPlanParam annualPlanParam,
                                  UserInfoToken<?> userInfo) {
        annualPlanParam.setId(id);
        ExportAuxMatAnnualPlanDto auxMatAnnualPlanDto = this.exportAuxMatAnnualPlanService.update(annualPlanParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, auxMatAnnualPlanDto);
    }

    /**
     * 删除年度计划
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     * @return 请求结果
     */
    @ApiOperation("删除年度计划")
    @DeleteMapping("/{ids}")
    public ResultObject<?> delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        this.exportAuxMatAnnualPlanService.deleteByIds(ids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }

    /**
     * 导出年度计划
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 响应实体
     */
    @ApiOperation("导出年度计划")
    @PostMapping("/export")
    public ResponseEntity<?> export(@RequestBody BasicExportParam<ExportAuxMatAnnualPlanParam> exportParam,
                                    UserInfoToken<?> userInfo) {
        List<ExportAuxMatAnnualPlanDto> dtoList = this.exportAuxMatAnnualPlanService
                .gerExcelList(exportParam.getExportColumns(), userInfo);
        try {
            return this.excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8)
                    , exportParam.getHeader(), dtoList);
        } catch (Exception e) {
            throw new ErrorException(500, "导出失败");
        }
    }
}