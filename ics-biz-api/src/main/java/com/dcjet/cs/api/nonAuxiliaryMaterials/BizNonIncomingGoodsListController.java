package com.dcjet.cs.api.nonAuxiliaryMaterials;


import com.dcjet.cs.common.service.ExcelService;

import com.dcjet.cs.dto.dec.BizBatchUpdateInvoiceNoParams;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsListDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsListExportParam;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsListParam;
import com.dcjet.cs.nonAuxiliaryMaterials.service.BizNonIncomingGoodsListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
* BizNonIncomingGoodsListController层
*
* <AUTHOR>
* @date 2025-05-23 13:32:21
*/
@RestController
@RequestMapping("v1/bizNonIncomingGoodsList")
@Api(tags = "进过管理-表体列表接口")
public class BizNonIncomingGoodsListController {

    @Resource
    private BizNonIncomingGoodsListService bizNonIncomingGoodsListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进过管理-表体列表数据
     * @param BizNonIncomingGoodsListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进过管理-表体列表数据")
    @PostMapping("list")
    public ResultObject<List<BizNonIncomingGoodsListDto>> getListPaged(@RequestBody BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsListDto>> paged = bizNonIncomingGoodsListService.getListPaged(BizNonIncomingGoodsListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param BizNonIncomingGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizNonIncomingGoodsListDto> insert(@Valid @RequestBody BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizNonIncomingGoodsListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizNonIncomingGoodsListDto BizNonIncomingGoodsListDto = bizNonIncomingGoodsListService.insert(BizNonIncomingGoodsListParam, userInfo);
        if (BizNonIncomingGoodsListDto != null) {
            resultObject.setData(BizNonIncomingGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }

    @PostMapping("/batchInsert")
    public ResultObject batchInsert(@RequestBody BizNonIncomingGoodsListParam bizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        bizNonIncomingGoodsListService.batchInsert(bizNonIncomingGoodsListParam, userInfo);
        return resultObject;
    }

    /**
     * @param id
     * @param BizNonIncomingGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{id}")
    public ResultObject<BizNonIncomingGoodsListDto> update(@PathVariable String id, @Valid @RequestBody BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        BizNonIncomingGoodsListParam.setId(id);
        BizNonIncomingGoodsListDto BizNonIncomingGoodsListDto = bizNonIncomingGoodsListService.update(BizNonIncomingGoodsListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (BizNonIncomingGoodsListDto != null) {
            resultObject.setData(BizNonIncomingGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
        bizNonIncomingGoodsListService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizNonIncomingGoodsListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizNonIncomingGoodsListDto> BizNonIncomingGoodsListDtos = bizNonIncomingGoodsListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(BizNonIncomingGoodsListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), BizNonIncomingGoodsListDtos);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizNonIncomingGoodsListDto> list) {
        for(BizNonIncomingGoodsListDto item : list) {
        
        }
    }



    @PostMapping("/getListSumByInvoice")
    @ApiOperation("按发票号汇总")
    public ResultObject<List<BizNonIncomingGoodsListDto>> getListSumByInvoice(@RequestBody BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsListDto>> paged = bizNonIncomingGoodsListService.getListSumByInvoice(BizNonIncomingGoodsListParam, pageParam,userInfo);
        return paged;
    }



    @PostMapping("/batchUpdateInvoiceNo")
    @ApiOperation("批量更新发票号")
    public ResultObject batchUpdateInvoiceNo(@RequestBody @Valid BizBatchUpdateInvoiceNoParams params, UserInfoToken userInfo) {
        return bizNonIncomingGoodsListService.batchUpdateInvoiceNo(params, userInfo);
    }


    @PostMapping("/getIncomingGoodsListBySid/{id}")
    @ApiOperation("根据ID查询")
    public ResultObject<BizNonIncomingGoodsListDto> getIncomingGoodsListBySid(@PathVariable String id, UserInfoToken userInfo) {
    		return bizNonIncomingGoodsListService.getIncomingGoodsListBySid(id, userInfo);
    }




    @PostMapping("/updateInQuality")
    @ApiOperation("修改进口数量")
    public ResultObject<BizNonIncomingGoodsListDto> updateInQuality(@RequestBody @Valid BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizNonIncomingGoodsListService.updateInQuality(BizNonIncomingGoodsListParam, userInfo);
    }


    @PostMapping("/updateQuantity")
    @ApiOperation("修改进口数量")
    public ResultObject<BizNonIncomingGoodsListDto> updateQuantity(@RequestBody @Valid BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizNonIncomingGoodsListService.updateQuantity(BizNonIncomingGoodsListParam, userInfo);
    }



    @PostMapping("/updateAmount")
    @ApiOperation("修改金额")
    public ResultObject<BizNonIncomingGoodsListDto> updateAmount(@RequestBody @Valid BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizNonIncomingGoodsListService.updateAmount(BizNonIncomingGoodsListParam, userInfo);
    }


    @PostMapping("/updateInvoiceNo")
    @ApiOperation("修改发票号")
    public ResultObject<BizNonIncomingGoodsListDto> updateInvoiceNo(@RequestBody @Valid BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizNonIncomingGoodsListService.updateInvoiceNo(BizNonIncomingGoodsListParam, userInfo);
    }


    @PostMapping("/getSumTotalByHeadId")
    @ApiOperation("根据头表ID查询汇总表体数据")
    public ResultObject getSumTotalByHeadId(@RequestBody  BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizNonIncomingGoodsListService.getSumTotalByHeadId(BizNonIncomingGoodsListParam, userInfo);
    }


}
