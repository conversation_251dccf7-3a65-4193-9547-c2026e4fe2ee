package com.dcjet.cs.api.dec;

import com.dcjet.cs.dec.service.BizExportCommonService;
import com.dcjet.cs.dto.dec.BizDecCommonCustomerListDto;
import com.dcjet.cs.dto.dec.BizDecCommonParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("v1/bizDecCommon")
@Api(tags = "第 1、2、3、7、9 通用处理方法")
public class BizDecCommonController {

    @Resource
    private BizExportCommonService bizExportCommonService;

    @ApiOperation("获取客户下拉列表信息")
    @PostMapping("getCustomerListByType")
    public ResultObject<BizDecCommonCustomerListDto> getCustomerListByType(@RequestBody BizDecCommonParam param, UserInfoToken userInfoToken) {
        ResultObject<BizDecCommonCustomerListDto> resultObject = ResultObject.createInstance(true,"查询客户下拉列表信息成功");
        BizDecCommonCustomerListDto dto = new BizDecCommonCustomerListDto();
        // 客商类别为客户，且常用标志含1的
        List<Map<String,String>> list = bizExportCommonService.getCustomerListByType(param,userInfoToken);
        dto.setCustomerOneList(list);

        // 客商类别为客户、供应商，且常用标志含1的
        List<Map<String,String>> list2 = bizExportCommonService.getCustomerSupplierListByType(param,userInfoToken);
        dto.setCustomerSupplierList(list2);



        resultObject.setData(dto);
        return resultObject;
    }
}
