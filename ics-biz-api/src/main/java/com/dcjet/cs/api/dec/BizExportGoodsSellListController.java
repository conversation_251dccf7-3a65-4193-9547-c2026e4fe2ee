package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizExportGoodsSellListService;
import com.dcjet.cs.dto.dec.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizExportGoodsSellListController层
*
* <AUTHOR>
* @date 2025-07-07 14:45:02
*/
@RestController
@RequestMapping("v1/bizExportGoodsSellList")
@Api(tags = "第9条线-非国营贸易出口辅料-外销发票表体接口")
public class BizExportGoodsSellListController{

    @Resource
    private BizExportGoodsSellListService bizExportGoodsSellListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取第9条线-非国营贸易出口辅料-外销发票表体数据
     * @param bizExportGoodsSellListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取第9条线-非国营贸易出口辅料-外销发票表体数据")
    @PostMapping("list")
    public ResultObject<List<BizExportGoodsSellListDto>> getListPaged(@RequestBody BizExportGoodsSellListParam bizExportGoodsSellListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizExportGoodsSellListDto>> paged = bizExportGoodsSellListService.getListPaged(bizExportGoodsSellListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizExportGoodsSellListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("insert")
    public ResultObject<BizExportGoodsSellListDto> insert(@Valid @RequestBody BizExportGoodsSellListParam bizExportGoodsSellListParam, UserInfoToken userInfo) {
        ResultObject<BizExportGoodsSellListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizExportGoodsSellListDto bizExportGoodsSellListDto = bizExportGoodsSellListService.insert(bizExportGoodsSellListParam, userInfo);
        if (bizExportGoodsSellListDto != null) {
            resultObject.setData(bizExportGoodsSellListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizExportGoodsSellListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizExportGoodsSellListDto> update(@PathVariable String sid, @Valid @RequestBody BizExportGoodsSellListParam bizExportGoodsSellListParam, UserInfoToken userInfo) {
        bizExportGoodsSellListParam.setId(sid);
        BizExportGoodsSellListDto bizExportGoodsSellListDto = bizExportGoodsSellListService.update(bizExportGoodsSellListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizExportGoodsSellListDto != null) {
            resultObject.setData(bizExportGoodsSellListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizExportGoodsSellListService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizExportGoodsSellListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizExportGoodsSellListDto> bizExportGoodsSellListDtos = bizExportGoodsSellListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizExportGoodsSellListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizExportGoodsSellListDtos);
    }


    @ApiOperation("获取外销发票表体-汇总信息")
    @PostMapping("getListTotal")
    public ResultObject<BizExportGoodsSellListTotal> getListTotal(@RequestBody BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsSellListService.getListTotal(param, userInfo);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizExportGoodsSellListDto> list) {
        for(BizExportGoodsSellListDto item : list) {
        
        }
    }


}
