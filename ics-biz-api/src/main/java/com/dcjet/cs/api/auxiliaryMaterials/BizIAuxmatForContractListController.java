package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListDto;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListExportParam;
import com.dcjet.cs.auxiliaryMaterials.service.BizIAuxmatForContractListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@RestController
@RequestMapping("v1/bizIAuxmatForContractList")
@Api(tags = "接口")
public class BizIAuxmatForContractListController extends BaseController {
    @Resource
    private BizIAuxmatForContractListService bizIAuxmatForContractListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIAuxmatForContractListParam
     * @param pageParam
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIAuxmatForContractListDto>> getListPaged(@RequestBody BizIAuxmatForContractListParam bizIAuxmatForContractListParam, PageParam pageParam) {
        ResultObject<List<BizIAuxmatForContractListDto>> paged = bizIAuxmatForContractListService.selectAllPaged(bizIAuxmatForContractListParam, pageParam);
        return paged;
    }
    /**
     * @param bizIAuxmatForContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIAuxmatForContractListDto> insert(@Valid @RequestBody BizIAuxmatForContractListParam bizIAuxmatForContractListParam, UserInfoToken userInfo) {
		ResultObject<BizIAuxmatForContractListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIAuxmatForContractListDto bizIAuxmatForContractListDto = bizIAuxmatForContractListService.insert(bizIAuxmatForContractListParam, userInfo);
        if (bizIAuxmatForContractListDto != null) {
            resultObject.setData(bizIAuxmatForContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }


    @ApiOperation("新增接口")
    @PostMapping("batchInsert")
    public ResultObject<BizIAuxmatForContractListDto> batchInsert(@RequestBody BizIAuxmatForContractListParam bizIAuxmatForContractListParam, UserInfoToken userInfo) {
        ResultObject<BizIAuxmatForContractListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        bizIAuxmatForContractListService.batchInsert(bizIAuxmatForContractListParam, userInfo);
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIAuxmatForContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIAuxmatForContractListDto> update(@PathVariable String sid, @Valid @RequestBody BizIAuxmatForContractListParam bizIAuxmatForContractListParam, UserInfoToken userInfo) {
        bizIAuxmatForContractListParam.setSid(sid);
        BizIAuxmatForContractListDto bizIAuxmatForContractListDto = bizIAuxmatForContractListService.update(bizIAuxmatForContractListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIAuxmatForContractListDto != null) {
            resultObject.setData(bizIAuxmatForContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
		ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        bizIAuxmatForContractListService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIAuxmatForContractListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIAuxmatForContractListDto> bizIAuxmatForContractListDtos = bizIAuxmatForContractListService.selectAll(exportParam.getExportColumns());
        bizIAuxmatForContractListDtos = convertForPrint(bizIAuxmatForContractListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIAuxmatForContractListDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<BizIAuxmatForContractListDto> convertForPrint(List<BizIAuxmatForContractListDto> list) {
        for(BizIAuxmatForContractListDto item : list) {
        }
        return list;
    }

    @ApiOperation("汇总表体合同数量及总金额")
    @PostMapping("getContractTotal")
    public ResultObject<BizIAuxmatForContractListDto> getContractTotal(@RequestBody BizIAuxmatForContractListParam bizIAuxmatForContractListParam, UserInfoToken userInfo) {
        return  bizIAuxmatForContractListService.getContractTotal(bizIAuxmatForContractListParam, userInfo);
    }
}
