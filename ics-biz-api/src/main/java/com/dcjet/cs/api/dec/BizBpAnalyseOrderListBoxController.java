package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizBpAnalyseOrderListBoxService;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxAddParam;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxExportParam;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizBpAnalyseOrderListBoxController层
*
* <AUTHOR>
* @date 2025-07-07 17:08:52
*/
@RestController
@RequestMapping("v1/bizBpAnalyseOrderListBox")
@Api(tags = "（第7条线）出料加工进口薄片-装箱列表接口")
public class BizBpAnalyseOrderListBoxController{

    @Resource
    private BizBpAnalyseOrderListBoxService bizBpAnalyseOrderListBoxService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取（第7条线）出料加工进口薄片-装箱列表数据
     * @param bizBpAnalyseOrderListBoxParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取（第7条线）出料加工进口薄片-装箱列表数据")
    @PostMapping("list")
    public ResultObject<List<BizBpAnalyseOrderListBoxDto>> getListPaged(@RequestBody BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizBpAnalyseOrderListBoxDto>> paged = bizBpAnalyseOrderListBoxService.getListPaged(bizBpAnalyseOrderListBoxParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizBpAnalyseOrderListBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizBpAnalyseOrderListBoxDto> insert(@Valid @RequestBody BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        ResultObject<BizBpAnalyseOrderListBoxDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizBpAnalyseOrderListBoxDto bizBpAnalyseOrderListBoxDto = bizBpAnalyseOrderListBoxService.insert(bizBpAnalyseOrderListBoxParam, userInfo);
        if (bizBpAnalyseOrderListBoxDto != null) {
            resultObject.setData(bizBpAnalyseOrderListBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizBpAnalyseOrderListBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizBpAnalyseOrderListBoxDto> update(@PathVariable String sid, @Valid @RequestBody BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        bizBpAnalyseOrderListBoxParam.setId(sid);
        BizBpAnalyseOrderListBoxDto bizBpAnalyseOrderListBoxDto = bizBpAnalyseOrderListBoxService.update(bizBpAnalyseOrderListBoxParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizBpAnalyseOrderListBoxDto != null) {
            resultObject.setData(bizBpAnalyseOrderListBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}/{parentId}")
    public ResultObject delete(@PathVariable List<String> sids,@PathVariable String parentId, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizBpAnalyseOrderListBoxService.delete(sids,parentId, userInfo);
        return resultObject;
    }


    @ApiOperation("根据商品名称统计箱数")
    @PostMapping("getBoxCountByProductName")
    public ResultObject<BigDecimal> getBoxCountByProductName(@RequestBody BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        return bizBpAnalyseOrderListBoxService.getBoxCountByProductName(bizBpAnalyseOrderListBoxParam, userInfo);
    }


    @ApiOperation("根据表头ID获取商品名称列表")
    @PostMapping("getProductNameListByHeadId")
    public ResultObject<List<Map<String,String>>> getProductNameListByHeadId(@RequestBody BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        return bizBpAnalyseOrderListBoxService.getProductNameListByHeadId(bizBpAnalyseOrderListBoxParam, userInfo);
    }


    @ApiOperation("新增集装箱列表")
    @PostMapping("insertContainerList")
    public ResultObject insertContainerList(@RequestBody @Valid BizBpAnalyseOrderListBoxAddParam param, UserInfoToken userInfo) {
        return bizBpAnalyseOrderListBoxService.insertContainerList(param, userInfo);
    }

    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizBpAnalyseOrderListBoxExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizBpAnalyseOrderListBoxDto> bizBpAnalyseOrderListBoxDtos = bizBpAnalyseOrderListBoxService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizBpAnalyseOrderListBoxDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizBpAnalyseOrderListBoxDtos);
    }


    @ApiOperation("判断表头分析单号是否填写")
    @PostMapping("/checkHeadId")
    public ResultObject checkHeadId(@RequestBody BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        return bizBpAnalyseOrderListBoxService.checkHeadId(bizBpAnalyseOrderListBoxParam, userInfo);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizBpAnalyseOrderListBoxDto> list) {
        for(BizBpAnalyseOrderListBoxDto item : list) {
        
        }
    }


}
