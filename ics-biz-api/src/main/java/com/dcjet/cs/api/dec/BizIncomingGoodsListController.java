package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizIncomingGoodsListService;
import com.dcjet.cs.dto.dec.BizBatchUpdateInvoiceNoParams;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListExportParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizIncomingGoodsListController层
*
* <AUTHOR>
* @date 2025-05-23 13:32:21
*/
@RestController
@RequestMapping("v1/bizIncomingGoodsList")
@Api(tags = "进过管理-表体列表接口")
public class BizIncomingGoodsListController{

    @Resource
    private BizIncomingGoodsListService bizIncomingGoodsListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进过管理-表体列表数据
     * @param bizIncomingGoodsListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进过管理-表体列表数据")
    @PostMapping("list")
    public ResultObject<List<BizIncomingGoodsListDto>> getListPaged(@RequestBody BizIncomingGoodsListParam bizIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsListDto>> paged = bizIncomingGoodsListService.getListPaged(bizIncomingGoodsListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIncomingGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizIncomingGoodsListDto> insert(@Valid @RequestBody BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIncomingGoodsListDto bizIncomingGoodsListDto = bizIncomingGoodsListService.insert(bizIncomingGoodsListParam, userInfo);
        if (bizIncomingGoodsListDto != null) {
            resultObject.setData(bizIncomingGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIncomingGoodsListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{id}")
    public ResultObject<BizIncomingGoodsListDto> update(@PathVariable String id, @Valid @RequestBody BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        bizIncomingGoodsListParam.setId(id);
        BizIncomingGoodsListDto bizIncomingGoodsListDto = bizIncomingGoodsListService.update(bizIncomingGoodsListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIncomingGoodsListDto != null) {
            resultObject.setData(bizIncomingGoodsListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIncomingGoodsListService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIncomingGoodsListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIncomingGoodsListDto> bizIncomingGoodsListDtos = bizIncomingGoodsListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIncomingGoodsListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIncomingGoodsListDtos);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIncomingGoodsListDto> list) {
        for(BizIncomingGoodsListDto item : list) {
        
        }
    }



    @PostMapping("/getListSumByInvoice")
    @ApiOperation("按发票号汇总")
    public ResultObject<List<BizIncomingGoodsListDto>> getListSumByInvoice(@RequestBody BizIncomingGoodsListParam bizIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsListDto>> paged = bizIncomingGoodsListService.getListSumByInvoice(bizIncomingGoodsListParam, pageParam,userInfo);
        return paged;
    }



    @PostMapping("/batchUpdateInvoiceNo")
    @ApiOperation("批量更新发票号")
    public ResultObject batchUpdateInvoiceNo(@RequestBody @Valid BizBatchUpdateInvoiceNoParams params, UserInfoToken userInfo) {
        return bizIncomingGoodsListService.batchUpdateInvoiceNo(params, userInfo);
    }


    @PostMapping("/getIncomingGoodsListBySid/{id}")
    @ApiOperation("根据ID查询")
    public ResultObject<BizIncomingGoodsListDto> getIncomingGoodsListBySid(@PathVariable String id, UserInfoToken userInfo) {
    		return bizIncomingGoodsListService.getIncomingGoodsListBySid(id, userInfo);
    }




    @PostMapping("/updateInQuality")
    @ApiOperation("修改进口数量")
    public ResultObject<BizIncomingGoodsListDto> updateInQuality(@RequestBody @Valid BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizIncomingGoodsListService.updateInQuality(bizIncomingGoodsListParam, userInfo);
    }


    @PostMapping("/updateQuantity")
    @ApiOperation("修改进口数量")
    public ResultObject<BizIncomingGoodsListDto> updateQuantity(@RequestBody @Valid BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizIncomingGoodsListService.updateQuantity(bizIncomingGoodsListParam, userInfo);
    }



    @PostMapping("/updateAmount")
    @ApiOperation("修改金额")
    public ResultObject<BizIncomingGoodsListDto> updateAmount(@RequestBody @Valid BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizIncomingGoodsListService.updateAmount(bizIncomingGoodsListParam, userInfo);
    }


    @PostMapping("/updateInvoiceNo")
    @ApiOperation("修改发票号")
    public ResultObject<BizIncomingGoodsListDto> updateInvoiceNo(@RequestBody @Valid BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizIncomingGoodsListService.updateInvoiceNo(bizIncomingGoodsListParam, userInfo);
    }


    @PostMapping("/getSumTotalByHeadId")
    @ApiOperation("根据头表ID查询汇总表体数据")
    public ResultObject getSumTotalByHeadId(@RequestBody  BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        return bizIncomingGoodsListService.getSumTotalByHeadId(bizIncomingGoodsListParam, userInfo);
    }


}
