package com.dcjet.cs.api.expenseIMessage;

import com.dcjet.cs.bi.service.ExpenseIHeadServise;
import com.dcjet.cs.bi.service.ExpenseIListServise;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.dto.dec.BizISellListParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderHeadParam;
import com.dcjet.cs.params.dao.CostTypeMapper;
import com.dcjet.cs.params.model.CostType;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/expenseIList")
@Api(tags = "费用头接口")
public class ExpenseIMessageListContrller extends BaseController {
    @Resource
    private ExpenseIListServise expenseIListServise;
    @Resource
    private ExcelService excelService;
    @Resource
    private CostTypeMapper costTypeMapper;
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<ExpenseIListDto>> getListPaged(@RequestBody ExpenseIListParam expenseIListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ExpenseIListDto>> paged = expenseIListServise.getListPaged(expenseIListParam, pageParam,userInfo);
        return paged;
    }
    
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<ExpenseIListDto> update(@PathVariable String sid, @Valid @RequestBody ExpenseIListParam expenseIListParam, UserInfoToken userInfo) {
        expenseIListParam.setSid(sid);
        ExpenseIListDto expenseIHeadDto = expenseIListServise.update(expenseIListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (expenseIHeadDto != null) {
            resultObject.setData(expenseIHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        expenseIListServise.delete(sids,userInfo);
        return resultObject;
    }
    @ApiOperation("Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<ExpenseIListParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<ExpenseIListDto> expenseIListDtos = expenseIListServise.selectAll(exportParam.getExportColumns(), userInfo);
        expenseIListDtos = convertForPrint(expenseIListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), expenseIListDtos);
    }

    private List<ExpenseIListDto> convertForPrint(List<ExpenseIListDto> expenseIListDtos) {
        for (ExpenseIListDto expenseIListDto : expenseIListDtos) {
            //费用类型
            if(StringUtils.isNotBlank(expenseIListDto.getExpenseType())){
                //如果expenseType存在","则将其转为数组对数组每个对象进行转换，转换后将数组内信息进行","汇总
                String[] expenseTypes = expenseIListDto.getExpenseType().split(",");
                StringBuilder expenseTypeBuilder = new StringBuilder();
                for (String expenseType : expenseTypes) {
                    List<CostType> select = costTypeMapper.select(new CostType() {{
                        setParamCode(expenseType);
                    }});
                    if(select!= null && select.size() > 0){
                        expenseTypeBuilder.append(select.get(0).getCostName()).append(",");
                    }else{
                        expenseTypeBuilder.append(expenseType).append(",");
                    }
                }
                expenseIListDto.setExpenseType(expenseTypeBuilder.toString().substring(0, expenseTypeBuilder.length() - 1));
            }
        }

        return expenseIListDtos;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listContract")
    public ResultObject<List<CostIContractParam>> listContract(@RequestBody CostIContractParam costIContractParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIContractParam>> paged = expenseIListServise.listContract(costIContractParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listShippingOrder6")
    public ResultObject<List<BizNonIncomingGoodsHeadParam>> listContract6(@RequestBody BizNonIncomingGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsHeadParam>> paged = expenseIListServise.listContract6(param, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("第三条线进口费用进出货单分页查询接口")
    @PostMapping("listShippingOrder3")
    public ResultObject<List<ForeignContractHeadParam>> listContract3(@RequestBody ForeignContractHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadParam>> paged = expenseIListServise.listContract3(param, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("第三条线进口费用进出货单分页查询接口")
    @PostMapping("listShippingOrder7")
    public ResultObject<List<ForeignContractHeadParam>> listContract7(@RequestBody ForeignContractHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadParam>> paged = expenseIListServise.listContract7(param, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listContractPayment")
    public ResultObject<List<CostIContractParam>> listContractPayment(@RequestBody CostIContractParam costIContractParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIContractParam>> paged = expenseIListServise.listContractPayment(costIContractParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("第三条线付款通知合同查询借口")
    @PostMapping("listContractPayment3")
    public ResultObject<List<ForeignContractHeadParam>> listContractPayment3(@RequestBody ForeignContractHeadParam foreignContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadParam>> paged = expenseIListServise.listContractPayment3(foreignContractHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listAuxmatContractPayment")
    public ResultObject<List<BizIAuxmatForContractHeadParam>> listAuxmatContractPayment(@RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIAuxmatForContractHeadParam>> paged = expenseIListServise.listAuxmatContractPayment(bizIAuxmatForContractHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listAuxnonContractPayment")
    public ResultObject<List<BizINonStateAuxmatAggrContractHeadParam>> listAuxnonContractPayment(@RequestBody BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizINonStateAuxmatAggrContractHeadParam>> paged = expenseIListServise.listAuxnonContractPayment(bizINonStateAuxmatAggrContractHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listAuxnonContractPaymentCost")
    public ResultObject<List<BizINonStateAuxmatAggrContractHeadParam>> listAuxnonContractPaymentCost(@RequestBody BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizINonStateAuxmatAggrContractHeadParam>> paged = expenseIListServise.listAuxnonContractPaymentCost(bizINonStateAuxmatAggrContractHeadParam, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("合同分页查询接口")
    @PostMapping("list3")
    public ResultObject<List<ForeignContractHeadParam>> list3(@RequestBody ForeignContractHeadParam foreignContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadParam>> paged = expenseIListServise.list3(foreignContractHeadParam, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("合同分页查询接口")
    @PostMapping("list7")
    public ResultObject<List<ForeignContractHeadParam>> list7(@RequestBody ForeignContractHeadParam foreignContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadParam>> paged = expenseIListServise.list7(foreignContractHeadParam, pageParam,userInfo);
        return paged;
    }


    @ApiOperation("合同新增明细接口")
    @PostMapping("insertContractDetail")
    public ResultObject insertContractDetail(@RequestBody CostIContractParam costIContractParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        costIContractParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertContractDetail(costIContractParam,userInfo);
        return resultObject;
    }

    @ApiOperation("合同新增明细接口")
    @PostMapping("insertContractDetail6")
    public ResultObject insertContractDetail6(@RequestBody BizINonStateAuxmatAggrContractHeadParam costIContractParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        costIContractParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertContractDetail6(costIContractParam,userInfo);
        return resultObject;
    }
    @ApiOperation("第三条线进口管理合同新增")
    @PostMapping("insertContractDetail3")
    public ResultObject insertContractDetail3(@RequestBody ForeignContractHeadParam costIContractParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        costIContractParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertContractDetail3(costIContractParam,userInfo);
        return resultObject;
    }
    @ApiOperation("第三条线进口管理合同新增")
    @PostMapping("insertContractDetail7")
    public ResultObject insertContractDetail7(@RequestBody ForeignContractHeadParam costIContractParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        costIContractParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertContractDetail7(costIContractParam,userInfo);
        return resultObject;
    }

    @ApiOperation("出货单分页查询接口")
    @PostMapping("listShippingOrder")
    public ResultObject<List<CostIShippingOrderParam>> listShippingOrder(@RequestBody CostIShippingOrderParam costIShippingOrderParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIShippingOrderParam>> paged = expenseIListServise.listShippingOrder(costIShippingOrderParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("出货单分页查询接口")
    @PostMapping("listShippingOrderPayment")
    public ResultObject<List<CostIShippingOrderParam>> listShippingOrderPayment(@RequestBody CostIShippingOrderParam costIShippingOrderParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIShippingOrderParam>> paged = expenseIListServise.listShippingOrderPayment(costIShippingOrderParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("第二条线出货单分页查询接口")
    @PostMapping("listPayment2")
    public ResultObject<List<BizIncomingGoodsHeadParam>> listPayment2(@RequestBody BizIncomingGoodsHeadParam bizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsHeadParam>> paged = expenseIListServise.listPayment2(bizIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("第六条线出货单分页查询接口")
    @PostMapping("listPayment6")
    public ResultObject<List<BizNonIncomingGoodsHeadParam>> listPayment6(@RequestBody BizNonIncomingGoodsHeadParam bizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsHeadParam>> paged = expenseIListServise.listPayment6(bizIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("第三条线付款通知进出货单分页查询接口")
    @PostMapping("listPayment3")
    public ResultObject<List<ForeignContractHeadParam>> listPayment3(@RequestBody ForeignContractHeadParam foreignContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadParam>> paged = expenseIListServise.listPayment3(foreignContractHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("出货单新增明细接口")
    @PostMapping("insertShippingOrder")
    public ResultObject insertShippingOrder(@RequestBody CostIShippingOrderParam costIShippingOrderParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        costIShippingOrderParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertShippingOrder(costIShippingOrderParam,userInfo);
        return resultObject;
    }

    @ApiOperation("出货单新增明细接口")
    @PostMapping("insertShippingOrder6")
    public ResultObject insertShippingOrder6(@RequestBody BizNonIncomingGoodsHeadParam bizNonIncomingGoodsHeadParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        bizNonIncomingGoodsHeadParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertShippingOrder6(bizNonIncomingGoodsHeadParam,userInfo);
        return resultObject;
    }
    @ApiOperation("第三条线进口管理出货单新增明细接口")
    @PostMapping("insertShippingOrder3")
    public ResultObject insertShippingOrder3(@RequestBody ForeignContractHeadParam foreignContractHeadParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        foreignContractHeadParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertShippingOrder3(foreignContractHeadParam,userInfo);
        return resultObject;
    }
    @ApiOperation("第七条线进口管理出货单新增明细接口")
    @PostMapping("insertShippingOrder7")
    public ResultObject insertShippingOrder7(@RequestBody BizPurchaseOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        param.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertShippingOrder7(param,userInfo);
        return resultObject;
    }

    @PostMapping("/getSumDataByInvoiceSummary")
    public ResultObject getSumDataByInvoiceSummary(@RequestBody CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {
        return expenseIListServise.getSumDataByInvoiceSellSummary(costIShippingOrderParam, userInfo);
    }
    //获取费用类型对应的下拉参数
    @PostMapping("/selectCostType")
    public ResultObject selectCostType(@RequestBody CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {
        return expenseIListServise.selectCostType(costIShippingOrderParam, userInfo);
    }


    /**
     * 读取税金
     * @param file
     * @param sids
     * @param headId
     * @return
     */
    @PostMapping("upload")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = " 附件", dataType = "String"),
            @ApiImplicitParam(name = "sids", value = "业务单证类型", dataType = "String"),
            @ApiImplicitParam(name = "headId", value = "进口费用表头id", dataType = "String"),
    })
    public ResultObject uploadTaxPdf(@RequestParam(value = "file", required = false) MultipartFile[] file,
                                     @RequestParam("sids") List<String> sids,
                                     @RequestParam("headId") String headId,
                                     UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("读取成功！"));
        expenseIListServise.uploadTaxPdf(file, sids, headId, userInfo);
        return resultObject;
    }
}
