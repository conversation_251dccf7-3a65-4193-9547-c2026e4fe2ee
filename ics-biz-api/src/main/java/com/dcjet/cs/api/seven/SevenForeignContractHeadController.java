package com.dcjet.cs.api.seven;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.seven.*;
import com.dcjet.cs.seven.service.SevenForeignContractHeadService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/v1/seven/foreignContract/head")
@Api(tags = "第七条线出口加工进口薄片-外商合同表头")
public class SevenForeignContractHeadController {
    @Resource
    private SevenForeignContractHeadService sevenForeignContractHeadService;
    @Resource
    private ExcelService excelService;

    /**
     * 获取分页列表
     *
     * @param headParam 外商合同表头参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 外商合同表头分页列表
     */
    @ApiOperation("获取外商合同表头分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication(paths = {"/tobacco/seven/foreignContract"})
    public ResultObject<List<SevenForeignContractHeadDto>> getListPaged(@RequestBody SevenForeignContractHeadParam headParam,
                                                                        PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.sevenForeignContractHeadService.getListPaged(headParam, pageParam, userInfo);
    }

    /**
     * 获取选项数据
     *
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @GetMapping("/options")
    public ResultObject<SevenForeignContractOptionsDto> getOptionsData(UserInfoToken<?> userInfo) {
        SevenForeignContractOptionsDto allOptions = this.sevenForeignContractHeadService.getAllOptions(userInfo);
        return ResultObject.createInstance(true, "查询成功", allOptions);
    }

    /**
     * 导出外商合同表头列表
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 响应实体
     */
    @ApiOperation("导出外商合同表头列表")
    @PostMapping("/export")
    public ResponseEntity<?> export(@RequestBody BasicExportParam<SevenForeignContractHeadParam> exportParam, UserInfoToken<?> userInfo) {
        List<SevenForeignContractHeadDto> dtoList = this.sevenForeignContractHeadService.getAllList(exportParam.getExportColumns(), userInfo);
        try {
            return this.excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8)
                    , exportParam.getHeader(), dtoList);
        } catch (Exception e) {
            throw new ErrorException(500, "导出失败!");
        }
    }


    /**
     * 新增外商合同
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("新增外商合同")
    @PostMapping
    public ResultObject<?> insert(@Valid @RequestBody SevenForeignContractAddParam addParam, UserInfoToken<?> userInfo) {
        SevenForeignContractHeadDto headDto = this.sevenForeignContractHeadService.insert(addParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, headDto);
    }

    /**
     * 更改外商合同
     *
     * @param headParam 表头参数
     * @param userInfo  用户信息
     * @return 响应数据
     */
    @ApiOperation("更改外商合同")
    @PutMapping("/{id}")
    public ResultObject<?> update(@PathVariable String id, @Valid @RequestBody SevenForeignContractHeadParam headParam,
                                  UserInfoToken<?> userInfo) {
        headParam.setId(id);
        SevenForeignContractHeadDto headDto = this.sevenForeignContractHeadService.update(headParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, headDto);
    }

    /**
     * 删除外商合同
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除外商合同")
    @DeleteMapping("/{ids}")
    public ResultObject<?> delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        this.sevenForeignContractHeadService.delete(ids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }

    /**
     * 确认外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("确认外商合同")
    @PutMapping("/confirm/{id}")
    public ResultObject<?> confirm(@PathVariable String id, UserInfoToken<?> userInfo) {
        SevenForeignContractHeadDto foreignContractHeadDto = this.sevenForeignContractHeadService.confirm(id, userInfo);
        return ResultObject.createInstance(true, "确认成功", foreignContractHeadDto);
    }

    /**
     * 作废外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("作废外商合同")
    @PutMapping("/invalidate/{id}")
    public ResultObject<?> invalidate(@PathVariable String id, UserInfoToken<?> userInfo) {
        this.sevenForeignContractHeadService.invalidate(id, userInfo);
        return ResultObject.createInstance(true, "作废成功");
    }

    /**
     * 校验外商合同版本复制
     *
     * @param contractNo 合同编号
     * @param userInfo   用户信息
     * @return 响应数据，data为1则表示存在有效数据
     */
    @ApiOperation("校验外商合同版本复制")
    @GetMapping("/checkVersionCopy/{contractNo}")
    public ResultObject<?> checkVersionCopy(@PathVariable String contractNo, UserInfoToken<?> userInfo) {
        String hasValid = this.sevenForeignContractHeadService.checkVersionCopy(contractNo, userInfo);
        return ResultObject.createInstance(true, "校验成功", hasValid);
    }

    /**
     * 外商合同版本复制
     *
     * @param headParam 外商合同表头参数
     * @param userInfo  用户信息
     * @return 响应数据
     */
    @ApiOperation("外商合同版本复制")
    @PostMapping("/versionCopy")
    public ResultObject<?> versionCopy(@RequestBody SevenForeignContractHeadParam headParam, UserInfoToken<?> userInfo) {
        this.sevenForeignContractHeadService.versionCopy(headParam, userInfo);
        return ResultObject.createInstance(true, "版本复制成功");
    }

    /**
     * 打印会签单
     *
     * @param printParam 打印参数
     * @return 会签单文件
     */
    @ApiOperation("打印会签单")
    @PostMapping("/print")
    public ResponseEntity<byte[]> printCountersignSheet(@RequestBody SevenForeignContractPrintCountersignSheetParam printParam,
                                                        UserInfoToken<?> userInfo) {
        try {
            List<String> ids = printParam.getIds();
            String type = printParam.getType();
            boolean pdf = "PDF".equals(type), single = CollectionUtils.isNotEmpty(ids) && ids.size() == 1;
            byte[] fileBytes = this.sevenForeignContractHeadService.printCountersignSheet(ids, type, userInfo);
            String outName = "会签单." + (single ? (pdf ? "pdf" : "xlsx") : "zip");
            HttpHeaders h = new HttpHeaders();
            h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
            h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(fileBytes, h, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException("打印会签单错误", e);
        }
    }

    /**
     * 查询待审核列表
     *
     * @param headParam 表头参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 数据列表
     */
    @ApiOperation("查询待审核列表")
    @PostMapping("/aeoList")
    public ResultObject<List<SevenForeignContractHeadDto>> getAeoListPaged(@RequestBody SevenForeignContractHeadParam headParam,
                                                                           PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.sevenForeignContractHeadService.getAeoListPaged(headParam, pageParam, userInfo);
    }
}