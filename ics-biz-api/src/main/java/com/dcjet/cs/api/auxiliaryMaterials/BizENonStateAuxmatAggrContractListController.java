package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListExportParam;
import com.dcjet.cs.auxiliaryMaterials.service.BizENonStateAuxmatAggrContractListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@RestController
@RequestMapping("v1/bizENonStateAuxmatAggrContractList")
@Api(tags = "接口")
public class BizENonStateAuxmatAggrContractListController extends BaseController {
    @Resource
    private BizENonStateAuxmatAggrContractListService bizENonStateAuxmatAggrContractListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizENonStateAuxmatAggrContractListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizENonStateAuxmatAggrContractListDto>> getListPaged(@RequestBody BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizENonStateAuxmatAggrContractListDto>> paged = bizENonStateAuxmatAggrContractListService.getListPaged(bizENonStateAuxmatAggrContractListParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizENonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizENonStateAuxmatAggrContractListDto> insert(@Valid @RequestBody BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        ResultObject<BizENonStateAuxmatAggrContractListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizENonStateAuxmatAggrContractListDto bizENonStateAuxmatAggrContractListDto = bizENonStateAuxmatAggrContractListService.insert(bizENonStateAuxmatAggrContractListParam, userInfo);
        if (bizENonStateAuxmatAggrContractListDto != null) {
            resultObject.setData(bizENonStateAuxmatAggrContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizENonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizENonStateAuxmatAggrContractListDto> update(@PathVariable String sid, @Valid @RequestBody BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        bizENonStateAuxmatAggrContractListParam.setSid(sid);
        BizENonStateAuxmatAggrContractListDto bizENonStateAuxmatAggrContractListDto = bizENonStateAuxmatAggrContractListService.update(bizENonStateAuxmatAggrContractListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizENonStateAuxmatAggrContractListDto != null) {
            resultObject.setData(bizENonStateAuxmatAggrContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizENonStateAuxmatAggrContractListService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizENonStateAuxmatAggrContractListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizENonStateAuxmatAggrContractListDto> bizENonStateAuxmatAggrContractListDtos = bizENonStateAuxmatAggrContractListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizENonStateAuxmatAggrContractListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizENonStateAuxmatAggrContractListDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizENonStateAuxmatAggrContractListDto> list) {
        for(BizENonStateAuxmatAggrContractListDto item : list) {
        }
    }
}
