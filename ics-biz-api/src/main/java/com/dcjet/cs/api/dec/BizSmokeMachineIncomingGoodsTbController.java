package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizSmokeCommonService;
import com.dcjet.cs.dec.service.BizSmokeMachineIncomingGoodsTbService;
import com.dcjet.cs.dto.dec.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;

/**
* BizSmokeMachineIncomingGoodsTbController层
*
* <AUTHOR>
* @date 2025-07-04 21:29:36
*/
@RestController
@RequestMapping("v1/bizSmokeMachineIncomingGoodsTb")
@Api(tags = "烟机设备-进货单-投保信息接口")
public class BizSmokeMachineIncomingGoodsTbController{

    @Resource
    private BizSmokeMachineIncomingGoodsTbService bizSmokeMachineIncomingGoodsTbService;

    @Resource
    private BizSmokeCommonService bizSmokeCommonService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取烟机设备-进货单-投保信息数据
     * @param bizSmokeMachineIncomingGoodsTbParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取烟机设备-进货单-投保信息数据")
    @PostMapping("list")
    public ResultObject<List<BizSmokeMachineIncomingGoodsTbDto>> getListPaged(@RequestBody BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizSmokeMachineIncomingGoodsTbDto>> paged = bizSmokeMachineIncomingGoodsTbService.getListPaged(bizSmokeMachineIncomingGoodsTbParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizSmokeMachineIncomingGoodsTbParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizSmokeMachineIncomingGoodsTbDto> insert(@Valid @RequestBody BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, UserInfoToken userInfo) {
        ResultObject<BizSmokeMachineIncomingGoodsTbDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizSmokeMachineIncomingGoodsTbDto bizSmokeMachineIncomingGoodsTbDto = bizSmokeMachineIncomingGoodsTbService.insert(bizSmokeMachineIncomingGoodsTbParam, userInfo);
        if (bizSmokeMachineIncomingGoodsTbDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsTbDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizSmokeMachineIncomingGoodsTbParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizSmokeMachineIncomingGoodsTbDto> update(@PathVariable String sid, @Valid @RequestBody BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, UserInfoToken userInfo) {
        bizSmokeMachineIncomingGoodsTbParam.setId(sid);
        BizSmokeMachineIncomingGoodsTbDto bizSmokeMachineIncomingGoodsTbDto = bizSmokeMachineIncomingGoodsTbService.update(bizSmokeMachineIncomingGoodsTbParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizSmokeMachineIncomingGoodsTbDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsTbDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizSmokeMachineIncomingGoodsTbService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizSmokeMachineIncomingGoodsTbExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizSmokeMachineIncomingGoodsTbDto> bizSmokeMachineIncomingGoodsTbDtos = bizSmokeMachineIncomingGoodsTbService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizSmokeMachineIncomingGoodsTbDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizSmokeMachineIncomingGoodsTbDtos);
    }


    @PostMapping("/getTBByHeadId/{headId}")
    @ApiOperation("根据头id查询投保信息")
    public ResultObject<BizSmokeMachineIncomingGoodsTbDto> getDocumentByHeadId(@PathVariable String headId, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsTbService.getTBByHeadId(headId, userInfo);
    }


    @PostMapping("/updateAndInsert")
    @ApiOperation("判断数据是否存在，存在更新，不存在新增")
    public ResultObject updateAndInsert(@RequestBody @Valid BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsTbService.updateAndInsert(bizSmokeMachineIncomingGoodsTbParam, userInfo);
    }


    @ApiOperation("获取通用当前表体数据")
    @PostMapping("/getCommonKeyValueList")
    public ResultObject getCommonKeyValueList(@RequestBody BizSmokeMachineIncomingGoodsTbParam params, UserInfoToken userInfo) {
        return bizSmokeCommonService.getListCommonKeyValueList(userInfo, Arrays.asList(BizSmokeCommonService.BizTypeEnum.CURR,BizSmokeCommonService.BizTypeEnum.CUSTOMER));
    }


    @ApiOperation("回车计算保费")
    @PostMapping("/calcPremium")
    public ResultObject calcPremium(@RequestBody BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsTbService.calcPremium(bizSmokeMachineIncomingGoodsTbParam, userInfo);
    }



    /**
     * 压缩包打印
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("压缩包打印")
    @PostMapping("/generateTB")
    public ResponseEntity generateTB(@RequestBody GenerateYJTBParams param, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsTbService.generateTB(param,userInfo);
    }



    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizSmokeMachineIncomingGoodsTbDto> list) {
        for(BizSmokeMachineIncomingGoodsTbDto item : list) {
        
        }
    }


}
