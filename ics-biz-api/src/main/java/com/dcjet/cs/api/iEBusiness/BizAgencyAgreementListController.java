package com.dcjet.cs.api.iEBusiness;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListDto;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListDto;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListParam;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListExportParam;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListParam;
import com.dcjet.cs.iEBusiness.service.BizAgencyAgreementListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizAgencyAgreementList")
@Api(tags = "接口")
public class BizAgencyAgreementListController extends BaseController {
    @Resource
    private BizAgencyAgreementListService bizAgencyAgreementListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizAgencyAgreementListParam
     * @param pageParam
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizAgencyAgreementListDto>> getListPaged(@RequestBody BizAgencyAgreementListParam bizAgencyAgreementListParam, PageParam pageParam) {
        ResultObject<List<BizAgencyAgreementListDto>> paged = bizAgencyAgreementListService.selectAllPaged(bizAgencyAgreementListParam, pageParam);
        return paged;
    }
    /**
     * @param bizAgencyAgreementListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizAgencyAgreementListDto> insert(@Valid @RequestBody BizAgencyAgreementListParam bizAgencyAgreementListParam, UserInfoToken userInfo) {
		ResultObject<BizAgencyAgreementListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizAgencyAgreementListDto bizAgencyAgreementListDto = bizAgencyAgreementListService.insert(bizAgencyAgreementListParam, userInfo);
        if (bizAgencyAgreementListDto != null) {
            resultObject.setData(bizAgencyAgreementListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizAgencyAgreementListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizAgencyAgreementListDto> update(@PathVariable String sid, @Valid @RequestBody BizAgencyAgreementListParam bizAgencyAgreementListParam, UserInfoToken userInfo) {
        bizAgencyAgreementListParam.setSid(sid);
        BizAgencyAgreementListDto bizAgencyAgreementListDto = bizAgencyAgreementListService.update(bizAgencyAgreementListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizAgencyAgreementListDto != null) {
            resultObject.setData(bizAgencyAgreementListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
		ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        bizAgencyAgreementListService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizAgencyAgreementListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizAgencyAgreementListDto> bizAgencyAgreementListDtos = bizAgencyAgreementListService.selectAll(exportParam.getExportColumns());
        bizAgencyAgreementListDtos = convertForPrint(bizAgencyAgreementListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizAgencyAgreementListDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<BizAgencyAgreementListDto> convertForPrint(List<BizAgencyAgreementListDto> list) {
        for(BizAgencyAgreementListDto item : list) {
        }
        return list;
    }

    @ApiOperation("汇总表体合同数量及总金额")
    @PostMapping("getSumTotal")
    public ResultObject<BizIContractListDto> getSumTotal(@RequestBody BizAgencyAgreementListParam bizAgencyAgreementListParam, UserInfoToken userInfo) {
        return  bizAgencyAgreementListService.getContractTotal(bizAgencyAgreementListParam, userInfo);
    }


}
