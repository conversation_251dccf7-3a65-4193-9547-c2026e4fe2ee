package com.dcjet.cs.api.equipment;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyDto;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyExportParam;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify;
import com.dcjet.cs.equipment.service.BizIEquipmentPlanPayNotifyService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@RestController
@RequestMapping("v1/bizIEquipmentPlanPayNotify")
@Api(tags = "接口")
public class BizIEquipmentPlanPayNotifyController extends BaseController {
    @Resource
    private BizIEquipmentPlanPayNotifyService bizIEquipmentPlanPayNotifyService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIEquipmentPlanPayNotifyParam
     * @param pageParam
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIEquipmentPlanPayNotifyDto>> getListPaged(@RequestBody BizIEquipmentPlanPayNotifyParam bizIEquipmentPlanPayNotifyParam, PageParam pageParam) {
        ResultObject<List<BizIEquipmentPlanPayNotifyDto>> paged = bizIEquipmentPlanPayNotifyService.selectAllPaged(bizIEquipmentPlanPayNotifyParam, pageParam);
        return paged;
    }
    /**
     * @param bizIEquipmentPlanPayNotifyParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIEquipmentPlanPayNotifyDto> insert(@Valid @RequestBody BizIEquipmentPlanPayNotifyParam bizIEquipmentPlanPayNotifyParam, UserInfoToken userInfo) {
		ResultObject<BizIEquipmentPlanPayNotifyDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIEquipmentPlanPayNotifyDto bizIEquipmentPlanPayNotifyDto = bizIEquipmentPlanPayNotifyService.insert(bizIEquipmentPlanPayNotifyParam, userInfo);
        if (bizIEquipmentPlanPayNotifyDto != null) {
            resultObject.setData(bizIEquipmentPlanPayNotifyDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIEquipmentPlanPayNotifyParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIEquipmentPlanPayNotifyDto> update(@PathVariable String sid, @Valid @RequestBody BizIEquipmentPlanPayNotifyParam bizIEquipmentPlanPayNotifyParam, UserInfoToken userInfo) {
        bizIEquipmentPlanPayNotifyParam.setId(sid);
        BizIEquipmentPlanPayNotifyDto bizIEquipmentPlanPayNotifyDto = bizIEquipmentPlanPayNotifyService.update(bizIEquipmentPlanPayNotifyParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentPlanPayNotifyDto != null) {
            resultObject.setData(bizIEquipmentPlanPayNotifyDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
		ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        bizIEquipmentPlanPayNotifyService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     *
     * @return
     */
    @ApiOperation("获取划款参数信息")
    @PostMapping("getTransMes/{headId}/{businessType}")
    public ResultObject getTransMes(@PathVariable String headId, @PathVariable String businessType,UserInfoToken userInfo) {
        return bizIEquipmentPlanPayNotifyService.getTransMes(businessType,headId,userInfo);
    }

    /**
     *获取箱单列表
     * @return
     */
    @ApiOperation("获取箱单列表")
    @PostMapping("getContainerList/{businessType}")
    public ResultObject getContainerList(@PathVariable String businessType,UserInfoToken userInfo) {
        return bizIEquipmentPlanPayNotifyService.getContainerList(businessType,userInfo);
    }
    /**
     *
     * @return
     */
    @ApiOperation("获取计费箱数信息")
    @PostMapping("getContainerMes/{businessType}/{containerType}")
    public ResultObject getContainerMes( @PathVariable String businessType,@PathVariable String containerType,UserInfoToken userInfo) {
        return bizIEquipmentPlanPayNotifyService.getContainerMes(businessType,containerType,userInfo);
    }

    /**
     * 划款通知保存
     * @param bizIEquipmentPlanPayNotify
     * @param userInfo
     * @return
     */
    @ApiOperation("划款通知打印")
    @PostMapping("printPayNotify")
    public ResponseEntity printPayNotify(@RequestBody BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify, UserInfoToken userInfo) throws Exception {
        return bizIEquipmentPlanPayNotifyService.printPayNotify(bizIEquipmentPlanPayNotify, userInfo);
    }
}
