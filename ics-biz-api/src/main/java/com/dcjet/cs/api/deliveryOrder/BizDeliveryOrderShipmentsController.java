package com.dcjet.cs.api.deliveryOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderShipmentsDto;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderShipmentsParam;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderShipmentsExportParam;
import com.dcjet.cs.deliveryOrder.service.BizDeliveryOrderShipmentsService;
import com.dcjet.cs.dto.warehouse.BizStoreEHeadDto;
import com.dcjet.cs.dto.warehouse.BizStoreEHeadParam;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizDeliveryOrderShipments")
@Api(tags = "接口")
public class BizDeliveryOrderShipmentsController extends BaseController {
    @Resource
    private BizDeliveryOrderShipmentsService bizDeliveryOrderShipmentsService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizDeliveryOrderShipmentsParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizDeliveryOrderShipmentsDto>> getListPaged(@RequestBody BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizDeliveryOrderShipmentsDto>> paged = bizDeliveryOrderShipmentsService.getListPaged(bizDeliveryOrderShipmentsParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizDeliveryOrderShipmentsParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizDeliveryOrderShipmentsDto> insert(@Valid @RequestBody BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderShipmentsDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizDeliveryOrderShipmentsDto bizDeliveryOrderShipmentsDto = bizDeliveryOrderShipmentsService.insert(bizDeliveryOrderShipmentsParam, userInfo);
        if (bizDeliveryOrderShipmentsDto != null) {
            resultObject.setData(bizDeliveryOrderShipmentsDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizDeliveryOrderShipmentsParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizDeliveryOrderShipmentsDto> update(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, UserInfoToken userInfo) {
        bizDeliveryOrderShipmentsParam.setSid(sid);
        BizDeliveryOrderShipmentsDto bizDeliveryOrderShipmentsDto = bizDeliveryOrderShipmentsService.update(bizDeliveryOrderShipmentsParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizDeliveryOrderShipmentsDto != null) {
            resultObject.setData(bizDeliveryOrderShipmentsDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizDeliveryOrderShipmentsService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizDeliveryOrderShipmentsExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizDeliveryOrderShipmentsDto> bizDeliveryOrderShipmentsDtos = bizDeliveryOrderShipmentsService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizDeliveryOrderShipmentsDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizDeliveryOrderShipmentsDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizDeliveryOrderShipmentsDto> list) {
        for(BizDeliveryOrderShipmentsDto item : list) {
        }
    }
    @ApiOperation("获取根据head_sid")
    @PostMapping("/getDeliveryOrderShipmentsByHeadSid")
    public ResultObject <BizDeliveryOrderShipmentsDto> getDeliveryOrderShipmentsByHeadSid(@RequestBody BizDeliveryOrderShipmentsParam param, UserInfoToken userInfo) {
        return bizDeliveryOrderShipmentsService.getDeliveryOrderShipmentsByHeadSid(param, userInfo);
    }
}
