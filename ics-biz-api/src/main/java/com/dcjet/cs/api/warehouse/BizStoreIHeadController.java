package com.dcjet.cs.api.warehouse;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.dec.BizIOrderExtractParams;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.dcjet.cs.dto.warehouse.BizIStoreExtractParams;
import com.dcjet.cs.dto.warehouse.BizStoreIHeadDto;
import com.dcjet.cs.dto.warehouse.BizStoreIHeadParam;
import com.dcjet.cs.dto.warehouse.BizStoreIHeadExportParam;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.dcjet.cs.warehouse.service.BizStoreIHeadService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@RestController
@RequestMapping("v1/bizStoreIHead")
@Api(tags = "接口")
public class BizStoreIHeadController extends BaseController {
    @Resource
    private BizStoreIHeadService bizStoreIHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizStoreIHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizStoreIHeadDto>> getListPaged(@RequestBody BizStoreIHeadParam bizStoreIHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizStoreIHeadDto>> paged = bizStoreIHeadService.getListPaged(bizStoreIHeadParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizStoreIHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizStoreIHeadDto> insert(@Valid @RequestBody BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        ResultObject<BizStoreIHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizStoreIHeadDto bizStoreIHeadDto = bizStoreIHeadService.insert(bizStoreIHeadParam, userInfo);
        if (bizStoreIHeadDto != null) {
            resultObject.setData(bizStoreIHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizStoreIHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizStoreIHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        bizStoreIHeadParam.setSid(sid);
        BizStoreIHeadDto bizStoreIHeadDto = bizStoreIHeadService.update(bizStoreIHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizStoreIHeadDto != null) {
            resultObject.setData(bizStoreIHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizStoreIHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizStoreIHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizStoreIHeadDto> bizStoreIHeadDtos = bizStoreIHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizStoreIHeadDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizStoreIHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizStoreIHeadDto> list, UserInfoToken userInfo) {
        List<Map<String,String>> supplierList = bizStoreIHeadService.getSupplierList(userInfo);
        for(BizStoreIHeadDto item : list) {
            if (StringUtils.isNotBlank(item.getMerchantCode())){
                item.setMerchantCode(getSupplierNameByCode(supplierList,item.getMerchantCode()));
            }
        }
    }
    public String getSupplierNameByCode(List<Map<String,String>> supplierList, String supplierCode) {
        if (CollectionUtils.isNotEmpty(supplierList) && StringUtils.isNotBlank(supplierCode)) {
            // 根据供应商code找到列表中code对应的map数据
            Map<String, String> supplierMap = supplierList.stream().filter(map -> supplierCode.equals(map.get("value"))).findFirst().orElse(null);
            if (supplierMap != null) {
                return supplierMap.get("value") + " " + supplierMap.get("label");
            }
        }
        return supplierCode;
    }

    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreIHeadService.sendApproval(sid, userInfo);
    }

    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        return bizStoreIHeadService.confirmStatus(bizStoreIHeadParam, userInfo);
    }

    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreIHeadService.invalidate(sid, userInfo);
    }
    @ApiOperation("红冲接口")
    @PostMapping("redFlush/{sid}")
    public ResultObject redFlush(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreIHeadService.redFlush(sid, userInfo);
    }
    @ApiOperation("红冲接口")
    @PostMapping("back/{sid}")
    public ResultObject back(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreIHeadService.back(sid, userInfo);
    }

    @ApiOperation("获取流向数据")
    @PostMapping("/checkIsNextModule")
    public ResultObject checkIsNextModule(@RequestBody BizStoreIHeadParam params, UserInfoToken userInfo) {
        return bizStoreIHeadService.checkIsNextModule(params,userInfo);
    }

    @ApiOperation("生成进口订单数据（将进口合同符合条数据加载到进口管理）")
    @PostMapping("/generateIOrder")
    public ResultObject generateIOrder(@RequestBody BizIStoreExtractParams params, UserInfoToken userInfo) {
        ResultObject resultObject = bizStoreIHeadService.generateIOrder(params, userInfo);
        return resultObject;
    }

    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("listIncomingGoods")
    public ResultObject<List<BizIncomingGoodsHeadDto>> getBizIncomingGoodsHeadListPaged(@RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsHeadDto>> paged = bizStoreIHeadService.getBizIncomingGoodsHeadListPaged(tBizIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("打印入库回单")
    @PostMapping("/printReceiptContainer")
    public ResponseEntity printReceiptContainer(@RequestBody BizStoreIHead bizStoreIHead, UserInfoToken userInfo) throws Exception {
        return bizStoreIHeadService.printReceiptContainer(bizStoreIHead, userInfo);
    }

    @ApiOperation("获取供应商列表信息")
    @PostMapping("/getOrderSupplierList")
    public ResultObject getOrderSupplierList(BizStoreIHeadParam params, UserInfoToken userInfo) {
        return bizStoreIHeadService.getOrderSupplierList(params,userInfo);
    }
}
