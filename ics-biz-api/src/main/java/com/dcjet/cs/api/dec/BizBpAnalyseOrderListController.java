package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizBpAnalyseOrderListService;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListExportParam;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizBpAnalyseOrderListController层
*
* <AUTHOR>
* @date 2025-07-07 17:08:36
*/
@RestController
@RequestMapping("v1/bizBpAnalyseOrderList")
@Api(tags = "（第7条线）出料加工进口薄片-分析单表表体接口")
public class BizBpAnalyseOrderListController{

    @Resource
    private BizBpAnalyseOrderListService bizBpAnalyseOrderListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取（第7条线）出料加工进口薄片-分析单表表体数据
     * @param bizBpAnalyseOrderListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取（第7条线）出料加工进口薄片-分析单表表体数据")
    @PostMapping("list")
    public ResultObject<List<BizBpAnalyseOrderListDto>> getListPaged(@RequestBody BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizBpAnalyseOrderListDto>> paged = bizBpAnalyseOrderListService.getListPaged(bizBpAnalyseOrderListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizBpAnalyseOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizBpAnalyseOrderListDto> insert(@Valid @RequestBody BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, UserInfoToken userInfo) {
        ResultObject<BizBpAnalyseOrderListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizBpAnalyseOrderListDto bizBpAnalyseOrderListDto = bizBpAnalyseOrderListService.insert(bizBpAnalyseOrderListParam, userInfo);
        if (bizBpAnalyseOrderListDto != null) {
            resultObject.setData(bizBpAnalyseOrderListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizBpAnalyseOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizBpAnalyseOrderListDto> update(@PathVariable String sid, @Valid @RequestBody BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, UserInfoToken userInfo) {
        bizBpAnalyseOrderListParam.setId(sid);
        BizBpAnalyseOrderListDto bizBpAnalyseOrderListDto = bizBpAnalyseOrderListService.update(bizBpAnalyseOrderListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizBpAnalyseOrderListDto != null) {
            resultObject.setData(bizBpAnalyseOrderListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizBpAnalyseOrderListService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizBpAnalyseOrderListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizBpAnalyseOrderListDto> bizBpAnalyseOrderListDtos = bizBpAnalyseOrderListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizBpAnalyseOrderListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizBpAnalyseOrderListDtos);
    }



    @ApiOperation("根据ID获取分析单表体信息")
    @PostMapping("/getListById")
    public ResultObject<BizBpAnalyseOrderListDto> getListById(@RequestBody BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, UserInfoToken userInfo) {
        return bizBpAnalyseOrderListService.getListById(bizBpAnalyseOrderListParam,userInfo);
    }

                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizBpAnalyseOrderListDto> list) {
        for(BizBpAnalyseOrderListDto item : list) {
        
        }
    }


}
