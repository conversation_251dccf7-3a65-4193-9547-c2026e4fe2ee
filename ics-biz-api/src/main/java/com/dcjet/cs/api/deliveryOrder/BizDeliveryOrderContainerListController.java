package com.dcjet.cs.api.deliveryOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.service.BizDeliveryOrderContainerListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizDeliveryOrderContainerList")
@Api(tags = "接口")
public class BizDeliveryOrderContainerListController extends BaseController {
    @Resource
    private BizDeliveryOrderContainerListService bizDeliveryOrderContainerListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizDeliveryOrderContainerListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizDeliveryOrderContainerListDto>> getListPaged(@RequestBody BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizDeliveryOrderContainerListDto>> paged = bizDeliveryOrderContainerListService.getListPaged(bizDeliveryOrderContainerListParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizDeliveryOrderContainerListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizDeliveryOrderContainerListDto> insert(@Valid @RequestBody BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderContainerListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizDeliveryOrderContainerListDto bizDeliveryOrderContainerListDto = bizDeliveryOrderContainerListService.insert(bizDeliveryOrderContainerListParam, userInfo);
        if (bizDeliveryOrderContainerListDto != null) {
            resultObject.setData(bizDeliveryOrderContainerListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizDeliveryOrderContainerListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizDeliveryOrderContainerListDto> update(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, UserInfoToken userInfo) {
        bizDeliveryOrderContainerListParam.setSid(sid);
        BizDeliveryOrderContainerListDto bizDeliveryOrderContainerListDto = bizDeliveryOrderContainerListService.update(bizDeliveryOrderContainerListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizDeliveryOrderContainerListDto != null) {
            resultObject.setData(bizDeliveryOrderContainerListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizDeliveryOrderContainerListService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizDeliveryOrderContainerListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizDeliveryOrderContainerListDto> bizDeliveryOrderContainerListDtos = bizDeliveryOrderContainerListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizDeliveryOrderContainerListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizDeliveryOrderContainerListDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizDeliveryOrderContainerListDto> list) {
        for(BizDeliveryOrderContainerListDto item : list) {
        }
    }

    @ApiOperation("获取根据sid")
    @PostMapping("/getDeliveryOrderCListBySid/{sid}")
    public ResultObject <BizDeliveryOrderContainerListDto> getDeliveryOrderCListBySid(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderContainerListParam param, UserInfoToken userInfo) {
        param.setSid(sid);
        return bizDeliveryOrderContainerListService.getDeliveryOrderCListBySid(param, userInfo);
    }
}
