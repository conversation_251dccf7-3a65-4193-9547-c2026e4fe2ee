package com.dcjet.cs.api.iEBusiness;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementDto;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementParam;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementExportParam;
import com.dcjet.cs.dto.iEBusiness.ForContractListDto;
import com.dcjet.cs.iEBusiness.service.BizAgencyAgreementService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizAgencyAgreement")
@Api(tags = "接口")
public class BizAgencyAgreementController extends BaseController {
    @Resource
    private BizAgencyAgreementService bizAgencyAgreementService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BizMerchantService bizMerchantService;
    /**
     * @param bizAgencyAgreementParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizAgencyAgreementDto>> getListPaged(@RequestBody BizAgencyAgreementParam bizAgencyAgreementParam, PageParam pageParam, UserInfoToken userInfo) {
        bizAgencyAgreementParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizAgencyAgreementDto>> paged = bizAgencyAgreementService.getListPaged(bizAgencyAgreementParam, pageParam);
        return paged;
    }

    @ApiOperation("待审核页面查询接口")
    @PostMapping("aeoList")
    public ResultObject<List<BizAgencyAgreementDto>> getAeoListPaged(@RequestBody BizAgencyAgreementParam bizAgencyAgreementParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizAgencyAgreementDto>> paged = bizAgencyAgreementService.getAeoListPaged(bizAgencyAgreementParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param bizAgencyAgreementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizAgencyAgreementDto> insert(@RequestBody BizAgencyAgreementParam bizAgencyAgreementParam, UserInfoToken userInfo) {
        ResultObject<BizAgencyAgreementDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizAgencyAgreementDto bizAgencyAgreementDto = bizAgencyAgreementService.insert(bizAgencyAgreementParam, userInfo);
        if (bizAgencyAgreementDto != null) {
            resultObject.setData(bizAgencyAgreementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizAgencyAgreementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizAgencyAgreementDto> update(@PathVariable String sid, @Valid @RequestBody BizAgencyAgreementParam bizAgencyAgreementParam, UserInfoToken userInfo) {
        bizAgencyAgreementParam.setSid(sid);
        BizAgencyAgreementDto bizAgencyAgreementDto = bizAgencyAgreementService.update(bizAgencyAgreementParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizAgencyAgreementDto != null) {
            resultObject.setData(bizAgencyAgreementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizAgencyAgreementService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizAgencyAgreementExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizAgencyAgreementDto> bizAgencyAgreementDtos = bizAgencyAgreementService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizAgencyAgreementDtos, userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizAgencyAgreementDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizAgencyAgreementDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        final Map<String, String> merchantMap = bizMerchantService.createMerchantMap(bizMerchants);
        for(BizAgencyAgreementDto item : list) {
            item.setCustomer(merchantMap.get(item.getCustomer()));
            item.setBillStatus(CommonEnum.OrderStatusEnum.getValue(item.getBillStatus()));
        }
    }

    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizAgencyAgreementParam param, UserInfoToken userInfo) {
        return bizAgencyAgreementService.confirmStatus(param, userInfo);
    }

    @ApiOperation("获取外商合同列表接口")
    @PostMapping("forContractList")
    public ResultObject<List<ForContractListDto>> getForContractList(@RequestBody BizAgencyAgreementParam param, UserInfoToken userInfo) {
        List<ForContractListDto> contractList = bizAgencyAgreementService.getForContractList(param, userInfo);
        return ResultObject.createInstance(true, "查询成功", contractList);
    }

    @ApiOperation(value = "打印")
    @PostMapping("print")
    public ResponseEntity print(@RequestBody BizAgencyAgreementParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("代理出口协议书") + ".xlsx";
        String exportFileName = bizAgencyAgreementService.print("代理出口协议", param,userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    @ApiOperation(value = "会签单")
    @PostMapping("sign")
    public ResponseEntity sign(@RequestBody BizAgencyAgreementParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("代理出口协议会签单") + ".xlsx";
        String exportFileName = bizAgencyAgreementService.sign("代理出口协议会签单", param,userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
}
