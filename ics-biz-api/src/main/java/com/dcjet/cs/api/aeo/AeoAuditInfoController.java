package com.dcjet.cs.api.aeo;

import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.aeo.AeoAuditInfoDto;
import com.dcjet.cs.dto.aeo.AeoAuditInfoParam;
import com.dcjet.cs.dto.aeo.AuditUserDto;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.XdoMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * generated by Generate dc
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@RestController
@RequestMapping("v1/aeoAuditInfo")
@Api(tags = "AEO管理-AEO审核信息接口")
public class AeoAuditInfoController extends BaseController {
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;

    /**
     * @param aeoAuditInfoParam
     * @param userInfo
     * @return java.util.List<com.dc.cs.dto.aeo.AeoAuditInfoDto>
     * @description 根据SID查询审核信息
     * <AUTHOR>
     * @date 2019-02-19
     */
    @ApiOperation("根据数据SID获取审核列表")
    @PostMapping("/selectListBySid")
    public List<AeoAuditInfoDto> selectListBySid(@RequestBody AeoAuditInfoParam aeoAuditInfoParam, UserInfoToken userInfo) {
        List<AeoAuditInfoDto> paged = aeoAuditInfoService.selectListBySid(aeoAuditInfoParam, userInfo);
        return paged;
    }


}
