package com.dcjet.cs.api.dec;
import com.dcjet.cs.dec.service.BizExportGoodsSellHeadService;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadDto;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadExportParam;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadParam;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListTotal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizExportGoodsSellHeadController层
*
* <AUTHOR>
* @date 2025-07-07 14:16:12
*/
@RestController
@RequestMapping("v1/bizExportGoodsSellHead")
@Api(tags = "第9条线-非国营贸易出口辅料-外销发票表头接口")
public class BizExportGoodsSellHeadController{

    @Resource
    private BizExportGoodsSellHeadService bizExportGoodsSellHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取第9条线-非国营贸易出口辅料-外销发票表头数据
     * @param bizExportGoodsSellHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取第9条线-非国营贸易出口辅料-外销发票表头数据")
    @PostMapping("list")
    public ResultObject<List<BizExportGoodsSellHeadDto>> getListPaged(@RequestBody BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizExportGoodsSellHeadDto>> paged = bizExportGoodsSellHeadService.getListPaged(bizExportGoodsSellHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizExportGoodsSellHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("insert")
    public ResultObject<BizExportGoodsSellHeadDto> insert(@Valid @RequestBody BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam, UserInfoToken userInfo) {
        ResultObject<BizExportGoodsSellHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizExportGoodsSellHeadDto bizExportGoodsSellHeadDto = bizExportGoodsSellHeadService.insert(bizExportGoodsSellHeadParam, userInfo);
        if (bizExportGoodsSellHeadDto != null) {
            resultObject.setData(bizExportGoodsSellHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizExportGoodsSellHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizExportGoodsSellHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam, UserInfoToken userInfo) {
        bizExportGoodsSellHeadParam.setId(sid);
        BizExportGoodsSellHeadDto bizExportGoodsSellHeadDto = bizExportGoodsSellHeadService.update(bizExportGoodsSellHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizExportGoodsSellHeadDto != null) {
            resultObject.setData(bizExportGoodsSellHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizExportGoodsSellHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizExportGoodsSellHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizExportGoodsSellHeadDto> bizExportGoodsSellHeadDtos = bizExportGoodsSellHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizExportGoodsSellHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizExportGoodsSellHeadDtos);
    }



    @ApiOperation("/根据ID获取第9条线-非国营贸易出口辅料-外销发票表头数据")
    @GetMapping("getFormDataById/{id}")
    public ResultObject<BizExportGoodsSellHeadDto> getFormDataById(@PathVariable String id, UserInfoToken userInfo) {
        return bizExportGoodsSellHeadService.getFormDataById(id, userInfo);
    }


    @ApiOperation("确认数据")
    @PostMapping("confirm")
    public ResultObject<BizExportGoodsSellHeadDto> confirm(@RequestBody BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsSellHeadService.confirm(param, userInfo);
    }


    @ApiOperation("红冲")
    @PostMapping("redFlush")
    public ResultObject<BizExportGoodsSellHeadDto> redFlush(@RequestBody BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsSellHeadService.redFlush(param, userInfo);
    }


    @ApiOperation("退单")
    @PostMapping("backOrder")
    public ResultObject<BizExportGoodsSellHeadDto> backOrder(@RequestBody BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        return bizExportGoodsSellHeadService.backOrder(param, userInfo);
    }





                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizExportGoodsSellHeadDto> list) {
        for(BizExportGoodsSellHeadDto item : list) {
        
        }
    }


}
