package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizExportGoodsListBoxService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListDto;
import com.dcjet.cs.dto.dec.BizExportGoodsListBoxDto;
import com.dcjet.cs.dto.dec.BizExportGoodsListBoxExportParam;
import com.dcjet.cs.dto.dec.BizExportGoodsListBoxParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizExportGoodsListBoxController层
*
* <AUTHOR>
* @date 2025-07-07 13:48:47
*/
@RestController
@RequestMapping("v1/bizExportGoodsListBox")
@Api(tags = "第9条线-非国营贸易出口辅料-出货信息表体（商品信息）-装箱子表接口")
public class BizExportGoodsListBoxController{

    @Resource
    private BizExportGoodsListBoxService bizExportGoodsListBoxService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取第9条线-非国营贸易出口辅料-出货信息表体（商品信息）-装箱子表数据
     * @param bizExportGoodsListBoxParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取第9条线-非国营贸易出口辅料-出货信息表体（商品信息）-装箱子表数据")
    @PostMapping("list")
    public ResultObject<List<BizExportGoodsListBoxDto>> getListPaged(@RequestBody BizExportGoodsListBoxParam bizExportGoodsListBoxParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizExportGoodsListBoxDto>> paged = bizExportGoodsListBoxService.getListPaged(bizExportGoodsListBoxParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizExportGoodsListBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizExportGoodsListBoxDto> insert(@Valid @RequestBody BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        ResultObject<BizExportGoodsListBoxDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizExportGoodsListBoxDto bizExportGoodsListBoxDto = bizExportGoodsListBoxService.insert(bizExportGoodsListBoxParam, userInfo);
        if (bizExportGoodsListBoxDto != null) {
            resultObject.setData(bizExportGoodsListBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizExportGoodsListBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizExportGoodsListBoxDto> update(@PathVariable String sid, @Valid @RequestBody BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        bizExportGoodsListBoxParam.setId(sid);
        BizExportGoodsListBoxDto bizExportGoodsListBoxDto = bizExportGoodsListBoxService.update(bizExportGoodsListBoxParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizExportGoodsListBoxDto != null) {
            resultObject.setData(bizExportGoodsListBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizExportGoodsListBoxService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizExportGoodsListBoxExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizExportGoodsListBoxDto> bizExportGoodsListBoxDtos = bizExportGoodsListBoxService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizExportGoodsListBoxDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizExportGoodsListBoxDtos);
    }



    @ApiOperation("根据合同号获取外商合同表体数据")
    @PostMapping("/getListByContractNo")
    public ResultObject<List<BizENonStateAuxmatAggrContractListDto>> getListByContractNo(@RequestBody BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        return bizExportGoodsListBoxService.getListByContractNo(bizExportGoodsListBoxParam, userInfo);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizExportGoodsListBoxDto> list) {
        for(BizExportGoodsListBoxDto item : list) {
        
        }
    }


}
