package com.dcjet.cs.api.deliveryOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.service.BizDeliveryOrderCertService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@RestController
@RequestMapping("v1/bizDeliveryOrderCert")
@Api(tags = "接口")
public class BizDeliveryOrderCertController extends BaseController {
    @Resource
    private BizDeliveryOrderCertService bizDeliveryOrderCertService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizDeliveryOrderCertParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizDeliveryOrderCertDto>> getListPaged(@RequestBody BizDeliveryOrderCertParam bizDeliveryOrderCertParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizDeliveryOrderCertDto>> paged = bizDeliveryOrderCertService.getListPaged(bizDeliveryOrderCertParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizDeliveryOrderCertParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizDeliveryOrderCertDto> insert(@Valid @RequestBody BizDeliveryOrderCertParam bizDeliveryOrderCertParam, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderCertDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizDeliveryOrderCertDto bizDeliveryOrderCertDto = bizDeliveryOrderCertService.insert(bizDeliveryOrderCertParam, userInfo);
        if (bizDeliveryOrderCertDto != null) {
            resultObject.setData(bizDeliveryOrderCertDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizDeliveryOrderCertParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizDeliveryOrderCertDto> update(@PathVariable String sid, @Valid @RequestBody BizDeliveryOrderCertParam bizDeliveryOrderCertParam, UserInfoToken userInfo) {
        bizDeliveryOrderCertParam.setSid(sid);
        BizDeliveryOrderCertDto bizDeliveryOrderCertDto = bizDeliveryOrderCertService.update(bizDeliveryOrderCertParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizDeliveryOrderCertDto != null) {
            resultObject.setData(bizDeliveryOrderCertDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizDeliveryOrderCertService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizDeliveryOrderCertExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizDeliveryOrderCertDto> bizDeliveryOrderCertDtos = bizDeliveryOrderCertService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizDeliveryOrderCertDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizDeliveryOrderCertDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizDeliveryOrderCertDto> list) {
        for(BizDeliveryOrderCertDto item : list) {
        }
    }
    @ApiOperation("获取根据head_sid")
    @PostMapping("/getDeliveryOrderCertByHeadSid")
    public ResultObject <BizDeliveryOrderCertDto> getDeliveryOrderCertByHeadSid(@RequestBody BizDeliveryOrderCertParam param, UserInfoToken userInfo) {
        return bizDeliveryOrderCertService.getDeliveryOrderCertByHeadSid(param, userInfo);
    }
}
