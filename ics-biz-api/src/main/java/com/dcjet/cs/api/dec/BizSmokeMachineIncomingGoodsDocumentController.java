package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizSmokeMachineIncomingGoodsDocumentService;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsDocumentDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsDocumentExportParam;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsDocumentParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizSmokeMachineIncomingGoodsDocumentController层
*
* <AUTHOR>
* @date 2025-07-04 21:29:58
*/
@RestController
@RequestMapping("v1/bizSmokeMachineIncomingGoodsDocument")
@Api(tags = "进货管理-进货单-证件信息接口")
public class BizSmokeMachineIncomingGoodsDocumentController{

    @Resource
    private BizSmokeMachineIncomingGoodsDocumentService bizSmokeMachineIncomingGoodsDocumentService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进货管理-进货单-证件信息数据
     * @param bizSmokeMachineIncomingGoodsDocumentParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进货管理-进货单-证件信息数据")
    @PostMapping("list")
    public ResultObject<List<BizSmokeMachineIncomingGoodsDocumentDto>> getListPaged(@RequestBody BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizSmokeMachineIncomingGoodsDocumentDto>> paged = bizSmokeMachineIncomingGoodsDocumentService.getListPaged(bizSmokeMachineIncomingGoodsDocumentParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizSmokeMachineIncomingGoodsDocumentParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizSmokeMachineIncomingGoodsDocumentDto> insert(@Valid @RequestBody BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        ResultObject<BizSmokeMachineIncomingGoodsDocumentDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizSmokeMachineIncomingGoodsDocumentDto bizSmokeMachineIncomingGoodsDocumentDto = bizSmokeMachineIncomingGoodsDocumentService.insert(bizSmokeMachineIncomingGoodsDocumentParam, userInfo);
        if (bizSmokeMachineIncomingGoodsDocumentDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsDocumentDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizSmokeMachineIncomingGoodsDocumentParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizSmokeMachineIncomingGoodsDocumentDto> update(@PathVariable String sid, @Valid @RequestBody BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        bizSmokeMachineIncomingGoodsDocumentParam.setId(sid);
        BizSmokeMachineIncomingGoodsDocumentDto bizSmokeMachineIncomingGoodsDocumentDto = bizSmokeMachineIncomingGoodsDocumentService.update(bizSmokeMachineIncomingGoodsDocumentParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizSmokeMachineIncomingGoodsDocumentDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsDocumentDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizSmokeMachineIncomingGoodsDocumentService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizSmokeMachineIncomingGoodsDocumentExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizSmokeMachineIncomingGoodsDocumentDto> bizSmokeMachineIncomingGoodsDocumentDtos = bizSmokeMachineIncomingGoodsDocumentService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizSmokeMachineIncomingGoodsDocumentDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizSmokeMachineIncomingGoodsDocumentDtos);
    }


    @PostMapping("/getDocumentByHeadId/{headId}")
    @ApiOperation("根据头id查询证件信息")
    public ResultObject<BizSmokeMachineIncomingGoodsDocumentDto> getDocumentByHeadId(@PathVariable String headId, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsDocumentService.getDocumentByHeadId(headId, userInfo);
    }


    @PostMapping("/updateAndInsert")
    @ApiOperation("判断数据是否存在，存在更新，不存在新增")
    public ResultObject updateAndInsert(@RequestBody @Valid BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsDocumentService.updateAndInsert(bizSmokeMachineIncomingGoodsDocumentParam, userInfo);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizSmokeMachineIncomingGoodsDocumentDto> list) {
        for(BizSmokeMachineIncomingGoodsDocumentDto item : list) {
        
        }
    }


}
