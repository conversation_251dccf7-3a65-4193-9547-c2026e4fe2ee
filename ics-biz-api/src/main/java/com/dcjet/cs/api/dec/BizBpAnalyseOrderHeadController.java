package com.dcjet.cs.api.dec;

import com.dcjet.cs.dec.service.BizBpAnalyseOrderHeadService;
import com.dcjet.cs.dec.service.BizSmokeMachineIncomingGoodsHeadService;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.util.CommonEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizBpAnalyseOrderHeadController层
*
* <AUTHOR>
* @date 2025-07-07 17:08:03
*/
@RestController
@RequestMapping("v1/bizBpAnalyseOrderHead")
@Api(tags = "（第7条线）出料加工进口薄片-分析单表头接口")
public class BizBpAnalyseOrderHeadController{

    @Resource
    private BizBpAnalyseOrderHeadService bizBpAnalyseOrderHeadService;

    @Resource
    private BizSmokeMachineIncomingGoodsHeadService bizSmokeMachineIncomingGoodsHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取（第7条线）出料加工进口薄片-分析单表头数据
     * @param bizBpAnalyseOrderHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取（第7条线）出料加工进口薄片-分析单表头数据")
    @PostMapping("list")
    public ResultObject<List<BizBpAnalyseOrderHeadDto>> getListPaged(@RequestBody BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizBpAnalyseOrderHeadDto>> paged = bizBpAnalyseOrderHeadService.getListPaged(bizBpAnalyseOrderHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizBpAnalyseOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizBpAnalyseOrderHeadDto> insert(@Valid @RequestBody BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
        ResultObject<BizBpAnalyseOrderHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizBpAnalyseOrderHeadDto bizBpAnalyseOrderHeadDto = bizBpAnalyseOrderHeadService.insert(bizBpAnalyseOrderHeadParam, userInfo);
        if (bizBpAnalyseOrderHeadDto != null) {
            resultObject.setData(bizBpAnalyseOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizBpAnalyseOrderHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizBpAnalyseOrderHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
        bizBpAnalyseOrderHeadParam.setId(sid);
        BizBpAnalyseOrderHeadDto bizBpAnalyseOrderHeadDto = bizBpAnalyseOrderHeadService.update(bizBpAnalyseOrderHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizBpAnalyseOrderHeadDto != null) {
            resultObject.setData(bizBpAnalyseOrderHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizBpAnalyseOrderHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizBpAnalyseOrderHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizBpAnalyseOrderHeadDto> bizBpAnalyseOrderHeadDtos = bizBpAnalyseOrderHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizBpAnalyseOrderHeadDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizBpAnalyseOrderHeadDtos);
    }




    @ApiOperation("校验分析单号是否存在")
    @PostMapping("/checkAnalyseOrderCode")
    public ResultObject checkAnalyseOrderCode(@RequestBody BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
        return bizBpAnalyseOrderHeadService.checkAnalyseOrderCode(bizBpAnalyseOrderHeadParam, userInfo);
    }


    @ApiOperation("获取常用下拉列表")
    @PostMapping("/getCommonKeyValueList")
    public ResultObject getCommonKeyValueList(@RequestBody BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
        return bizBpAnalyseOrderHeadService.getCommonKeyValueList(bizBpAnalyseOrderHeadParam, userInfo);
    }



    @ApiOperation("获取通用的当前用户信息数据")
    @PostMapping("/getCommonSearchList")
    public ResultObject getCommonSearchList(@RequestBody BizBpAnalyseOrderHeadParam params, UserInfoToken userInfo) {
        return bizBpAnalyseOrderHeadService.getCommonSearchList(params,userInfo);
    }


    @ApiOperation("确认")
    @PostMapping("/confirm/{id}")
    public ResultObject confirm(@PathVariable String id, UserInfoToken userInfo) {
        return bizBpAnalyseOrderHeadService.confirm(id, userInfo);
    }


    @ApiOperation("获取提取外商合同列表数据")
    @PostMapping("/getContractNoList")
    public ResultObject getContractNoList(@RequestBody BizBpAnalyseOrderHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        return bizBpAnalyseOrderHeadService.getContractNoList(param, pageParam, userInfo);
    }



    @ApiOperation("提取外商合同")
    @PostMapping("/extractContract")
    public ResultObject extractContract(@RequestBody BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) {
        return bizBpAnalyseOrderHeadService.extractContract(param, userInfo);
    }

                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizBpAnalyseOrderHeadDto> list, UserInfoToken userInfo) {
        // 获取全部客户/港口数据 进行转换
        ResultObject commonSearchList = bizBpAnalyseOrderHeadService.getCommonSearchList(new BizBpAnalyseOrderHeadParam(), userInfo);
        BizSmokeMachineIncomingGoodsHeadCommonDto data = (BizSmokeMachineIncomingGoodsHeadCommonDto) commonSearchList.getData();
        for(BizBpAnalyseOrderHeadDto item : list) {


            // 转换客户信息
            if(StringUtils.isNotBlank(item.getCustomerCode())){
                List<Map<String, String>> customerList = data.getCustomerList();
                for(Map<String, String> customer : customerList){
                    if(customer.get("value").equals(item.getCustomerCode())){
                        item.setCustomerCode(item.getCustomerCode() + " " +  customer.get("label"));
                    }
                }
            }

            // 转换单据状态
            if(StringUtils.isNotBlank(item.getDataState())){
                item.setDataState(item.getDataState() +  " " + CommonEnum.STATE_ENUM.getValue(item.getDataState()));
            }


            // 转换币种信息
            if (StringUtils.isNotBlank(item.getCurr())){
            }

            // 转换客户信息
            if(StringUtils.isNotBlank(item.getCurr())){
                List<Map<String, String>> customerList = data.getCurrList();
                for(Map<String, String> customer : customerList){
                    if(customer.get("value").equals(item.getCurr())){
                        item.setCurr(item.getCurr() + " " +  customer.get("label"));
                    }
                }
            }
        }
    }

    /**
     * 打印分析单
     * @param param
     * @param userInfo
     * @return
     * @throws Exception
     */
    @PostMapping("printAnalysis")
    public ResponseEntity printAnalysis(@RequestBody BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("分析单") + ".xlsx";
        String exportFileName = bizBpAnalyseOrderHeadService.printAnalysis(param, userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    /**
     * 打印装运通知单
     * @param param
     * @param userInfo
     * @return
     * @throws Exception
     */
    @PostMapping("printShipment")
    public ResponseEntity printShipment(@RequestBody BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("装运通知") + ".xlsx";
        String exportFileName = bizBpAnalyseOrderHeadService.printShipment(param, userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    /**
     * 打印装船电
     * @param param
     * @param userInfo
     * @return
     * @throws Exception
     */
    @PostMapping("printShipping")
    public ResponseEntity printShipping(@RequestBody BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("装船电") + ".xlsx";
        String exportFileName = bizBpAnalyseOrderHeadService.printShipping(param, userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    /**
     * 打印装箱码单
     * @param param
     * @param userInfo
     * @return
     * @throws Exception
     */
    @PostMapping("printZxmd")
    public ResponseEntity printZxmd(@RequestBody BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("装箱码单") + ".xlsx";
        String exportFileName = bizBpAnalyseOrderHeadService.printZxmd(param, userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
}
