package com.dcjet.cs.api.equipment;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListDto;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListExportParam;
import com.dcjet.cs.equipment.service.BizIEquipmentPlanListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@RestController
@RequestMapping("v1/bizIEquipmentPlanList")
@Api(tags = "接口")
public class BizIEquipmentPlanListController extends BaseController {
    @Resource
    private BizIEquipmentPlanListService bizIEquipmentPlanListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIEquipmentPlanListParam
     * @param pageParam
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIEquipmentPlanListDto>> getListPaged(@RequestBody BizIEquipmentPlanListParam bizIEquipmentPlanListParam, PageParam pageParam) {
        ResultObject<List<BizIEquipmentPlanListDto>> paged = bizIEquipmentPlanListService.selectAllPaged(bizIEquipmentPlanListParam, pageParam);
        return paged;
    }
    /**
     * @param bizIEquipmentPlanListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIEquipmentPlanListDto> insert(@Valid @RequestBody BizIEquipmentPlanListParam bizIEquipmentPlanListParam, UserInfoToken userInfo) {
		ResultObject<BizIEquipmentPlanListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIEquipmentPlanListDto bizIEquipmentPlanListDto = bizIEquipmentPlanListService.insert(bizIEquipmentPlanListParam, userInfo);
        if (bizIEquipmentPlanListDto != null) {
            resultObject.setData(bizIEquipmentPlanListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIEquipmentPlanListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIEquipmentPlanListDto> update(@PathVariable String sid, @Valid @RequestBody BizIEquipmentPlanListParam bizIEquipmentPlanListParam, UserInfoToken userInfo) {
        bizIEquipmentPlanListParam.setId(sid);
        BizIEquipmentPlanListDto bizIEquipmentPlanListDto = bizIEquipmentPlanListService.update(bizIEquipmentPlanListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentPlanListDto != null) {
            resultObject.setData(bizIEquipmentPlanListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
		ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        bizIEquipmentPlanListService.delete(sids, userInfo);
        return resultObject;
    }
}
