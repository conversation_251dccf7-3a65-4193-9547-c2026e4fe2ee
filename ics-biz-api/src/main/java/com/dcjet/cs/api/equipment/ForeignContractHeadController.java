package com.dcjet.cs.api.equipment;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.equipment.ForeignContractAddParam;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.dto.equipment.ForeignContractOptionsDto;
import com.dcjet.cs.equipment.service.ForeignContractHeadService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/v1/equipment/foreignContract/head")
@Api(tags = "国营贸易进口烟机设备-外商合同表头")
public class ForeignContractHeadController {
    @Resource
    private ForeignContractHeadService foreignContractHeadService;
    @Resource
    private ExcelService excelService;

    /**
     * 获取美元汇率
     *
     * @param curr     币种
     * @param userInfo 用户信息
     * @return 美元汇率响应数据
     */
    @ApiOperation("获取美元汇率")
    @GetMapping("/dollarRate/{curr}")
    public ResultObject<?> getDollarRate(@PathVariable String curr, UserInfoToken<?> userInfo) {
        BigDecimal dollarRate = this.foreignContractHeadService.getDollarRate(curr, userInfo);
        return ResultObject.createInstance(true, "查询成功", dollarRate);
    }

    /**
     * 获取分页列表
     *
     * @param headParam 外商合同表头参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 外商合同表头分页列表
     */
    @ApiOperation("获取外商合同表头分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication(paths = {"/tobacco/equipment/foreignContract"})
    public ResultObject<List<ForeignContractHeadDto>> getListPaged(@RequestBody ForeignContractHeadParam headParam,
                                                                   PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.foreignContractHeadService.getListPaged(headParam, pageParam, userInfo);
    }

    /**
     * 获取选项数据
     *
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @GetMapping("/options")
    public ResultObject<ForeignContractOptionsDto> getOptionsData(UserInfoToken<?> userInfo) {
        ForeignContractOptionsDto allOptions = this.foreignContractHeadService.getAllOptions(userInfo);
        return ResultObject.createInstance(true, "查询成功", allOptions);
    }

    /**
     * 导出外商合同表头列表
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 响应实体
     */
    @ApiOperation("导出外商合同表头列表")
    @PostMapping("/export")
    public ResponseEntity<?> export(@RequestBody BasicExportParam<ForeignContractHeadParam> exportParam, UserInfoToken<?> userInfo) {
        List<ForeignContractHeadDto> dtoList = this.foreignContractHeadService.getAllList(exportParam.getExportColumns(), userInfo);
        try {
            return this.excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8)
                    , exportParam.getHeader(), dtoList);
        } catch (Exception e) {
            throw new ErrorException(500, "导出失败!");
        }
    }


    /**
     * 新增外商合同
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("新增外商合同")
    @PostMapping
    public ResultObject<?> insert(@Valid @RequestBody ForeignContractAddParam addParam, UserInfoToken<?> userInfo) {
        ForeignContractHeadDto headDto = this.foreignContractHeadService.insert(addParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, headDto);
    }

    /**
     * 更改外商合同
     *
     * @param headParam 表头参数
     * @param userInfo  用户信息
     * @return 响应数据
     */
    @ApiOperation("更改外商合同")
    @PutMapping("/{id}")
    public ResultObject<?> update(@PathVariable String id, @Valid @RequestBody ForeignContractHeadParam headParam,
                                  UserInfoToken<?> userInfo) {
        headParam.setId(id);
        ForeignContractHeadDto headDto = this.foreignContractHeadService.update(headParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, headDto);
    }

    /**
     * 删除外商合同
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除外商合同")
    @DeleteMapping("/{ids}")
    public ResultObject<?> delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        this.foreignContractHeadService.delete(ids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }

    /**
     * 确认外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("确认外商合同")
    @PutMapping("/confirm/{id}")
    public ResultObject<?> confirm(@PathVariable String id, UserInfoToken<?> userInfo) {
        ForeignContractHeadDto foreignContractHeadDto = this.foreignContractHeadService.confirm(id, userInfo);
        return ResultObject.createInstance(true, "确认成功", foreignContractHeadDto);
    }

    /**
     * 作废外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("作废外商合同")
    @PutMapping("/invalidate/{id}")
    public ResultObject<?> invalidate(@PathVariable String id, UserInfoToken<?> userInfo) {
        this.foreignContractHeadService.invalidate(id, userInfo);
        return ResultObject.createInstance(true, "作废成功");
    }

    /**
     * 校验外商合同版本复制
     *
     * @param contractNo 合同编号
     * @param userInfo   用户信息
     * @return 响应数据，data为1则表示存在有效数据
     */
    @ApiOperation("校验外商合同版本复制")
    @GetMapping("/checkVersionCopy/{contractNo}")
    public ResultObject<?> checkVersionCopy(@PathVariable String contractNo, UserInfoToken<?> userInfo) {
        String hasValid = this.foreignContractHeadService.checkVersionCopy(contractNo, userInfo);
        return ResultObject.createInstance(true, "校验成功", hasValid);
    }

    /**
     * 外商合同版本复制
     *
     * @param headParam 外商合同表头参数
     * @param userInfo  用户信息
     * @return 响应数据
     */
    @ApiOperation("外商合同版本复制")
    @PostMapping("/versionCopy")
    public ResultObject<?> versionCopy(@RequestBody ForeignContractHeadParam headParam, UserInfoToken<?> userInfo) {
        this.foreignContractHeadService.versionCopy(headParam, userInfo);
        return ResultObject.createInstance(true, "版本复制成功");
    }

    /**
     * 获取分页列表
     *
     * @param foreignContractHeadParam 外商合同表头参数
     * @param pageParam                分页参数
     * @param userInfo                 用户信息
     * @return 外商合同表头分页列表
     */
    @ApiOperation("获取外商合同表头分页列表")
    @PostMapping("/listForPlan")
    public ResultObject<List<ForeignContractHeadDto>> listForPlan(@RequestBody ForeignContractHeadParam foreignContractHeadParam,
                                                                  PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.foreignContractHeadService.getContractForPlan(foreignContractHeadParam, pageParam, userInfo);
    }

    /**
     * 查询待审核列表
     *
     * @param headParam 表头参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 数据列表
     */
    @ApiOperation("查询待审核列表")
    @PostMapping("/aeoList")
    public ResultObject<List<ForeignContractHeadDto>> getAeoListPaged(@RequestBody ForeignContractHeadParam headParam,
                                                                      PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.foreignContractHeadService.getAeoListPaged(headParam, pageParam, userInfo);
    }
}