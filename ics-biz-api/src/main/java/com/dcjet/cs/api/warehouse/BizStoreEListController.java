package com.dcjet.cs.api.warehouse;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.warehouse.BizStoreEListDto;
import com.dcjet.cs.dto.warehouse.BizStoreEListParam;
import com.dcjet.cs.dto.warehouse.BizStoreEListExportParam;
import com.dcjet.cs.warehouse.service.BizStoreEListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@RestController
@RequestMapping("v1/bizStoreEList")
@Api(tags = "接口")
public class BizStoreEListController extends BaseController {
    @Resource
    private BizStoreEListService bizStoreEListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizStoreEListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizStoreEListDto>> getListPaged(@RequestBody BizStoreEListParam bizStoreEListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizStoreEListDto>> paged = bizStoreEListService.getListPaged(bizStoreEListParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizStoreEListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizStoreEListDto> insert(@Valid @RequestBody BizStoreEListParam bizStoreEListParam, UserInfoToken userInfo) {
        ResultObject<BizStoreEListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizStoreEListDto bizStoreEListDto = bizStoreEListService.insert(bizStoreEListParam, userInfo);
        if (bizStoreEListDto != null) {
            resultObject.setData(bizStoreEListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizStoreEListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizStoreEListDto> update(@PathVariable String sid, @Valid @RequestBody BizStoreEListParam bizStoreEListParam, UserInfoToken userInfo) {
        bizStoreEListParam.setSid(sid);
        BizStoreEListDto bizStoreEListDto = bizStoreEListService.update(bizStoreEListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizStoreEListDto != null) {
            resultObject.setData(bizStoreEListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizStoreEListService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizStoreEListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizStoreEListDto> bizStoreEListDtos = bizStoreEListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizStoreEListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizStoreEListDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizStoreEListDto> list) {
        for(BizStoreEListDto item : list) {
        }
    }
    @PostMapping("/getListBySid/{sid}")
    public ResultObject getListBySid(@PathVariable String sid, UserInfoToken userInfo) {
        return bizStoreEListService.getListBySid(sid,userInfo);
    }
}
