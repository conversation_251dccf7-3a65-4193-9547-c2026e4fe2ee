package com.dcjet.cs.api.customerAccount;

import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.customerAccount.service.BizCustomerAccountCTobaccoService;
import com.dcjet.cs.customerAccount.service.BizCustomerAccountTobacooService;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountCTobaccoDto;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountCTobaccoExportParam;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountCTobaccoParam;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-6-16
 */
@RestController
@RequestMapping("v1/customerAccountCTobacco")
@Api(tags = "接口")
public class BizCustomerAccountCTobaccoController extends BaseController {
    @Resource
    private BizCustomerAccountCTobaccoService bizCustomerAccountCTobaccoService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BizCustomerAccountTobacooService bizCustomerAccountTobacooService;
    /**
     * @param BizCustomerAccountCTobaccoParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizCustomerAccountCTobaccoDto>> getListPaged(@RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizCustomerAccountCTobaccoDto>> paged = bizCustomerAccountCTobaccoService.getListPaged(BizCustomerAccountCTobaccoParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param BizCustomerAccountCTobaccoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizCustomerAccountCTobaccoDto> insert(@Valid @RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        ResultObject<BizCustomerAccountCTobaccoDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizCustomerAccountCTobaccoDto BizCustomerAccountCTobaccoDto = bizCustomerAccountCTobaccoService.insert(BizCustomerAccountCTobaccoParam, userInfo);
        if (BizCustomerAccountCTobaccoDto != null) {
            resultObject.setData(BizCustomerAccountCTobaccoDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param BizCustomerAccountCTobaccoParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizCustomerAccountCTobaccoDto> update(@PathVariable String sid, @Valid @RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        BizCustomerAccountCTobaccoParam.setSid(sid);
        BizCustomerAccountCTobaccoDto BizCustomerAccountCTobaccoDto = bizCustomerAccountCTobaccoService.update(BizCustomerAccountCTobaccoParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (BizCustomerAccountCTobaccoDto != null) {
            resultObject.setData(BizCustomerAccountCTobaccoDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizCustomerAccountCTobaccoService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizCustomerAccountCTobaccoExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizCustomerAccountCTobaccoDto> BizCustomerAccountCTobaccoDtos = bizCustomerAccountCTobaccoService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(BizCustomerAccountCTobaccoDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), BizCustomerAccountCTobaccoDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizCustomerAccountCTobaccoDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);

        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);
        for(BizCustomerAccountCTobaccoDto item : list) {
            item.setBusinessType(item.getBusinessType() + " " + CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(item.getBusinessType()));
            String customerNameCn = getMerchantNameSafely(bizMerchantMap, item.getCustomer());
            item.setCustomer(item.getCustomer() + " " + customerNameCn);
            item.setCurr(pCodeHolder.getValue(PCodeType.CURR, item.getCurr()));
            if(StringUtils.isNotBlank(item.getSendFinance())){
                if("0".equals(item.getSendFinance())){
                    item.setSendFinance("0 是");
                }
                if("1".equals(item.getSendFinance())){
                    item.setSendFinance("1 否");
                }
            }
            item.setStatus(CommonEnum.OrderStatusEnum.getValue(item.getStatus()));
        }
    }
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.sendApproval(sid, userInfo);
    }
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.confirmStatus(BizCustomerAccountCTobaccoParam, userInfo);
    }
    @ApiOperation("退单接口")
    @PostMapping("back/{sid}")
    public ResultObject back(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.back(sid, userInfo);
    }
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.invalidate(sid, userInfo);
    }
    @ApiOperation("红冲接口")
    @PostMapping("redFlush/{sid}")
    public ResultObject redFlush(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.redFlush(sid, userInfo);
    }

    @ApiOperation("校验是否存在 同一个订单号是否存在未作废的数据")
    @PostMapping("/checkIdNotCancel")
    public ResultObject checkPlanIdNotCancel(@RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.checkIdNotCancel(BizCustomerAccountCTobaccoParam,userInfo);
    }
    @ApiOperation("版本复制")
    @PostMapping("/copyVersion")
    public ResultObject copyVersion(@RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.copyVersion(BizCustomerAccountCTobaccoParam,userInfo);
    }

    @ApiOperation("打印入库回单")
    @PostMapping("/print")
    public ResponseEntity print(@RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) throws Exception {
        return bizCustomerAccountCTobaccoService.print(BizCustomerAccountCTobaccoParam, userInfo);
    }
    @ApiOperation("从合同新增")
    @PostMapping("insertByShipping")
    public ResultObject insertByShipping(@RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.insertByShipping(BizCustomerAccountCTobaccoParam, userInfo);
    }
    @ApiOperation("从进货明细新增")
    @PostMapping("insertByContract")
    public ResultObject insertByContract(@RequestBody BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.insertByContract(BizCustomerAccountCTobaccoParam, userInfo);
    }
    @ApiOperation("获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(BizCustomerAccountCTobaccoParam params, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.getSupplierList(params,userInfo);
    }
    @ApiOperation("获取制单人列表信息")
    @PostMapping("/getCreateUserList")
    public ResultObject getCreateUserList(BizCustomerAccountCTobaccoParam params, UserInfoToken userInfo) {
        return bizCustomerAccountCTobaccoService.getCreateUserList(params,userInfo);
    }
    @ApiOperation("分页查询接口")
    @PostMapping("/getForeignContractHeadList")
    public ResultObject<List<ForeignContractHeadDto>> getForeignContractHeadList(@RequestBody ForeignContractHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadDto>> paged = bizCustomerAccountCTobaccoService.getForeignContractHeadList(param, pageParam,userInfo);
        return paged;
    }
    @ApiOperation("分页获取（3）烟机设备-进货单-表头数据数据")
    @PostMapping("/getSmokeMachineIncomingGoodsHeadList")
    public ResultObject<List<BizSmokeMachineIncomingGoodsHeadDto>> getSmokeMachineIncomingGoodsHeadList(@RequestBody BizSmokeMachineIncomingGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizSmokeMachineIncomingGoodsHeadDto>> paged = bizCustomerAccountCTobaccoService.getSmokeMachineIncomingGoodsHeadList(param, pageParam,userInfo);
        return paged;
    }

}
