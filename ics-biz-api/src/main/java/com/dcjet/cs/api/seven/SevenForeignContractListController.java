package com.dcjet.cs.api.seven;

import com.dcjet.cs.dto.seven.*;
import com.dcjet.cs.seven.service.SevenForeignContractListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/seven/foreignContract/body")
@Api(tags = "第七条线出口加工进口薄片-外商合同表体")
public class SevenForeignContractListController {
    @Resource
    private SevenForeignContractListService sevenForeignContractListService;

    /**
     * 获取新增表体物料信息
     *
     * @param userInfo 用户信息
     * @return 物料信息
     */
    @ApiOperation("获取新增表体物料信息")
    @GetMapping("/material")
    public ResultObject<List<SevenForeignContractAddBodyMaterialDto>> getMaterialInfo(UserInfoToken<?> userInfo) {
        List<SevenForeignContractAddBodyMaterialDto> materialInfoList = this.sevenForeignContractListService.getMaterialInfoForAddBody(userInfo);
        return ResultObject.createInstance(true, "查询成功", materialInfoList);
    }

    /**
     * 获取分页列表
     *
     * @param param     外商合同表体参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 外商合同表体分页列表
     */
    @ApiOperation("获取外商合同表体分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication("/tobacco/seven/foreignContract")
    public ResultObject<List<SevenForeignContractListDto>> getListPaged(@RequestBody SevenForeignContractListParam param,
                                                                        PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.sevenForeignContractListService.getListPaged(param, pageParam, userInfo);
    }

    /**
     * 获取表体汇总数据
     *
     * @param param    外商合同表体参数
     * @param userInfo 用户信息
     * @return 响应数据
     */
    @ApiOperation("获取表体汇总数据")
    @PostMapping("/summary")
    public ResultObject<SevenForeignContractListSummaryDto> getListSummary(@RequestBody SevenForeignContractListParam param,
                                                                           UserInfoToken<?> userInfo) {
        SevenForeignContractListSummaryDto listSummaryDto = this.sevenForeignContractListService
                .getListSummary(param, userInfo);
        return ResultObject.createInstance(true, "查询成功", listSummaryDto);
    }

    /**
     * 新增表体
     *
     * @param addBodyParam 新增表体参数
     * @return 响应数据
     */
    @ApiOperation("新增表体")
    @PostMapping
    public ResultObject<?> addBody(@RequestBody SevenForeignContractAddBodyParam addBodyParam, UserInfoToken<?> userInfo) {
        this.sevenForeignContractListService.addBody(addBodyParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
    }

    /**
     * 修改外商合同表体
     *
     * @param id       主键
     * @param param    外商合同表体参数
     * @param userInfo 用户信息
     * @return 外商合同表体传输模型
     */
    @ApiOperation("修改外商合同表体")
    @PutMapping("/{id}")
    public ResultObject<?> update(@PathVariable String id, @RequestBody @Valid SevenForeignContractListParam param,
                                  UserInfoToken<?> userInfo) {
        param.setId(id);
        SevenForeignContractListDto foreignContractListDto = this.sevenForeignContractListService.updateBody(param, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, foreignContractListDto);
    }

    /**
     * 删除外商合同表体
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除外商合同表体")
    @DeleteMapping("/{ids}")
    public ResultObject<?> delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        this.sevenForeignContractListService.delete(ids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }
}