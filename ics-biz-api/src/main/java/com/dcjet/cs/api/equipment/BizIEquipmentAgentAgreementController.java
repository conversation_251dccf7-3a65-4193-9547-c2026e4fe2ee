package com.dcjet.cs.api.equipment;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementExportParam;
import com.dcjet.cs.dto.iEBusiness.ForContractListDto;
import com.dcjet.cs.equipment.service.BizIEquipmentAgentAgreementService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@RestController
@RequestMapping("v1/bizIEquipmentAgentAgreement")
@Api(tags = "接口")
public class BizIEquipmentAgentAgreementController extends BaseController {
    @Resource
    private BizIEquipmentAgentAgreementService bizIEquipmentAgentAgreementService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BizMerchantService bizMerchantService;
    /**
     * @param bizIEquipmentAgentAgreementParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/equipment/bizIEquipmentAgentAgreementList")
    public ResultObject<List<BizIEquipmentAgentAgreementDto>> getListPaged(@RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, PageParam pageParam, UserInfoToken userInfo) {
        bizIEquipmentAgentAgreementParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizIEquipmentAgentAgreementDto>> paged = bizIEquipmentAgentAgreementService.getListPaged(bizIEquipmentAgentAgreementParam, pageParam);
        return paged;
    }

    @ApiOperation("待审核页面查询接口")
    @PostMapping("aeoList")
    public ResultObject<List<BizIEquipmentAgentAgreementDto>> getAeoListPaged(@RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIEquipmentAgentAgreementDto>> paged = bizIEquipmentAgentAgreementService.getAeoListPaged(bizIEquipmentAgentAgreementParam, pageParam, userInfo);
        return paged;
    }

    /**
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIEquipmentAgentAgreementDto> insert(@RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        ResultObject<BizIEquipmentAgentAgreementDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIEquipmentAgentAgreementDto bizIEquipmentAgentAgreementDto = bizIEquipmentAgentAgreementService.insert(bizIEquipmentAgentAgreementParam, userInfo);
        if (bizIEquipmentAgentAgreementDto != null) {
            resultObject.setData(bizIEquipmentAgentAgreementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIEquipmentAgentAgreementDto> update(@PathVariable String sid, @Valid @RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        bizIEquipmentAgentAgreementParam.setSid(sid);
        BizIEquipmentAgentAgreementDto bizIEquipmentAgentAgreementDto = bizIEquipmentAgentAgreementService.update(bizIEquipmentAgentAgreementParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentAgentAgreementDto != null) {
            resultObject.setData(bizIEquipmentAgentAgreementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIEquipmentAgentAgreementService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIEquipmentAgentAgreementExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIEquipmentAgentAgreementDto> bizIEquipmentAgentAgreementDtos = bizIEquipmentAgentAgreementService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIEquipmentAgentAgreementDtos, userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIEquipmentAgentAgreementDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIEquipmentAgentAgreementDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        final Map<String, String> merchantMap = bizMerchantService.createMerchantMap(bizMerchants);
        for(BizIEquipmentAgentAgreementDto item : list) {
            item.setCustomer(merchantMap.get(item.getCustomer()));
            item.setSupplier(merchantMap.get(item.getSupplier()));
            item.setBillStatus(CommonEnum.OrderStatusEnum.getValue(item.getBillStatus()));
        }
    }

    /**
     * 确认接口
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("确认接口")
    @PostMapping("confirm")
    public ResultObject confirmDataStatus(@RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIEquipmentAgentAgreementService.confirmDataStatus(bizIEquipmentAgentAgreementParam, userInfo);
        return resultObject;
    }

    @PostMapping("forContractList")
    public ResultObject<List<ForContractListDto>> getForContractList(@RequestBody BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) {
        List<ForContractListDto> contractList = bizIEquipmentAgentAgreementService.getForContractList(param, userInfo);
        return ResultObject.createInstance(true, "查询成功", contractList);
    }

    @PostMapping("getDataByType")
    public ResultObject<BizIEquipmentAgentAgreementDto> getDataByType(@RequestBody BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreementDto data = bizIEquipmentAgentAgreementService.getDataByType(param, userInfo);
        return ResultObject.createInstance(true, "查询成功", data);
    }

    /**
     * 打印会签单
     * @param param
     * @param userInfo
     * @return
     * @throws Exception
     */
    @PostMapping("printSign")
    public ResponseEntity printSign(@RequestBody BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("代理协议会签单") + ".xlsx";
        String exportFileName = bizIEquipmentAgentAgreementService.printSign(param,userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    @PostMapping("print")
    public ResponseEntity print(@RequestBody BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("代理协议书") + ".xlsx";
        String exportFileName = bizIEquipmentAgentAgreementService.print(param,userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
}
