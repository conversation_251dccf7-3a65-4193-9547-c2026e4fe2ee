package com.dcjet.cs.api.purchaseOrder;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderListDto;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderListParam;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderListExportParam;
import com.dcjet.cs.purchaseOrder.service.BizPurchaseOrderListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-14
 */
@RestController
@RequestMapping("v1/bizPurchaseOrderList")
@Api(tags = "接口")
public class BizPurchaseOrderListController extends BaseController {
    @Resource
    private BizPurchaseOrderListService bizPurchaseOrderListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizPurchaseOrderListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizPurchaseOrderListDto>> getListPaged(@RequestBody BizPurchaseOrderListParam bizPurchaseOrderListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizPurchaseOrderListDto>> paged = bizPurchaseOrderListService.getListPaged(bizPurchaseOrderListParam, pageParam);
        return paged;
    }
    /**
     * @param bizPurchaseOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizPurchaseOrderListDto> insert(@Valid @RequestBody BizPurchaseOrderListParam bizPurchaseOrderListParam, UserInfoToken userInfo) {
        ResultObject<BizPurchaseOrderListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizPurchaseOrderListDto bizPurchaseOrderListDto = bizPurchaseOrderListService.insert(bizPurchaseOrderListParam, userInfo);
        if (bizPurchaseOrderListDto != null) {
            resultObject.setData(bizPurchaseOrderListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizPurchaseOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizPurchaseOrderListDto> update(@PathVariable String sid, @Valid @RequestBody BizPurchaseOrderListParam bizPurchaseOrderListParam, UserInfoToken userInfo) {
        bizPurchaseOrderListParam.setSid(sid);
        BizPurchaseOrderListDto bizPurchaseOrderListDto = bizPurchaseOrderListService.update(bizPurchaseOrderListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizPurchaseOrderListDto != null) {
            resultObject.setData(bizPurchaseOrderListDto);
            resultObject.setCode(200);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizPurchaseOrderListService.delete(sids, userInfo);
        return resultObject;
    }
}
