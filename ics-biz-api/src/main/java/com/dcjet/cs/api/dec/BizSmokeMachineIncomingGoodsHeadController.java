package com.dcjet.cs.api.dec;


import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsListMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList;
import com.dcjet.cs.dec.service.BizSmokeMachineIncomingGoodsHeadService;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizSmokeMachineIncomingGoodsHeadController层
*
* <AUTHOR>
* @date 2025-06-30 13:51:46
*/
@RestController
@RequestMapping("v1/bizSmokeMachineIncomingGoodsHead")
@Api(tags = "（3）烟机设备-进货单-表头数据接口")
public class BizSmokeMachineIncomingGoodsHeadController{

    @Resource
    private BizSmokeMachineIncomingGoodsHeadService bizSmokeMachineIncomingGoodsHeadService;
    @Resource
    private BizSmokeMachineIncomingGoodsHeadMapper bizSmokeMachineIncomingGoodsHeadMapper;
    @Resource
    private BizSmokeMachineIncomingGoodsListMapper bizSmokeMachineIncomingGoodsListMapper;
    @Resource
    private ExportService exportService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取（3）烟机设备-进货单-表头数据数据
     * @param bizSmokeMachineIncomingGoodsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取（3）烟机设备-进货单-表头数据数据")
    @PostMapping("list")
    public ResultObject<List<BizSmokeMachineIncomingGoodsHeadDto>> getListPaged(@RequestBody BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizSmokeMachineIncomingGoodsHeadDto>> paged = bizSmokeMachineIncomingGoodsHeadService.getListPaged(bizSmokeMachineIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizSmokeMachineIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizSmokeMachineIncomingGoodsHeadDto> insert(@Valid @RequestBody BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam, UserInfoToken userInfo) {
        ResultObject<BizSmokeMachineIncomingGoodsHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizSmokeMachineIncomingGoodsHeadDto bizSmokeMachineIncomingGoodsHeadDto = bizSmokeMachineIncomingGoodsHeadService.insert(bizSmokeMachineIncomingGoodsHeadParam, userInfo);
        if (bizSmokeMachineIncomingGoodsHeadDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizSmokeMachineIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/update/{sid}")
    public ResultObject<BizSmokeMachineIncomingGoodsHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam, UserInfoToken userInfo) {
        bizSmokeMachineIncomingGoodsHeadParam.setId(sid);
        BizSmokeMachineIncomingGoodsHeadDto bizSmokeMachineIncomingGoodsHeadDto = bizSmokeMachineIncomingGoodsHeadService.update(bizSmokeMachineIncomingGoodsHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizSmokeMachineIncomingGoodsHeadDto != null) {
            resultObject.setData(bizSmokeMachineIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/delete/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizSmokeMachineIncomingGoodsHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizSmokeMachineIncomingGoodsHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizSmokeMachineIncomingGoodsHeadDto> bizSmokeMachineIncomingGoodsHeadDtos = bizSmokeMachineIncomingGoodsHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizSmokeMachineIncomingGoodsHeadDtos,userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizSmokeMachineIncomingGoodsHeadDtos);
    }



    @ApiOperation("进货单-获取供应商信息")
    @PostMapping("/getCustomerList")
    public ResultObject getCurrList(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.getCustomerList(params,userInfo);
    }



    @ApiOperation("进货单-获取港口信息")
    @PostMapping("/getPortList")
    public ResultObject getPortList(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.getPortList(params,userInfo);
    }



    @ApiOperation("获取通用的当前用户信息数据")
    @PostMapping("/getCommonSearchList")
    public ResultObject getCommonSearchList(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.getCommonSearchList(params,userInfo);
    }



    @ApiOperation("根据ID获取进货单信息")
    @PostMapping("/getIncomingGoodsHeadById")
    public ResultObject getIncomingGoodsHeadById(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.getIncomingGoodsHeadById(params,userInfo);
    }



    @ApiOperation("确认数据-将进货单数据流转投保单")
    @PostMapping("/confirmIncomingGoodsHead")
    public ResultObject confirmIncomingGoodsHead(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.confirmIncomingGoodsHead(params,userInfo);
    }



    @ApiOperation("第3条线 -> 烟机设备 -> 获取可提取的外商合同")
    @PostMapping("/getExtractContractInfo")
    public ResultObject<List<ForeignContractHeadDto>> getExtractContractInfo(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.getExtractContractInfo(params,userInfo);
    }


    @ApiOperation("第3条线 -> 烟机设备 -> 获取可提取的外商合同")
    @PostMapping("/extractContract")
    public ResultObject extractContract(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.extractContract(params,userInfo);
    }



    @ApiOperation("第3条线 -> 烟机设备 -> 作废数据")
    @PostMapping("/cancel")
    public ResultObject cancel(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.cancel(params,userInfo);
    }


    @PostMapping("/sendAudit")
    @ApiOperation("第3条线 -> 烟机设备 -> 进货单发送您内审")
    public ResultObject sendAudit(@RequestBody BizSmokeMachineIncomingGoodsHeadParam param, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.sendAudit(param, userInfo);
    }





    @PostMapping("audit")
    @ApiOperation("第3条线 -> 烟机设备 -> 内审通过")
    public ResultObject audit(@RequestBody BizSmokeMachineIncomingGoodsHeadParam param, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.audit(param, userInfo);
    }




    @ApiOperation("第3条线 -> 烟机设备 -> 内审退回")
    @PostMapping("reject")
    public ResultObject reject(@RequestBody BizSmokeMachineIncomingGoodsHeadParam param, UserInfoToken userInfo) {
         return bizSmokeMachineIncomingGoodsHeadService.reject(param, userInfo);
    }



    @ApiOperation("第3条线 -> 烟机设备 -> 查询内审记录")
    @PostMapping("aeoList")
    public ResultObject<List<BizIContractHeadDto>> getAeoListPaged(@RequestBody BizSmokeMachineIncomingGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        param.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizIContractHeadDto>> paged = bizSmokeMachineIncomingGoodsHeadService.getAeoListPaged(param, pageParam, userInfo);
        return paged;
    }



    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizSmokeMachineIncomingGoodsHeadDto> list,UserInfoToken userInfo) {
        // 获取全部客户/港口数据 进行转换
        ResultObject commonSearchList = bizSmokeMachineIncomingGoodsHeadService.getCommonSearchList(new BizSmokeMachineIncomingGoodsHeadParam(), userInfo);
        BizSmokeMachineIncomingGoodsHeadCommonDto data = (BizSmokeMachineIncomingGoodsHeadCommonDto) commonSearchList.getData();
        for(BizSmokeMachineIncomingGoodsHeadDto item : list) {
            // 导出转换审核状态
            if(StringUtils.isNotBlank(item.getDataState())){
                item.setDataState(item.getDataState() +  " " + CommonEnum.STATE_ENUM.getValue(item.getDataState()));
            }
            // 内审状态
            if(StringUtils.isNotBlank(item.getApprovalStatus())){
                item.setApprovalStatus(item.getApprovalStatus() +  " " + CommonEnum.APPROVAL_STATUS.getValue(item.getApprovalStatus()));
            }

            // 转换客户信息
            if(StringUtils.isNotBlank(item.getCustomer())){
                List<Map<String, String>> customerList = data.getCustomerList();
                for(Map<String, String> customer : customerList){
                    if(customer.get("value").equals(item.getCustomer())){
                        item.setCustomer(item.getCustomer() + " " +  customer.get("label"));
                    }
                }
            }
            if (StringUtils.isNotBlank(item.getSupplier())){
                List<Map<String, String>> supplierList = data.getSupplierList();
                for(Map<String, String> supplier : supplierList){
                    if(supplier.get("value").equals(item.getSupplier())){
                        item.setSupplier(item.getSupplier() + " " + supplier.get("label"));
                    }
                }
            }
            if (StringUtils.isNotBlank(item.getDestination())){
                List<Map<String, String>> destinationList = data.getPortList();
                for(Map<String, String> destination : destinationList){
                    if(destination.get("value").equals(item.getDestination())){
                        item.setDestination(item.getDestination() + " " + destination.get("label"));
                    }
                }
            }
        }
    }



    @ApiOperation("打印接口")
    @PostMapping("print")
    public ResponseEntity print(@RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) throws Exception {
        String sid = params.getSid();
        BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(sid);
        List<BizSmokeMachineIncomingGoodsList> bizSmokeMachineIncomingGoodsLists = bizSmokeMachineIncomingGoodsListMapper.selectByHeadId(sid);
        convertForPrintForPrint(bizSmokeMachineIncomingGoodsHead,bizSmokeMachineIncomingGoodsLists,userInfo);

        String templateName = "biz_i_jh_order.xls";



        String outName = xdoi18n.XdoI18nUtil.t("进货单打印") + ".pdf";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        String exportFileName = exportService.export(Arrays.asList(bizSmokeMachineIncomingGoodsHead), bizSmokeMachineIncomingGoodsLists, fileName, templateName);
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        fileBytes = ExportService.excelToPdf(fileBytes);
        HttpHeaders h = new HttpHeaders();

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    private void convertForPrintForPrint(BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead, List<BizSmokeMachineIncomingGoodsList> bizSmokeMachineIncomingGoodsLists, UserInfoToken userInfo) {
        ResultObject commonSearchList = bizSmokeMachineIncomingGoodsHeadService.getCommonSearchList(new BizSmokeMachineIncomingGoodsHeadParam(), userInfo);
        BizSmokeMachineIncomingGoodsHeadCommonDto data = (BizSmokeMachineIncomingGoodsHeadCommonDto) commonSearchList.getData();
        //转换港口
        if (StringUtils.isNotBlank(bizSmokeMachineIncomingGoodsHead.getPortOfDeparture())){
            List<Map<String, String>> portList = data.getPortList();
            for(Map<String, String> port : portList){
                if(port.get("value").equals(bizSmokeMachineIncomingGoodsHead.getPortOfDeparture())){
                    bizSmokeMachineIncomingGoodsHead.setPortOfDeparture( port.get("label"));
                }
            }
        }
        if (StringUtils.isNotBlank(bizSmokeMachineIncomingGoodsHead.getDestination())){
            List<Map<String, String>> portList = data.getPortList();
            for(Map<String, String> port : portList){
                if(port.get("value").equals(bizSmokeMachineIncomingGoodsHead.getDestination())){
                    bizSmokeMachineIncomingGoodsHead.setDestination( port.get("label"));
                }
            }
        }

        if (bizSmokeMachineIncomingGoodsHead.getSailingDate() != null){
            // 转换日期格式为yyyy-MM-dd

            bizSmokeMachineIncomingGoodsHead.setSailingDateStr(DateUtils.dateToString(bizSmokeMachineIncomingGoodsHead.getSailingDate(), "yyyy-MM-dd"));
        }


        for (BizSmokeMachineIncomingGoodsList item : bizSmokeMachineIncomingGoodsLists) {
            // 商品名称换行 + 产品型号
            item.setGoodsInfo(item.getGoodsName() + "\n" + item.getProductModel());
        }
    }


    @PostMapping("/getSumTotal")
    @ApiOperation("第3条线 -> 烟机设备 -> 获取进货单表头金额汇总信息")
    public ResultObject getSumTotal(@RequestBody BizSmokeMachineIncomingGoodsHeadParam params,PageParam pageParam, UserInfoToken userInfo) {
        return bizSmokeMachineIncomingGoodsHeadService.getSumTotal(params,pageParam,userInfo);
    }


}
