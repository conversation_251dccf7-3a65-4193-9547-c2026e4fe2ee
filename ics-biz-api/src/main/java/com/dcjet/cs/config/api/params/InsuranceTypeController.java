package com.dcjet.cs.config.api.params;


import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.BizMaterialInformationKeyCode;
import com.dcjet.cs.dto.params.InsuranceTypeDto;
import com.dcjet.cs.dto.params.InsuranceTypeParam;
import com.dcjet.cs.params.service.InsuranceTypeService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("v1/insuranceType")
@Api(tags = "基础管理-企业基础信息接口")
public class InsuranceTypeController extends BaseController {
    @Resource
    private InsuranceTypeService insuranceTypeService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    @Resource
    private PCodeHolder pCodeServiceHolder;
    /**
     * @param insuranceTypeParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/params/insuranceType")
    public ResultObject<List<InsuranceTypeDto>> getListPaged(@RequestBody InsuranceTypeParam insuranceTypeParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<InsuranceTypeDto>> paged = insuranceTypeService.getListPaged(insuranceTypeParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param insuranceTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<InsuranceTypeDto> insert(@Valid @RequestBody InsuranceTypeParam insuranceTypeParam, UserInfoToken userInfo) {
        ResultObject<InsuranceTypeDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        InsuranceTypeDto insuranceTypeDto = insuranceTypeService.insert(insuranceTypeParam, userInfo);
        if (insuranceTypeDto != null) {
            resultObject.setData(insuranceTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param insuranceTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<InsuranceTypeDto> update(@PathVariable String sid, @Valid @RequestBody InsuranceTypeParam insuranceTypeParam, UserInfoToken userInfo) {
        insuranceTypeParam.setSid(sid);
        InsuranceTypeDto insuranceTypeDto = insuranceTypeService.update(insuranceTypeParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (insuranceTypeDto != null) {
            resultObject.setData(insuranceTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        insuranceTypeService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<InsuranceTypeParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<InsuranceTypeDto> insuranceTypeDtos = insuranceTypeService.selectAll(exportParam.getExportColumns(), userInfo);
        insuranceTypeDtos = convertForPrint(insuranceTypeDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), insuranceTypeDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<InsuranceTypeDto> convertForPrint(List<InsuranceTypeDto> list) {
        for (InsuranceTypeDto item : list) {


        }
        return list;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getNextCode")
    public ResultObject getNextCode(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = insuranceTypeService.getNextCode(userInfo);
        resultObject.setData(code);
        return resultObject;
    }


}
