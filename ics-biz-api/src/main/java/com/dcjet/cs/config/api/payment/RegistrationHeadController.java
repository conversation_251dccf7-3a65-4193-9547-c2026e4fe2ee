package com.dcjet.cs.config.api.payment;

import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExcelService;

import com.dcjet.cs.dto.params.RegistrationHeadDto;
import com.dcjet.cs.dto.params.RegistrationHeadExportParam;
import com.dcjet.cs.dto.params.RegistrationHeadParam;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.dcjet.cs.payment.service.RegistrationHeadService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@RestController
@RequestMapping("v1/registrationHead")
@Api(tags = "接口")
public class RegistrationHeadController extends BaseController {
    @Resource
    private RegistrationHeadService registrationHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    /**
     * @param registrationHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/payment/registration")
    public ResultObject<List<RegistrationHeadDto>> getListPaged(@RequestBody RegistrationHeadParam registrationHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        registrationHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<RegistrationHeadDto>> paged = registrationHeadService.getListPaged(registrationHeadParam, pageParam);
        return paged;
    }

    @ApiOperation("分页查询接口")
    @PostMapping("planList")
    public ResultObject<List<RegistrationHeadDto>> getPlanListPaged(@RequestBody RegistrationHeadParam registrationHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        registrationHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<RegistrationHeadDto>> paged = registrationHeadService.getPlanListPaged(registrationHeadParam, pageParam);
        return paged;
    }

    /**
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<RegistrationHeadDto> insert(@Valid @RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject<RegistrationHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        RegistrationHeadDto registrationHeadDto = registrationHeadService.insert(registrationHeadParam, userInfo);
        if (registrationHeadDto != null) {
            resultObject.setData(registrationHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<RegistrationHeadDto> update(@PathVariable String sid, @Valid @RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        registrationHeadParam.setSid(sid);
        RegistrationHeadDto registrationHeadDto = registrationHeadService.update(registrationHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (registrationHeadDto != null) {
            resultObject.setData(registrationHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 registrationHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody RegistrationHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<RegistrationHeadDto> registrationHeadDtos = registrationHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(registrationHeadDtos, userInfo);

        List<KeyValuePair<String, String>> modifiedHeaders = new ArrayList<>(exportParam.getHeader());
//        for (int i = 0; i < modifiedHeaders.size(); i++) {
//            KeyValuePair<String, String> entry = modifiedHeaders.get(i);
//            if ("shortOverPercent".equals(entry.getKey())) {
//                // 替换为新的键值对（保持值不变，仅修改键）
//                modifiedHeaders.set(i, new KeyValuePair<>("shortOverPercentStr", entry.getValue()));
//            }
//            if ("planYear".equals(entry.getKey())) {
//                modifiedHeaders.set(i, new KeyValuePair<>("planYearStr", entry.getValue()));
//            }
//        }
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), modifiedHeaders, registrationHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<RegistrationHeadDto> list, UserInfoToken userInfo) {
        for(RegistrationHeadDto item : list) {
            item.setBusinessType(CommonEnum.businessTypeEnum.getValue(item.getBusinessType()));
            item.setDocumentStatus(CommonEnum.OrderStatusEnum.getValue(item.getDocumentStatus()));
            item.setFinanceFlag(CommonEnum.IS_NOT_ENUM.getValue(item.getFinanceFlag()));
        }
    }




    /**
     * 确认
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("confirm")
    public ResultObject confirmDataStatus(@RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = registrationHeadService.confirmDataStatus(registrationHeadParam, userInfo);
        return resultObject;
    }


    /**
     * 作废
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("cancel")
    public ResultObject cancel(@RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = registrationHeadService.cancelDataStatus(registrationHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 退单
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("back")
    public ResultObject back(@RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = registrationHeadService.backDataStatus(registrationHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 校验是否存在基础状态非2作废的
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("checkStatus")
    public ResultObject checkStatus(@RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = registrationHeadService.checkStatus(registrationHeadParam, userInfo);
        return resultObject;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getDocNo")
    public ResultObject getDocNo(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = registrationHeadService.getDocNo("",userInfo);
        resultObject.setData(code);
        return resultObject;
    }


    /**
     * 复制
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("copy")
    public ResultObject copy(@RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = registrationHeadService.copy(registrationHeadParam, userInfo);
        return resultObject;
    }
    /**
     * 红冲
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("redRush")
    public ResultObject redRush(@RequestBody RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = registrationHeadService.redRush(registrationHeadParam, userInfo);
        return resultObject;
    }

}
