package com.dcjet.cs.config.api.params;


import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.params.EnterpriseRateDto;
import com.dcjet.cs.dto.params.EnterpriseRateParam;
import com.dcjet.cs.params.service.EnterpriseRateService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/EnterpriseRate")
@Api(tags = "基础管理-企业基础信息接口")
public class EnterpriseRateController extends BaseController {
    @Resource
    private EnterpriseRateService enterpriseRateService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    @Resource
    private PCodeHolder pCodeServiceHolder;
    /**
     * @param rateTableParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/params/rateTable")
    public ResultObject<List<EnterpriseRateDto>> getListPaged(@RequestBody EnterpriseRateParam rateTableParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<EnterpriseRateDto>> paged = enterpriseRateService.getListPaged(rateTableParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param rateTableParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<EnterpriseRateDto> insert(@Valid @RequestBody EnterpriseRateParam rateTableParam, UserInfoToken userInfo) {
        ResultObject<EnterpriseRateDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        EnterpriseRateDto rateTableDto = enterpriseRateService.insert(rateTableParam, userInfo);
        if (rateTableDto != null) {
            resultObject.setData(rateTableDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param rateTableParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<EnterpriseRateDto> update(@PathVariable String sid, @Valid @RequestBody EnterpriseRateParam rateTableParam, UserInfoToken userInfo) {
        rateTableParam.setSid(sid);
        EnterpriseRateDto rateTableDto = enterpriseRateService.update(rateTableParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (rateTableDto != null) {
            resultObject.setData(rateTableDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        enterpriseRateService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<EnterpriseRateParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<EnterpriseRateDto> rateTableDtos = enterpriseRateService.selectAll(exportParam.getExportColumns(), userInfo);
        rateTableDtos = convertForPrint(rateTableDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), rateTableDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<EnterpriseRateDto> convertForPrint(List<EnterpriseRateDto> list) {
        for (EnterpriseRateDto item : list) {
            if (StringUtils.isNotBlank(item.getCurr())){
                item.setCurr(pCodeServiceHolder.getValue(PCodeType.CURR, item.getCurr()));
            }
            if (item.getRate() != null){
                item.setFloatRateStr(item.getRate().stripTrailingZeros().toPlainString()+"%");
            }

        }
        return list;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getNextCode")
    public ResultObject getNextCode(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = enterpriseRateService.getNextCode(userInfo);
        resultObject.setData(code);
        return resultObject;
    }

    /**
     * 根据当前月份插入汇率数据接口
     * 如果当月数据存在则直接返回，不存在则复制上月数据为当月数据
     *
     * @param userInfo 用户信息
     * @return 当月汇率数据列表
     */
    @ApiOperation("根据当前月份插入汇率数据接口")
    @PostMapping("insertMonth")
    public ResultObject<List<EnterpriseRateDto>> insertMonth(UserInfoToken userInfo) {
        try {
            List<EnterpriseRateDto> rateList = enterpriseRateService.insertMonth(userInfo);
            ResultObject<List<EnterpriseRateDto>> resultObject = ResultObject.createInstance(true, "操作成功");
            resultObject.setData(rateList);
            return resultObject;
        } catch (Exception e) {
            ResultObject<List<EnterpriseRateDto>> resultObject = ResultObject.createInstance(false, e.getMessage());
            return resultObject;
        }
    }

}
