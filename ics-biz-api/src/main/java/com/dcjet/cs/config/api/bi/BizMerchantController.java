package com.dcjet.cs.config.api.bi;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;

import com.dcjet.cs.dto.bi.BizMerchantDto;
import com.dcjet.cs.dto.bi.BizMerchantExportParam;
import com.dcjet.cs.dto.bi.BizMerchantParam;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@RestController
@RequestMapping("v1/bizMerchant")
@Api(tags = "接口")
public class BizMerchantController extends BaseController {
    @Resource
    private BizMerchantService bizMerchantService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizMerchantParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/merchant")
    public ResultObject<List<BizMerchantDto>> getListPaged(@RequestBody BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        bizMerchantParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizMerchantDto>> paged = bizMerchantService.getListPaged(bizMerchantParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizMerchantParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizMerchantDto> insert(@Valid @RequestBody BizMerchantParam bizMerchantParam, UserInfoToken userInfo) {
        ResultObject<BizMerchantDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizMerchantDto bizMerchantDto = bizMerchantService.insert(bizMerchantParam, userInfo);
        if (bizMerchantDto != null) {
            resultObject.setData(bizMerchantDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizMerchantParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizMerchantDto> update(@PathVariable String sid, @Valid @RequestBody BizMerchantParam bizMerchantParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        bizMerchantParam.setSid(sid);
        BizMerchantDto bizMerchantDto = bizMerchantService.update(bizMerchantParam, userInfo);
        if (bizMerchantDto != null) {
            resultObject.setData(bizMerchantDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizMerchantService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizMerchantExportParam exportParam, UserInfoToken userInfo) throws Exception{
        exportParam.getExportColumns().setTradeCode(userInfo.getCompany());
        List<BizMerchantDto> bizMerchantDtos = bizMerchantService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizMerchantDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizMerchantDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizMerchantDto> list) {
        for(BizMerchantDto item : list) {
        }
    }

    @ApiOperation("获取流水号")
    @PostMapping(value = "/getMerchantCode")
    public ResultObject<String> getMerchantCode(@Valid @RequestBody BizMerchantParam bizMerchantParam, UserInfoToken userInfo) {
        ResultObject<String> resultObject = ResultObject.createInstance(true);
        String bizMerchantDto = bizMerchantService.getMerchantCode(bizMerchantParam, userInfo);
        if (bizMerchantDto != null) {
            resultObject.setData(bizMerchantDto);
        }
        return resultObject;
    }

    @ApiOperation("获取合同与协议默认买方")
    @PostMapping(value = "/getDefaultBuyer")
    public ResultObject getDefaultBuyer(@Valid @RequestBody BizMerchantParam bizMerchantParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        BizMerchant bizMerchant = bizMerchantService.getDefaultBuyer(bizMerchantParam, userInfo);
        if (bizMerchant != null) {
            resultObject.setData(bizMerchant);
        }
        return resultObject;
    }

    /**
     * @param bizMerchantParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("合同与协议供应商查询条件下拉数据源")
    @PostMapping("getForAggrSupplierSearch")
    public ResultObject<List<BizMerchantDto>> getForAggrSearch(@RequestBody BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        bizMerchantParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizMerchantDto>> paged = bizMerchantService.getForAggrSearch(bizMerchantParam, pageParam,userInfo);
        return paged;
    }

    /**
     * @param bizMerchantParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("合同与协议国内委托方查询条件下拉数据源")
    @PostMapping("getForAggrDomesticSearch")
    public ResultObject<List<BizMerchantDto>> getForAggrDomesticSearch(@RequestBody BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        bizMerchantParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizMerchantDto>> paged = bizMerchantService.getForAggrDomesticSearch(bizMerchantParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizMerchantParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("第三条线进货计划买家查询条件下拉数据源")
    @PostMapping("getForThirdPlanBuyerSearch")
    public ResultObject<List<BizMerchantDto>> getForThirdPlanBuyerSearch(@RequestBody BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        bizMerchantParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizMerchantDto>> paged = bizMerchantService.getForThirdPlanBuyerSearch(bizMerchantParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizMerchantParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("第三条线进货计划卖家查询条件下拉数据源")
    @PostMapping("getForThirdPlanSellerSearch")
    public ResultObject<List<BizMerchantDto>> getForThirdPlanSellerSearch(@RequestBody BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        bizMerchantParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizMerchantDto>> paged = bizMerchantService.getForThirdPlanSellerSearch(bizMerchantParam, pageParam,userInfo);
        return paged;
    }
}
