--liquibase formatted sql

--changeset bhyue:1
DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"
(
    "SID"              VARCHAR(50) PRIMARY KEY,
    "PARAM_CODE"       VARCHAR(30),
    "PRICE_TERM"       VARCHAR(10),
    "PRICE_TERM_DESC"  VARCHAR(80),
    "NOTE"             VARCHAR(200),
    "TRADE_CODE"       VARCHAR(50),
    "INSERT_USER"      VARCHAR(50)  NOT NULL,
    "INSERT_TIME"      TIMESTAMP(6) NOT NULL,
    "INSERT_USER_NAME" VARCHAR(50),
    "UPDATE_USER"      VARCHAR(50),
    "UPDATE_TIME"      TIMESTAMP(6),
    "UPDATE_USER_NAME" VARCHAR(50),
    "EXTEND1"          VARCHAR(200),
    "EXTEND2"          VARCHAR(200),
    "EXTEND3"          VARCHAR(200),
    "EXTEND4"          VARCHAR(200),
    "EXTEND5"          VARCHAR(200),
    "EXTEND6"          VARCHAR(200),
    "EXTEND7"          VARCHAR(200),
    "EXTEND8"          VARCHAR(200),
    "EXTEND9"          VARCHAR(200),
    "EXTEND10"         VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS" IS '价格条款表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."PARAM_CODE" IS '参数代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."PRICE_TERM" IS '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."PRICE_TERM_DESC" IS '价格条款描述';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND10" IS '扩展字段10';
--changeset tlhuang:1
CREATE TABLESPACE if not EXISTS "BIZ_TOBACOO" DATAFILE '/home/<USER>/dmdbms/BIZ_TOBACOO.DBF' SIZE 30720 AUTOEXTEND ON MAXSIZE 67108863 CACHE = NORMAL;
CREATE TABLE "BIZ_TOBACOO"."T_BIZ_TRANSCODE"
(
    "ID"                VARCHAR(40)  DEFAULT SYS_GUID()        NOT NULL,
    "TRADE_CODE"        VARCHAR(10),
    "SYS_ORG_CODE"      VARCHAR(10),
    "CREATE_BY"         VARCHAR(50)                            NOT NULL,
    "CREATE_TIME"       TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY"         VARCHAR(50),
    "UPDATE_TIME"       TIMESTAMP(6),
    "INSERT_USER_NAME"  VARCHAR(50),
    "UPDATE_USER_NAME"  VARCHAR(50),
    "BIZ_TYPE"          VARCHAR(60),
    "TARIFF_RATE"       numeric(19, 6),
    "CONSUMPTION_TAX_RATE"   numeric(19, 6),
    "VAT_RATE"               numeric(19, 6),
    "IE_AGENT_FEE_RATE" numeric(19, 4),
    "HQ_AGENT_FEE_RATE" numeric(19, 4),
    "INTL_TRANS_TYPE"   VARCHAR(20),
    "IS_CONTAINER_SHIP" VARCHAR(20),
    "CONTAINER_CAP"     VARCHAR(20),
    "CONTAINER_TYPE"    VARCHAR(20),
    "INTL_FREIGHT_AMT"  NUMERIC(19, 4),
    "PORT_CHARGES_AMT"  NUMERIC(19, 4),
    "LAND_FREIGHT_AMT"  NUMERIC(19, 4),
    "CUSTOMS_FEE_AMT"   NUMERIC(19, 4),
    "CNTR_INSP_FEE_AMT" NUMERIC(19, 4),
    "INSURANCE_RATE"    NUMERIC(19, 6),
    "OTHER_CHARGES_AMT" NUMERIC(19, 4),
    "REMARK"            VARCHAR(200),
    "EXTEND1"           VARCHAR(200),
    "EXTEND2"           VARCHAR(200),
    "EXTEND3"           VARCHAR(200),
    "EXTEND4"           VARCHAR(200),
    "EXTEND5"           VARCHAR(200),
    "EXTEND6"           VARCHAR(200),
    "EXTEND7"           VARCHAR(200),
    "EXTEND8"           VARCHAR(200),
    "EXTEND9"           VARCHAR(200),
    "EXTEND10"          VARCHAR(200),
    CONSTRAINT          "PK_T_BIZ_TRANSCODE"                   NOT CLUSTER PRIMARY KEY ("ID")
) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT
    ON TABLE "BIZ_TOBACOO"."T_BIZ_TRANSCODE" IS '划款参数表';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.ID is '主键';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.TRADE_CODE is '企业编码';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CREATE_BY is '插入人';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CREATE_TIME is '插入时间';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INSERT_USER_NAME is '插入人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.UPDATE_BY is '更新人';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.UPDATE_TIME is '更新时间';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.UPDATE_USER_NAME is '更新人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.BIZ_TYPE is '业务类型';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.TARIFF_RATE is '关税率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CONSUMPTION_TAX_RATE is '消费税率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.VAT_RATE is '增值税率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.IE_AGENT_FEE_RATE is '进出口公司代理费率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.HQ_AGENT_FEE_RATE is '总公司代理费率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INTL_TRANS_TYPE is '国际运输类型';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.IS_CONTAINER_SHIP is '是否集装箱装运';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CONTAINER_CAP is '集装箱容量';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CONTAINER_TYPE is '集装箱型号';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INTL_FREIGHT_AMT is '国际运费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.PORT_CHARGES_AMT is '港杂费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.LAND_FREIGHT_AMT is '陆运费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CUSTOMS_FEE_AMT is '通关费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CNTR_INSP_FEE_AMT is '验柜服务费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INSURANCE_RATE is '保险费率';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.OTHER_CHARGES_AMT is '其他费用';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.REMARK is '备注';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND1 is '备用1';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND2 is '备用2';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND3 is '备用3';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND4 is '备用4';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND5 is '备用5';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND6 is '备用6';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND7 is '备用7';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND8 is '备用8';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND9 is '备用9';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND10 is '备用10';


DROP INDEX IF EXISTS IDX_BIZ_PRICE_TERMS_SEQ;
CREATE INDEX IDX_BIZ_PRICE_TERMS_SEQ ON "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS" (TRADE_CODE, PARAM_CODE);

DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_CITY";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_CITY" (
                                                 "SID" VARCHAR(50) PRIMARY KEY,
                                                 "PARAM_CODE" VARCHAR(30),
                                                 "CITY_CN_NAME" VARCHAR(50),
                                                 "CITY_EN_NAME" VARCHAR(80),
                                                 "NOTE" VARCHAR(200),
                                                 "TRADE_CODE" VARCHAR(50),
                                                 "INSERT_USER" VARCHAR(50) NOT NULL,
                                                 "INSERT_TIME" TIMESTAMP(6) NOT NULL,
                                                 "INSERT_USER_NAME" VARCHAR(50),
                                                 "UPDATE_USER" VARCHAR(50),
                                                 "UPDATE_TIME" TIMESTAMP(6),
                                                 "UPDATE_USER_NAME" VARCHAR(50),
                                                 "EXTEND1" VARCHAR(200),
                                                 "EXTEND2" VARCHAR(200),
                                                 "EXTEND3" VARCHAR(200),
                                                 "EXTEND4" VARCHAR(200),
                                                 "EXTEND5" VARCHAR(200),
                                                 "EXTEND6" VARCHAR(200),
                                                 "EXTEND7" VARCHAR(200),
                                                 "EXTEND8" VARCHAR(200),
                                                 "EXTEND9" VARCHAR(200),
                                                 "EXTEND10" VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_CITY" IS '城市表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."PARAM_CODE" IS '参数代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."CITY_CN_NAME" IS '城市中文名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."CITY_EN_NAME" IS '城市英文名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND10" IS '扩展字段10';

DROP INDEX IF EXISTS IDX_BIZ_CITY_SEQ;
CREATE INDEX IDX_BIZ_CITY_SEQ ON "BIZ_TOBACOO"."T_BIZ_CITY" (TRADE_CODE, PARAM_CODE);


--changeset tlhuang:2
create table IF NOT EXISTS T_BIZ_BOX_TYPE
(
    SID         VARCHAR(50) not null PRIMARY KEY,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP(6) not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP(6),
    UPDATE_USER_NAME VARCHAR(50),
    PARAM_CODE VARCHAR(30),
    BOX_TYPE VARCHAR(100),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_BOX_TYPE is '箱型表';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.SID is '主键';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.TRADE_CODE is '企业编码';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.INSERT_USER is '插入人';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.INSERT_TIME is '插入时间';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.INSERT_USER_NAME is '插入人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.UPDATE_USER is '更新人';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.UPDATE_TIME is '更新时间';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.UPDATE_USER_NAME is '更新人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.PARAM_CODE is '参数代码';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.BOX_TYPE is '箱型';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.NOTE is '备注';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND1 is '备用1';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND2 is '备用2';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND3 is '备用3';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND4 is '备用4';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND5 is '备用5';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND6 is '备用6';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND7 is '备用7';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND8 is '备用8';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND9 is '备用9';

comment
    on column BIZ_TOBACOO.T_BIZ_BOX_TYPE.EXTEND10 is '备用10';

--changeset jzrong:1
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ADD COLUMN if not exists "YY_CODE" VARCHAR(60);
INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('1',null,null,null,'UNIT','001','台',null,null,'3101915092','yc001','2025-05-29 15:38:11.521239',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('2',null,null,null,'UNIT','002','座',null,null,'3101915092','yc001','2025-05-29 15:38:11.566357',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('3',null,null,null,'UNIT','003','辆',null,null,'3101915092','yc001','2025-05-29 15:38:11.605085',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('4',null,null,null,'UNIT','004','艘',null,null,'3101915092','yc001','2025-05-29 15:38:11.64386',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('5',null,null,null,'UNIT','005','架',null,null,'3101915092','yc001','2025-05-29 15:38:11.685604',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('6',null,null,null,'UNIT','006','套',null,null,'3101915092','yc001','2025-05-29 15:38:11.723227',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('7',null,null,null,'UNIT','007','个',null,null,'3101915092','yc001','2025-05-29 15:38:11.773897',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('8',null,null,null,'UNIT','008','只',null,null,'3101915092','yc001','2025-05-29 15:38:11.839002',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('9',null,null,null,'UNIT','009','头',null,null,'3101915092','yc001','2025-05-29 15:38:11.878325',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('10',null,null,null,'UNIT','010','张',null,null,'3101915092','yc001','2025-05-29 15:38:11.916756',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('11',null,null,null,'UNIT','011','件',null,null,'3101915092','yc001','2025-05-29 15:38:11.97621',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('12',null,null,null,'UNIT','012','支',null,null,'3101915092','yc001','2025-05-29 15:38:12.038872',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('13',null,null,null,'UNIT','013','枝',null,null,'3101915092','yc001','2025-05-29 15:38:12.10016',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('14',null,null,null,'UNIT','014','根',null,null,'3101915092','yc001','2025-05-29 15:38:12.162326',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('15',null,null,null,'UNIT','015','条',null,null,'3101915092','yc001','2025-05-29 15:38:12.201939',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('16',null,null,null,'UNIT','016','把',null,null,'3101915092','yc001','2025-05-29 15:38:12.241976',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('17',null,null,null,'UNIT','017','块',null,null,'3101915092','yc001','2025-05-29 15:38:12.2807',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('18',null,null,null,'UNIT','018','卷',null,null,'3101915092','yc001','2025-05-29 15:38:12.319154',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('19',null,null,null,'UNIT','019','副',null,null,'3101915092','yc001','2025-05-29 15:38:12.357987',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('20',null,null,null,'UNIT','020','片',null,null,'3101915092','yc001','2025-05-29 15:38:12.398415',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('21',null,null,null,'UNIT','021','组',null,null,'3101915092','yc001','2025-05-29 15:38:12.4369',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('22',null,null,null,'UNIT','022','份',null,null,'3101915092','yc001','2025-05-29 15:38:12.477808',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('23',null,null,null,'UNIT','023','幅',null,null,'3101915092','yc001','2025-05-29 15:38:12.517937',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('24',null,null,null,'UNIT','025','双',null,null,'3101915092','yc001','2025-05-29 15:38:12.561542',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('25',null,null,null,'UNIT','026','对',null,null,'3101915092','yc001','2025-05-29 15:38:12.601619',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('26',null,null,null,'UNIT','027','棵',null,null,'3101915092','yc001','2025-05-29 15:38:12.640443',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('27',null,null,null,'UNIT','028','株',null,null,'3101915092','yc001','2025-05-29 15:38:12.678511',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('28',null,null,null,'UNIT','029','井',null,null,'3101915092','yc001','2025-05-29 15:38:12.718015',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('29',null,null,null,'UNIT','030','米',null,null,'3101915092','yc001','2025-05-29 15:38:12.756952',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('30',null,null,null,'UNIT','031','盘',null,null,'3101915092','yc001','2025-05-29 15:38:12.796699',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('31',null,null,null,'UNIT','032','平方米',null,null,'3101915092','yc001','2025-05-29 15:38:12.837457',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('32',null,null,null,'UNIT','033','立方米',null,null,'3101915092','yc001','2025-05-29 15:38:12.87665',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('33',null,null,null,'UNIT','034','筒',null,null,'3101915092','yc001','2025-05-29 15:38:12.914887',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('34',null,null,null,'UNIT','035','千克',null,null,'3101915092','yc001','2025-05-29 15:38:12.954646',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('35',null,null,null,'UNIT','036','克',null,null,'3101915092','yc001','2025-05-29 15:38:12.993764',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('36',null,null,null,'UNIT','037','盆',null,null,'3101915092','yc001','2025-05-29 15:38:13.032289',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('37',null,null,null,'UNIT','038','万个',null,null,'3101915092','yc001','2025-05-29 15:38:13.072365',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('38',null,null,null,'UNIT','039','具',null,null,'3101915092','yc001','2025-05-29 15:38:13.127093',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('39',null,null,null,'UNIT','040','百副',null,null,'3101915092','yc001','2025-05-29 15:38:13.175594',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('40',null,null,null,'UNIT','041','百支',null,null,'3101915092','yc001','2025-05-29 15:38:13.235514',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('41',null,null,null,'UNIT','042','百把',null,null,'3101915092','yc001','2025-05-29 15:38:13.307683',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('42',null,null,null,'UNIT','043','百个',null,null,'3101915092','yc001','2025-05-29 15:38:13.366667',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('43',null,null,null,'UNIT','044','百片',null,null,'3101915092','yc001','2025-05-29 15:38:13.411988',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('44',null,null,null,'UNIT','045','刀',null,null,'3101915092','yc001','2025-05-29 15:38:13.450264',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('45',null,null,null,'UNIT','046','疋',null,null,'3101915092','yc001','2025-05-29 15:38:13.502894',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('46',null,null,null,'UNIT','047','公担',null,null,'3101915092','yc001','2025-05-29 15:38:13.565399',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('47',null,null,null,'UNIT','048','扇',null,null,'3101915092','yc001','2025-05-29 15:38:13.635705',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('48',null,null,null,'UNIT','049','百枝',null,null,'3101915092','yc001','2025-05-29 15:38:13.68969',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('49',null,null,null,'UNIT','050','千只',null,null,'3101915092','yc001','2025-05-29 15:38:13.749253',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('50',null,null,null,'UNIT','051','千块',null,null,'3101915092','yc001','2025-05-29 15:38:13.790397',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('51',null,null,null,'UNIT','052','千盒',null,null,'3101915092','yc001','2025-05-29 15:38:13.82959',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('52',null,null,null,'UNIT','053','千枝',null,null,'3101915092','yc001','2025-05-29 15:38:13.869133',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('53',null,null,null,'UNIT','054','千个',null,null,'3101915092','yc001','2025-05-29 15:38:13.908625',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('54',null,null,null,'UNIT','055','亿支',null,null,'3101915092','yc001','2025-05-29 15:38:13.949308',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('55',null,null,null,'UNIT','056','亿个',null,null,'3101915092','yc001','2025-05-29 15:38:13.987613',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('56',null,null,null,'UNIT','057','万套',null,null,'3101915092','yc001','2025-05-29 15:38:14.027277',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('57',null,null,null,'UNIT','058','千张',null,null,'3101915092','yc001','2025-05-29 15:38:14.065921',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('58',null,null,null,'UNIT','059','万张',null,null,'3101915092','yc001','2025-05-29 15:38:14.104946',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('59',null,null,null,'UNIT','060','千伏安',null,null,'3101915092','yc001','2025-05-29 15:38:14.150723',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('60',null,null,null,'UNIT','061','千瓦',null,null,'3101915092','yc001','2025-05-29 15:38:14.210798',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('61',null,null,null,'UNIT','062','千瓦时',null,null,'3101915092','yc001','2025-05-29 15:38:14.270708',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('62',null,null,null,'UNIT','063','千升',null,null,'3101915092','yc001','2025-05-29 15:38:14.30927',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('63',null,null,null,'UNIT','067','英尺',null,null,'3101915092','yc001','2025-05-29 15:38:14.348765',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('64',null,null,null,'UNIT','070','吨',null,null,'3101915092','yc001','2025-05-29 15:38:14.387431',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('65',null,null,null,'UNIT','071','长吨',null,null,'3101915092','yc001','2025-05-29 15:38:14.426463',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('66',null,null,null,'UNIT','072','短吨',null,null,'3101915092','yc001','2025-05-29 15:38:14.488946',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('67',null,null,null,'UNIT','073','司马担',null,null,'3101915092','yc001','2025-05-29 15:38:14.555408',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('68',null,null,null,'UNIT','074','司马斤',null,null,'3101915092','yc001','2025-05-29 15:38:14.61596',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('69',null,null,null,'UNIT','075','斤',null,null,'3101915092','yc001','2025-05-29 15:38:14.682596',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('70',null,null,null,'UNIT','076','磅',null,null,'3101915092','yc001','2025-05-29 15:38:14.738824',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('71',null,null,null,'UNIT','077','担',null,null,'3101915092','yc001','2025-05-29 15:38:14.796146',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('72',null,null,null,'UNIT','078','英担',null,null,'3101915092','yc001','2025-05-29 15:38:14.846674',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('73',null,null,null,'UNIT','079','短担',null,null,'3101915092','yc001','2025-05-29 15:38:14.88665',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('74',null,null,null,'UNIT','080','两',null,null,'3101915092','yc001','2025-05-29 15:38:14.927678',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('75',null,null,null,'UNIT','081','市担',null,null,'3101915092','yc001','2025-05-29 15:38:14.9658',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('76',null,null,null,'UNIT','083','盎司',null,null,'3101915092','yc001','2025-05-29 15:38:15.005469',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('77',null,null,null,'UNIT','084','克拉',null,null,'3101915092','yc001','2025-05-29 15:38:15.044451',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('78',null,null,null,'UNIT','085','市尺',null,null,'3101915092','yc001','2025-05-29 15:38:15.08449',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('79',null,null,null,'UNIT','086','码',null,null,'3101915092','yc001','2025-05-29 15:38:15.123475',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('80',null,null,null,'UNIT','088','英寸',null,null,'3101915092','yc001','2025-05-29 15:38:15.162665',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('81',null,null,null,'UNIT','089','寸',null,null,'3101915092','yc001','2025-05-29 15:38:15.217007',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('82',null,null,null,'UNIT','095','升',null,null,'3101915092','yc001','2025-05-29 15:38:15.25597',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('83',null,null,null,'UNIT','096','毫升',null,null,'3101915092','yc001','2025-05-29 15:38:15.295319',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('84',null,null,null,'UNIT','097','英加仑',null,null,'3101915092','yc001','2025-05-29 15:38:15.334589',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('85',null,null,null,'UNIT','098','美加仑',null,null,'3101915092','yc001','2025-05-29 15:38:15.373109',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('86',null,null,null,'UNIT','099','立方英尺',null,null,'3101915092','yc001','2025-05-29 15:38:15.41226',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('87',null,null,null,'UNIT','101','立方尺',null,null,'3101915092','yc001','2025-05-29 15:38:15.450424',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('88',null,null,null,'UNIT','110','平方码',null,null,'3101915092','yc001','2025-05-29 15:38:15.488875',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('89',null,null,null,'UNIT','111','平方英尺',null,null,'3101915092','yc001','2025-05-29 15:38:15.546121',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('90',null,null,null,'UNIT','112','平方尺',null,null,'3101915092','yc001','2025-05-29 15:38:15.58531',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('91',null,null,null,'UNIT','115','英制马力',null,null,'3101915092','yc001','2025-05-29 15:38:15.625233',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('92',null,null,null,'UNIT','116','公制马力',null,null,'3101915092','yc001','2025-05-29 15:38:15.666899',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('93',null,null,null,'UNIT','118','令',null,null,'3101915092','yc001','2025-05-29 15:38:15.755892',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('94',null,null,null,'UNIT','120','箱',null,null,'3101915092','yc001','2025-05-29 15:38:15.815951',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('95',null,null,null,'UNIT','121','批',null,null,'3101915092','yc001','2025-05-29 15:38:15.866551',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('96',null,null,null,'UNIT','122','罐',null,null,'3101915092','yc001','2025-05-29 15:38:15.907778',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('97',null,null,null,'UNIT','123','桶',null,null,'3101915092','yc001','2025-05-29 15:38:16.012104',null,null,'烟草测试-1',null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('98',null,null,null,'UNIT','124','扎',null,null,'3101915092','yc001','2025-05-29 15:38:16.156308',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('99',null,null,null,'UNIT','125','包',null,null,'3101915092','yc001','2025-05-29 15:38:16.196618',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null);

INSERT INTO "BIZ_TOBACOO"."T_BIZ_CUSTOMS_PARAMS" ("SID","BUSINESS_TYPE","DATA_STATUS","VERSION_NO","PARAMS_TYPE","PARAMS_CODE","PARAMS_NAME","CUSTOM_PARAM_CODE","CUSTOM_PARAM_NAME","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME","EXTEND1","EXTEND2","EXTEND3","EXTEND4","EXTEND5","EXTEND6","EXTEND7","EXTEND8","EXTEND9","EXTEND10","UNIT_YONYOU","YY_CODE" ) VALUES ('100',null,null,null,'UNIT','126','箩',null,null,'3101915092','yc001','2025-05-29 15:38:16.235284',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null);
--changeset tlhuang:3
create table IF NOT EXISTS T_BIZ_REGISTRATION_HEAD
(
    SID         VARCHAR(50) not null PRIMARY KEY,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP(6) not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP(6),
    UPDATE_USER_NAME VARCHAR(50),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200),
    "DOCUMENT_NO" VARCHAR(60),
    "BUSINESS_TYPE" VARCHAR(60),
    "ADVANCE_FLAG" VARCHAR(10),
    "DEPARTMENT" VARCHAR(60),
    "CURRENCY" VARCHAR(10),
    "PAYER_NAME" VARCHAR(200),
    "ENTRUST_COMPANY" VARCHAR(200),
    "BANK_FEE" numeric(19,2),
    "PAYMENT_AMOUNT" numeric(19,4),
    "FINANCE_FLAG" VARCHAR(10),
    "BUSINESS_DATE" timestamp,
    "REVERSAL_FLAG" VARCHAR(10) ,
    "DOCUMENT_STATUS" VARCHAR(10) ,
    "CONFIRM_TIME" TIMESTAMP,
    "REMARK" VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD is '收款登记表';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.SID is '主键';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.TRADE_CODE is '企业编码';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.INSERT_USER is '插入人';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.INSERT_TIME is '插入时间';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.INSERT_USER_NAME is '插入人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.UPDATE_USER is '更新人';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.UPDATE_TIME is '更新时间';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.UPDATE_USER_NAME is '更新人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND1 is '备用1';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND2 is '备用2';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND3 is '备用3';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND4 is '备用4';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND5 is '备用5';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND6 is '备用6';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND7 is '备用7';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND8 is '备用8';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND9 is '备用9';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_HEAD.EXTEND10 is '备用10';

COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.DOCUMENT_NO IS '单据号';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.BUSINESS_TYPE IS '业务类型';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.ADVANCE_FLAG IS '预付标志';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.DEPARTMENT IS '部门';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.CURRENCY IS '币种';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.PAYER_NAME IS '付款客户';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.ENTRUST_COMPANY IS '委托单位';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.BANK_FEE IS '银行手续费';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.PAYMENT_AMOUNT IS '收款金额';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.FINANCE_FLAG IS '发送财务系统';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.BUSINESS_DATE IS '业务日期';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.REVERSAL_FLAG IS '是否红冲';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.DOCUMENT_STATUS IS '单据状态';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.REMARK IS '备注';
COMMENT ON COLUMN T_BIZ_REGISTRATION_HEAD.CONFIRM_TIME IS '确认时间';


create table IF NOT EXISTS T_BIZ_REGISTRATION_LIST
(
    SID         VARCHAR(50) not null PRIMARY KEY,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP(6) not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP(6),
    UPDATE_USER_NAME VARCHAR(50),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200),
    head_id VARCHAR(50),
    contract_no VARCHAR(60),
    order_number VARCHAR(60),
    g_name VARCHAR(60),
    invoice_number VARCHAR(60),
    dec_total numeric(19,4)
);

comment
    on table BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST is '收款登记表';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.SID is '主键';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.TRADE_CODE is '企业编码';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.INSERT_USER is '插入人';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.INSERT_TIME is '插入时间';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.INSERT_USER_NAME is '插入人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.UPDATE_USER is '更新人';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.UPDATE_TIME is '更新时间';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.UPDATE_USER_NAME is '更新人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND1 is '备用1';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND2 is '备用2';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND3 is '备用3';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND4 is '备用4';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND5 is '备用5';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND6 is '备用6';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND7 is '备用7';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND8 is '备用8';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND9 is '备用9';

comment
    on column BIZ_TOBACOO.T_BIZ_REGISTRATION_LIST.EXTEND10 is '备用10';

COMMENT ON COLUMN T_BIZ_REGISTRATION_LIST.head_id IS '表头id';
COMMENT ON COLUMN T_BIZ_REGISTRATION_LIST.contract_no IS '购销合同号';
COMMENT ON COLUMN T_BIZ_REGISTRATION_LIST.order_number IS '进货单号';
COMMENT ON COLUMN T_BIZ_REGISTRATION_LIST.g_name IS '商品名称';
COMMENT ON COLUMN T_BIZ_REGISTRATION_LIST.invoice_number IS '发票号';
COMMENT ON COLUMN T_BIZ_REGISTRATION_LIST.dec_total IS '金额';



--changeset xbxu1:1
CREATE TABLE IF not exists  "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(60),
    "CONTRACT_NO" VARCHAR(120),
    "CUSTOMER_NAME" VARCHAR(400),
    "SUPPLIER_NAME" VARCHAR(400),
    "SIGN_DATE" DATE,
    "CONTRACT_START_DATE" DATE,
    "CONTRACT_END_DATE" DATE,
    "SIGN_PLACE" VARCHAR(100),
    "SIGN_PLACE_EN" VARCHAR(100),
    "PORT_OF_SHIPMENT" VARCHAR(100),
    "PORT_OF_DESTINATION" VARCHAR(100),
    "CUSTOMS_PORT" VARCHAR(100),
    "PAYMENT_METHOD" VARCHAR(40),
    "CURRENCY" VARCHAR(20),
    "REMARK" VARCHAR(400),
    "DOC_STATUS" VARCHAR(20),
    "CONFIRM_DATE" TIMESTAMP(6),
    "AUDIT_STATUS" VARCHAR(20),
    "DATA_STATE" VARCHAR(10),
    "VERSION_NO" VARCHAR(10),
    "TRADE_CODE" VARCHAR(10),
    "SYS_ORG_CODE" VARCHAR(10),
    "PARENT_ID" VARCHAR(40),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(50),
    "UPDATE_USER_NAME" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    CONSTRAINT "PK_T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD" IS '国营贸易进口辅料-外商合同表头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."BUSINESS_TYPE" IS '业务类型，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CONTRACT_NO" IS '合同号，文本';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CUSTOMER_NAME" IS '客户，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."SUPPLIER_NAME" IS '供应商，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."SIGN_DATE" IS '签约日期，日期控件';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CONTRACT_START_DATE" IS '合同生效期，日期控件';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CONTRACT_END_DATE" IS '合同有效期，日期控件';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."SIGN_PLACE" IS '签约地点，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."SIGN_PLACE_EN" IS '签约地点(英文)，文本';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."PORT_OF_SHIPMENT" IS '装运港，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."PORT_OF_DESTINATION" IS '目的港，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CUSTOMS_PORT" IS '报关口岸，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."PAYMENT_METHOD" IS '付款方式，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CURRENCY" IS '币种，下拉框';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."REMARK" IS '备注，文本';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."DOC_STATUS" IS '单据状态，文本';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CONFIRM_DATE" IS '确认时间，日期控件';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."AUDIT_STATUS" IS '审核状态，文本';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."TRADE_CODE" IS '业务编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."SYS_ORG_CODE" IS '所属机构编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."UPDATE_BY" IS '修改人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."INSERT_USER_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD"."EXTEND10" IS '扩展字段10';

CREATE TABLE IF not exists "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "HEAD_ID" VARCHAR(40) NOT NULL,
    "PRODUCT_NAME" VARCHAR(160),
    "IMPORT_QUANTITY" NUMERIC(19,6),
    "IMPORT_UNIT" VARCHAR(60),
    "QUANTITY" NUMERIC(19,6),
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "DELIVERY_DATE" DATE,
    "USD_TOTAL" NUMERIC(19,4),
    "SPECIFICATION" VARCHAR(400),
    "PRODUCT_CATEGORY" VARCHAR(160),
    "DATA_STATE" VARCHAR(10),
    "VERSION_NO" VARCHAR(10),
    "TRADE_CODE" VARCHAR(10),
    "SYS_ORG_CODE" VARCHAR(10),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(50),
    "UPDATE_USER_NAME" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "SUPPLIER" VARCHAR(200),
    "ORDER_NO" VARCHAR(50),
    "PARENT_ID" VARCHAR(50),
    CONSTRAINT "PK_T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST" IS '国营贸易进口辅料-外商合同表体';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."HEAD_ID" IS '关联主表ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."PRODUCT_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."IMPORT_QUANTITY" IS '进口数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."IMPORT_UNIT" IS '进口计量单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."QUANTITY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."DELIVERY_DATE" IS '发货日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."USD_TOTAL" IS '总值折美元';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."SPECIFICATION" IS '规格';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."PRODUCT_CATEGORY" IS '商品类别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."TRADE_CODE" IS '业务编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."SYS_ORG_CODE" IS '所属机构编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."UPDATE_BY" IS '修改人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."INSERT_USER_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."SUPPLIER" IS '订单卖方';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."ORDER_NO" IS '订单编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST"."PARENT_ID" IS '上游关联id';


CREATE TABLE IF NOT EXISTS BIZ_TOBACOO.T_BIZ_QUOTATION (
                                                           SID VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    CREATE_BY VARCHAR(50) NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    CREATE_USER_NAME VARCHAR(50) NULL,
    UPDATE_BY VARCHAR(50) NULL,
    UPDATE_TIME TIMESTAMP NULL,
    UPDATE_USER_NAME VARCHAR(50) NULL,
    TRADE_CODE VARCHAR(50) NULL,
    SYS_ORG_CODE VARCHAR(50) NULL,
    EXTEND1 VARCHAR(200) NULL,
    EXTEND2 VARCHAR(200) NULL,
    EXTEND3 VARCHAR(200) NULL,
    EXTEND4 VARCHAR(200) NULL,
    EXTEND5 VARCHAR(200) NULL,
    EXTEND6 VARCHAR(200) NULL,
    EXTEND7 VARCHAR(200) NULL,
    EXTEND8 VARCHAR(200) NULL,
    EXTEND9 VARCHAR(200) NULL,
    EXTEND10 VARCHAR(200) NULL,
    BUSINESS_TYPE VARCHAR(120) NULL,
    G_NAME VARCHAR(160) NULL,
    MERCHANDISE_CATEGORIES VARCHAR(160) NULL,
    PRODUCT_MODEL VARCHAR(160) NULL,
    SPECIFICATIONS VARCHAR(400) NULL,
    GRAMMAGE NUMERIC(19,5) NULL,
    MATERIAL_NO VARCHAR(100) NULL,
    UNIT VARCHAR(40) NULL,
    UNIT_I VARCHAR(40) NULL,
    MERCHANT_CODE VARCHAR(400) NULL,
    PRICE_TERM VARCHAR(40) NULL,
    DESTINATION_PORT VARCHAR(100) NULL,
    IMPORT_UNIT_PRICE NUMERIC(19,8) NULL,
    UNIT_TRAY_PRICE NUMERIC(19,8) NULL,
    CURR VARCHAR(20) NULL,
    EXCHANGE_RATE NUMERIC(19,6) NULL,
    TARIFF_RATE NUMERIC(19,6) NULL,
    TARIFF_PRICE NUMERIC(19,2) NULL,
    VAT NUMERIC(19,6) NULL,
    HEAD_AGENCY_FEE_RATE NUMERIC(19,4) NULL,
    HEAD_AGENCY_FEE_PRICE NUMERIC(19,2) NULL,
    FREIGHT_FORWARDING_FEE_PRICE NUMERIC(19,4) NULL,
    FREIGHT_FORWARDING_FEE NUMERIC(19,2) NULL,
    INSURANCE_FEE NUMERIC(19,2) NULL,
    PURCHASE_COST NUMERIC(19,2) NULL,
    STORAGE_TRANSPORT_TAX NUMERIC(19,2) NULL,
    GROSS_MARGIN NUMERIC(19,2) NULL,
    PRICE_EXCLUDING_TAX DECIMAL(19,2) NULL,
    PRICE_RMB DECIMAL(19,2) NULL,
    NOTE VARCHAR(400) NULL,
    STATUS VARCHAR(20) NULL,
    CONSTRAINT T_BIZ_QUOTATION_PK PRIMARY KEY (SID)
    );

COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.SID IS '主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.CREATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.CREATE_USER_NAME IS '创建人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.UPDATE_BY IS '最后修改人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.UPDATE_TIME IS '最后修改时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.UPDATE_USER_NAME IS '最后修改人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.TRADE_CODE IS '企业编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.SYS_ORG_CODE IS '创建人部门编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND1 IS '拓展字段1';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND10 IS '拓展字段10';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND9 IS '拓展字段9';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND8 IS '拓展字段8';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND7 IS '拓展字段7';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND6 IS '拓展字段6';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND5 IS '拓展字段5';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND4 IS '拓展字段4';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND3 IS '拓展字段3';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXTEND2 IS '拓展字段2';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.BUSINESS_TYPE IS '业务类型';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.G_NAME IS '商品名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.MERCHANDISE_CATEGORIES IS '商品类别';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.PRODUCT_MODEL IS '产品型号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.SPECIFICATIONS IS '规格';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.GRAMMAGE IS '克重';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.MATERIAL_NO IS '材料编号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.UNIT IS '计量单位';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.UNIT_I IS '进口计量单位';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.MERCHANT_CODE IS '供应商';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.PRICE_TERM IS '价格条款';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.DESTINATION_PORT IS '指运港';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.IMPORT_UNIT_PRICE IS '进口单价';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.UNIT_TRAY_PRICE IS '单价/盘（USD）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.CURR IS '币种';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.EXCHANGE_RATE IS '汇率';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.TARIFF_RATE IS '关税率%';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.TARIFF_PRICE IS '关税金额';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.VAT IS '增值税率%';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.HEAD_AGENCY_FEE_RATE IS '总公司代理费率%';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.HEAD_AGENCY_FEE_PRICE IS '总公司代理费';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.FREIGHT_FORWARDING_FEE_PRICE IS '货代费单价';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.FREIGHT_FORWARDING_FEE IS '货代费用';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.INSURANCE_FEE IS '保险费';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.PURCHASE_COST IS '购进成本';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.STORAGE_TRANSPORT_TAX IS '仓储运输及税额';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.GROSS_MARGIN IS '毛利';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.PRICE_EXCLUDING_TAX IS '不含税单价';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.PRICE_RMB IS '人民币单价（含税）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.NOTE IS '备注';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_QUOTATION.STATUS IS '数据状态';

--changeset cblin:1
CREATE TABLE IF NOT EXISTS BIZ_TOBACOO.T_BIZ_STORE_I_HEAD (
    SID VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    CREATE_BY VARCHAR(50) NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    CREATE_USER_NAME VARCHAR(50) NULL,
    UPDATE_BY VARCHAR(50) NULL,
    UPDATE_TIME TIMESTAMP NULL,
    UPDATE_USER_NAME VARCHAR(50) NULL,
    TRADE_CODE VARCHAR(50) NULL,
    SYS_ORG_CODE VARCHAR(50) NULL,
    EXTEND1 VARCHAR(200) NULL,
    EXTEND2 VARCHAR(200) NULL,
    EXTEND3 VARCHAR(200) NULL,
    EXTEND4 VARCHAR(200) NULL,
    EXTEND5 VARCHAR(200) NULL,
    EXTEND6 VARCHAR(200) NULL,
    EXTEND7 VARCHAR(200) NULL,
    EXTEND8 VARCHAR(200) NULL,
    EXTEND9 VARCHAR(200) NULL,
    EXTEND10 VARCHAR(200) NULL,
    STORE_I_NO VARCHAR(120) NULL,
    CONTRACT_NO VARCHAR(120) NULL,
    PUR_SALE_CONTRACT_NO VARCHAR(120) NULL,
    PURCHASE_ORDER_NO VARCHAR(120) NULL,
    INVOICE_NO VARCHAR(120) NULL,
    MERCHANT_CODE VARCHAR(400) NULL,
    SELLING_RATE NUMERIC(19,6) NULL,
    FOREIGN_CURR_PRICE NUMERIC(19,2) NULL,
    CURR VARCHAR(20) NULL,
    PRICE_TERM VARCHAR(40) NULL,
    TARIFF_PRICE NUMERIC(19,2) NULL,
    VAT_PRICE NUMERIC(19,2) NULL,
    AGENT_FEE NUMERIC(19,2) NULL,
    INSURANCE_FEE NUMERIC(19,2) NULL,
    INSURANCE_FEE_CURR NUMERIC(19,2) NULL,
    PRODUCT_AMOUNT_TOTAL NUMERIC(19,2) NULL,
    FEE_AMOUNT_TOTAL NUMERIC(19,2) NULL,
    TAX_AMOUNT_TOTAL NUMERIC(19,2) NULL,
    COST_AMOUNT_TOTAL NUMERIC(19,2) NULL,
    TOTAL_PRICE NUMERIC(19,2) NULL,
    BUSINESS_DATE TIMESTAMP NULL,
    SEND_FINANCE VARCHAR(20) NULL,
    RED_FLUSH VARCHAR(20) NULL,
    NOTE VARCHAR(400) NULL,
    STATUS VARCHAR(20) NULL,
    APPR_STATUS VARCHAR(20) NULL,
    CONFIRM_TIME TIMESTAMP NULL,
    IS_NEXT VARCHAR(10) NULL,
    STORE_E_STATUS VARCHAR(10) NULL,
    PURCHASE_NO_MARK VARCHAR(600) NULL,
    CONSTRAINT T_BIZ_STORE_I_HEAD_PK PRIMARY KEY (SID)
    );


COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.SID IS '主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.CREATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.CREATE_USER_NAME IS '创建人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.UPDATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.UPDATE_TIME IS '最后修改时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.UPDATE_USER_NAME IS '最后修改人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.TRADE_CODE IS '企业编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.SYS_ORG_CODE IS '创建人部门编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND1 IS '拓展字段1';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND2 IS '拓展字段2';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND3 IS '拓展字段3';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND4 IS '拓展字段4';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND5 IS '拓展字段5';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND6 IS '拓展字段6';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND7 IS '拓展字段7';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND8 IS '拓展字段8';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND9 IS '拓展字段9';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.EXTEND10 IS '拓展字段10';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.STORE_I_NO IS '入库回单编号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.CONTRACT_NO IS '合同号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.PUR_SALE_CONTRACT_NO IS '购销合同号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.PURCHASE_ORDER_NO  IS '进货单号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.INVOICE_NO IS '发票号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.MERCHANT_CODE IS '供应商';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.SELLING_RATE IS '卖出价（汇率）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.FOREIGN_CURR_PRICE IS '外币货价';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.CURR IS '币种';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.PRICE_TERM IS '价格条款';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.TARIFF_PRICE IS '关税';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.VAT_PRICE IS '增值税';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.AGENT_FEE IS '代理费用';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.INSURANCE_FEE IS '保险费用';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.INSURANCE_FEE_CURR IS '保险费用(外币)';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.PRODUCT_AMOUNT_TOTAL IS '商品金额小计';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.FEE_AMOUNT_TOTAL IS '费用金额小计';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.TAX_AMOUNT_TOTAL IS '税金金额小计';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.COST_AMOUNT_TOTAL IS '成本金额小计';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.TOTAL_PRICE IS '合计金额（RMB）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.BUSINESS_DATE IS '业务日期';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.SEND_FINANCE IS '发送财务系统';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.RED_FLUSH IS '是否红冲';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.NOTE IS '备注';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.STATUS IS '单据状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.APPR_STATUS IS '审核状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.CONFIRM_TIME IS '确认时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.IS_NEXT IS '是否流入下一个节点';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.STORE_E_STATUS IS '出库回单状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_HEAD.PURCHASE_NO_MARK IS '进货信息数据标记';

CREATE TABLE IF NOT EXISTS BIZ_TOBACOO.T_BIZ_STORE_I_LIST (
    SID VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    HEAD_ID VARCHAR(50) NULL,
    CREATE_BY VARCHAR(50) NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    CREATE_USER_NAME VARCHAR(50) NULL,
    UPDATE_BY VARCHAR(50) NULL,
    UPDATE_TIME TIMESTAMP NULL,
    UPDATE_USER_NAME VARCHAR(50) NULL,
    TRADE_CODE VARCHAR(50) NULL,
    SYS_ORG_CODE VARCHAR(50) NULL,
    EXTEND1 VARCHAR(200) NULL,
    EXTEND2 VARCHAR(200) NULL,
    EXTEND3 VARCHAR(200) NULL,
    EXTEND4 VARCHAR(200) NULL,
    EXTEND5 VARCHAR(200) NULL,
    EXTEND6 VARCHAR(200) NULL,
    EXTEND7 VARCHAR(200) NULL,
    EXTEND8 VARCHAR(200) NULL,
    EXTEND9 VARCHAR(200) NULL,
    EXTEND10 VARCHAR(200) NULL,
    G_NAME VARCHAR(160) NULL,
    SPECIFICATIONS VARCHAR(400) NULL,
    QTY NUMERIC(19,6) NULL,
    UNIT VARCHAR(60) NULL,
    CURR_PRICE NUMERIC(19,5) NULL,
    RMB_PRICE NUMERIC(19,5) NULL,
    CURR_TOTAL_PRICE NUMERIC(19,2) NULL,
    RMB_TOTAL_PRICE NUMERIC(19,2) NULL,
    CONSTRAINT T_BIZ_STORE_I_LIST_PK PRIMARY KEY (SID)
    );

COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.SID IS '主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.HEAD_ID IS '表头主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.CREATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.CREATE_USER_NAME IS '创建人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.UPDATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.UPDATE_TIME IS '最后修改时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.UPDATE_USER_NAME IS '最后修改人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.TRADE_CODE IS '企业编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.SYS_ORG_CODE IS '创建人部门编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND1 IS '拓展字段1';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND2 IS '拓展字段2';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND3 IS '拓展字段3';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND4 IS '拓展字段4';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND5 IS '拓展字段5';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND6 IS '拓展字段6';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND7 IS '拓展字段7';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND8 IS '拓展字段8';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND9 IS '拓展字段9';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.EXTEND10 IS '拓展字段10';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.G_NAME IS '商品名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.SPECIFICATIONS IS '规格';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.QTY IS '数量';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.UNIT IS '单位';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.CURR_PRICE IS '外币单价';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.RMB_PRICE IS '人民币单价';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.CURR_TOTAL_PRICE IS '外币货价';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_I_LIST.RMB_TOTAL_PRICE IS '人民币货价';

CREATE TABLE IF NOT EXISTS BIZ_TOBACOO.T_BIZ_STORE_E_HEAD (
    SID VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    CREATE_BY VARCHAR(50) NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    CREATE_USER_NAME VARCHAR(50) NULL,
    UPDATE_BY VARCHAR(50) NULL,
    UPDATE_TIME TIMESTAMP NULL,
    UPDATE_USER_NAME VARCHAR(50) NULL,
    TRADE_CODE VARCHAR(50) NULL,
    SYS_ORG_CODE VARCHAR(50) NULL,
    EXTEND1 VARCHAR(200) NULL,
    EXTEND2 VARCHAR(200) NULL,
    EXTEND3 VARCHAR(200) NULL,
    EXTEND4 VARCHAR(200) NULL,
    EXTEND5 VARCHAR(200) NULL,
    EXTEND6 VARCHAR(200) NULL,
    EXTEND7 VARCHAR(200) NULL,
    EXTEND8 VARCHAR(200) NULL,
    EXTEND9 VARCHAR(200) NULL,
    EXTEND10 VARCHAR(200) NULL,
    STORE_E_NO VARCHAR(120) NULL,
    CONTRACT_NO VARCHAR(120) NULL,
    PURCHASE_ORDER_NO VARCHAR(120) NULL,
    PUR_SALE_CONTRACT_NO VARCHAR(120) NULL,
    CONSIGNEE VARCHAR(400) NULL,
    DELIVERY_DATE TIMESTAMP NULL,
    PRODUCT_AMOUNT_TOTAL NUMERIC(19,2) NULL,
    TARIFF_PRICE NUMERIC(19,2) NULL,
    INSURANCE_FEE NUMERIC(19,2) NULL,
    AGENT_FEE NUMERIC(19,2) NULL,
    BUSINESS_DATE TIMESTAMP NULL,
    SEND_FINANCE VARCHAR(20) NULL,
    NOTE VARCHAR(400) NULL,
    STATUS VARCHAR(20) NULL,
    HEAD_ID VARCHAR(50) NULL,
    CONFIRM_TIME TIMESTAMP NULL,
    CONSTRAINT T_BIZ_STORE_E_HEAD_PK PRIMARY KEY (SID)
    );


COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.SID IS '主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.CREATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.CREATE_USER_NAME IS '创建人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.UPDATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.UPDATE_TIME IS '最后修改时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.UPDATE_USER_NAME IS '最后修改人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.TRADE_CODE IS '企业编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.SYS_ORG_CODE IS '创建人部门编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND1 IS '拓展字段1';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND2 IS '拓展字段2';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND3 IS '拓展字段3';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND4 IS '拓展字段4';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND5 IS '拓展字段5';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND6 IS '拓展字段6';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND7 IS '拓展字段7';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND8 IS '拓展字段8';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND9 IS '拓展字段9';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.EXTEND10 IS '拓展字段10';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.STORE_E_NO IS '出库回单编号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.CONTRACT_NO IS '合同号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.PURCHASE_ORDER_NO  IS '进货单号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.PUR_SALE_CONTRACT_NO IS '购销合同号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.CONSIGNEE IS '提货人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.DELIVERY_DATE IS '出库日期';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.PRODUCT_AMOUNT_TOTAL IS '金额';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.TARIFF_PRICE IS '关税';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.INSURANCE_FEE IS '保险费用';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.AGENT_FEE IS '代理费用';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.BUSINESS_DATE IS '业务日期';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.SEND_FINANCE IS '发送财务系统';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.NOTE IS '备注';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.STATUS IS '出库数据状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.HEAD_ID IS '入库回单主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.CONFIRM_TIME IS '确认时间';

CREATE TABLE IF NOT EXISTS BIZ_TOBACOO.T_BIZ_STORE_E_LIST (
    SID VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    HEAD_ID VARCHAR(50) NULL,
    CREATE_BY VARCHAR(50) NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    CREATE_USER_NAME VARCHAR(50) NULL,
    UPDATE_BY VARCHAR(50) NULL,
    UPDATE_TIME TIMESTAMP NULL,
    UPDATE_USER_NAME VARCHAR(50) NULL,
    TRADE_CODE VARCHAR(50) NULL,
    SYS_ORG_CODE VARCHAR(50) NULL,
    EXTEND1 VARCHAR(200) NULL,
    EXTEND2 VARCHAR(200) NULL,
    EXTEND3 VARCHAR(200) NULL,
    EXTEND4 VARCHAR(200) NULL,
    EXTEND5 VARCHAR(200) NULL,
    EXTEND6 VARCHAR(200) NULL,
    EXTEND7 VARCHAR(200) NULL,
    EXTEND8 VARCHAR(200) NULL,
    EXTEND9 VARCHAR(200) NULL,
    EXTEND10 VARCHAR(200) NULL,
    G_NAME_NO VARCHAR(160) NULL,
    QTY_DELI NUMERIC(19,6) NULL,
    QTY_ISS NUMERIC(19,6) NULL,
    UNIT VARCHAR(40) NULL,
    CONSTRAINT T_BIZ_STORE_E_LIST_PK PRIMARY KEY (SID)
    );

COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.SID IS '主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.HEAD_ID IS '表头主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.CREATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.CREATE_USER_NAME IS '创建人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.UPDATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.UPDATE_TIME IS '最后修改时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.UPDATE_USER_NAME IS '最后修改人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.TRADE_CODE IS '企业编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.SYS_ORG_CODE IS '创建人部门编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND1 IS '拓展字段1';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND2 IS '拓展字段2';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND3 IS '拓展字段3';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND4 IS '拓展字段4';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND5 IS '拓展字段5';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND6 IS '拓展字段6';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND7 IS '拓展字段7';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND8 IS '拓展字段8';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND9 IS '拓展字段9';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.EXTEND10 IS '拓展字段10';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.G_NAME_NO IS '商品牌号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.QTY_DELI IS '出货数量';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.QTY_ISS IS '实发数量';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_STORE_E_LIST.UNIT IS '单位';




--changeset tlhuang:4
alter table BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD
    add if not exists red_flush varchar(10);

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.red_flush is '是否红冲';

--changeset ycmeng:1
CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"
(
    "ID" VARCHAR(36) NOT NULL,
    "BUSINESS_TYPE" VARCHAR(60) DEFAULT '2',
    "CONTRACT_NO" VARCHAR(60),
    "CONTRACT_YEAR" CHAR(4),
    "BUSINESS_DISTINCTION" VARCHAR(20) DEFAULT '0',
    "REMARK" VARCHAR(200),
    "VERSION_NO" VARCHAR(10) DEFAULT '1' NOT NULL,
    "STATUS" VARCHAR(10) DEFAULT '0' NOT NULL,
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPR_STATUS" VARCHAR(10),
    "CREATE_BY" VARCHAR(50),
    "CREATE_TIME" TIMESTAMP(6),
    "UPDATE_BY" VARCHAR(10),
    "UPDATE_TIME" TIMESTAMP(6),
    "SYS_ORG_CODE" VARCHAR(36),
    "TRADE_CODE" VARCHAR(36),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "CREATE_BY_NAME" VARCHAR(50),
    "UPDATE_BY_NAME" VARCHAR(50),
    "PAYMENT_AMOUNT" NUMERIC(19,4),
    "CONTRACT_AMOUNT" NUMERIC(19,6),
    "EXCHANGE_RATE" NUMERIC(19,6),
    "TARIFF_RATE" NUMERIC(19,6),
    "TARIFF_AMOUNT" NUMERIC(19,2),
    "CONSUMPTION_TAX_RATE" NUMERIC(19,6),
    "CONSUMPTION_TAX_AMOUNT" NUMERIC(19,6),
    "VAT_RATE" NUMERIC(19,6),
    "VAT_AMOUNT" NUMERIC(19,6),
    "IMPORT_EXPORT_AGENCY_RATE" NUMERIC(19,4),
    "IMPORT_EXPORT_AGENCY_FEE" NUMERIC(19,2),
    "HEADQUARTERS_AGENCY_RATE" NUMERIC(19,4),
    "HEADQUARTERS_AGENCY_FEE" NUMERIC(19,2),
    "CONTRACT_QUANTITY" NUMERIC(19,4),
    "BILLING_WEIGHT" NUMERIC(19,0),
    "CUSTOMS_CLEARANCE_FEE" NUMERIC(19,4),
    "CONTAINER_INSPECTION_FEE" NUMERIC(19,4),
    "FREIGHT_FORWARDER_FEE" NUMERIC(19,2),
    "INSURANCE_RATE" NUMERIC(19,4),
    "INSURANCE_FEE" NUMERIC(19,2),
    "IS_TRANSFER_NOTICE" VARCHAR(10),
    CONSTRAINT "T_BIZ_I_AUXMAT_BUY_CONTRACT_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT" IS '购销合同表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."ID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONTRACT_NO" IS '购销合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONTRACT_YEAR" IS '购销年份';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."BUSINESS_DISTINCTION" IS '业务区分';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."REMARK" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."APPR_STATUS" IS '审批状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CREATE_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."UPDATE_BY_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."PAYMENT_AMOUNT" IS '划款金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONTRACT_AMOUNT" IS '合同金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."EXCHANGE_RATE" IS '汇率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."TARIFF_RATE" IS '关税率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."TARIFF_AMOUNT" IS '关税金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONSUMPTION_TAX_RATE" IS '消费税率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONSUMPTION_TAX_AMOUNT" IS '消费税金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."VAT_RATE" IS '增值税率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."VAT_AMOUNT" IS '增值税金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."IMPORT_EXPORT_AGENCY_RATE" IS '进出口公司代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."IMPORT_EXPORT_AGENCY_FEE" IS '进出口公司代理费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."HEADQUARTERS_AGENCY_RATE" IS '总公司代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."HEADQUARTERS_AGENCY_FEE" IS '总公司代理费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONTRACT_QUANTITY" IS '合同数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."BILLING_WEIGHT" IS '计费重量（箱）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CUSTOMS_CLEARANCE_FEE" IS '通关费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."CONTAINER_INSPECTION_FEE" IS '验柜服务费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."FREIGHT_FORWARDER_FEE" IS '货代费用';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."INSURANCE_RATE" IS '保险费率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."INSURANCE_FEE" IS '保险费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT"."IS_TRANSFER_NOTICE" IS '是否划款通知保存过';



CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"
(
    "ID" VARCHAR(50) NOT NULL,
    "HEAD_ID" VARCHAR(50),
    "G_NAME" VARCHAR(120) NOT NULL,
    "G_MODEL" VARCHAR(200),
    "SPECIFICATIONS" VARCHAR(200),
    "GRAM_WEIGHT" NUMERIC(19,4),
    "SUPPLIER" VARCHAR(400),
    "QTY" NUMERIC(19,6) NOT NULL,
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" NUMERIC(19,8) NOT NULL,
    "AMOUNT" NUMERIC(19,4) NOT NULL,
    "CURR" VARCHAR(20) DEFAULT 'USD' NOT NULL,
    "CREATE_BY" VARCHAR(50),
    "CREATE_TIME" TIMESTAMP(6),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "SYS_ORG_CODE" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "CREATE_BY_NAME" VARCHAR(100),
    "UPDATE_BY_NAME" VARCHAR(100),
    CONSTRAINT "T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST_PKEY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST" IS '购销合同表体';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."ID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."HEAD_ID" IS '关联SID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."G_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."G_MODEL" IS '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."SPECIFICATIONS" IS '规格';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."GRAM_WEIGHT" IS '克重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."SUPPLIER" IS '供应商';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."QTY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."UPDATE_BY" IS '最后修改人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."CREATE_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST"."UPDATE_BY_NAME" IS '修改人姓名';

--changeset jzrong:2

ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD" ADD COLUMN if not EXISTS "CONTRACT_NO" VARCHAR(50);
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD" ADD COLUMN if not EXISTS "CUSTOMER" VARCHAR(50);
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD" ADD COLUMN if not EXISTS "SEND_FINANCIAL" VARCHAR(50);
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD" ADD COLUMN if not EXISTS "IS_FLUSH_RED" VARCHAR(50);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD"."SEND_FINANCIAL" IS '发送财务系统';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_SELL_HEAD"."IS_FLUSH_RED" IS '是否冲红';

ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_SELL_LIST" ADD COLUMN if not EXISTS "I_COUNT" NUMERIC(19,6);
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_SELL_LIST" ADD COLUMN if not EXISTS "I_UNIT" VARCHAR(50);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_SELL_LIST"."I_COUNT" IS '进口数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_SELL_LIST"."I_UNIT" IS '进口单位';

ALTER TABLE "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD" ADD COLUMN if not EXISTS "SALES_DOCUMENT_STATUS" VARCHAR(50);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."SALES_DOCUMENT_STATUS" IS '销售状态';

--changeset bhyue:2
alter table "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"
    modify "PRICE_TERM" varchar(20);

alter table "BIZ_TOBACOO"."T_BIZ_CITY"
    modify "CITY_CN_NAME" varchar(100);

DROP TABLE IF EXISTS "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD";
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"
(
    "ID"                         VARCHAR(50) PRIMARY KEY,
    "PARENT_ID"                  VARCHAR(50),
    "BUY_HEAD_IDS"               VARCHAR(255),
    "BUSINESS_TYPE"              VARCHAR(60),
    "ORDER_NO"                   VARCHAR(60),
    "ORDER_DATE"                 TIMESTAMP,
    "CUSTOMER"                   VARCHAR(200),
    "CUSTOMER_NAME"              VARCHAR(255),
    "VERSION_NO"                 VARCHAR(10),
    "DATA_STATUS"                VARCHAR(10),
    "PURCHASE_SALES_CONTRACT_NO" VARCHAR(255),
    "NOTE"                       VARCHAR(200),
    "CONFIRM_TIME"               TIMESTAMP,
    "APPR_STATUS"                VARCHAR(10),
    "TRADE_CODE"                 VARCHAR(50),
    "SYS_ORG_CODE"               VARCHAR(100),
    "CREATE_BY"                  VARCHAR(50) NOT NULL,
    "CREATE_TIME"                TIMESTAMP   NOT NULL,
    "CREATE_BY_NAME"             VARCHAR(50),
    "UPDATE_BY"                  VARCHAR(50),
    "UPDATE_TIME"                TIMESTAMP,
    "UPDATE_BY_NAME"             VARCHAR(50),
    "EXTEND1"                    VARCHAR(200),
    "EXTEND2"                    VARCHAR(200),
    "EXTEND3"                    VARCHAR(200),
    "EXTEND4"                    VARCHAR(200),
    "EXTEND5"                    VARCHAR(200),
    "EXTEND6"                    VARCHAR(200),
    "EXTEND7"                    VARCHAR(200),
    "EXTEND8"                    VARCHAR(200),
    "EXTEND9"                    VARCHAR(200),
    "EXTEND10"                   VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD" is '订货通知表头表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."ID" is '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."PARENT_ID" is '父ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."BUY_HEAD_IDS" is '购销合同表头关联ID列表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."BUSINESS_TYPE" is '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."ORDER_NO" is '订货编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."ORDER_DATE" is '订货日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CUSTOMER" is '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CUSTOMER_NAME" is '客户名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."VERSION_NO" is '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."DATA_STATUS" is '单据状态 0编制 1确认 2作废';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."PURCHASE_SALES_CONTRACT_NO" is '购销合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."NOTE" is '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CONFIRM_TIME" is '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."APPR_STATUS" is '审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."SYS_ORG_CODE" IS '部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_BY" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_BY_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."UPDATE_BY_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."EXTEND10" IS '扩展字段10';

DROP TABLE IF EXISTS "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST";
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"
(
    "ID"                         VARCHAR(50) PRIMARY KEY,
    "PARENT_ID"                  VARCHAR(50),
    "HEAD_ID"                    VARCHAR(50),
    "BUY_LIST_ID"                VARCHAR(50),
    "PRODUCT_NAME"               VARCHAR(80),
    "PRODUCT_MODEL"              VARCHAR(100),
    "SPECIFICATION"              VARCHAR(100),
    "WEIGHT"                     NUMERIC(19, 4),
    "SUPPLIER"                   VARCHAR(200),
    "SUPPLIER_NAME"              VARCHAR(255),
    "TRANSPORT_MODE"             VARCHAR(10),
    "PORT"                       VARCHAR(50),
    "QTY"                        NUMERIC(19, 6),
    "UNIT"                       VARCHAR(30),
    "UNIT_PRICE"                 NUMERIC(19, 8),
    "REQUEST_DELIVERY_DATE"      TIMESTAMP,
    "DATA_STATUS"                VARCHAR(10),
    "PURCHASE_SALES_CONTRACT_NO" VARCHAR(60),
    "NOTE"                       VARCHAR(200),
    "CONFIRM_TIME"               TIMESTAMP,
    "TRADE_CODE"                 VARCHAR(50),
    "SYS_ORG_CODE"               VARCHAR(100),
    "CREATE_BY"                  VARCHAR(50) NOT NULL,
    "CREATE_TIME"                TIMESTAMP   NOT NULL,
    "CREATE_BY_NAME"             VARCHAR(50),
    "UPDATE_BY"                  VARCHAR(50),
    "UPDATE_TIME"                TIMESTAMP,
    "UPDATE_BY_NAME"             VARCHAR(50),
    "EXTEND1"                    VARCHAR(200),
    "EXTEND2"                    VARCHAR(200),
    "EXTEND3"                    VARCHAR(200),
    "EXTEND4"                    VARCHAR(200),
    "EXTEND5"                    VARCHAR(200),
    "EXTEND6"                    VARCHAR(200),
    "EXTEND7"                    VARCHAR(200),
    "EXTEND8"                    VARCHAR(200),
    "EXTEND9"                    VARCHAR(200),
    "EXTEND10"                   VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST" is '订货通知表体表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."ID" is '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."PARENT_ID" is '上一个版本id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."HEAD_ID" is '表头sid';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."BUY_LIST_ID" is '购销合同表体关联ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."PRODUCT_NAME" is '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."PRODUCT_MODEL" is '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."SPECIFICATION" is '规格';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."WEIGHT" is '克重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."SUPPLIER" is '供应商';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."SUPPLIER_NAME" is '供应商名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."TRANSPORT_MODE" is '运输方式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."PORT" is '港口';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."QTY" is '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."UNIT" is '单位';
comment on column "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."UNIT_PRICE" is '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."REQUEST_DELIVERY_DATE" is '要求到货日';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."DATA_STATUS" is '单据状态 0编制 1确认 2作废';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."PURCHASE_SALES_CONTRACT_NO" is '购销合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."NOTE" is '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."CONFIRM_TIME" is '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."SYS_ORG_CODE" IS '部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_BY" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_BY_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."UPDATE_BY_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."EXTEND10" IS '扩展字段10';

--changeset xbxu1:2
CREATE TABLE If not exists  "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"
(
    "SID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "APPR_TYPE" VARCHAR(20),
    "STATUS" VARCHAR(2),
    "APPR_USER" VARCHAR(50),
    "APPR_DATE" TIMESTAMP(6),
    "APPR_NOTE" VARCHAR(255),
    "BILL_SID" VARCHAR(255),
    "INSERT_USER" VARCHAR(50),
    "INSERT_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UPDATE_USER" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "TRADE_CODE" VARCHAR(10),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(50),
    "AUDIT_TYPE" VARCHAR(1) DEFAULT '0',
    NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_AUDIT_INFO_01" ON "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"("BILL_SID" ASC) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_AUDIT_INFO_TRADECODE" ON "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"("TRADE_CODE" ASC) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_AEO_AUDIT_INFO" IS 'AEO审核配置信息';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."SID" IS '唯一键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."APPR_TYPE" IS '审批类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."STATUS" IS '审批状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."APPR_USER" IS '审核人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."APPR_DATE" IS '审批时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."APPR_NOTE" IS '审核意见';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."BILL_SID" IS '审批单据SID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."INSERT_USER" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."INSERT_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."TRADE_CODE" IS '企业代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."INSERT_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_AEO_AUDIT_INFO"."AUDIT_TYPE" IS '0 网页端审核 1 手机端审核';


--changeset jzrong:3
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT" ADD COLUMN "BILL_ID" VARCHAR(30);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."BILL_ID" IS '结算单据号';
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT" MODIFY "BILL_ID" DEFAULT (TO_CHAR(SYSTIMESTAMP, 'YYYYMMDDHH24MISSFF')) NULL;

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"
(
    "SID" VARCHAR(40 CHAR) NOT NULL,
"BASE_URL" VARCHAR(200 CHAR) NOT NULL,
"SERVICE_URL" VARCHAR(200 CHAR) NOT NULL,
"TOKEN" VARCHAR(100 CHAR),
"TYPE" VARCHAR(100 CHAR) NOT NULL,
"EXTEND_FILED1" VARCHAR(1000 CHAR),
"EXTEND_FILED2" VARCHAR(100 CHAR),
"EXTEND_FILED3" VARCHAR(100 CHAR),
"NOTE" VARCHAR(200 CHAR),
"TRADE_CODE" VARCHAR(50 CHAR) NOT NULL,
"INSERT_USER" VARCHAR(50 CHAR) NOT NULL,
"INSERT_TIME" TIMESTAMP(6),
"UPDATE_USER" VARCHAR(50 CHAR),
"UPDATE_TIME" TIMESTAMP(6),
"INSERT_USER_NAME" VARCHAR(50 CHAR),
"UPDATE_USER_NAME" VARCHAR(50 CHAR),
CONSTRAINT "T_GWSTD_HTTP_CONFIG_PKEY" NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE UNIQUE  INDEX "BIZ_TOBACOO"."IDX_HTTP_CONFIG_U1" ON "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"("TRADE_CODE" ASC,"TYPE" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."SID" IS '主键';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."BASE_URL" IS 'Http接口基地址(如:http://ip:port/)';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."SERVICE_URL" IS 'Http接口服务地址';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."TOKEN" IS 'TOKEN参数';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."TYPE" IS '接口类型';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."EXTEND_FILED1" IS '备用字段1(预留)';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."EXTEND_FILED2" IS '备用字段2(预留)';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."EXTEND_FILED3" IS '备用字段3(预留)';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."NOTE" IS '备注';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."TRADE_CODE" IS '企业代码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."INSERT_USER" IS '创建人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."INSERT_TIME" IS '创建日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."UPDATE_USER" IS '修改人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."UPDATE_TIME" IS '修改时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."INSERT_USER_NAME" IS '制单人姓名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG"."UPDATE_USER_NAME" IS '修改人姓名';

INSERT INTO BIZ_TOBACOO.t_gwstd_http_config (sid, base_url, service_url, token, type, extend_filed1, extend_filed2,
                                       extend_filed3, note, trade_code, insert_user, insert_time, update_user,
                                       update_time, insert_user_name, update_user_name)
VALUES ('95ec1f96-04e7-4227-1111-57dedf321f11', '', '1', null, 'YonyouState', null, null, null, '1：启用 0：不启用',
        '9999999999', 'SYSTEM', '2024-01-09 19:40:51.000000', null, null, null, null);


--changeset xbxu1:3
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"
(
    "ID" VARCHAR(64) NOT NULL,
    "SID" VARCHAR(64) NOT NULL,
    "FORM_TYPE" VARCHAR(32) DEFAULT 'default' NOT NULL,
    "FIELD_MARKINGS" CLOB,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UPDATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "CREATE_USER" VARCHAR(64),
    "UPDATE_USER" VARCHAR(64),
    "REMARK" VARCHAR(500),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_FIELD_MARKING_SID" ON "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"("SID" ASC) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_FIELD_MARKING_FORM_TYPE" ON "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"("FORM_TYPE" ASC) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
CREATE OR REPLACE UNIQUE  INDEX "BIZ_TOBACOO"."UK_FIELD_MARKING_SID_FORM" ON "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"("SID" ASC,"FORM_TYPE" ASC) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING" IS '表单字段标记信息表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."SID" IS '表单记录ID，关联具体的业务记录';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."FORM_TYPE" IS '表单类型，用于区分不同的表单页面';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."FIELD_MARKINGS" IS '字段标记数据，JSON格式存储fieldMarkings.value的完整内容';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."CREATE_TIME" IS '记录创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."UPDATE_TIME" IS '记录更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."CREATE_USER" IS '创建用户ID或用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."UPDATE_USER" IS '更新用户ID或用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_FIELD_MARKING"."REMARK" IS '备注信息，可用于存储额外的标记说明';


--changeset xbxu1:4
alter table BIZ_TOBACOO.T_AEO_AUDIT_INFO
    modify STATUS varchar(50);