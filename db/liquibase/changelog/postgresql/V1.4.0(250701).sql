--liquibase formatted sql

--changeset xbxu1:
-- 1）贸易国别（长度 60 × 2 = 120）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "TRADE_COUNTRY" VARCHAR(120);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."TRADE_COUNTRY" IS '贸易国别';

-- 2）装运人 SHIPPER（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "SHIPPER" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."SHIPPER" IS '装运人';

-- 3）收货人 CONSIGNEE（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONSIGNEE" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONSIGNEE" IS '收货人';

-- 4）通知人 NOTIFY PARTY（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "NOTIFY_PARTY" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."NOTIFY_PARTY" IS '通知人';

-- 5）仓储地址（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "WAREHOUSE_ADDRESS" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."WAREHOUSE_ADDRESS" IS '仓储地址';

-- 6）联系人（长度 20 × 2 = 40）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONTACT_PERSON" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONTACT_PERSON" IS '联系人';

-- 7）联系电话（长度 20 × 2 = 40）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONTACT_PHONE" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONTACT_PHONE" IS '联系电话';

--changeset ycmeng:1
CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"
(
    "ID" VARCHAR(80) NOT NULL,
    "PLAN_NO" VARCHAR(120),
    "BUSINESS_TYPE" VARCHAR(120),
    "CONTRACT_NO" VARCHAR(120),
    "BUSINESS_LOCATION" VARCHAR(40),
    "BUYER" VARCHAR(400),
    "SELLER" VARCHAR(400),
    "MANUFACTURER" VARCHAR(400),
    "DOMESTIC_CLIENT" VARCHAR(400),
    "EST_RECEIVE_DATE" TIMESTAMP(6),
    "RECEIVE_STATUS" VARCHAR(40),
    "EST_PAYMENT_DATE" TIMESTAMP(6),
    "PAYMENT_STATUS" VARCHAR(40),
    "EST_ARBITRATION_DATE" TIMESTAMP(6),
    "ARBITRATION_STATUS" VARCHAR(40),
    "EST_LICENSE_DATE" TIMESTAMP(6),
    "LICENSE_STATUS" VARCHAR(40),
    "EST_TRANSPORT_CERT_DATE" TIMESTAMP(6),
    "TRANSPORT_CERT_STATUS" VARCHAR(40),
    "EST_INSURANCE_DATE" TIMESTAMP(6),
    "INSURANCE_STATUS" VARCHAR(40),
    "EST_PACKING_INFO" VARCHAR(400),
    "LICENSE_NO" VARCHAR(120),
    "LICENSE_APPLY_DATE" TIMESTAMP(6),
    "LICENSE_VALIDITY_DATE" TIMESTAMP(6),
    "LICENSE_REMARK" VARCHAR(400),
    "ENTRY_STATUS" VARCHAR(40),
    "REMARK" VARCHAR(400),
    "STATUS" VARCHAR(20),
    "APPR_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "CREATE_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    CONSTRAINT "PK_T_BIZ_I_EQUIPMENT_PLAN_HEAD" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD" IS '进口烟机设备业务表';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."ID" IS '主键';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."PLAN_NO" IS '计划书单号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."BUSINESS_TYPE" IS '业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."CONTRACT_NO" IS '合同号（弹窗选择，允许修改）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."BUSINESS_LOCATION" IS '业务地点（从外商合同带入，不允许修改）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."BUYER" IS '买家（从外商合同带入，不允许修改）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."SELLER" IS '卖家（从外商合同带入，不允许修改）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."MANUFACTURER" IS '使用厂家（从外商合同带入）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."DOMESTIC_CLIENT" IS '国内委托方（从外商合同带入）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EST_RECEIVE_DATE" IS '预计收款时间（用户录入，当收款状态为未收款时触发预警）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."RECEIVE_STATUS" IS '收款状态（0未收款，1已收款。默认未收款）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EST_PAYMENT_DATE" IS '预计付款时间（用户录入，当付款状态为未付款时触发预警）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."PAYMENT_STATUS" IS '付款状态（0未付款，1已付款。默认未付款）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EST_ARBITRATION_DATE" IS '预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."ARBITRATION_STATUS" IS '预裁定状态（0未裁定，1已裁定。默认未裁定）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EST_LICENSE_DATE" IS '预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."LICENSE_STATUS" IS '许可证状态（0未办理，1已办理。默认未办理）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EST_TRANSPORT_CERT_DATE" IS '预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."TRANSPORT_CERT_STATUS" IS '准运证状态（0未办理，1已办理。默认未办理）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EST_INSURANCE_DATE" IS '预计保险申办时间（用户录入，当保险状态为未办理时触发预警）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."INSURANCE_STATUS" IS '保险状态（0未办理，1已办理。默认未办理）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EST_PACKING_INFO" IS '预计装箱信息（用户录入，预估箱型及箱数）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."LICENSE_NO" IS '许可证号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."LICENSE_APPLY_DATE" IS '许可证申请日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."LICENSE_VALIDITY_DATE" IS '许可证有效期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."LICENSE_REMARK" IS '许可证备注';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."ENTRY_STATUS" IS '报关状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."REMARK" IS '备注（用户录入）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."STATUS" IS '数据状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."APPR_STATUS" IS '审批状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."CONFIRM_TIME" IS '确认时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."VERSION_NO" IS '版本号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."TRADE_CODE" IS '企业编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."SYS_ORG_CODE" IS '创建人部门编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."PARENT_ID" IS '父级ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."UPDATE_BY" IS '最后修改人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."UPDATE_TIME" IS '最后修改时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."CREATE_USER_NAME" IS '创建人姓名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."UPDATE_USER_NAME" IS '最后修改人姓名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND1" IS '扩展字段1';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND2" IS '扩展字段2';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND3" IS '扩展字段3';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND4" IS '扩展字段4';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND5" IS '扩展字段5';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND6" IS '扩展字段6';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND7" IS '扩展字段7';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND8" IS '扩展字段8';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND9" IS '扩展字段9';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_HEAD"."EXTEND10" IS '扩展字段10';

CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "HEAD_ID" VARCHAR(80) NOT NULL,
    "PARENT_ID" VARCHAR(80) NOT NULL,
    "PRODUCT_NAME" VARCHAR(200),
    "PRODUCT_MODEL" VARCHAR(100),
    "UNIT" VARCHAR(40),
    "QUANTITY" DECIMAL(20,4),
    "UNIT_PRICE" DECIMAL(20,4),
    "AMOUNT" DECIMAL(20,4),
    "EST_DELIVERY_DATE" TIMESTAMP(6),
    "REMARK" VARCHAR(400),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "CREATE_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    CONSTRAINT "PK_T_BIZ_I_EQUIPMENT_PLAN_LIST" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST" IS '进口烟机设备业务明细表';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."ID" IS '主键';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."HEAD_ID" IS '关联主表ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."PARENT_ID" IS '关联外商合同ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."PRODUCT_NAME" IS '商品名称';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."PRODUCT_MODEL" IS '产品型号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."UNIT" IS '单位';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."QUANTITY" IS '数量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."UNIT_PRICE" IS '单价';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."AMOUNT" IS '金额';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EST_DELIVERY_DATE" IS '预计交货日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."REMARK" IS '备注';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."TRADE_CODE" IS '企业编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."SYS_ORG_CODE" IS '创建人部门编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."UPDATE_BY" IS '最后修改人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."UPDATE_TIME" IS '最后修改时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."CREATE_USER_NAME" IS '创建人姓名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."UPDATE_USER_NAME" IS '最后修改人姓名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND1" IS '扩展字段1';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND2" IS '扩展字段2';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND3" IS '扩展字段3';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND4" IS '扩展字段4';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND5" IS '扩展字段5';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND6" IS '扩展字段6';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND7" IS '扩展字段7';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND8" IS '扩展字段8';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND9" IS '扩展字段9';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_LIST"."EXTEND10" IS '扩展字段10';












--changeset hrfan1:
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "PERMIT_NUMBER" VARCHAR(200),
    "ARRIVAL_DATE" TIMESTAMP(6),
    "ENTRY_NO" VARCHAR(36),
    "ENTRY_DATE" TIMESTAMP(6),
    "RELEASE_DATE" TIMESTAMP(6),
    "NOTE" VARCHAR(400),
    "HEAD_ID" VARCHAR(100),
    CONSTRAINT "PK_T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT" IS '进货管理-';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."PERMIT_NUMBER" IS '准运证编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ARRIVAL_DATE" IS '到货日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ENTRY_NO" IS '报关单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ENTRY_DATE" IS '申报日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."RELEASE_DATE" IS '放行日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."HEAD_ID" IS '进货单表头ID';



CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "CONTRACT_NO" VARCHAR(120),
    "PURCHASE_NO" VARCHAR(120),
    "CUSTOMER" VARCHAR(400),
    "SUPPLIER" VARCHAR(400),
    "INVOICE_NO" VARCHAR(120),
    "PORT_OF_DEPARTURE" VARCHAR(100),
    "DESTINATION" VARCHAR(100),
    "PAYMENT_METHOD" VARCHAR(40),
    "PRICE_TERM" VARCHAR(20),
    "PRICE_TERM_PORT" VARCHAR(40),
    "VESSEL_VOYAGE" VARCHAR(200),
    "SAILING_DATE" TIMESTAMP(6),
    "EXPECTED_ARRIVAL_DATE" TIMESTAMP(6),
    "SALES_DATE" TIMESTAMP(6),
    "CONTRACT_AMOUNT" NUMERIC(19,4),
    "INSURANCE_RATE" NUMERIC(19,4),
    "INSURANCE_MARKUP" NUMERIC(19,4),
    "DOCUMENT_CREATOR" VARCHAR(20),
    "DOCUMENT_DATE" TIMESTAMP(6),
    "DOCUMENT_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPROVAL_STATUS" VARCHAR(20),
    "DATE_OF_CONTRACT" TIMESTAMP(6),
    "IS_NEXT" VARCHAR(1) DEFAULT '0',
    "PURCHASE_CONTRACT_NO" VARCHAR(120),
    "ENTRY_NO" VARCHAR(200),
    "ENTRY_DATE" TIMESTAMP(6),
    "SERIAL_NO" INTEGER,
    "SALES_DOCUMENT_STATUS" VARCHAR(50),
    "SELL_CONTRACT_NO" VARCHAR(120),
    "CANCEL_DATE" TIMESTAMP(6),
    "NOTE" VARCHAR(400),
    "CONTRACT_HEAD_ID" VARCHAR(80),
    CONSTRAINT "PK_T_BIZ_SMOKE_MACHINE_INCOMING__GOODS_HEAD" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD" IS '（3）烟机设备-进货单-表头数据';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PURCHASE_NO" IS '进货单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SUPPLIER" IS '供应商';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INVOICE_NO" IS '发票号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PORT_OF_DEPARTURE" IS '启运港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DESTINATION" IS '目的地/港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PAYMENT_METHOD" IS '付款方式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PRICE_TERM" IS '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PRICE_TERM_PORT" IS '价格条款对应港口';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."VESSEL_VOYAGE" IS '船名航次';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SAILING_DATE" IS '开航日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXPECTED_ARRIVAL_DATE" IS '预计到达日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SALES_DATE" IS '做销日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONTRACT_AMOUNT" IS '合同金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INSURANCE_RATE" IS '保险费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INSURANCE_MARKUP" IS '投保加成%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DOCUMENT_CREATOR" IS '制单人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DOCUMENT_DATE" IS '制单日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DOCUMENT_STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."APPROVAL_STATUS" IS '审批状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DATE_OF_CONTRACT" IS '签约日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."IS_NEXT" IS '是否流入下一个节点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PURCHASE_CONTRACT_NO" IS '购销合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."ENTRY_NO" IS '报关单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."ENTRY_DATE" IS '报关日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SERIAL_NO" IS '序号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SALES_DOCUMENT_STATUS" IS '销售状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SELL_CONTRACT_NO" IS '购销合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CANCEL_DATE" IS '作销日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONTRACT_HEAD_ID" IS '外商合同表头SID';



CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "GOODS_NAME" VARCHAR(160),
    "NOTE" VARCHAR(500),
    "PRODUCT_MODEL" VARCHAR(200),
    "QUANTITY" NUMERIC(19,4),
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "DELIVERY_DATE" TIMESTAMP(6),
    "TOTAL_USD" NUMERIC(19,4),
    "REMARKS" VARCHAR(400),
    "HEAD_ID" VARCHAR(40),
    "IN_QUANTITY" NUMERIC(19,6),
    "IN_UNIT" VARCHAR(60),
    "CURR" VARCHAR(40),
    "INVOICE_NO" VARCHAR(60),
    "CONTRACT_LIST_ID" VARCHAR(80),
    CONSTRAINT "PK_T_BIZT_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST" IS '进过管理-表体列表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."GOODS_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."PRODUCT_MODEL" IS '规格';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."QUANTITY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."DELIVERY_DATE" IS '交货日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."TOTAL_USD" IS '总价折美元';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."REMARKS" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."HEAD_ID" IS '表头HEAD_ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."IN_QUANTITY" IS '进口数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."IN_UNIT" IS '进口单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."INVOICE_NO" IS '进口发票号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CONTRACT_LIST_ID" IS '合同表体的ID';




CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "HEAD_ID" VARCHAR(100),
    "CODE_NO" VARCHAR(120),
    "INSURANCE_COMPANY" VARCHAR(400),
    "INSURED_PERSON" VARCHAR(400),
    "INVOICE_TITLE" VARCHAR(400),
    "TRANSPORT_NAME" VARCHAR(400),
    "DEPARTURE_DATE" TIMESTAMP(6),
    "ROUTE_FROM" VARCHAR(400),
    "ROUTE_VIA" VARCHAR(400),
    "ROUTE_TO" VARCHAR(400),
    "INSURANCE_TYPE" VARCHAR(400),
    "CURRENCY" VARCHAR(20),
    "INSURANCE_PREMIUM_RATE" NUMBER(19,4),
    "INSURANCE_AMOUNT" NUMBER(19,4),
    "INSURANCE_RATE" NUMBER(19,4),
    "PREMIUM" NUMBER(19,2),
    "INSURANCE_DATE" TIMESTAMP(6),
    "REMARK" VARCHAR(1000),
    CONSTRAINT "PK_T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB" IS '进货单-投保信息';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."HEAD_ID" IS '表头ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CODE_NO" IS '编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_COMPANY" IS '保险公司';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURED_PERSON" IS '被保险人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INVOICE_TITLE" IS '发票抬头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."TRANSPORT_NAME" IS '运输工具名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."DEPARTURE_DATE" IS '开航日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ROUTE_FROM" IS '运输路线自';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ROUTE_VIA" IS '经';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ROUTE_TO" IS '至';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_TYPE" IS '投保险别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CURRENCY" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_PREMIUM_RATE" IS '投保加成%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_AMOUNT" IS '保险金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_RATE" IS '保险费率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."PREMIUM" IS '保费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_DATE" IS '投保日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."REMARK" IS '备注';

--xbxu1:2
CREATE TABLE if not exists "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(60),
    "TRADE_CODE" VARCHAR(10),
    "SYS_ORG_CODE" VARCHAR(10),
    "DATA_STATE" VARCHAR(10),
    "VERSION_NO" VARCHAR(10),
    "PARENT_ID" VARCHAR(40),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(50),
    "UPDATE_USER_NAME" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "CONTRACT_NO" VARCHAR(120),
    "BUSINESS_PLACE" VARCHAR(40),
    "AGREEMENT_TYPE" VARCHAR(40),
    "AGREEMENT_NO" VARCHAR(120),
    "CUSTOMER" VARCHAR(400),
    "SUPPLIER" VARCHAR(400),
    "SIGN_DATE" DATE,
    "SIGN_PLACE" VARCHAR(400),
    "CURRENCY" VARCHAR(20),
    "CONTRACT_AMOUNT" DECIMAL(19,4),
    "AGENCY_RATE" DECIMAL(19,2),
    "AGENCY_FEE" DECIMAL(19,2),
    "AGREEMENT_TERMS" VARCHAR(2000),
    "MAKE_BY" VARCHAR(20),
    "MAKE_DATE" TIMESTAMP(6),
    "BILL_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPROVAL_STATUS" VARCHAR(20),
    CONSTRAINT "PK_T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT" IS '烟机设备代理协议表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."TRADE_CODE" IS '业务单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."PARENT_ID" IS '父ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."CREATE_BY" IS '创建人账号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."UPDATE_BY" IS '修改人账号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."INSERT_USER_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."BUSINESS_PLACE" IS '业务地点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."AGREEMENT_TYPE" IS '协议类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."AGREEMENT_NO" IS '协议编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."SUPPLIER" IS '供应商';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."SIGN_DATE" IS '签约日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."SIGN_PLACE" IS '签约地点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."CURRENCY" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."CONTRACT_AMOUNT" IS '合同金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."AGENCY_RATE" IS '代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."AGENCY_FEE" IS '代理费用';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."AGREEMENT_TERMS" IS '协议条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."MAKE_BY" IS '制单人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."MAKE_DATE" IS '制单日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."BILL_STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT"."APPROVAL_STATUS" IS '审批状态';

CREATE TABLE if not exists "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(60),
    "TRADE_CODE" VARCHAR(10),
    "SYS_ORG_CODE" VARCHAR(10),
    "DATA_STATE" VARCHAR(10),
    "VERSION_NO" VARCHAR(10),
    "PARENT_ID" VARCHAR(40),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(50),
    "UPDATE_USER_NAME" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "PRODUCT_NAME" VARCHAR(160),
    "PRODUCT_MODEL" VARCHAR(200),
    "QUANTITY" DECIMAL(19,4),
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" DECIMAL(19,8),
    "TOTAL_AMOUNT" DECIMAL(19,4),
    "SHIP_DATE" DATE,
    "REMARK" VARCHAR(1000),
    "HEAD_ID" VARCHAR(50),
    CONSTRAINT "PK_T_BIZ_I_EQUIP_AGREE_ITEM" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST" IS '烟机设备代理协议明细表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."TRADE_CODE" IS '业务单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."PARENT_ID" IS '父ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."CREATE_BY" IS '创建人账号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."UPDATE_BY" IS '修改人账号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."INSERT_USER_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."PRODUCT_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."PRODUCT_MODEL" IS '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."QUANTITY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."TOTAL_AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."SHIP_DATE" IS '装运日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."REMARK" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_AGENT_AGREEMENT_LIST"."HEAD_ID" IS '表头ID';

CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"
(
    "ID" VARCHAR(40) NOT NULL,
    "HEAD_ID" VARCHAR(100) NOT NULL,
    "SERIAL_NO" VARCHAR(100),
    "PAYMENT_TYPE" VARCHAR(400) NOT NULL,
    "CONTRACT_AMOUNT" NUMERIC(19,6) NOT NULL,
    "EXCHANGE_RATE" NUMERIC(19,6) NOT NULL,
    "IMPORT_EXPORT_AGENT_RATE" NUMERIC(19,4) NOT NULL,
    "IMPORT_EXPORT_AGENT_FEE" NUMERIC(19,2) NOT NULL,
    "HEAD_OFFICE_AGENT_RATE" NUMERIC(19,4) NOT NULL,
    "HEAD_OFFICE_AGENT_FEE" NUMERIC(19,2) NOT NULL,
    "CHARGE_CONTAINER_COUNT" VARCHAR(100) NOT NULL,
    "CUSTOMS_CLEARANCE_FEE" NUMERIC(19,4),
    "CONTAINER_INSPECTION_FEE" NUMERIC(19,4),
    "FREIGHT_FORWARDING_FEE" NUMERIC(19,2) NOT NULL,
    "INSURANCE_RATE" NUMERIC(19,4),
    "INSURANCE_FEE" NUMERIC(19,2),
    "REMITTANCE_AMOUNT_RMB" NUMERIC(19,2) NOT NULL,
    "REMARK" VARCHAR(400),
    "STATUS" VARCHAR(40),
    "VERSION_NO" VARCHAR(40),
    "PARENT_ID" VARCHAR(40),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "CREATE_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "SYS_ORG_CODE" VARCHAR(40),
    "TRADE_CODE" VARCHAR(40) NOT NULL,
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    CONSTRAINT "PK_T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY" IS '进口进货计划书划款通知表';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."ID" IS '主键';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."HEAD_ID" IS '表头ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."PAYMENT_TYPE" IS '款项类型（多复选框）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."CONTRACT_AMOUNT" IS '合同金额';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXCHANGE_RATE" IS '汇率';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."IMPORT_EXPORT_AGENT_RATE" IS '进出口公司代理费率%';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."IMPORT_EXPORT_AGENT_FEE" IS '进出口公司代理费';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."HEAD_OFFICE_AGENT_RATE" IS '总公司代理费率%';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."HEAD_OFFICE_AGENT_FEE" IS '总公司代理费';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."CHARGE_CONTAINER_COUNT" IS '计费箱数';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."CUSTOMS_CLEARANCE_FEE" IS '通关费';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."CONTAINER_INSPECTION_FEE" IS '验柜服务费';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."FREIGHT_FORWARDING_FEE" IS '货代费用';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."INSURANCE_RATE" IS '保险费率';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."INSURANCE_FEE" IS '保险费';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."REMITTANCE_AMOUNT_RMB" IS '划款金额（RMB）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."REMARK" IS '备注';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."STATUS" IS '数据状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."VERSION_NO" IS '版本号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."PARENT_ID" IS '父ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."UPDATE_BY" IS '最后修改人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."UPDATE_TIME" IS '最后修改时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."CREATE_USER_NAME" IS '创建人姓名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."UPDATE_USER_NAME" IS '最后修改人姓名';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."SYS_ORG_CODE" IS '创建人部门编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."TRADE_CODE" IS '企业编码';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND1" IS '扩展字段1';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND2" IS '扩展字段2';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND3" IS '扩展字段3';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND4" IS '扩展字段4';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND5" IS '扩展字段5';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND6" IS '扩展字段6';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND7" IS '扩展字段7';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND8" IS '扩展字段8';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND9" IS '扩展字段9';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY"."EXTEND10" IS '扩展字段10';


-- changeset bhyue:1
DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"
(
    "ID"                       VARCHAR(50) PRIMARY KEY,
    "PREV_VERSION_ID"          VARCHAR(50),
    "BUSINESS_TYPE"            VARCHAR(60),
    "CONTRACT_NO"              VARCHAR(60),
    "BUSINESS_PLACE"           VARCHAR(20),
    "BUYER"                    VARCHAR(200),
    "SELLER"                   VARCHAR(200),
    "USING_MANUFACTURER"       VARCHAR(200),
    "DOMESTIC_CLIENT"          VARCHAR(200),
    "SIGN_DATE"                TIMESTAMP,
    "SIGN_PLACE_CN"            VARCHAR(200),
    "SIGN_PLACE_EN"            VARCHAR(200),
    "CONTRACT_EFFECTIVE_DATE"  TIMESTAMP,
    "CONTRACT_VALIDITY_DATE"   TIMESTAMP,
    "TRANSPORT_MODE"           VARCHAR(10),
    "SHIPPING_PORT"            VARCHAR(50),
    "DEST_PORT"                VARCHAR(50),
    "CUSTOMS_DECLARATION_PORT" VARCHAR(50),
    "PAYMENT_METHOD"           VARCHAR(20),
    "CURR"                     VARCHAR(10),
    "PRICE_TERM"               VARCHAR(30),
    "PRICE_TERM_PORT"          VARCHAR(20),
    "SUGGEST_AUTHOR_SIGNATORY" VARCHAR(50),
    "SHORT_OVERFLOW_NUMBER"    NUMERIC(9, 4),
    "NOTE"                     VARCHAR(200),
    "VERSION_NO"               VARCHAR(10),
    "DOCUMENT_MAKER"           VARCHAR(20),
    "DOCUMENT_MAKER_NO"        VARCHAR(30),
    "DOCUMENT_MAKE_DATE"       TIMESTAMP,
    "DATA_STATUS"              VARCHAR(10),
    "CONFIRM_TIME"             TIMESTAMP,
    "APPR_STATUS"              VARCHAR(10),
    "TRADE_CODE"               VARCHAR(50),
    "SYS_ORG_CODE"             VARCHAR(100),
    "CREATE_BY"                VARCHAR(50) NOT NULL,
    "CREATE_TIME"              TIMESTAMP   NOT NULL,
    "CREATE_BY_NAME"           VARCHAR(50),
    "UPDATE_BY"                VARCHAR(50),
    "UPDATE_TIME"              TIMESTAMP,
    "UPDATE_BY_NAME"           VARCHAR(50),
    "EXTEND1"                  VARCHAR(200),
    "EXTEND2"                  VARCHAR(200),
    "EXTEND3"                  VARCHAR(200),
    "EXTEND4"                  VARCHAR(200),
    "EXTEND5"                  VARCHAR(200),
    "EXTEND6"                  VARCHAR(200),
    "EXTEND7"                  VARCHAR(200),
    "EXTEND8"                  VARCHAR(200),
    "EXTEND9"                  VARCHAR(200),
    "EXTEND10"                 VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD" is '国营贸易进口烟机设备——外商合同表头表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."ID" is '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."PREV_VERSION_ID" is '上一版本ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."BUSINESS_TYPE" is '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CONTRACT_NO" is '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."BUSINESS_PLACE" is '业务地点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."BUYER" is '买家';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SELLER" is '卖家';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."USING_MANUFACTURER" is '使用厂家';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."DOMESTIC_CLIENT" is '国内委托方';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SIGN_DATE" is '签约日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SIGN_PLACE_CN" is '签约地点(中文)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SIGN_PLACE_EN" is '签约地点(英文)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CONTRACT_EFFECTIVE_DATE" is '合同生效期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CONTRACT_VALIDITY_DATE" is '合同有效期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."TRANSPORT_MODE" is '运输方式 0海运 1空运 2陆运';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SHIPPING_PORT" is '装运港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."DEST_PORT" is '目的港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CUSTOMS_DECLARATION_PORT" is '报关口岸';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."PAYMENT_METHOD" is '付款方式 0付款交单 1即期信用证 2电汇 3预付款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CURR" is '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."PRICE_TERM" is '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."PRICE_TERM_PORT" is '价格条款对应港口 0起运港 1目的港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SUGGEST_AUTHOR_SIGNATORY" is '建议授权签约人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SHORT_OVERFLOW_NUMBER" is '短溢数%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."NOTE" is '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."VERSION_NO" is '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."DOCUMENT_MAKER" is '制单人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."DOCUMENT_MAKER_NO" is '制单人编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."DOCUMENT_MAKE_DATE" is '制单日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."DATA_STATUS" is '单据状态 0编制 1确认 2作废';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CONFIRM_TIME" is '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."APPR_STATUS" is '审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."SYS_ORG_CODE" IS '部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CREATE_BY" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CREATE_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."CREATE_BY_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."UPDATE_BY_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD"."EXTEND10" IS '扩展字段10';


DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"
(
    "ID"                      VARCHAR(50) PRIMARY KEY,
    "PREV_VERSION_ID"         VARCHAR(50),
    "HEAD_ID"                 VARCHAR(50),
    "G_NAME"                  VARCHAR(80),
    "G_MODEL"                 VARCHAR(100),
    "QTY"                     NUMERIC(23, 4),
    "UNIT"                    VARCHAR(30),
    "UNIT_PRICE"              NUMERIC(27, 8),
    "MONEY_AMOUNT"            NUMERIC(23, 4),
    "DELIVERY_DATE"           TIMESTAMP,
    "CONVERTED_TOTAL_DOLLARS" NUMERIC(23, 4),
    "NOTE"                    VARCHAR(500),
    "DATA_STATUS"             VARCHAR(10),
    "CONFIRM_TIME"            TIMESTAMP,
    "TRADE_CODE"              VARCHAR(50),
    "SYS_ORG_CODE"            VARCHAR(100),
    "CREATE_BY"               VARCHAR(50) NOT NULL,
    "CREATE_TIME"             TIMESTAMP   NOT NULL,
    "CREATE_BY_NAME"          VARCHAR(50),
    "UPDATE_BY"               VARCHAR(50),
    "UPDATE_TIME"             TIMESTAMP,
    "UPDATE_BY_NAME"          VARCHAR(50),
    "EXTEND1"                 VARCHAR(200),
    "EXTEND2"                 VARCHAR(200),
    "EXTEND3"                 VARCHAR(200),
    "EXTEND4"                 VARCHAR(200),
    "EXTEND5"                 VARCHAR(200),
    "EXTEND6"                 VARCHAR(200),
    "EXTEND7"                 VARCHAR(200),
    "EXTEND8"                 VARCHAR(200),
    "EXTEND9"                 VARCHAR(200),
    "EXTEND10"                VARCHAR(200)
);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST" is '国营贸易进口烟机设备——外商合同表体表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."ID" is '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."PREV_VERSION_ID" is '上一个版本id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."HEAD_ID" is '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."G_NAME" is '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."G_MODEL" is '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."QTY" is '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."UNIT" is '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."UNIT_PRICE" is '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."MONEY_AMOUNT" is '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."DELIVERY_DATE" is '交货日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."CONVERTED_TOTAL_DOLLARS" is '总价折美元';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."NOTE" is '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."DATA_STATUS" is '单据状态 0编制 1确认 2作废';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."CONFIRM_TIME" is '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."SYS_ORG_CODE" IS '部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."CREATE_BY" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."CREATE_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."CREATE_BY_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."UPDATE_BY_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST"."EXTEND10" IS '扩展字段10';