--liquibase formatted sql

--changeset xbxu1:
CREATE TABLE IF NOT EXISTS  "T_BIZ_NON_INCOMING_GOODS_HEAD"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "CONTRACT_NO" VARCHAR(120),
    "PURCHASE_NO" VARCHAR(120),
    "CURR" VARCHAR(50),
    "CUSTOMER" VARCHAR(400),
    "SUPPLIER" VARCHAR(400),
    "INVOICE_NO" VARCHAR(120),
    "PORT_OF_DEPARTURE" VARCHAR(100),
    "DESTINATION" VARCHAR(100),
    "PRICE_TERM" VARCHAR(40),
    "PRICE_TERM_PORT" VARCHAR(100),
    "VESSEL_VOYAGE" VARCHAR(200),
    "SAILING_DATE" TIMESTAMP(6),
    "SALES_DATE" TIMESTAMP(6),
    "DOCUMENT_CREATOR" VARCHAR(20),
    "DOCUMENT_DATE" TIMESTAMP(6),
    "DOCUMENT_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPROVAL_STATUS" VARCHAR(20),
    "DATE_OF_CONTRACT" TIMESTAMP(6),
    "IS_NEXT" VARCHAR(1) DEFAULT '0',
    "PURCHASE_CONTRACT_NO" VARCHAR(120),
    "ENTRY_NO" VARCHAR(400),
    "ENTRY_DATE" TIMESTAMP(6),
    "SEND_ENTRY" VARCHAR(20),
    "NOTE" VARCHAR(400),
    "NON_INCOMING_INVOICE_NO" VARCHAR(60),
    "HEAD_ID" VARCHAR(60),
    CONSTRAINT "PK_T_BIZ_NON_INCOMING_GOODS_HEAD" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "T_BIZ_NON_INCOMING_GOODS_HEAD" IS '非国营贸易辅料-进口管理';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."ID" IS '主键ID';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."BUSINESS_TYPE" IS '业务类型';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."DATA_STATE" IS '数据状态';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."VERSION_NO" IS '版本号';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."TRADE_CODE" IS '企业10位编码';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."SYS_ORG_CODE" IS '组织机构代码';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."PARENT_ID" IS '父级ID';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."INSERT_USER_NAME" IS '插入用户名';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."UPDATE_USER_NAME" IS '更新用户名';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND1" IS '扩展字段1';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND2" IS '扩展字段2';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND3" IS '扩展字段3';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND4" IS '扩展字段4';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND5" IS '扩展字段5';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND6" IS '扩展字段6';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND7" IS '扩展字段7';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND8" IS '扩展字段8';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND9" IS '扩展字段9';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."EXTEND10" IS '扩展字段10';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."CONTRACT_NO" IS '合同号';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."PURCHASE_NO" IS '进货单号';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."CURR" IS '币种';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."CUSTOMER" IS '客户';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."SUPPLIER" IS '供应商';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."INVOICE_NO" IS '发票号';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."PORT_OF_DEPARTURE" IS '启运港';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."DESTINATION" IS '目的地/港';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."PRICE_TERM" IS '价格条款';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."PRICE_TERM_PORT" IS '价格条款对应港口';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."VESSEL_VOYAGE" IS '船名航次';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."SAILING_DATE" IS '开航日期';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."SALES_DATE" IS '做销日期';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."DOCUMENT_CREATOR" IS '制单人';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."DOCUMENT_DATE" IS '制单日期';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."DOCUMENT_STATUS" IS '单据状态';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."CONFIRM_TIME" IS '确认时间';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."APPROVAL_STATUS" IS '审批状态';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."IS_NEXT" IS '是否流入下一个节点';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."ENTRY_NO" IS '报关单号';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."ENTRY_DATE" IS '报关日期';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."SEND_ENTRY" IS '发送报关';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."NOTE" IS '备注';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_HEAD"."NON_INCOMING_INVOICE_NO" IS '发票号码';


CREATE TABLE IF NOT EXISTS  "T_BIZ_NON_INCOMING_GOODS_LIST"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "GOODS_NAME" VARCHAR(160),
    "PRODUCT_MODEL" VARCHAR(200),
    "QUANTITY" NUMERIC(19,4),
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "DELIVERY_DATE" TIMESTAMP(6),
    "TOTAL_USD" NUMERIC(19,4),
    "REMARKS" VARCHAR(400),
    "HEAD_ID" VARCHAR(40),
    "IN_QUANTITY" NUMERIC(19,6),
    "IN_UNIT" VARCHAR(60),
    "CURR" VARCHAR(40),
    "INVOICE_NO" VARCHAR(60),
    "CONTRACT_LIST_ID" VARCHAR(80),
    CONSTRAINT "PK_T_BIZT_BIZ_NON_INCOMING_GOODS_LIST_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "T_BIZ_NON_INCOMING_GOODS_LIST" IS '非国营贸易辅料-进口管理-表体列表';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."ID" IS '主键ID';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."BUSINESS_TYPE" IS '业务类型';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."DATA_STATE" IS '数据状态';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."VERSION_NO" IS '版本号';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."TRADE_CODE" IS '企业10位编码';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."SYS_ORG_CODE" IS '组织机构代码';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."PARENT_ID" IS '父级ID';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."INSERT_USER_NAME" IS '插入用户名';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."UPDATE_USER_NAME" IS '更新用户名';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND1" IS '扩展字段1';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND2" IS '扩展字段2';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND3" IS '扩展字段3';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND4" IS '扩展字段4';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND5" IS '扩展字段5';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND6" IS '扩展字段6';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND7" IS '扩展字段7';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND8" IS '扩展字段8';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND9" IS '扩展字段9';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."EXTEND10" IS '扩展字段10';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."GOODS_NAME" IS '商品名称';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."PRODUCT_MODEL" IS '商品描述';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."QUANTITY" IS '数量';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."UNIT" IS '单位';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."UNIT_PRICE" IS '单价';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."AMOUNT" IS '金额';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."DELIVERY_DATE" IS '交货日期';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."TOTAL_USD" IS '总价折美元';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."REMARKS" IS '备注';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."HEAD_ID" IS '表头HEAD_ID';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."IN_QUANTITY" IS '进口数量';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."IN_UNIT" IS '进口单位';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."CURR" IS '币种';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."INVOICE_NO" IS '进口发票号';

COMMENT ON COLUMN "T_BIZ_NON_INCOMING_GOODS_LIST"."CONTRACT_LIST_ID" IS '合同表体的ID';



--changeset fhfang:1
-- alter table BIZ_TOBACOO.T_BIZ_NON_INCOMING_GOODS_HEAD add column NON_INCOMING_Invoice_No varchar (60);
-- comment on column BIZ_TOBACOO.T_BIZ_NON_INCOMING_GOODS_HEAD.NON_INCOMING_Invoice_No is '发票号码';
-- alter table BIZ_TOBACOO.T_BIZ_NON_INCOMING_GOODS_HEAD add column head_id varchar (60);

--changeset cblin:1
CREATE TABLE IF NOT EXISTS BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT (
    SID VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    CREATE_BY VARCHAR(50) NOT NULL,
    CREATE_TIME TIMESTAMP NOT NULL,
    CREATE_USER_NAME VARCHAR(50) NULL,
    UPDATE_BY VARCHAR(50) NULL,
    UPDATE_TIME TIMESTAMP NULL,
    UPDATE_USER_NAME VARCHAR(50) NULL,
    TRADE_CODE VARCHAR(50) NULL,
    SYS_ORG_CODE VARCHAR(50) NULL,
    EXTEND1 VARCHAR(200) NULL,
    EXTEND2 VARCHAR(200) NULL,
    EXTEND3 VARCHAR(200) NULL,
    EXTEND4 VARCHAR(200) NULL,
    EXTEND5 VARCHAR(200) NULL,
    EXTEND6 VARCHAR(200) NULL,
    EXTEND7 VARCHAR(200) NULL,
    EXTEND8 VARCHAR(200) NULL,
    EXTEND9 VARCHAR(200) NULL,
    EXTEND10 VARCHAR(200) NULL,
    BUSINESS_TYPE VARCHAR(120) NULL,
    ACCOUNT_NO VARCHAR(120) NULL,
    PURCHASE_ORDER_NO VARCHAR(120) NULL,
    CONTRACT_NO VARCHAR(120) NULL,
    CUSTOMER VARCHAR(400) NULL,
    CURR VARCHAR(20) NULL,
    EXCHANGE_RATE NUMERIC(19,6) NULL,
    GOODS_PRICE NUMERIC(19,5) NULL,
    AGENT_FEE_RATE NUMERIC(19,6) NULL,
    AGENT_FEE NUMERIC(19,2) NULL,
    AGENT_TAX_FEE NUMERIC(19,2) NULL,
    AGENT_FEE_TOTAL NUMERIC(19,2) NULL,
    BUSINESS_DATE TIMESTAMP NULL,
    G_NAME VARCHAR(160) NULL,
    SEND_FINANCE VARCHAR(20) NULL,
    PRODUCR_SOME VARCHAR(600) NULL,
    NOTE VARCHAR(400) NULL,
    RED_FLUSH VARCHAR(20) NULL,
    STATUS VARCHAR(20) NULL,
    APPR_STATUS VARCHAR(20) NULL,
    CONFIRM_TIME TIMESTAMP NULL,
    IS_CONFIRM VARCHAR(10) NULL,
    PURCHASE_MARK VARCHAR(20) NULL,
    PURCHASE_NO_MARK VARCHAR(600) NULL,
    VERSION_NO VARCHAR(100) NULL,
    CONSTRAINT T_BIZ_CUSTOMER_ACCOUNT_PK PRIMARY KEY (SID)
    );


COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.SID IS '主键';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CREATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CREATE_USER_NAME IS '创建人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.UPDATE_BY IS '创建人';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.UPDATE_TIME IS '最后修改时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.UPDATE_USER_NAME IS '最后修改人名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.TRADE_CODE IS '企业编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.SYS_ORG_CODE IS '创建人部门编码';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND1 IS '拓展字段1';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND2 IS '拓展字段2';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND3 IS '拓展字段3';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND4 IS '拓展字段4';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND5 IS '拓展字段5';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND6 IS '拓展字段6';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND7 IS '拓展字段7';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND8 IS '拓展字段8';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND9 IS '拓展字段9';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXTEND10 IS '拓展字段10';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.BUSINESS_TYPE IS '业务类型';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.ACCOUNT_NO IS '结算单号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PURCHASE_ORDER_NO  IS '进货单号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CONTRACT_NO IS '合同号';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CUSTOMER IS '客户';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CURR IS '币种';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.EXCHANGE_RATE IS '汇率';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.GOODS_PRICE IS '货款';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_FEE_RATE IS '代理费率%';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_FEE IS '代理费（不含税金额）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_TAX_FEE IS '代理费税额';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.AGENT_FEE_TOTAL IS '代理费（价税合计）';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.BUSINESS_DATE IS '业务日期';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.G_NAME IS '商品名称';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.SEND_FINANCE IS '发送财务系统';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PRODUCR_SOME IS '商品与数量';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.NOTE IS '备注';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.RED_FLUSH IS '是否红冲';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.STATUS IS '单据状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.APPR_STATUS IS '审核状态';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.CONFIRM_TIME IS '确认时间';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.IS_CONFIRM IS '是否确认';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PURCHASE_MARK IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.PURCHASE_NO_MARK IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN BIZ_TOBACOO.T_BIZ_CUSTOMER_ACCOUNT.VERSION_NO IS '版本号';


alter table BIZ_TOBACOO.T_BIZ_STORE_E_HEAD add if not exists red_flush varchar(10);
comment on column BIZ_TOBACOO.T_BIZ_STORE_E_HEAD.red_flush is '是否红冲';



--changeset ycmeng:1
CREATE TABLE "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "TRADE_CODE" VARCHAR(60),
    "BUSINESS_TYPE" VARCHAR(60),
    "CONTRACT_NO" VARCHAR(120),
    "BUYER" VARCHAR(400),
    "SUPPLIER" VARCHAR(400),
    "SIGNING_DATE" DATE,
    "DOMESTIC_PRINCIPAL" VARCHAR(400),
    "TRANSPORT_MODE" VARCHAR(20),
    "EFFECTIVE_DATE" DATE,
    "EXPIRY_DATE" DATE,
    "SIGNING_LOCATION_CN" VARCHAR(100),
    "SIGNING_LOCATION_EN" VARCHAR(100),
    "PORT_OF_LOADING" VARCHAR(100),
    "PORT_OF_DESTINATION" VARCHAR(100),
    "CUSTOMS_PORT" VARCHAR(100),
    "PAYMENT_METHOD" VARCHAR(40),
    "CURRENCY" VARCHAR(20),
    "PRICE_TERM" VARCHAR(40),
    "PRICE_TERM_PORT" VARCHAR(100),
    "SUGGESTED_SIGNER" VARCHAR(60),
    "SHORTAGE_OVERFLOW_PERCENT" DECIMAL(19,6),
    "REMARKS" VARCHAR(400),
    "VERSION_NO" VARCHAR(20),
    "STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPR_STATUS" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100),
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "PAYMENT_AMOUNT" NUMERIC(19,4),
    "CONTRACT_AMOUNT" NUMERIC(19,6),
    "EXCHANGE_RATE" NUMERIC(19,6),
    "TARIFF_RATE" NUMERIC(19,6),
    "TARIFF_AMOUNT" NUMERIC(19,2),
    "CONSUMPTION_TAX_RATE" NUMERIC(19,6),
    "CONSUMPTION_TAX_AMOUNT" NUMERIC(19,6),
    "VAT_RATE" NUMERIC(19,6),
    "VAT_AMOUNT" NUMERIC(19,6),
    "IMPORT_EXPORT_AGENCY_RATE" NUMERIC(19,4),
    "IMPORT_EXPORT_AGENCY_FEE" NUMERIC(19,2),
    "HEADQUARTERS_AGENCY_RATE" NUMERIC(19,4),
    "HEADQUARTERS_AGENCY_FEE" NUMERIC(19,2),
    "CONTRACT_QUANTITY" NUMERIC(19,4),
    "BILLING_WEIGHT" NUMERIC(19,0),
    "CUSTOMS_CLEARANCE_FEE" NUMERIC(19,4),
    "CONTAINER_INSPECTION_FEE" NUMERIC(19,4),
    "FREIGHT_FORWARDER_FEE" NUMERIC(19,2),
    "INSURANCE_RATE" NUMERIC(19,4),
    "INSURANCE_FEE" NUMERIC(19,2),
    "AGREEMENT_NO" VARCHAR(120),
    "AGREEMENT_SIGNING_DATE" TIMESTAMP(6),
    "AGREEMENT_AGENT_FEE_RATE" DECIMAL(19,6),
    "AGREEMENT_SUGGESTED_SIGNER" VARCHAR(30),
    "AGREEMENT_TERMS" VARCHAR(300),
    "AGREEMENT_REMARKS" VARCHAR(200),
    "IS_TRANSFER_NOTICE" VARCHAR(50),
    CONSTRAINT "PK_T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD" IS '合同协议表头表';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."TRADE_CODE" IS ' 企业编码';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."BUSINESS_TYPE" IS '业务类型';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CONTRACT_NO" IS '合同号';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."BUYER" IS '买方';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."SUPPLIER" IS '供应商';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."SIGNING_DATE" IS '签约日期';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."DOMESTIC_PRINCIPAL" IS '国内委托方';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."TRANSPORT_MODE" IS '运输方式';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."EFFECTIVE_DATE" IS '合同生效期';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."EXPIRY_DATE" IS '合同有效期';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."SIGNING_LOCATION_CN" IS '签约地点(中文)';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."SIGNING_LOCATION_EN" IS '签约地点(英文)';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."PORT_OF_LOADING" IS '装运港';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."PORT_OF_DESTINATION" IS '目的港';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CUSTOMS_PORT" IS '报关口岸';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."PAYMENT_METHOD" IS '付款方式';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CURRENCY" IS '币种';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."PRICE_TERM" IS '价格条款';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."PRICE_TERM_PORT" IS '价格条款对应港口';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."SUGGESTED_SIGNER" IS '建议授权签约人';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."SHORTAGE_OVERFLOW_PERCENT" IS '短溢数%';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."REMARKS" IS '备注';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."VERSION_NO" IS '版本号';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."STATUS" IS '单据状态';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CONFIRM_TIME" IS '确认时间';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."APPR_STATUS" IS '审批状态';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."SYS_ORG_CODE" IS '创建人部门编码';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CREATE_BY" IS '制单人';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CREATE_TIME" IS '制单日期';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."UPDATE_BY" IS '更新人';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."UPDATE_TIME" IS '更新时间';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."INSERT_USER_NAME" IS '插入用户名';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."UPDATE_USER_NAME" IS '更新用户名';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."PAYMENT_AMOUNT" IS '划款金额';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CONTRACT_AMOUNT" IS '合同金额';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."EXCHANGE_RATE" IS '汇率';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."TARIFF_RATE" IS '关税率%';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."TARIFF_AMOUNT" IS '关税金额';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CONSUMPTION_TAX_RATE" IS '消费税率';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CONSUMPTION_TAX_AMOUNT" IS '消费税金额';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."VAT_RATE" IS '增值税率%';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."VAT_AMOUNT" IS '增值税金额';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."IMPORT_EXPORT_AGENCY_RATE" IS '进出口公司代理费率%';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."IMPORT_EXPORT_AGENCY_FEE" IS '进出口公司代理费';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."HEADQUARTERS_AGENCY_RATE" IS '总公司代理费率%';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."HEADQUARTERS_AGENCY_FEE" IS '总公司代理费';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CONTRACT_QUANTITY" IS '合同数量';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."BILLING_WEIGHT" IS '计费重量（箱）';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CUSTOMS_CLEARANCE_FEE" IS '通关费';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."CONTAINER_INSPECTION_FEE" IS '验柜服务费';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."FREIGHT_FORWARDER_FEE" IS '货代费用';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."INSURANCE_RATE" IS '保险费率';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."INSURANCE_FEE" IS '保险费';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."AGREEMENT_NO" IS '协议编号（文本）';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."AGREEMENT_SIGNING_DATE" IS '签约日期（日期控件，协议签约日期）';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."AGREEMENT_AGENT_FEE_RATE" IS '代理费率（数值型，如0.05表示5%）';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."AGREEMENT_SUGGESTED_SIGNER" IS '建议授权签约人（文本，协议相关）';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."AGREEMENT_TERMS" IS '协议条款（文本，详细条款描述）';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."AGREEMENT_REMARKS" IS '协议备注（文本，协议相关补充说明）';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD"."IS_TRANSFER_NOTICE" IS '是否划款通知保存过(0否;1是)';




CREATE TABLE "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "HEAD_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "SYS_ORG_CODE" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "GOODS_NAME" VARCHAR(160),
    "GOODS_DESC" VARCHAR(200),
    "QTY" DECIMAL(19,6),
    "UNIT" VARCHAR(40),
    "UNIT_PRICE" DECIMAL(19,8),
    "AMOUNT" DECIMAL(19,4),
    "DELIVERY_DATE" TIMESTAMP(6),
    "REMARK" VARCHAR(400),
    "GOODS_CATEGORY" VARCHAR(160),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);

COMMENT ON TABLE "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST" IS '商品信息表';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."HEAD_ID" IS '表头ID';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."CREATE_BY" IS '创建人';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."CREATE_TIME" IS '创建时间';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."UPDATE_BY" IS ' 修改人';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."UPDATE_TIME" IS '修改时间';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."SYS_ORG_CODE" IS '创建人部门编码';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."TRADE_CODE" IS '企业编码';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."GOODS_NAME" IS '商品名称';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."GOODS_DESC" IS '商品描述';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."QTY" IS '数量';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."UNIT" IS '单位';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."UNIT_PRICE" IS '单价';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."AMOUNT" IS '金额';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."DELIVERY_DATE" IS '交货日期';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."REMARK" IS '备注';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."GOODS_CATEGORY" IS '商品类别';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."INSERT_USER_NAME" IS '插入用户名';

COMMENT ON COLUMN "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST"."UPDATE_USER_NAME" IS '更新用户名';

--发送报关HTTP配置
INSERT INTO "T_GWSTD_HTTP_CONFIG" ("SID", "BASE_URL", "SERVICE_URL", "TOKEN", "TYPE", "EXTEND_FILED1",
                                                 "EXTEND_FILED2", "EXTEND_FILED3", "NOTE", "TRADE_CODE", "INSERT_USER",
                                                 "INSERT_TIME", "UPDATE_USER", "UPDATE_TIME", "INSERT_USER_NAME",
                                                 "UPDATE_USER_NAME")
VALUES ('f34c1f96-04e7-4227-1111-57dedf321f11', 'http://localhost:9981',
        '/gw-edi-up/api/open/v1/erpDecE/insertErpDecEList', null, 'GW_ERP_I_DEC_LIST', null, null, null, null,
        '9999999999', 'SYSTEM', '2024-01-09 19:40:51', null, null, null, null);

INSERT INTO "T_GWSTD_HTTP_CONFIG" ("SID", "BASE_URL", "SERVICE_URL", "TOKEN", "TYPE", "EXTEND_FILED1",
                                                 "EXTEND_FILED2", "EXTEND_FILED3", "NOTE", "TRADE_CODE", "INSERT_USER",
                                                 "INSERT_TIME", "UPDATE_USER", "UPDATE_TIME", "INSERT_USER_NAME",
                                                 "UPDATE_USER_NAME")
VALUES ('vsdc1f96-04e7-4227-1111-57dedf321f11', 'http://**************', '/pms/api/session', null, 'GW_AUTH_TOKEN',
        'tobacoo001', 'zhgw@123', null, null, '9999999999', 'SYSTEM', '2024-01-09 19:40:51', null, null, null, null);
--changeset xbxu1:2
INSERT INTO "BIZ_TOBACOO"."T_GWSTD_HTTP_CONFIG" ("SID","BASE_URL","SERVICE_URL","TOKEN","TYPE","EXTEND_FILED1","EXTEND_FILED2","EXTEND_FILED3","NOTE","TRADE_CODE","INSERT_USER","INSERT_TIME","UPDATE_USER","UPDATE_TIME","INSERT_USER_NAME","UPDATE_USER_NAME" ) VALUES ('vsdc1f96-04e7-4227-1111-57dedf321f22','http://localhost:8080','/gwstd/api/v1/attached/uploadAttachments',null,'GW_ATTACH',null,null,null,null,'9999999999','SYSTEM','2024-01-09 19:40:51.000000',null,null,null,null);