--liquibase formatted sql

--changeset xbxu1:
create table IF NOT EXISTS T_BIZ_INSURANCE_TYPE
(
    SID         VARCHAR(50) not null PRIMARY KEY,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP(6) not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP(6),
    UPDATE_USER_NAME VARCHAR(50),
    PARAM_CODE VARCHAR(30),
    INSURANCE_TYPE_CN VARCHAR(200),
    INSURANCE_TYPE_EN VARCHAR(200),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    <PERSON>XTEND<PERSON> VARCHAR(200),
    EXTEND10 VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE is '箱型表';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.SID is '主键';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.TRADE_CODE is '企业编码';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.INSERT_USER is '插入人';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.INSERT_TIME is '插入时间';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.INSERT_USER_NAME is '插入人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.UPDATE_USER is '更新人';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.UPDATE_TIME is '更新时间';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.UPDATE_USER_NAME is '更新人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.PARAM_CODE is '参数代码';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.INSURANCE_TYPE_CN is '保险类别中文名称';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.INSURANCE_TYPE_EN is '保险类别英文名称';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.NOTE is '备注';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND1 is '备用1';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND2 is '备用2';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND3 is '备用3';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND4 is '备用4';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND5 is '备用5';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND6 is '备用6';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND7 is '备用7';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND8 is '备用8';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND9 is '备用9';

comment
    on column BIZ_TOBACOO.T_BIZ_INSURANCE_TYPE.EXTEND10 is '备用10';



--changeset cblin1:
--发送报关HTTP配置
INSERT INTO "T_GWSTD_HTTP_CONFIG" ("SID", "BASE_URL", "SERVICE_URL", "TOKEN", "TYPE", "EXTEND_FILED1",
                                   "EXTEND_FILED2", "EXTEND_FILED3", "NOTE", "TRADE_CODE", "INSERT_USER",
                                   "INSERT_TIME", "UPDATE_USER", "UPDATE_TIME", "INSERT_USER_NAME",
                                   "UPDATE_USER_NAME")
VALUES ('f34c1f96-04e7-4227-1111-57dedf321f12', 'http://localhost:9981',
        '/gw-edi-up/api/open/v1/erpDecE/insertErpDecEList', null, 'GW_ERP_E_DEC_LIST', null, null, null, null,
        '9999999999', 'SYSTEM', '2024-01-09 19:40:51', null, null, null, null);

-- changeset bhyue:1
-- 出货通知补充SQL
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."SYS_ORG_CODE" IS '部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."CREATE_BY_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."UPDATE_BY_NAME" IS '更新人名称';

ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"
    ADD COLUMN IF NOT EXISTS DOCUMENT_MAKE_TIME TIMESTAMP;
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"
    ADD COLUMN IF NOT EXISTS DOCUMENT_MAKER VARCHAR(50);
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"
    ADD COLUMN IF NOT EXISTS DOCUMENT_MAKER_NAME VARCHAR(50);

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."DOCUMENT_MAKE_TIME" is '制单时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."DOCUMENT_MAKER" is '制单人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."DOCUMENT_MAKER_NAME" is '制单人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_TIME" is '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_BY" is '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."CREATE_BY_NAME" is '创建人名称';

ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"
    ADD COLUMN IF NOT EXISTS PREV_VERSION_ID VARCHAR(50);
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"
    ADD COLUMN IF NOT EXISTS PREV_VERSION_ID VARCHAR(50);

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD"."PREV_VERSION_ID" is '上一版本ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST"."PREV_VERSION_ID" is '上一版本ID';




--changeset xcchen:1
CREATE TABLE  IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"
(
    "SID" VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "PRODUCT_NAME" VARCHAR(200),
    "PRODUCT_MODEL" VARCHAR(200),
    "UNIT" VARCHAR(40),
    "QTY" NUMERIC(19,6),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "NOTE" VARCHAR(1000),
    "CONTAINER_NUM" NUMERIC(19,6),
    "HEAD_ID" VARCHAR(50),
    "CONTRACT_LIST_ID" VARCHAR(50),
    "ANALYSE_LIST_ID" VARCHAR(50),
    "GROSS_WEIGHT" NUMERIC(19,4),
    "NET_WEIGHT" NUMERIC(19,4),
    NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."PRODUCT_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."PRODUCT_MODEL" IS '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."QTY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."CONTAINER_NUM" IS '箱数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."CONTRACT_LIST_ID" IS '外商合同表体id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."ANALYSE_LIST_ID" IS '分析单表体id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."GROSS_WEIGHT" IS '毛重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_LIST"."NET_WEIGHT" IS '净重';


CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"
(
    "SID" VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "CONTRACT_NO" VARCHAR(120),
    "PURCHASE_ORDER_NO" VARCHAR(120),
    "SUPPLIER" VARCHAR(400),
    "CUSTOMER" VARCHAR(400),
    "CUSTOMER_ADDRESS" VARCHAR(400),
    "TRADE_COUNTRY" VARCHAR(120),
    "BUSINESS_ENTERPRISE" VARCHAR(120),
    "PORT_OF_DESTINATION" VARCHAR(100),
    "PAYMENT_METHOD" VARCHAR(40),
    "CURR" VARCHAR(20),
    "TOTAL_AMOUNT" DECIMAL(19,4),
    "TRANSPORT_MODE" VARCHAR(100),
    "PRICE_TERM" VARCHAR(20),
    "PRICE_TERM_PORT" VARCHAR(40),
    "DELIVERY_ENTERPRISE" VARCHAR(120),
    "WRAP_TYPE" VARCHAR(200),
    "PACK_NUM" DECIMAL(19,4),
    "DELIVERY_ENTERPRISE_ADDRESS" VARCHAR(400),
    "TOTAL_NET_WT" DECIMAL(19,4),
    "TOTAL_GROSS_WT" DECIMAL(19,4),
    "TOTAL_TARE" DECIMAL(19,4),
    "SEND_DECLARE" VARCHAR(20),
    "BUSINESS_DATE" TIMESTAMP(6),
    "CONFIRM_TIME" TIMESTAMP(6),
    "IS_CONFIRM" VARCHAR(10),
    "IS_SAVE" VARCHAR(10),
    "NOTE" VARCHAR(1000),
    "STATUS" VARCHAR(20),
    "APPR_STATUS" VARCHAR(20),
    "SEND_FINANCE" VARCHAR(20),
    "RED_FLUSH" VARCHAR(20),
    "PURCHASE_MARK" VARCHAR(20),
    "PURCHASE_NO_MARK" VARCHAR(600),
    "INVOICE_NO" VARCHAR(120),
    "PORT_OF_DEPARTURE" VARCHAR(100),
    "VESSEL_VOYAGE" VARCHAR(100),
    "SAILING_DATE" TIMESTAMP(6),
    "SALES_DATE" TIMESTAMP(6),
    "TOTAL_QUANTITY" DECIMAL(19,4),
    NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PURCHASE_ORDER_NO" IS '进货单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."SUPPLIER" IS '供应商';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CUSTOMER_ADDRESS" IS '客户地址';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TRADE_COUNTRY" IS '贸易国别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."BUSINESS_ENTERPRISE" IS '经营单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PORT_OF_DESTINATION" IS '目的地/港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PAYMENT_METHOD" IS '付款方式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TOTAL_AMOUNT" IS '总金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TRANSPORT_MODE" IS '运输方式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PRICE_TERM" IS '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PRICE_TERM_PORT" IS '价格条款对应港口';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."DELIVERY_ENTERPRISE" IS '发货单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."WRAP_TYPE" IS '包装种类';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PACK_NUM" IS '包装数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."DELIVERY_ENTERPRISE_ADDRESS" IS '发货单位所在地';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TOTAL_NET_WT" IS '总毛重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TOTAL_GROSS_WT" IS '总净重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TOTAL_TARE" IS '总皮重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."SEND_DECLARE" IS '发送报关';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."BUSINESS_DATE" IS '业务日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."IS_CONFIRM" IS '是否确认';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."IS_SAVE" IS '是否保存';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."APPR_STATUS" IS '审核状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."SEND_FINANCE" IS '发送财务系统';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."RED_FLUSH" IS '是否红冲';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PURCHASE_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PURCHASE_NO_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."INVOICE_NO" IS '发票号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."PORT_OF_DEPARTURE" IS '启运港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."VESSEL_VOYAGE" IS '船名航次';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."SAILING_DATE" IS '开航日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."SALES_DATE" IS '作销日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_HEAD"."TOTAL_QUANTITY" IS '总数量';


CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"
(
    "SID" VARCHAR(50) DEFAULT SYS_GUID() NOT NULL,
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP(6),
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "TRANSPORT_PERMIT" VARCHAR(200),
    "SHIPMENT_CONFIRM_DATE" TIMESTAMP(6),
    "ENTRY_NO" VARCHAR(40),
    "DECLARATION_DATE" TIMESTAMP(6),
    "RELEASE_DATE" TIMESTAMP(6),
    "SHIPPING_MARK" VARCHAR(400),
    "NOTE" VARCHAR(1000),
    "HEAD_ID" VARCHAR(50),
    "ANALYSIS_ID" VARCHAR(50),
    NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."TRANSPORT_PERMIT" IS '准运证编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."SHIPMENT_CONFIRM_DATE" IS '出货确认日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."ENTRY_NO" IS '报关单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."DECLARATION_DATE" IS '申报日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."RELEASE_DATE" IS '放行日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."SHIPPING_MARK" IS '唛头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PURCHASE_ORDER_CERT"."ANALYSIS_ID" IS '分析单id';


--changeset cblin2:
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "CONTRACT_NO" VARCHAR(120),
    "PURCHASE_ORDER_NO" VARCHAR(120),
    "SUPPLIER" VARCHAR(400),
    "CUSTOMER" VARCHAR(400),
    "CUSTOMER_ADDRESS" VARCHAR(400),
    "TRADE_COUNTRY" VARCHAR(120),
    "BUSINESS_ENTERPRISE" VARCHAR(120),
    "PORT_OF_DESTINATION" VARCHAR(100),
    "PAYMENT_METHOD" VARCHAR(40),
    "CURR" VARCHAR(20),
    "TOTAL_AMOUNT" NUMERIC(19,4),
    "TRANSPORT_MODE" VARCHAR(100),
    "PRICE_TERM" VARCHAR(20),
    "PRICE_TERM_PORT" VARCHAR(40),
    "DELIVERY_ENTERPRISE" VARCHAR(120),
    "WRAP_TYPE" VARCHAR(200),
    "PACK_NUM" NUMERIC(19,4),
    "DELIVERY_ENTERPRISE_ADDRESS" VARCHAR(400),
    "TOTAL_NET_WT" NUMERIC(19,4),
    "TOTAL_GROSS_WT" NUMERIC(19,4),
    "TOTAL_TARE" NUMERIC(19,4),
    "SEND_DECLARE" VARCHAR(20),
    "BUSINESS_DATE" TIMESTAMP,
    "CONFIRM_TIME" TIMESTAMP,
    "IS_CONFIRM" VARCHAR(10),
    "IS_SAVE" VARCHAR(10),
    "NOTE" VARCHAR(1000),
    "STATUS" VARCHAR(20),
    "APPR_STATUS" VARCHAR(20),
    "SEND_FINANCE" VARCHAR(20),
    "RED_FLUSH" VARCHAR(20),
    "PURCHASE_MARK" VARCHAR(20),
    "PURCHASE_NO_MARK" VARCHAR(600),

    CONSTRAINT "T_BIZ_DELIVERY_ORDER_HEAD_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PURCHASE_ORDER_NO" IS '出货单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."SUPPLIER" IS '供应商';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CUSTOMER_ADDRESS" IS '客户地址';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."TRADE_COUNTRY" IS '贸易国别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."BUSINESS_ENTERPRISE" IS '经营单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PORT_OF_DESTINATION" IS '目的地/港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PAYMENT_METHOD" IS '付款方式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."TOTAL_AMOUNT" IS '总金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."TRANSPORT_MODE" IS '运输方式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PRICE_TERM" IS '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PRICE_TERM_PORT" IS '价格条款对应港口';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."DELIVERY_ENTERPRISE" IS '发货单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."WRAP_TYPE" IS '包装种类';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PACK_NUM" IS '包装数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."DELIVERY_ENTERPRISE_ADDRESS" IS '发货单位所在地';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."TOTAL_NET_WT" IS '总净重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."TOTAL_GROSS_WT" IS '总毛重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."TOTAL_TARE" IS '总皮重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."SEND_DECLARE" IS '发送报关';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."BUSINESS_DATE" IS '业务日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."SEND_FINANCE" IS '发送财务系统';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."RED_FLUSH" IS '是否红冲';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."APPR_STATUS" IS '审核状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."IS_CONFIRM" IS '是否确认';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."IS_SAVE" IS '是否保存';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PURCHASE_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_HEAD"."PURCHASE_NO_MARK" IS '外商合同、进货明细数据标记';

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "PRODUCT_NAME" VARCHAR(200),
    "PRODUCT_MODEL" VARCHAR(200),
    "UNIT" VARCHAR(40),
    "QTY" NUMERIC(19,6),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "NOTE" VARCHAR(1000),
    "CONTAINER_NUM" NUMERIC(19,6),
    "HEAD_ID" VARCHAR(50),
    "CONTRACT_LIST_ID" VARCHAR(50),
    "ANALYSE_LIST_ID" VARCHAR(50),
    "GROSS_WEIGHT" NUMERIC(19,4),
    "NET_WEIGHT" NUMERIC(19,4),

    CONSTRAINT "T_BIZ_DELIVERY_ORDER_LIST_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."PRODUCT_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."PRODUCT_MODEL" IS '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."QTY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."CONTAINER_NUM" IS '箱数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."CONTRACT_LIST_ID" IS '外商合同表体id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."ANALYSE_LIST_ID" IS '分析单表体id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."GROSS_WEIGHT" IS '毛重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_LIST"."NET_WEIGHT" IS '净重';

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "BOX_NO_START" VARCHAR(200),
    "BOX_NO_END" VARCHAR(200),
    "PRODUCT_NAME" VARCHAR(200),
    "PACKAGE_STYLE" VARCHAR(200),
    "GROSS_WT" NUMERIC(19,4),
    "NET_WT" NUMERIC(19,4),
    "TARE_WT" NUMERIC(19,4),
    "LONGER" NUMERIC(19,4),
    "WHITHER" NUMERIC(19,4),
    "HIGHER" NUMERIC(19,4),
    "NOTE" VARCHAR(1000),
    "CONTAINER_NUM" NUMERIC(19,4),
    "QTY" NUMERIC(19,4),
    "HEAD_ID" VARCHAR(50),
    "CONTRACT_LIST_ID" VARCHAR(50),

    CONSTRAINT "T_BIZ_DELIVERY_ORDER_CONTAINER_LIST_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."BOX_NO_START" IS '起始箱号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."BOX_NO_END" IS '结束箱号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."PRODUCT_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."PACKAGE_STYLE" IS '包装样式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."GROSS_WT" IS '毛重(KG)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."NET_WT" IS '净重(KG)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."TARE_WT" IS '皮重(KG)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."LONGER" IS '长(M)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."WHITHER" IS '宽(M)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."HIGHER" IS '高(M)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."CONTAINER_NUM" IS '件数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."QTY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CONTAINER_LIST"."CONTRACT_LIST_ID" IS '外商合同表体id';

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "SHIPPER" VARCHAR(1000),
    "CONSIGNEE" VARCHAR(1000),
    "NOTIFY_PARTY" VARCHAR(1000),
    "PORT_OF_SHIPMENT" VARCHAR(100),
    "PORT_OF_DESTINATION" VARCHAR(100),
    "DESTINATION_DATE" TIMESTAMP,
    "WAREHOUSE_ADDRESS" VARCHAR(1000),
    "CONTACT_PERSON" VARCHAR(200),
    "CONTACT_PHONE" VARCHAR(200),
    "NOTE" VARCHAR(1000),
    "HEAD_ID" VARCHAR(50),
    "ANALYSIS_ID" VARCHAR(50),

    CONSTRAINT "T_BIZ_DELIVERY_ORDER_SHIPMENTS_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."SHIPPER" IS '装运人SHIPPER';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."CONSIGNEE" IS '收件人CONSIGNEE';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."NOTIFY_PARTY" IS '通知人NOTIFY PARTY';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."PORT_OF_SHIPMENT" IS '装运港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."PORT_OF_DESTINATION" IS '目的地/港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."DESTINATION_DATE" IS '装运期限';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."WAREHOUSE_ADDRESS" IS '仓库地址';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."CONTACT_PERSON" IS '联系人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."CONTACT_PHONE" IS '电话';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_SHIPMENTS"."ANALYSIS_ID" IS '分析单id';

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "TRANSPORT_PERMIT" VARCHAR(200),
    "SHIPMENT_CONFIRM_DATE" TIMESTAMP,
    "ENTRY_NO" VARCHAR(40),
    "DECLARATION_DATE" TIMESTAMP,
    "RELEASE_DATE" TIMESTAMP,
    "SHIPPING_MARK" VARCHAR(400),
    "NOTE" VARCHAR(1000),
    "HEAD_ID" VARCHAR(50),
    "ANALYSIS_ID" VARCHAR(50),

    CONSTRAINT "T_BIZ_DELIVERY_ORDER_CERT_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."TRANSPORT_PERMIT" IS '准运证编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."SHIPMENT_CONFIRM_DATE" IS '出货确认日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."ENTRY_NO" IS '报关单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."DECLARATION_DATE" IS '申报日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."RELEASE_DATE" IS '放行日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."SHIPPING_MARK" IS '唛头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_CERT"."ANALYSIS_ID" IS '分析单id';

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "PURCHASE_ORDER_NO" VARCHAR(120),
    "INSURANCE_COMPANY" VARCHAR(400),
    "INSURANT" VARCHAR(400),
    "INVOICE_TITLE" VARCHAR(400),
    "TRAF_NAME" VARCHAR(400),
    "START_SHIPMENT_DATE" TIMESTAMP,
    "TRAF_ROUTE_FROM" VARCHAR(400),
    "TRAF_ROUTE_PASS" VARCHAR(400),
    "TRAF_ROUTE_TO" VARCHAR(400),
    "INSURANCE_TYPE" VARCHAR(400),
    "CURR" VARCHAR(20),
    "INSURANCE_MARKUP" NUMERIC(19,4),
    "INSURANCE_AMOUNT" NUMERIC(19,4),
    "INSURANCE_RATE" NUMERIC(19,4),
    "INSURANCE_FEE" NUMERIC(19,4),
    "INSURANCE_DATE" TIMESTAMP,
    "FREIGHT_FEE" NUMERIC(19,4),
    "FREIGHT_CURR" VARCHAR(20),
    "NOTE" VARCHAR(1000),
    "HEAD_ID" VARCHAR(50),
    "ANALYSIS_ID" VARCHAR(50),

    CONSTRAINT "T_BIZ_DELIVERY_ORDER_INSURANCE_INFO_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."PURCHASE_ORDER_NO" IS '编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANCE_COMPANY" IS '保险公司';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANT" IS '被保险人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INVOICE_TITLE" IS '发票抬头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."TRAF_NAME" IS '运输工具名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."START_SHIPMENT_DATE" IS '开航日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."TRAF_ROUTE_FROM" IS '运输路线自';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."TRAF_ROUTE_PASS" IS '经';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."TRAF_ROUTE_TO" IS '至';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANCE_TYPE" IS '投保险别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANCE_MARKUP" IS '投保加成%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANCE_AMOUNT" IS '保险金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANCE_RATE" IS '保险费率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANCE_FEE" IS '保费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."INSURANCE_DATE" IS '投保日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."FREIGHT_FEE" IS '运费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."FREIGHT_CURR" IS '运费币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_DELIVERY_ORDER_INSURANCE_INFO"."ANALYSIS_ID" IS '分析单id';






--changeset hrfan:1
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"
(
    "ID" VARCHAR(160) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(240),
    "DATA_STATE" VARCHAR(40),
    "VERSION_NO" VARCHAR(40),
    "TRADE_CODE" VARCHAR(40),
    "SYS_ORG_CODE" VARCHAR(40),
    "PARENT_ID" VARCHAR(160),
    "CREATE_BY" VARCHAR(200),
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UPDATE_BY" VARCHAR(200),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(200),
    "UPDATE_USER_NAME" VARCHAR(200),
    "EXTEND1" VARCHAR(800),
    "EXTEND2" VARCHAR(800),
    "EXTEND3" VARCHAR(800),
    "EXTEND4" VARCHAR(800),
    "EXTEND5" VARCHAR(800),
    "EXTEND6" VARCHAR(800),
    "EXTEND7" VARCHAR(800),
    "EXTEND8" VARCHAR(800),
    "EXTEND9" VARCHAR(800),
    "EXTEND10" VARCHAR(800),
    "RECEIVER" VARCHAR(2000),
    "CONTRACT_NO" VARCHAR(120),
    "ANALYSIS_NO" VARCHAR(120),
    "TRADE_COUNTRY" VARCHAR(100),
    "DESTINATION" VARCHAR(200),
    "CONSUME_COUNTRY" VARCHAR(100),
    "IS_TRANSIT" VARCHAR(20),
    "SHIPMENT_DEADLINE" TIMESTAMP(6),
    "SHIPPER" VARCHAR(1000),
    "CONSIGNEE" VARCHAR(1000),
    "NOTIFY_PARTY" VARCHAR(1000),
    "FREIGHT" VARCHAR(1000),
    "PROCESS_ACCOUNT_NO" VARCHAR(100),
    "PACKING_TIME" TIMESTAMP(6),
    "PACKING_TIME_TO" TIMESTAMP(6),
    "WAREHOUSE_ADDRESS" VARCHAR(1000),
    "SHIP_NAME_VOYAGE" VARCHAR(200),
    "BILL_NO" VARCHAR(200),
    "CONTACT_PHONE" VARCHAR(200),
    "BILL_DATE" TIMESTAMP(6),
    "APPR_STATUS" VARCHAR(10),
    "CURR" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "TOTAL" NUMERIC(19,4),
    "CUSTOMER_CODE" VARCHAR(200),
    CONSTRAINT "PK_T_BIZ_BP_ANALYSE_ORDER_HEAD_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD" IS '（第7条线）出料加工进口薄片-分析单表头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."RECEIVER" IS '收货人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."ANALYSIS_NO" IS '分析单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."TRADE_COUNTRY" IS '贸易国别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."DESTINATION" IS '目的地';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CONSUME_COUNTRY" IS '消费国别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."IS_TRANSIT" IS '是否转运';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."SHIPMENT_DEADLINE" IS '装运期限';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."SHIPPER" IS 'SHIPPER';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CONSIGNEE" IS 'CONSIGNEE';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."NOTIFY_PARTY" IS 'NOTIFY PARTY';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."FREIGHT" IS 'FREIGHT';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."PROCESS_ACCOUNT_NO" IS '出境加工账册编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."PACKING_TIME" IS '装箱时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."PACKING_TIME_TO" IS '至';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."WAREHOUSE_ADDRESS" IS '仓库地址';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."SHIP_NAME_VOYAGE" IS '船名航次';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."BILL_NO" IS '提单编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CONTACT_PHONE" IS '联系人及电话';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."BILL_DATE" IS '提单日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."APPR_STATUS" IS '审批状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."TOTAL" IS '表体金额汇总';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_HEAD"."CUSTOMER_CODE" IS '客户';



CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"
(
    "ID" VARCHAR(160) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(240),
    "DATA_STATE" VARCHAR(40),
    "VERSION_NO" VARCHAR(40),
    "TRADE_CODE" VARCHAR(40),
    "SYS_ORG_CODE" VARCHAR(40),
    "PARENT_ID" VARCHAR(160),
    "CREATE_BY" VARCHAR(200),
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UPDATE_BY" VARCHAR(200),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(200),
    "UPDATE_USER_NAME" VARCHAR(200),
    "EXTEND1" VARCHAR(800),
    "EXTEND2" VARCHAR(800),
    "EXTEND3" VARCHAR(800),
    "EXTEND4" VARCHAR(800),
    "EXTEND5" VARCHAR(800),
    "EXTEND6" VARCHAR(800),
    "EXTEND7" VARCHAR(800),
    "EXTEND8" VARCHAR(800),
    "EXTEND9" VARCHAR(800),
    "EXTEND10" VARCHAR(800),
    "PRODUCT_NAME" VARCHAR(400),
    "PRODUCT_MODEL" VARCHAR(400),
    "UNIT" VARCHAR(80),
    "QUANTITY" NUMERIC(19,4),
    "BOX_COUNT" NUMERIC(19,4),
    "GROSS_WEIGHT" NUMERIC(19,4),
    "UNIT_PRICE" NUMERIC(19,4),
    "AMOUNT" NUMERIC(19,4),
    "REMARK" VARCHAR(800),
    "NET_WT" NUMERIC(19,4),
    "VOLUME" NUMERIC(19,4) DEFAULT 4,
    CONSTRAINT "PK_T_BIZ_BP_ANALYSE_ORDER_LIST_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST" IS '（第7条线）出料加工进口薄片-分析单表体';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."PRODUCT_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."PRODUCT_MODEL" IS '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."QUANTITY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."BOX_COUNT" IS '箱数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."GROSS_WEIGHT" IS '毛重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."REMARK" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."NET_WT" IS '净重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST"."VOLUME" IS '体积';







CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"
(
    "ID" VARCHAR(160) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(240),
    "DATA_STATE" VARCHAR(40),
    "VERSION_NO" VARCHAR(40),
    "TRADE_CODE" VARCHAR(40),
    "SYS_ORG_CODE" VARCHAR(40),
    "PARENT_ID" VARCHAR(160),
    "CREATE_BY" VARCHAR(200),
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UPDATE_BY" VARCHAR(200),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(200),
    "UPDATE_USER_NAME" VARCHAR(200),
    "EXTEND1" VARCHAR(800),
    "EXTEND2" VARCHAR(800),
    "EXTEND3" VARCHAR(800),
    "EXTEND4" VARCHAR(800),
    "EXTEND5" VARCHAR(800),
    "EXTEND6" VARCHAR(800),
    "EXTEND7" VARCHAR(800),
    "EXTEND8" VARCHAR(800),
    "EXTEND9" VARCHAR(800),
    "EXTEND10" VARCHAR(800),
    "CONTAINER_SPEC" VARCHAR(400),
    "CONTAINER_COUNT" NUMBER(19,0),
    "CONTAINER_NO" VARCHAR(1000),
    "PRODUCT_NAME" VARCHAR(400),
    "BOX_COUNT" NUMBER(19,0),
    "REMAINING_BOX_COUNT" NUMBER(19,0),
    "SERIAL_NO" NUMERIC(19,0),
    "NOTE" VARCHAR(2000),
    CONSTRAINT "PK_T_BIZ_BP_ANALYSE_ORDER_LIST_BOX_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX" IS '（第7条线）出料加工进口薄片-装箱列表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."CONTAINER_SPEC" IS '集装箱规格';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."CONTAINER_COUNT" IS '集装箱数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."CONTAINER_NO" IS '集装箱号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."PRODUCT_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."BOX_COUNT" IS '箱数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."REMAINING_BOX_COUNT" IS '剩余箱数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."SERIAL_NO" IS '序号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_BP_ANALYSE_ORDER_LIST_BOX"."NOTE" IS '备注';

--changeset cblin3:
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "ACCOUNT_NO" VARCHAR(120),
    "CONTRACT_NO" VARCHAR(120),
    "CURR" VARCHAR(20),
    "EXCHANGE_RATE" NUMERIC(19,6),
    "GOODS_PRICE" NUMERIC(19,2),
    "AGENT_FEE_RATE" NUMERIC(19,4),
    "AGENT_FEE" NUMERIC(19,2),
    "AGENT_TAX_FEE" NUMERIC(19,2),
    "AGENT_FEE_TOTAL" NUMERIC(19,2),
    "TOTAL_AGENT_FEE_RATE" NUMERIC(19,4),
    "TOTAL_AGENT_FEE" NUMERIC(19,2),
    "TOTAL_AGENT_TAX_FEE" NUMERIC(19,2),
    "TOTAL_AGENT_FEE_TOTAL" NUMERIC(19,2),
    "BUSINESS_DATE" TIMESTAMP,
    "G_NAME" VARCHAR(160),
    "SEND_FINANCE" VARCHAR(20),
    "PRODUCR_SOME" VARCHAR(1000),
    "NOTE" VARCHAR(1000),
    "FREIGHT_RATIO" VARCHAR(400),
    "RED_FLUSH" VARCHAR(20),
    "STATUS" VARCHAR(20),
    "APPR_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP,
    "IS_CONFIRM" VARCHAR(10),
    "PURCHASE_MARK" VARCHAR(20),
    "PURCHASE_NO_MARK" VARCHAR(600),
    "GOODS_PRICE_RMB" NUMERIC(19,2),
    "VAT_RATE" NUMERIC(19,4),
    "FREIGHT_FORWARDING_FEE" NUMERIC(19,2),
    "INSURANCE_FEE" NUMERIC(19,2),
    "COST_FEE" NUMERIC(19,2),
    "DEPOSIT_RECEIVED" NUMERIC(19,2),
    "REFUND_FEE" NUMERIC(19,2),
    "CUSTOMER" VARCHAR(400),
    "BUSINESS_LOCATION" VARCHAR(40),
    CONSTRAINT "T_BIZ_CUSTOMER_ACCOUNT_TOBACOO_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."ACCOUNT_NO" IS '结算单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."EXCHANGE_RATE" IS '汇率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."GOODS_PRICE" IS '货款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."AGENT_FEE_RATE" IS '中烟代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."AGENT_FEE" IS '中烟代理费（不含税）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."AGENT_TAX_FEE" IS '中烟代理费税额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."AGENT_FEE_TOTAL" IS '中烟代理费（价税合计）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."BUSINESS_DATE" IS '结算日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."G_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."SEND_FINANCE" IS '发送财务系统';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."PRODUCR_SOME" IS '商品与数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."RED_FLUSH" IS '是否红冲';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."APPR_STATUS" IS '审核状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."IS_CONFIRM" IS '是否确认';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."PURCHASE_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."PURCHASE_NO_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."GOODS_PRICE_RMB" IS '人民币货款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."VAT_RATE" IS '增值税率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."FREIGHT_FORWARDING_FEE" IS '货代费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."INSURANCE_FEE" IS '保险费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."COST_FEE" IS '已结算款项合计';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."DEPOSIT_RECEIVED" IS '实际预收款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."REFUND_FEE" IS '应退款项';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."FREIGHT_RATIO" IS '货款比例';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."BUSINESS_LOCATION" IS '业务地点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."TOTAL_AGENT_FEE_RATE" IS '中烟代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."TOTAL_AGENT_FEE" IS '中烟代理费（不含税）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."TOTAL_AGENT_TAX_FEE" IS '中烟代理费税额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_TOBACOO"."TOTAL_AGENT_FEE_TOTAL" IS '中烟代理费（价税合计）';

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "ACCOUNT_NO" VARCHAR(120),
    "CONTRACT_NO" VARCHAR(120),
    "CURR" VARCHAR(20),
    "EXCHANGE_RATE" NUMERIC(19,6),
    "GOODS_PRICE" NUMERIC(19,2),
    "AGENT_FEE_RATE" NUMERIC(19,4),
    "AGENT_FEE" NUMERIC(19,2),
    "AGENT_TAX_FEE" NUMERIC(19,2),
    "AGENT_FEE_TOTAL" NUMERIC(19,2),
    "BUSINESS_DATE" TIMESTAMP,
    "G_NAME" VARCHAR(160),
    "SEND_FINANCE" VARCHAR(20),
    "PRODUCR_SOME" VARCHAR(1000),
    "NOTE" VARCHAR(1000),
    "RED_FLUSH" VARCHAR(20),
    "STATUS" VARCHAR(20),
    "APPR_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP,
    "IS_CONFIRM" VARCHAR(10),
    "PURCHASE_MARK" VARCHAR(20),
    "PURCHASE_NO_MARK" VARCHAR(600),
    "GOODS_PRICE_RMB" NUMERIC(19,2),
    "VAT_RATE" NUMERIC(19,4),
    "FREIGHT_FORWARDING_FEE" NUMERIC(19,2),
    "INSURANCE_FEE" NUMERIC(19,2),
    "COST_FEE" NUMERIC(19,2),
    "DEPOSIT_RECEIVED" NUMERIC(19,2),
    "REFUND_FEE" NUMERIC(19,2),
    "CUSTOMER" VARCHAR(400),
    CONSTRAINT "T_BIZ_CUSTOMER_ACCOUNT_SUMMARY_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."ACCOUNT_NO" IS '结算单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."EXCHANGE_RATE" IS '汇率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."GOODS_PRICE" IS '货款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."AGENT_FEE_RATE" IS '代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."AGENT_FEE" IS '代理费（不含税）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."AGENT_TAX_FEE" IS '代理费税额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."AGENT_FEE_TOTAL" IS '代理费（价税合计）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."BUSINESS_DATE" IS '结算日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."G_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."SEND_FINANCE" IS '发送财务系统';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."PRODUCR_SOME" IS '商品与数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."RED_FLUSH" IS '是否红冲';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."APPR_STATUS" IS '审核状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."IS_CONFIRM" IS '是否确认';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."PURCHASE_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."PURCHASE_NO_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."GOODS_PRICE_RMB" IS '人民币货款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."VAT_RATE" IS '增值税率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."FREIGHT_FORWARDING_FEE" IS '货代费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."INSURANCE_FEE" IS '保险费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."COST_FEE" IS '已结算款项合计';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."DEPOSIT_RECEIVED" IS '实际预收款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."REFUND_FEE" IS '应退款项';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SUMMARY"."CUSTOMER" IS '客户';

CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE" (
    "SID" VARCHAR(50) NOT NULL DEFAULT SYS_GUID(),
    "CREATE_BY" VARCHAR(50) NOT NULL,
    "CREATE_TIME" TIMESTAMP NOT NULL,
    "CREATE_USER_NAME" VARCHAR(50),
    "UPDATE_BY" VARCHAR(50),
    "UPDATE_TIME" TIMESTAMP,
    "UPDATE_USER_NAME" VARCHAR(50),
    "TRADE_CODE" VARCHAR(50),
    "SYS_ORG_CODE" VARCHAR(50),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "BUSINESS_TYPE" VARCHAR(120),
    "ACCOUNT_NO" VARCHAR(120),
    "CONTRACT_NO" VARCHAR(120),
    "CURR_E" VARCHAR(20),
    "CURR_I" VARCHAR(20),
    "EXCHANGE_RATE_E" NUMERIC(19,6),
    "EXCHANGE_RATE_I" NUMERIC(19,6),
    "GOODS_PRICE_E" NUMERIC(19,2),
    "GOODS_PRICE_I" NUMERIC(19,2),
    "AGENT_FEE_RATE" NUMERIC(19,4),
    "AGENT_FEE" NUMERIC(19,2),
    "AGENT_TAX_FEE" NUMERIC(19,2),
    "AGENT_FEE_TOTAL" NUMERIC(19,2),
    "BUSINESS_DATE" TIMESTAMP,
    "G_NAME" VARCHAR(160),
    "SEND_FINANCE" VARCHAR(20),
    "PRODUCR_SOME" VARCHAR(1000),
    "NOTE" VARCHAR(1000),
    "FREIGHT_RATIO" VARCHAR(400),
    "RED_FLUSH" VARCHAR(20),
    "STATUS" VARCHAR(20),
    "APPR_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP,
    "IS_CONFIRM" VARCHAR(10),
    "PURCHASE_MARK" VARCHAR(20),
    "PURCHASE_NO_MARK" VARCHAR(600),
    "GOODS_PRICE_E_RMB" NUMERIC(19,2),
    "GOODS_PRICE_I_RMB" NUMERIC(19,2),
    "VAT_RATE" NUMERIC(19,4),
    "FREIGHT_FORWARDING_FEE" NUMERIC(19,2),
    "INSURANCE_FEE" NUMERIC(19,2),
    "COST_FEE" NUMERIC(19,2),
    "DEPOSIT_RECEIVED" NUMERIC(19,2),
    "REFUND_FEE" NUMERIC(19,2),
    "TOTAL_AMOUNT" NUMERIC(19,2),
    "CUSTOMER" VARCHAR(400),
    "BUSINESS_LOCATION" VARCHAR(40),
    "PURCHASE_ORDER_NO" VARCHAR(120),
    CONSTRAINT "T_BIZ_CUSTOMER_ACCOUNT_SLICE_PK" PRIMARY KEY ("SID")
    );

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CREATE_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."UPDATE_TIME" IS '最后修改时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."UPDATE_USER_NAME" IS '最后修改人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND1" IS '拓展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND2" IS '拓展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND3" IS '拓展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND4" IS '拓展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND5" IS '拓展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND6" IS '拓展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND7" IS '拓展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND8" IS '拓展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND9" IS '拓展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXTEND10" IS '拓展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."ACCOUNT_NO" IS '结算单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CURR_E" IS '出口币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CURR_I" IS '进口币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXCHANGE_RATE_E" IS '出口汇率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."EXCHANGE_RATE_I" IS '进口汇率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."GOODS_PRICE_E" IS '出口货款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."GOODS_PRICE_I" IS '进口货款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."AGENT_FEE_RATE" IS '中烟代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."AGENT_FEE" IS '中烟代理费（不含税）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."AGENT_TAX_FEE" IS '中烟代理费税额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."AGENT_FEE_TOTAL" IS '中烟代理费（价税合计）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."BUSINESS_DATE" IS '结算日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."G_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."SEND_FINANCE" IS '发送财务系统';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."PRODUCR_SOME" IS '商品与数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."RED_FLUSH" IS '是否红冲';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."APPR_STATUS" IS '审核状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."IS_CONFIRM" IS '是否确认';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."PURCHASE_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."PURCHASE_NO_MARK" IS '外商合同、进货明细数据标记';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."GOODS_PRICE_E_RMB" IS '出口货款(人民币)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."GOODS_PRICE_I_RMB" IS '进口货款(人民币)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."VAT_RATE" IS '增值税率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."FREIGHT_FORWARDING_FEE" IS '货代费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."INSURANCE_FEE" IS '保险费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."COST_FEE" IS '已结算款项合计';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."DEPOSIT_RECEIVED" IS '实际预收款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."REFUND_FEE" IS '应退款项';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."FREIGHT_RATIO" IS '货款比例';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."BUSINESS_LOCATION" IS '业务地点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."TOTAL_AMOUNT" IS '共计金额(人民币)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CUSTOMER_ACCOUNT_SLICE"."PURCHASE_ORDER_NO" IS '出货单号';


--changeset hrfan:2
ALTER TABLE  "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD" ADD COLUMN IF NOT EXISTS "INSURANCE_CATEGORY" VARCHAR(200) NULL;
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_CATEGORY"  IS '投保险别（企业自定义参数-保险类别 中文名称）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_RATE" IS '费率%（数值型，保留4位小数）';
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD" ADD COLUMN IF NOT EXISTS "INSURANCE_AMOUNT" NUMERIC(19,4) NULL;
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_AMOUNT" IS '保额（数值型，保留4位小数）';
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD" ADD COLUMN IF NOT EXISTS "INSURANCE_PREMIUM" NUMERIC(19,4) NULL;
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_PREMIUM" IS '保费数（数值型，保留4位小数）';

--changeset xbxu1:2
CREATE TABLE if not exists "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "CONTRACT_NO" VARCHAR(120),
    "AGREEMENT_NO" VARCHAR(120),
    "CUSTOMER" VARCHAR(400),
    "SIGNING_DATE" TIMESTAMP(6),
    "SIGNING_PLACE" VARCHAR(200),
    "CURRENCY" VARCHAR(20),
    "CONTRACT_AMOUNT" NUMERIC(19,4),
    "AGENCY_RATE" NUMERIC(19,2),
    "AGENCY_FEE" NUMERIC(19,2),
    "SUGGESTED_SIGNATORY" VARCHAR(120),
    "AGREEMENT_TERMS" VARCHAR(2000),
    "MAKER" VARCHAR(20),
    "MAKE_DATE" TIMESTAMP(6),
    "BILL_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPROVAL_STATUS" VARCHAR(20),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "PARENT_ID" VARCHAR(200),
    "TRADE_CODE" VARCHAR(20) NOT NULL,
    "SYS_ORG_CODE" VARCHAR(20),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    CONSTRAINT "PK_T_BIZ_AGENCY_AGREEMENT" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT" IS '出料加工进口薄片-代理协议模块';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."ID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."AGREEMENT_NO" IS '协议编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."SIGNING_DATE" IS '签约日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."SIGNING_PLACE" IS '签约地点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."CURRENCY" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."CONTRACT_AMOUNT" IS '合同金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."AGENCY_RATE" IS '代理费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."AGENCY_FEE" IS '代理费用';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."SUGGESTED_SIGNATORY" IS '建议授权签约人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."AGREEMENT_TERMS" IS '协议条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."MAKER" IS '制单人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."MAKE_DATE" IS '制单日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."BILL_STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."APPROVAL_STATUS" IS '审批状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."PARENT_ID" IS '父节点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."UPDATE_BY" IS '最后修改人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT"."UPDATE_TIME" IS '最后修改时间';

CREATE TABLE if not exists "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "GOODS_NAME" VARCHAR(160),
    "PRODUCT_MODEL" VARCHAR(200),
    "QUANTITY" NUMERIC(19,4),
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "REMARK" VARCHAR(1000),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    "HEAD_ID" VARCHAR(200),
    "PARENT_ID" VARCHAR(200),
    "I_E_MARK" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20) NOT NULL,
    "SYS_ORG_CODE" VARCHAR(20),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    CONSTRAINT "PK_T_BIZ_AGENCY_AGREEMENT_LIST" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST" IS '出料加工进口薄片-代理协议模块 - 表体';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."ID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."GOODS_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."PRODUCT_MODEL" IS '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."QUANTITY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."REMARK" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."HEAD_ID" IS '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."PARENT_ID" IS '上游id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."I_E_MARK" IS '进出口标识';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."SYS_ORG_CODE" IS '创建人部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."UPDATE_BY" IS '最后修改人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_AGENCY_AGREEMENT_LIST"."UPDATE_TIME" IS '最后修改时间';

--changeset xbxu1:3
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "MERCHANT_TYPE" VARCHAR(20);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."MERCHANT_TYPE" IS '客商类别';

ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "COMMON_FLAG" VARCHAR(60);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."COMMON_FLAG" IS '常用标识';

-- 1）传真（非必填，长20，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "FAX" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."FAX" IS '传真';

-- 2）邮件（非必填，长200，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "EMAIL" VARCHAR(400);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."EMAIL" IS '邮件';

-- 3）英文地址（非必填，长200，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "EN_ADDRESS" VARCHAR(400);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."EN_ADDRESS" IS '英文地址';

-- 4）贸易国别英文（非必填，长50，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "TRADE_COUNTRY_EN" VARCHAR(100);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."TRADE_COUNTRY_EN" IS '贸易国别英文';

-- 5）邮编（非必填，长10，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "POSTCODE" VARCHAR(20);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."POSTCODE" IS '邮编';

-- 6）开户行地址（非必填，长200，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "BANK_ADDRESS" VARCHAR(400);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."BANK_ADDRESS" IS '开户行地址';

-- 7）支出账号（非必填，长50，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "EXPEND_ACCOUNT" VARCHAR(100);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."EXPEND_ACCOUNT" IS '支出账号';

-- 8）税号（非必填，长50，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "TAX_NO" VARCHAR(100);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."TAX_NO" IS '税号';

-- 9）法人代表（非必填，长20，字符型）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD IF NOT EXISTS "LEGAL_PERSON" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."LEGAL_PERSON" IS '法人代表';

-- changeset bhyue:1
DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"
(
    "ID"                       VARCHAR(50) PRIMARY KEY,
    "PREV_VERSION_ID"          VARCHAR(50),
    "BUSINESS_TYPE"            VARCHAR(60),
    "CONTRACT_NO"              VARCHAR(60),
    "BUYER"                    VARCHAR(200),
    "SELLER"                   VARCHAR(200),
    "SIGN_DATE"                TIMESTAMP,
    "SHIP_PERIOD_DATE"         TIMESTAMP,
    "SIGN_PLACE_CN"            VARCHAR(100),
    "SIGN_PLACE_EN"            VARCHAR(100),
    "CONTRACT_EFFECTIVE_DATE"  TIMESTAMP,
    "CONTRACT_VALIDITY_DATE"   TIMESTAMP,
    "SHIPPING_PORT"            VARCHAR(50),
    "DEST_PORT"                VARCHAR(50),
    "PAYMENT_METHOD"           VARCHAR(20),
    "CURR"                     VARCHAR(10),
    "PRICE_TERM"               VARCHAR(30),
    "PRICE_TERM_PORT"          VARCHAR(20),
    "SUGGEST_AUTHOR_SIGNATORY" VARCHAR(50),
    "SHORT_OVERFLOW_NUMBER"    NUMERIC(9, 4),
    "NOTE"                     VARCHAR(200),
    "VERSION_NO"               VARCHAR(10),
    "DOCUMENT_MAKER"           VARCHAR(20),
    "DOCUMENT_MAKER_NO"        VARCHAR(30),
    "DOCUMENT_MAKE_DATE"       TIMESTAMP,
    "DATA_STATUS"              VARCHAR(10),
    "CONFIRM_TIME"             TIMESTAMP,
    "APPR_STATUS"              VARCHAR(10),
    "TRADE_CODE"               VARCHAR(50),
    "SYS_ORG_CODE"             VARCHAR(100),
    "CREATE_BY"                VARCHAR(50) NOT NULL,
    "CREATE_TIME"              TIMESTAMP   NOT NULL,
    "CREATE_BY_NAME"           VARCHAR(50),
    "UPDATE_BY"                VARCHAR(50),
    "UPDATE_TIME"              TIMESTAMP,
    "UPDATE_BY_NAME"           VARCHAR(50),
    "EXTEND1"                  VARCHAR(200),
    "EXTEND2"                  VARCHAR(200),
    "EXTEND3"                  VARCHAR(200),
    "EXTEND4"                  VARCHAR(200),
    "EXTEND5"                  VARCHAR(200),
    "EXTEND6"                  VARCHAR(200),
    "EXTEND7"                  VARCHAR(200),
    "EXTEND8"                  VARCHAR(200),
    "EXTEND9"                  VARCHAR(200),
    "EXTEND10"                 VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD" is '第七条线（外商合同）表头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."ID" is '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."PREV_VERSION_ID" is '上一版本ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."BUSINESS_TYPE" is '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CONTRACT_NO" is '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."BUYER" is '买家';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SELLER" is '卖家';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SIGN_DATE" is '签约日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SHIP_PERIOD_DATE" is '装运期限';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SIGN_PLACE_CN" is '签约地点(中文)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SIGN_PLACE_EN" is '签约地点(英文)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CONTRACT_EFFECTIVE_DATE" is '合同生效期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CONTRACT_VALIDITY_DATE" is '合同有效期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SHIPPING_PORT" is '装运港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."DEST_PORT" is '目的港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."PAYMENT_METHOD" is '收汇方式 0付款交单 1即期信用证 2电汇 3预付款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CURR" is '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."PRICE_TERM" is '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."PRICE_TERM_PORT" is '价格条款对应港口 0起运港 1目的港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SUGGEST_AUTHOR_SIGNATORY" is '建议授权签约人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SHORT_OVERFLOW_NUMBER" is '短溢数%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."NOTE" is '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."VERSION_NO" is '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."DOCUMENT_MAKER" is '制单人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."DOCUMENT_MAKER_NO" is '制单人编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."DOCUMENT_MAKE_DATE" is '制单日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."DATA_STATUS" is '单据状态 0编制 1确认 2作废';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CONFIRM_TIME" is '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."APPR_STATUS" is '审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."SYS_ORG_CODE" IS '部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CREATE_BY" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CREATE_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."CREATE_BY_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."UPDATE_BY_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD"."EXTEND10" IS '扩展字段10';

DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"
(
    "ID"              VARCHAR(50) PRIMARY KEY,
    "PREV_VERSION_ID" VARCHAR(50),
    "HEAD_ID"         VARCHAR(50),
    "G_NAME"          VARCHAR(80),
    "G_MODEL"         VARCHAR(100),
    "UNIT"            VARCHAR(30),
    "QTY"             NUMERIC(19, 4),
    "BOX_NUM"         NUMERIC(19, 4),
    "GROSS_WEIGHT"    NUMERIC(19, 4),
    "UNIT_PRICE"      NUMERIC(19, 8),
    "MONEY_AMOUNT"    NUMERIC(23, 4),
    "NOTE"            VARCHAR(500),
    "BODY_TYPE"       VARCHAR(20),
    "DATA_STATUS"     VARCHAR(10),
    "CONFIRM_TIME"    TIMESTAMP,
    "TRADE_CODE"      VARCHAR(50),
    "SYS_ORG_CODE"    VARCHAR(100),
    "CREATE_BY"       VARCHAR(50) NOT NULL,
    "CREATE_TIME"     TIMESTAMP   NOT NULL,
    "CREATE_BY_NAME"  VARCHAR(50),
    "UPDATE_BY"       VARCHAR(50),
    "UPDATE_TIME"     TIMESTAMP,
    "UPDATE_BY_NAME"  VARCHAR(50),
    "EXTEND1"         VARCHAR(200),
    "EXTEND2"         VARCHAR(200),
    "EXTEND3"         VARCHAR(200),
    "EXTEND4"         VARCHAR(200),
    "EXTEND5"         VARCHAR(200),
    "EXTEND6"         VARCHAR(200),
    "EXTEND7"         VARCHAR(200),
    "EXTEND8"         VARCHAR(200),
    "EXTEND9"         VARCHAR(200),
    "EXTEND10"        VARCHAR(200)
);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST" is '第七条线（外商合同）表体';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."ID" is '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."PREV_VERSION_ID" is '上一个版本id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."HEAD_ID" is '表头id';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."G_NAME" is '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."G_MODEL" is '产品型号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."UNIT" is '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."QTY" is '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."BOX_NUM" is '箱数';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."GROSS_WEIGHT" is '毛重';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."UNIT_PRICE" is '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."MONEY_AMOUNT" is '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."NOTE" is '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."BODY_TYPE" is '表体类型 process出料加工 slice出口薄片';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."DATA_STATUS" is '单据状态 0编制 1确认 2作废';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."CONFIRM_TIME" is '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."SYS_ORG_CODE" IS '部门编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."CREATE_BY" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."CREATE_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."CREATE_BY_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."UPDATE_BY_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST"."EXTEND10" IS '扩展字段10';


--changeset tlhuang:1
alter table BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD
    add IF NOT EXISTS entrust_company varchar(400);

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.entrust_company is '委托单位';

