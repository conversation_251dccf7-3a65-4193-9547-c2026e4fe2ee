package com.dcjet.cs.purchaseOrder.service;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderCertDto;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.purchaseOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.purchaseOrder.dao.BizPurchaseOrderCertMapper;
import com.dcjet.cs.purchaseOrder.mapper.BizPurchaseOrderCertDtoMapper;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderCert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Service
public class BizPurchaseOrderCertService extends BaseService<BizPurchaseOrderCert> {
    @Resource
    private BizPurchaseOrderCertMapper bizPurchaseOrderCertMapper;
    @Resource
    private BizPurchaseOrderCertDtoMapper bizPurchaseOrderCertDtoMapper;
    @Override
    public Mapper<BizPurchaseOrderCert> getMapper() {
        return bizPurchaseOrderCertMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizPurchaseOrderCertParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizPurchaseOrderCertDto>> getListPaged(BizPurchaseOrderCertParam bizPurchaseOrderCertParam, PageParam pageParam) {
        // 启用分页查询
        BizPurchaseOrderCert bizPurchaseOrderCert = bizPurchaseOrderCertDtoMapper.toPo(bizPurchaseOrderCertParam);
        Page<BizPurchaseOrderCert> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizPurchaseOrderCertMapper.getList(bizPurchaseOrderCert));
        List<BizPurchaseOrderCertDto> bizPurchaseOrderCertDtos = page.getResult().stream().map(head -> {
            BizPurchaseOrderCertDto dto = bizPurchaseOrderCertDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizPurchaseOrderCertDto>> paged = ResultObject.createInstance(bizPurchaseOrderCertDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizPurchaseOrderCertParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPurchaseOrderCertDto insert(BizPurchaseOrderCertParam bizPurchaseOrderCertParam, UserInfoToken userInfo) {
        BizPurchaseOrderCert bizPurchaseOrderCert = bizPurchaseOrderCertDtoMapper.toPo(bizPurchaseOrderCertParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizPurchaseOrderCert.setSid(sid);
        bizPurchaseOrderCert.setCreateBy(userInfo.getUserNo());
        bizPurchaseOrderCert.setCreateTime(new Date());
        bizPurchaseOrderCert.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = bizPurchaseOrderCertMapper.insert(bizPurchaseOrderCert);
        return  insertStatus > 0 ? bizPurchaseOrderCertDtoMapper.toDto(bizPurchaseOrderCert) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizPurchaseOrderCertParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPurchaseOrderCertDto update(BizPurchaseOrderCertParam bizPurchaseOrderCertParam, UserInfoToken userInfo) {
        BizPurchaseOrderCert bizPurchaseOrderCert = bizPurchaseOrderCertMapper.selectByPrimaryKey(bizPurchaseOrderCertParam.getSid());
        bizPurchaseOrderCertDtoMapper.updatePo(bizPurchaseOrderCertParam, bizPurchaseOrderCert);
        bizPurchaseOrderCert.setUpdateBy(userInfo.getUserNo());
        bizPurchaseOrderCert.setUpdateUserName(userInfo.getUserName());
        bizPurchaseOrderCert.setUpdateTime(new Date());
        // 更新数据
        int update = bizPurchaseOrderCertMapper.updateByPrimaryKey(bizPurchaseOrderCert);
        return update > 0 ? bizPurchaseOrderCertDtoMapper.toDto(bizPurchaseOrderCert) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizPurchaseOrderCertMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizPurchaseOrderCertDto> selectAll(BizPurchaseOrderCertParam exportParam, UserInfoToken userInfo) {
        BizPurchaseOrderCert bizPurchaseOrderCert = bizPurchaseOrderCertDtoMapper.toPo(exportParam);
        // bizPurchaseOrderCert.setTradeCode(userInfo.getCompany());
        List<BizPurchaseOrderCertDto> bizPurchaseOrderCertDtos = new ArrayList<>();
        List<BizPurchaseOrderCert> bizPurchaseOrderCerts = bizPurchaseOrderCertMapper.getList(bizPurchaseOrderCert);
        if (CollectionUtils.isNotEmpty(bizPurchaseOrderCerts)) {
            bizPurchaseOrderCertDtos = bizPurchaseOrderCerts.stream().map(head -> {
                BizPurchaseOrderCertDto dto = bizPurchaseOrderCertDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizPurchaseOrderCertDtos;
    }

    public ResultObject<BizPurchaseOrderCertDto> getPurchaseOrderCertByHeadSid(BizPurchaseOrderCertParam param, UserInfoToken userInfo) {
        ResultObject<BizPurchaseOrderCertDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizPurchaseOrderCert bizPurchaseOrderCert = bizPurchaseOrderCertMapper.getPurchaseOrderCertByHeadSid(param.getHeadId());
        if (bizPurchaseOrderCert != null) {
            BizPurchaseOrderCertDto dto = bizPurchaseOrderCertDtoMapper.toDto(bizPurchaseOrderCert);
            resultObject.setData(dto);
        }
        return resultObject;
    }

    public void deleteByHeadSids(List<String> sids) {
        bizPurchaseOrderCertMapper.deleteByHeadSids(sids);
    }
}
