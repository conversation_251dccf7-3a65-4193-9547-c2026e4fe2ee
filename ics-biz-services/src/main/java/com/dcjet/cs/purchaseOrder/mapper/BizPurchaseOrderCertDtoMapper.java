package com.dcjet.cs.purchaseOrder.mapper;
import com.dcjet.cs.dto.purchaseOrder.*;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderCert;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizPurchaseOrderCertDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizPurchaseOrderCertDto toDto(BizPurchaseOrderCert po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizPurchaseOrderCert toPo(BizPurchaseOrderCertParam param);
    /**
     * 数据库原始数据更新
     * @param bizPurchaseOrderCertParam
     * @param bizPurchaseOrderCert
     */
    void updatePo(BizPurchaseOrderCertParam bizPurchaseOrderCertParam, @MappingTarget BizPurchaseOrderCert bizPurchaseOrderCert);
    default void patchPo(BizPurchaseOrderCertParam bizPurchaseOrderCertParam, BizPurchaseOrderCert bizPurchaseOrderCert) {
        // TODO 自行实现局部更新
    }
}
