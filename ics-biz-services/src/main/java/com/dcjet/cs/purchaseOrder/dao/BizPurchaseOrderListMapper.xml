<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.purchaseOrder.dao.BizPurchaseOrderListMapper">
    <resultMap id="bizPurchaseOrderListResultMap" type="com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderList">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR" />
		<result column="PRODUCT_MODEL" property="productModel" jdbcType="VARCHAR" />
		<result column="UNIT" property="unit" jdbcType="VARCHAR" />
		<result column="QTY" property="qty" jdbcType="NUMERIC" />
		<result column="UNIT_PRICE" property="unitPrice" jdbcType="NUMERIC" />
		<result column="AMOUNT" property="amount" jdbcType="NUMERIC" />
		<result column="NOTE" property="note" jdbcType="VARCHAR" />
		<result column="CONTAINER_NUM" property="containerNum" jdbcType="NUMERIC" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="CONTRACT_LIST_ID" property="contractListId" jdbcType="VARCHAR" />
		<result column="ANALYSE_LIST_ID" property="analyseListId" jdbcType="VARCHAR" />
		<result column="GROSS_WEIGHT" property="grossWeight" jdbcType="NUMERIC" />
		<result column="NET_WEIGHT" property="netWeight" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,CREATE_BY
     ,CREATE_TIME
     ,CREATE_USER_NAME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,UPDATE_USER_NAME
     ,TRADE_CODE
     ,SYS_ORG_CODE
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
     ,BUSINESS_TYPE
     ,PRODUCT_NAME
     ,PRODUCT_MODEL
     ,UNIT
     ,QTY
     ,UNIT_PRICE
     ,AMOUNT
     ,NOTE
     ,CONTAINER_NUM
     ,HEAD_ID
     ,CONTRACT_LIST_ID
     ,ANALYSE_LIST_ID
     ,GROSS_WEIGHT
     ,NET_WEIGHT
    </sql>
    <sql id="condition">
     HEAD_ID = #{headId}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizPurchaseOrderListResultMap" parameterType="com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_PURCHASE_ORDER_LIST t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getPurchaseOrderListQty" resultType="com.dcjet.cs.seven.model.SevenForeignContractList">
        SELECT t.ID, t.G_NAME ,t.G_MODEL,
               (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) AS qtya
        FROM T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST t
                 LEFT JOIN T_BIZ_PURCHASE_ORDER_LIST o ON t.id = o.CONTRACT_LIST_ID and t.BODY_TYPE = 'slice'
        WHERE t.ID = #{sid}
        GROUP BY t.ID, t.G_NAME ,t.G_MODEL,t.QTY
        HAVING (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) > 0;
    </select>
    <select id="getListByHeadId" resultType="com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_PURCHASE_ORDER_LIST t
        where HEAD_ID = #{headId}
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_PURCHASE_ORDER_LIST t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadSids">
        delete from T_BIZ_PURCHASE_ORDER_LIST t where t.HEAD_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
