package com.dcjet.cs.purchaseOrder.service;
import com.aizuda.bpm.engine.assist.ObjectUtils;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderList;
import com.dcjet.cs.purchaseOrder.dao.BizPurchaseOrderHeadMapper;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.purchaseOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.purchaseOrder.dao.BizPurchaseOrderListMapper;
import com.dcjet.cs.purchaseOrder.mapper.BizPurchaseOrderListDtoMapper;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-14
 */
@Service
public class BizPurchaseOrderListService extends BaseService<BizPurchaseOrderList> {
    @Resource
    private BizPurchaseOrderListMapper bizPurchaseOrderListMapper;
    @Resource
    private BizPurchaseOrderListDtoMapper bizPurchaseOrderListDtoMapper;
    @Resource
    private BizPurchaseOrderHeadMapper bizPurchaseOrderHeadMapper;
    @Override
    public Mapper<BizPurchaseOrderList> getMapper() {
        return bizPurchaseOrderListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizPurchaseOrderListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizPurchaseOrderListDto>> getListPaged(BizPurchaseOrderListParam bizPurchaseOrderListParam, PageParam pageParam) {
        // 启用分页查询
        BizPurchaseOrderList bizPurchaseOrderList = bizPurchaseOrderListDtoMapper.toPo(bizPurchaseOrderListParam);
        Page<BizPurchaseOrderList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizPurchaseOrderListMapper.getList(bizPurchaseOrderList));
        List<BizPurchaseOrderListDto> bizPurchaseOrderListDtos = page.getResult().stream().map(head -> {
            BizPurchaseOrderListDto dto = bizPurchaseOrderListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizPurchaseOrderListDto>> paged = ResultObject.createInstance(bizPurchaseOrderListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizPurchaseOrderListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPurchaseOrderListDto insert(BizPurchaseOrderListParam bizPurchaseOrderListParam, UserInfoToken userInfo) {
        BizPurchaseOrderList bizPurchaseOrderList = bizPurchaseOrderListDtoMapper.toPo(bizPurchaseOrderListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizPurchaseOrderList.setSid(sid);
        bizPurchaseOrderList.setCreateBy(userInfo.getUserNo());
        bizPurchaseOrderList.setCreateUserName(userInfo.getUserName());
        bizPurchaseOrderList.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizPurchaseOrderListMapper.insert(bizPurchaseOrderList);
        return  insertStatus > 0 ? bizPurchaseOrderListDtoMapper.toDto(bizPurchaseOrderList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizPurchaseOrderListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPurchaseOrderListDto update(BizPurchaseOrderListParam bizPurchaseOrderListParam, UserInfoToken userInfo) {
        BizPurchaseOrderList bizPurchaseOrderList = bizPurchaseOrderListMapper.selectByPrimaryKey(bizPurchaseOrderListParam.getSid());
        if(null != bizPurchaseOrderListParam.getQty() && bizPurchaseOrderListParam.getQty().compareTo(bizPurchaseOrderList.getQty()) != 0) {
            SevenForeignContractList foreignContractListQty = bizPurchaseOrderListMapper.getPurchaseOrderListQty(bizPurchaseOrderList.getContractListId());
            if ((ObjectUtils.isEmpty(foreignContractListQty) && bizPurchaseOrderListParam.getQty().compareTo(bizPurchaseOrderList.getQty()) > 0)
                    || (!ObjectUtils.isEmpty(foreignContractListQty) && bizPurchaseOrderListParam.getQty().compareTo(foreignContractListQty.getQtya().add(bizPurchaseOrderList.getQty())) > 0)) {
                throw new ErrorException(400, "进货单表体数量超过外商合同表体数量！");
            }
        }
        bizPurchaseOrderListDtoMapper.updatePo(bizPurchaseOrderListParam, bizPurchaseOrderList);
        if(null != bizPurchaseOrderList.getQty() && null != bizPurchaseOrderList.getUnitPrice()){
            bizPurchaseOrderList.setAmount(bizPurchaseOrderList.getQty().multiply(bizPurchaseOrderList.getUnitPrice()).setScale(2, RoundingMode.HALF_UP));
        }

        bizPurchaseOrderList.setUpdateBy(userInfo.getUserNo());
        bizPurchaseOrderList.setCreateUserName(userInfo.getUserName());
        bizPurchaseOrderList.setUpdateTime(new Date());
        // 更新数据
        int update = bizPurchaseOrderListMapper.updateByPrimaryKey(bizPurchaseOrderList);
        if(update > 0){
            List<BizPurchaseOrderList> listByHeadId = bizPurchaseOrderListMapper.getListByHeadId(bizPurchaseOrderList.getHeadId());
            BizPurchaseOrderHead head = new BizPurchaseOrderHead();
            head.setSid(bizPurchaseOrderList.getHeadId());
            head.setTotalAmount(listByHeadId.stream().map(BizPurchaseOrderList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            bizPurchaseOrderHeadMapper.updateByPrimaryKeySelective(head);
        }
        return update > 0 ? bizPurchaseOrderListDtoMapper.toDto(bizPurchaseOrderList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizPurchaseOrderListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizPurchaseOrderListDto> selectAll(BizPurchaseOrderListParam exportParam, UserInfoToken userInfo) {
        BizPurchaseOrderList bizPurchaseOrderList = bizPurchaseOrderListDtoMapper.toPo(exportParam);
        // bizPurchaseOrderList.setTradeCode(userInfo.getCompany());
        List<BizPurchaseOrderListDto> bizPurchaseOrderListDtos = new ArrayList<>();
        List<BizPurchaseOrderList> bizPurchaseOrderLists = bizPurchaseOrderListMapper.getList(bizPurchaseOrderList);
        if (CollectionUtils.isNotEmpty(bizPurchaseOrderLists)) {
            bizPurchaseOrderListDtos = bizPurchaseOrderLists.stream().map(head -> {
                BizPurchaseOrderListDto dto = bizPurchaseOrderListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizPurchaseOrderListDtos;
    }

    public void deleteByHeadSids(List<String> sids) {
        bizPurchaseOrderListMapper.deleteByHeadSids(sids);
    }

    public List<BizPurchaseOrderList> getListByHeadId(String sid) {
        return bizPurchaseOrderListMapper.getListByHeadId(sid);
    }
}
