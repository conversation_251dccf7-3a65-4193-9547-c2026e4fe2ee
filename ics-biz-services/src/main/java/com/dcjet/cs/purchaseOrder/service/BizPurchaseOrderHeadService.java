package com.dcjet.cs.purchaseOrder.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.attach.dao.AttachedMapper;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.baseInfoCustomerParams.service.BaseInfoCustomerParamsService;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.customerAccount.model.BizCustomerAccount;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderContainerList;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderList;
import com.dcjet.cs.deliveryOrder.service.BizDeliveryOrderHeadService;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderHeadDto;
import com.dcjet.cs.dto.deliveryOrder.DeliveryOrderDeclarationData;
import com.dcjet.cs.dto.deliveryOrder.DeliveryOrderDeclarationRequest;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.AttachmentItemDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.AttachmentRequestDto;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadDto;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadParam;
import com.dcjet.cs.params.service.PriceTermsService;
import com.dcjet.cs.purchaseOrder.dao.BizPurchaseOrderListMapper;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderList;
import com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper;
import com.dcjet.cs.seven.mapper.SevenForeignContractHeadDtoMapper;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.RequestUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.purchaseOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.purchaseOrder.dao.BizPurchaseOrderHeadMapper;
import com.dcjet.cs.purchaseOrder.mapper.BizPurchaseOrderHeadDtoMapper;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Service
public class BizPurchaseOrderHeadService extends BaseService<BizPurchaseOrderHead> {
    private static final Logger log = LoggerFactory.getLogger(BizPurchaseOrderHeadService.class);
    @Resource
    private BaseInfoCustomerParamsService baseInfoCustomerParamsService;
    @Resource
    private BizPurchaseOrderHeadMapper bizPurchaseOrderHeadMapper;
    @Resource
    private SevenForeignContractHeadMapper sevenForeignContractHeadMapper;
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private BizPurchaseOrderListMapper bizPurchaseOrderListMapper;
    @Resource
    private BizPurchaseOrderHeadDtoMapper bizPurchaseOrderHeadDtoMapper;
    @Resource
    private BizPurchaseOrderListService bizPurchaseOrderListService;
    @Resource
    private BizPurchaseOrderCertService bizPurchaseOrderCertService;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private PriceTermsService priceTermsService;
    @Resource
    private CommonService commonService;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Override
    public Mapper<BizPurchaseOrderHead> getMapper() {
        return bizPurchaseOrderHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizPurchaseOrderHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizPurchaseOrderHeadDto>> getListPaged(BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadDtoMapper.toPo(bizPurchaseOrderHeadParam);
        Page<BizPurchaseOrderHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizPurchaseOrderHeadMapper.getList(bizPurchaseOrderHead));
        List<BizPurchaseOrderHeadDto> bizPurchaseOrderHeadDtos = page.getResult().stream().map(head -> {
            BizPurchaseOrderHeadDto dto = bizPurchaseOrderHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizPurchaseOrderHeadDto>> paged = ResultObject.createInstance(bizPurchaseOrderHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizPurchaseOrderHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPurchaseOrderHeadDto insert(BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, UserInfoToken userInfo) {
        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadDtoMapper.toPo(bizPurchaseOrderHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizPurchaseOrderHead.setSid(sid);
        bizPurchaseOrderHead.setCreateBy(userInfo.getUserNo());
        bizPurchaseOrderHead.setCreateTime(new Date());
        bizPurchaseOrderHead.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = bizPurchaseOrderHeadMapper.insert(bizPurchaseOrderHead);
        return  insertStatus > 0 ? bizPurchaseOrderHeadDtoMapper.toDto(bizPurchaseOrderHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizPurchaseOrderHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPurchaseOrderHeadDto update(BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, UserInfoToken userInfo) {
        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadMapper.selectByPrimaryKey(bizPurchaseOrderHeadParam.getSid());
        bizPurchaseOrderHeadDtoMapper.updatePo(bizPurchaseOrderHeadParam, bizPurchaseOrderHead);
        bizPurchaseOrderHead.setUpdateBy(userInfo.getUserNo());
        bizPurchaseOrderHead.setUpdateUserName(userInfo.getUserName());
        bizPurchaseOrderHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizPurchaseOrderHeadMapper.updateByPrimaryKey(bizPurchaseOrderHead);
        return update > 0 ? bizPurchaseOrderHeadDtoMapper.toDto(bizPurchaseOrderHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizPurchaseOrderHeadMapper.deleteBySids(sids);
        bizPurchaseOrderListService.deleteByHeadSids(sids);
        bizPurchaseOrderCertService.deleteByHeadSids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizPurchaseOrderHeadDto> selectAll(BizPurchaseOrderHeadParam exportParam, UserInfoToken userInfo) {
        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadDtoMapper.toPo(exportParam);
        // bizPurchaseOrderHead.setTradeCode(userInfo.getCompany());
        List<BizPurchaseOrderHeadDto> bizPurchaseOrderHeadDtos = new ArrayList<>();
        List<BizPurchaseOrderHead> bizPurchaseOrderHeads = bizPurchaseOrderHeadMapper.getList(bizPurchaseOrderHead);
        if (CollectionUtils.isNotEmpty(bizPurchaseOrderHeads)) {
            bizPurchaseOrderHeadDtos = bizPurchaseOrderHeads.stream().map(head -> {
                BizPurchaseOrderHeadDto dto = bizPurchaseOrderHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizPurchaseOrderHeadDtos;
    }

    public ResultObject getCreateUserList(BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizPurchaseOrderHeadMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    public ResultObject<BizPurchaseOrderHeadDto> getPurchaseOrderSid(BizPurchaseOrderHeadParam param, UserInfoToken userInfo) {
        ResultObject<BizPurchaseOrderHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadMapper.selectByPrimaryKey(param.getSid());
        if (bizPurchaseOrderHead != null) {
            bizPurchaseOrderHead.setCreaterBy(StringUtils.isNotBlank(bizPurchaseOrderHead.getUpdateBy()) ? bizPurchaseOrderHead.getUpdateBy() : bizPurchaseOrderHead.getCreateBy());
            bizPurchaseOrderHead.setCreaterTime(null != bizPurchaseOrderHead.getUpdateTime() ? bizPurchaseOrderHead.getUpdateTime() : bizPurchaseOrderHead.getCreateTime());
            bizPurchaseOrderHead.setCreaterUserName(StringUtils.isNotBlank(bizPurchaseOrderHead.getUpdateUserName()) ? bizPurchaseOrderHead.getUpdateUserName() : bizPurchaseOrderHead.getCreateUserName());
            BizPurchaseOrderHeadDto dto = bizPurchaseOrderHeadDtoMapper.toDto(bizPurchaseOrderHead);
            resultObject.setData(dto);
        }
        return resultObject;
    }

    public ResultObject confirmStatus(BizPurchaseOrderHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadMapper.selectByPrimaryKey(param.getSid());
        if (bizPurchaseOrderHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizPurchaseOrderHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizPurchaseOrderHead update = bizPurchaseOrderHeadDtoMapper.toPo(param);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsConfirm("1");
        bizPurchaseOrderHeadMapper.updateByPrimaryKeySelective(update);
        BizPurchaseOrderHeadDto dto = bizPurchaseOrderHeadDtoMapper.toDto(update);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadMapper.selectByPrimaryKey(sid);
        if (bizPurchaseOrderHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizPurchaseOrderHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizPurchaseOrderHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizPurchaseOrderHead update = new BizPurchaseOrderHead();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizPurchaseOrderHeadMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResponseEntity print(BizPurchaseOrderHeadParam param, UserInfoToken userInfo) throws IOException {
        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadMapper.selectByPrimaryKey(param.getSid());
        if(bizPurchaseOrderHead == null){
            throw new ErrorException(400,"进货单表头不存在！");
        }
        List<BizPurchaseOrderList> bizPurchaseOrderList = bizPurchaseOrderListService.getListByHeadId(param.getSid());
        if(bizPurchaseOrderList == null || bizPurchaseOrderList.size() == 0){
            throw new ErrorException(400,"进货单表体不存在！");
        }
        extracted(bizPurchaseOrderHead, bizPurchaseOrderList);

        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        if(!ObjectUtils.isEmpty(bizMerchants)){
            Optional<BizMerchant> first = bizMerchants.stream().filter(x -> x.getMerchantCode().equals(bizPurchaseOrderHead.getSupplier())).findFirst();
            first.ifPresent(merchant -> bizPurchaseOrderHead.setSupplier(bizPurchaseOrderHead.getSupplier()));
        }
        String portOfDeparture = baseInfoCustomerParamsService.getNameByCode(bizPurchaseOrderHead.getPortOfDeparture(), "PORT", bizPurchaseOrderHead.getBusinessType());
        String portOfDestination = baseInfoCustomerParamsService.getNameByCode(bizPurchaseOrderHead.getPortOfDestination(), "PORT", bizPurchaseOrderHead.getBusinessType());

        String priceTerm = priceTermsService.getNameByPriceTerm(bizPurchaseOrderHead.getPriceTerm());
        bizPurchaseOrderHead.setPriceTerm(priceTerm!=null?bizPurchaseOrderHead.getPriceTerm()+" "+priceTerm:"");
        bizPurchaseOrderHead.setPortOfDeparture(portOfDeparture!=null?portOfDeparture:"");
        bizPurchaseOrderHead.setPortOfDestination(portOfDestination!=null?portOfDestination:"");

        String tempName = "purchase_order.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(new Date());

        bizPurchaseOrderHead.setCreaterUserName(StringUtils.isEmpty(bizPurchaseOrderHead.getUpdateUserName()) ? bizPurchaseOrderHead.getCreateUserName() : bizPurchaseOrderHead.getUpdateUserName());
        bizPurchaseOrderHead.setCreaterTime(null != bizPurchaseOrderHead.getUpdateTime() ? bizPurchaseOrderHead.getUpdateTime() : bizPurchaseOrderHead.getCreateTime());
        String outName = xdoi18n.XdoI18nUtil.t("进货单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Collections.singletonList(bizPurchaseOrderHead),bizPurchaseOrderList,fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    private void extracted(BizPurchaseOrderHead bizPurchaseOrderHead, List<BizPurchaseOrderList> bizPurchaseOrderList) {
        for(BizPurchaseOrderList list : bizPurchaseOrderList){
            if(list.getUnit()!= null){
                String unit = baseInfoCustomerParamsService.getNameByCode(list.getUnit(), "UNIT", list.getBusinessType());
                list.setUnit(unit);
            }
            if(bizPurchaseOrderHead.getCurr()!= null){
                if(list.getUnitPrice()!= null){
                    list.setUnitPriceCurr(bizPurchaseOrderHead.getCurr()+" "+list.getUnitPrice().setScale(2, RoundingMode.HALF_UP));
                }
                if(list.getUnitPrice()!= null){
                    list.setAmountCurr(bizPurchaseOrderHead.getCurr()+" "+list.getAmount().setScale(2, RoundingMode.HALF_UP));
                }
            }
        }
    }

    public ResultObject sendEntry(BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String id = params.getSid();

        try {
            // 1. 获取认证Token
            String accessToken = getAuthToken();
            if (accessToken == null) {
                throw new ErrorException(400, "获取认证Token失败，无法发送数据");
            }

            // 2. 获取通关配置信息
            GwstdHttpConfig httpConfig = commonService.getHttpConfigInfo("GW_ERP_I_DEC_LIST");
            if (httpConfig == null) {
                throw new ErrorException(400, "未配置通关接口信息，type: GW_ERP_I_DEC_LIST");
            }

            // 拼接完整的接口地址
            String apiUrl = httpConfig.getBaseUrl() + httpConfig.getServiceUrl();

            // 2. 查询表头数据
            BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadMapper.selectByPrimaryKey(id);
            if (bizPurchaseOrderHead == null) {
                throw new ErrorException(400, "未找到对应的进货信息表头数据");
            }

            // 3. 查询表体数据
            List<BizPurchaseOrderList> listByHeadId = bizPurchaseOrderListMapper.getListByHeadId(bizPurchaseOrderHead.getSid());

            if (CollectionUtils.isEmpty(listByHeadId)) {
                throw new ErrorException(400, "未找到对应的进货信息表体数据");
            }

            // 4. 构建发送数据
            PurchaseOrderDeclarationRequest requestData = buildSendData(bizPurchaseOrderHead, listByHeadId);

            // 5. 转换为JSON并发送
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonData = objectMapper.writeValueAsString(requestData);

            log.info("发送进货信息到通关接口，URL: {}, 数据: {}", apiUrl, jsonData);

            // 6. 发送HTTP请求，使用获取到的accessToken
            String response = RequestUtil.sendPost(apiUrl, accessToken, jsonData);

            log.info("通关接口返回结果: {}", response);

            // 7. 解析返回结果并处理
            ObjectMapper responseMapper = new ObjectMapper();
            Map<String, Object> responseMap = responseMapper.readValue(response, Map.class);

            Boolean success = (Boolean) responseMap.get("success");
            String message = (String) responseMap.get("message");
            Object data = responseMap.get("data");

            if (success != null && success && data == null) {
                // 成功且data为null，更新发送状态
                bizPurchaseOrderHead.setSendDeclare("0");
                bizPurchaseOrderHeadMapper.updateByPrimaryKey(bizPurchaseOrderHead);

                resultObject.setMessage("发送成功");
                resultObject.setData(response);
                log.info("数据发送成功，已更新发送状态");

            } else if (data != null) {
                // data不为null，说明有错误信息，不更新状态，返回错误信息
                resultObject.setSuccess(false);
                resultObject.setMessage(message != null ? message : "数据校验失败");

                // 处理错误数据，提取facGNo和errorMsg
                String formattedErrorMessage = formatErrorMessage(data);
                resultObject.setData(formattedErrorMessage);
                log.warn("数据校验失败: {}", message);

            } else {
                // 其他情况，根据success状态处理
                if (success != null && success) {
                    bizPurchaseOrderHead.setSendDeclare("0");
                    bizPurchaseOrderHeadMapper.updateByPrimaryKey(bizPurchaseOrderHead);
                    resultObject.setMessage(message != null ? message : "发送成功");
                    resultObject.setData(response);
                } else {
                    resultObject.setSuccess(false);
                    resultObject.setMessage(message != null ? message : "发送失败");
                    resultObject.setData(response);
                }
            }
            //发送附件至关务
            processAttachments(bizPurchaseOrderHead, accessToken, userInfo);

        } catch (Exception e) {
            log.error("发送进货信息到通关接口失败", e);
            resultObject.setSuccess(false);
            resultObject.setMessage("发送失败: " + e.getMessage());
        }

        return resultObject;
    }
    private void processAttachments(BizPurchaseOrderHead bizPurchaseOrderHead, String accessToken, UserInfoToken userInfo) {
        GwstdHttpConfig httpConfig = commonService.getHttpConfigInfo("GW_ATTACH");
        if (httpConfig == null) {
            throw new ErrorException(400, "未配置附件接口信息，type: GW_ATTACH");
        }

        // 拼接完整的接口地址
        String apiUrl = httpConfig.getBaseUrl() + httpConfig.getServiceUrl();

        //组装body
        List<Attached> byHeadId = attachedMapper.getByHeadId(bizPurchaseOrderHead.getSid(), userInfo.getCompany());

        AttachmentRequestDto attachmentRequestDto = new AttachmentRequestDto();
        attachmentRequestDto.setPurchaseOrderNo(bizPurchaseOrderHead.getPurchaseOrderNo());
        List<AttachmentItemDto> attachmentItemList = new ArrayList<>();
        byHeadId.forEach(item -> {
            AttachmentItemDto attachmentItemDto = new AttachmentItemDto();
            attachmentItemDto.setAttachmentType(item.getAcmpType());
            //base64
            try {
                attachmentItemDto.setFileContent(Base64.getEncoder().encodeToString(fileHandler.downloadFile(item.getFileName())));
            } catch (Exception e) {
                attachmentItemDto.setFileContent(StringUtils.EMPTY);
            }
            attachmentItemDto.setFileName(item.getOriginFileName());
            attachmentItemDto.setNote(item.getNote());
            attachmentItemList.add(attachmentItemDto);
        });
        attachmentRequestDto.setAttachmentList(attachmentItemList);

        try {
            // 5. 转换为JSON并发送
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonData = objectMapper.writeValueAsString(attachmentRequestDto);

            log.info("发送附件到附件接口，URL: {}, 数据: {}", apiUrl, jsonData);

            // 6. 发送HTTP请求，使用获取到的accessToken
            String response = RequestUtil.sendPost(apiUrl, accessToken, jsonData);

            log.info("附件接口返回结果: {}", response);

        } catch (Exception e) {
            log.error("发送附件到附件接口失败", e);
        }

    }
    /**
     * 获取中央认证接口的Token
     * @return 返回accessToken，如果获取失败返回null
     */
    private String getAuthToken() {
        try {
            // 1. 获取认证接口配置信息
            GwstdHttpConfig authConfig = commonService.getHttpConfigInfo("GW_AUTH_TOKEN");
            if (authConfig == null) {
                log.error("未配置认证接口信息，type: GW_AUTH_TOKEN");
                return null;
            }

            // 2. 构建认证请求数据
            Map<String, String> authData = new HashMap<>();
            authData.put("userno", authConfig.getExtendFiled1()); // 用户名存储在扩展字段1
            authData.put("password", authConfig.getExtendFiled2()); // 密码存储在扩展字段2

            // 3. 转换为JSON
            ObjectMapper objectMapper = new ObjectMapper();
            String authJson = objectMapper.writeValueAsString(authData);

            // 4. 拼接认证接口地址
            String authUrl = authConfig.getBaseUrl() + authConfig.getServiceUrl();

            log.info("调用中央认证接口，URL: {}, 请求数据: {}", authUrl, authJson);

            // 5. 发送认证请求
            String response = RequestUtil.sendPost(authUrl, null, authJson);

            log.info("中央认证接口返回结果: {}", response);

            // 6. 解析返回结果
            Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);
            Boolean success = (Boolean) responseMap.get("success");

            if (success != null && success) {
                Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
                if (data != null) {
                    String accessToken = (String) data.get("accessToken");
                    log.info("成功获取accessToken: {}", accessToken);
                    return accessToken;
                }
            } else {
                String message = (String) responseMap.get("message");
                log.error("认证失败: {}", message);
            }

        } catch (Exception e) {
            log.error("获取认证Token失败", e);
        }

        return null;
    }
    private String formatErrorMessage(Object data) {
        try {
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                Object errorDataListObj = dataMap.get("errorDataList");

                if (errorDataListObj instanceof List) {
                    List<Map<String, Object>> errorDataList = (List<Map<String, Object>>) errorDataListObj;
                    StringBuilder errorMessage = new StringBuilder();

                    for (int i = 0; i < errorDataList.size(); i++) {
                        Map<String, Object> errorItem = errorDataList.get(i);

                        // 获取errorData中的facGNo和errorMsg
                        Object errorDataObj = errorItem.get("errorData");
                        String errorMsg = (String) errorItem.get("errorMsg");

                        String facGNo = "";
                        if (errorDataObj instanceof Map) {
                            Map<String, Object> errorDataMap = (Map<String, Object>) errorDataObj;
                            facGNo = (String) errorDataMap.get("facGNo");
                        }

                        // 组合facGNo和errorMsg
                        if (StringUtils.isNotBlank(facGNo) && StringUtils.isNotBlank(errorMsg)) {
                            if (i > 0) {
                                errorMessage.append("; ");
                            }
                            errorMessage.append("商品[").append(facGNo).append("]: ").append(errorMsg);
                        } else if (StringUtils.isNotBlank(errorMsg)) {
                            if (i > 0) {
                                errorMessage.append("; ");
                            }
                            errorMessage.append(errorMsg);
                        }
                    }

                    return errorMessage.toString();
                }
            }
        } catch (Exception e) {
            log.error("格式化错误信息失败", e);
        }

        return "数据校验失败，请检查数据格式";
    }
    private PurchaseOrderDeclarationRequest buildSendData(BizPurchaseOrderHead head, List<BizPurchaseOrderList> list) {
        PurchaseOrderDeclarationRequest request = new PurchaseOrderDeclarationRequest();
        List<PurchaseOrderDeclarationData> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = dateFormat.format(new Date());

        for (int i = 0; i < list.size(); i++) {
            BizPurchaseOrderList goods = list.get(i);
            PurchaseOrderDeclarationData data = new PurchaseOrderDeclarationData();

            // 根据字段对应关系构建数据
            data.setBillNo(head.getPurchaseOrderNo()); // 单据号 - 出货单号
            data.setBillSerialNo(i + 1); // 单据序号 - 表体行号
            data.setBondMark(""); // 保完税标志 - 空
            data.setGMark(""); // 物料类型 - 空
            data.setFacGNo(goods.getProductName()); // 企业料号 - 商品名称
            data.setQtyErp(goods.getQty()); // 数量 - 进货信息表体-数量
            data.setDecPrice(goods.getUnitPrice()); // 单价 - 进货信息表体-单价
            data.setDecTotal(goods.getAmount()); // 总价 - 进货信息表体-金额
            data.setUnitErp(goods.getUnit()); // ERP交易单位 - 进货信息表体-单位
            data.setCurr(head.getCurr()); // 币制 - 进货信息表头-币种
            data.setNetWt(goods.getQty()); // 净重 - 当前数量对应的净重
            data.setGrossWet(""); // 毛重 - 空
            data.setOriginCountry(""); // 原产国 - 空
            data.setInvNo(head.getInvoiceNo()); // 发票号 - 进货信息表头-发票号码
            data.setPOInvDate("");// 发票日期 - 空
            data.setPONumber("");
            data.setPOLineNumber("");
            data.setPODate("");
            data.setPOQty("");
            data.setSupplierCode(head.getSupplier()); // 供应商代码 - 进货信息表头-供应商
            data.setIeMode(""); // 进口方式 - 空
            data.setFirstQty(""); // 法定数量 - 空
            data.setSecondQty(""); // 法定第二数量 - 空
            data.setNote(""); // 备注 - 空
            data.setLastUpdateTime(currentTime); // ERP创建时间 - 接口数据接收时间
            data.setTempOwner(currentTime); // 传输批次号 - 接口数据接收时间
            data.setDutyMode(""); // 征免方式 - 空
            data.setDistrictCode(""); // 境内目的地 - 空
            data.setDistrictPostCode(""); // 境内目的地行政区划 - 空
            data.setDestinationCountry(""); // 最终目的国 - 空
            data.setEntryGNo(""); // 报关单归并序号 - 空
            data.setRemark1(goods.getNote()); // 备注1 - 进货信息表体-商品描述
            data.setRemark2(""); // 备注2 - 空
            data.setRemark3(""); // 备注3 - 空
            data.setInOutRelNo(""); // 入库关联单号 - 空
            dataList.add(data);
        }
        request.setData(dataList);
        return request;
    }


    @Resource
    private SevenForeignContractHeadDtoMapper sevenForeignContractHeadDtoMapper;

    public ResultObject listInPurchaseOrderHead(BizPurchaseOrderHeadParam params,PageParam pageParam, UserInfoToken userInfo) {
        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadDtoMapper.toPo(params);
        bizPurchaseOrderHead.setTradeCode(userInfo.getCompany());
        Page<SevenForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizPurchaseOrderHeadMapper.getForeignContractHeadListQty(bizPurchaseOrderHead));
        List<SevenForeignContractHeadDto> headDtoList = page.getResult().stream()
                .map(fcd -> this.sevenForeignContractHeadDtoMapper.toDto(fcd))
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }

    public ResultObject insertByContract(BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        SevenForeignContractHead sevenForeignContractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(params.getSid());
        if(ObjectUtils.isEmpty(sevenForeignContractHead)){
            resultObject.setSuccess(false);
            return resultObject;
        }
        BizMerchant bizMerchant = bizMerchantMapper.getByMerchantCode(sevenForeignContractHead.getBuyer(), userInfo.getCompany());
        List<SevenForeignContractList> contractList = bizPurchaseOrderHeadMapper.getForeignContractListQty(sevenForeignContractHead.getId());

        BigDecimal totalGrossWt = new BigDecimal(0);
        BigDecimal totalNetWt = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);

        BizPurchaseOrderHead bizPurchaseOrderHead = new BizPurchaseOrderHead();
        String sid = UUID.randomUUID().toString();
        bizPurchaseOrderHead.setSid(sid);
        bizPurchaseOrderHead.setBusinessType("7");
        bizPurchaseOrderHead.setContractNo(sevenForeignContractHead.getContractNo());
        bizPurchaseOrderHead.setPurchaseOrderNo(createSerilNo(bizPurchaseOrderHead.getContractNo()));
        bizPurchaseOrderHead.setSupplier(sevenForeignContractHead.getSeller());
        bizPurchaseOrderHead.setCustomer(sevenForeignContractHead.getBuyer());
        bizPurchaseOrderHead.setCustomerAddress(bizMerchant.getMerchantAddress());
        bizPurchaseOrderHead.setBusinessEnterprise(sevenForeignContractHead.getBuyer());
        bizPurchaseOrderHead.setDeliveryEnterprise(sevenForeignContractHead.getBuyer());
        bizPurchaseOrderHead.setDeliveryEnterpriseAddress(bizMerchant.getMerchantAddress());
        bizPurchaseOrderHead.setPortOfDestination(sevenForeignContractHead.getDestPort());
        bizPurchaseOrderHead.setPaymentMethod(sevenForeignContractHead.getPaymentMethod());
        bizPurchaseOrderHead.setCurr(sevenForeignContractHead.getCurr());
        bizPurchaseOrderHead.setPriceTerm(sevenForeignContractHead.getPriceTerm());
        bizPurchaseOrderHead.setPriceTermPort(sevenForeignContractHead.getPriceTermPort());

        if(CollectionUtils.isNotEmpty(contractList)){
            for (SevenForeignContractList foreignContractList : contractList) {
                BizPurchaseOrderList bizPurchaseOrderList = new BizPurchaseOrderList();
                bizPurchaseOrderList.setHeadId(sid);
                bizPurchaseOrderList.setContractListId(foreignContractList.getId());
                bizPurchaseOrderList.setProductName(foreignContractList.getGName());
                bizPurchaseOrderList.setProductModel(foreignContractList.getGModel());
                bizPurchaseOrderList.setUnit(foreignContractList.getUnit());
                bizPurchaseOrderList.setQty(null != foreignContractList.getQtya() ? foreignContractList.getQtya() : new BigDecimal(0));
                bizPurchaseOrderList.setUnitPrice(foreignContractList.getUnitPrice());
                BigDecimal amount = foreignContractList.getUnitPrice().multiply(bizPurchaseOrderList.getQty()).setScale(2, RoundingMode.HALF_UP);
                bizPurchaseOrderList.setAmount(amount);
                String lsid = UUID.randomUUID().toString();
                bizPurchaseOrderList.setSid(lsid);
                bizPurchaseOrderList.setTradeCode(userInfo.getCompany());
                bizPurchaseOrderList.setCreateUserName(userInfo.getUserName());
                bizPurchaseOrderList.setCreateBy(userInfo.getUserNo());
                bizPurchaseOrderList.setCreateTime(new Date());
                bizPurchaseOrderListMapper.insert(bizPurchaseOrderList);
                totalAmount = totalAmount.add(amount);

            }
            bizPurchaseOrderHead.setTotalGrossWt(totalGrossWt);
            bizPurchaseOrderHead.setTotalNetWt(totalNetWt);
            bizPurchaseOrderHead.setTotalAmount(totalAmount);
            if(null != bizPurchaseOrderHead.getTotalGrossWt() && null != bizPurchaseOrderHead.getTotalNetWt()){
                bizPurchaseOrderHead.setTotalTare(bizPurchaseOrderHead.getTotalGrossWt().subtract(bizPurchaseOrderHead.getTotalNetWt()).setScale(4,RoundingMode.HALF_UP));
            }
        }
        bizPurchaseOrderHead.setCreateUserName(userInfo.getUserName());
        bizPurchaseOrderHead.setCreateBy(userInfo.getUserNo());
        bizPurchaseOrderHead.setCreateTime(new Date());
        bizPurchaseOrderHead.setBusinessType("7");
        bizPurchaseOrderHead.setSendDeclare("1");
        bizPurchaseOrderHead.setPurchaseMark("1");
        bizPurchaseOrderHead.setPurchaseNoMark(sevenForeignContractHead.getId());
        bizPurchaseOrderHead.setStatus("0");
        bizPurchaseOrderHead.setRedFlush("1");
        bizPurchaseOrderHead.setSendFinance("0");
        bizPurchaseOrderHead.setApprStatus("0");
        bizPurchaseOrderHead.setTradeCode(userInfo.getCompany());

        int insertStatus = bizPurchaseOrderHeadMapper.insert(bizPurchaseOrderHead);
        bizPurchaseOrderHead.setCreaterBy(userInfo.getUserNo());
        bizPurchaseOrderHead.setCreaterTime(new Date());
        bizPurchaseOrderHead.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? bizPurchaseOrderHeadDtoMapper.toDto(bizPurchaseOrderHead) : null);
        return resultObject;
    }

    private String createSerilNo(String contractNo) {
        String serilNo = bizPurchaseOrderHeadMapper.getSerilNo(contractNo);
        if(StringUtils.isNotBlank(serilNo)){
            return contractNo+String.format("%03d",Integer.valueOf(serilNo.substring(serilNo.length() - 2))+1);
        }
        return contractNo+"01";
    }

    public ResultObject insertListByContract(BizPurchaseOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        SevenForeignContractHead sevenForeignContractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(params.getSid());
        if(ObjectUtils.isEmpty(sevenForeignContractHead)){
            resultObject.setSuccess(false);
            return resultObject;
        }
//        BigDecimal totalGrossWt = new BigDecimal(0);
        BigDecimal totalQty = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);

        BizPurchaseOrderHead bizPurchaseOrderHead = bizPurchaseOrderHeadMapper.getDataByContract(params.getContractNo());


        List<SevenForeignContractList> contractList = bizPurchaseOrderHeadMapper.getForeignContractListQty(sevenForeignContractHead.getId());
        if(CollectionUtils.isNotEmpty(contractList)){
            for (SevenForeignContractList foreignContractList : contractList) {
                BizPurchaseOrderList bizPurchaseOrderList = new BizPurchaseOrderList();
                bizPurchaseOrderList.setHeadId(bizPurchaseOrderHead.getSid());
                bizPurchaseOrderList.setContractListId(foreignContractList.getId());
                bizPurchaseOrderList.setProductName(foreignContractList.getGName());
                bizPurchaseOrderList.setProductModel(foreignContractList.getGModel());
                bizPurchaseOrderList.setUnit(foreignContractList.getUnit());
                bizPurchaseOrderList.setQty(null != foreignContractList.getQtya() ? foreignContractList.getQtya() : new BigDecimal(0));
                bizPurchaseOrderList.setUnitPrice(foreignContractList.getUnitPrice());
                BigDecimal amount = foreignContractList.getUnitPrice().multiply(bizPurchaseOrderList.getQty()).setScale(2, RoundingMode.HALF_UP);
                bizPurchaseOrderList.setAmount(amount);
                String lsid = UUID.randomUUID().toString();
                bizPurchaseOrderList.setSid(lsid);
                bizPurchaseOrderList.setTradeCode(userInfo.getCompany());
                bizPurchaseOrderList.setCreateUserName(userInfo.getUserName());
                bizPurchaseOrderList.setCreateBy(userInfo.getUserNo());
                bizPurchaseOrderList.setCreateTime(new Date());
                bizPurchaseOrderListMapper.insert(bizPurchaseOrderList);
                totalAmount = totalAmount.add(amount);
                totalQty = totalQty.add(bizPurchaseOrderList.getQty());
            }
//            bizPurchaseOrderHead.setTotalGrossWt(totalGrossWt);
//            bizPurchaseOrderHead.setTotalNetWt(totalNetWt);
            bizPurchaseOrderHead.setTotalAmount(totalAmount.add(bizPurchaseOrderHead.getTotalAmount()));
            bizPurchaseOrderHead.setTotalQuantity(totalQty.add(bizPurchaseOrderHead.getTotalQuantity()));
//            if(null != bizPurchaseOrderHead.getTotalGrossWt() && null != bizPurchaseOrderHead.getTotalNetWt()){
//                bizPurchaseOrderHead.setTotalTare(bizPurchaseOrderHead.getTotalGrossWt().subtract(bizPurchaseOrderHead.getTotalNetWt()).setScale(4,RoundingMode.HALF_UP));
//            }
        }
        bizPurchaseOrderHead.setUpdateUserName(userInfo.getUserName());
        bizPurchaseOrderHead.setUpdateBy(userInfo.getUserNo());
        bizPurchaseOrderHead.setUpdateTime(new Date());
        int updateStatus = bizPurchaseOrderHeadMapper.updateByPrimaryKey(bizPurchaseOrderHead);
        resultObject.setData(updateStatus > 0 ? bizPurchaseOrderHeadDtoMapper.toDto(bizPurchaseOrderHead) : null);
        return resultObject;
    }
}
