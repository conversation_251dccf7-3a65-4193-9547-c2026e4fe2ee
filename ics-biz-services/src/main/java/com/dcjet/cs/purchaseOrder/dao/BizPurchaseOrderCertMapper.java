package com.dcjet.cs.purchaseOrder.dao;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderCert;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizPurchaseOrderCert
* <AUTHOR>
* @date: 2025-7-11
*/
public interface BizPurchaseOrderCertMapper extends Mapper<BizPurchaseOrderCert> {
    /**
     * 查询获取数据
     * @param bizPurchaseOrderCert
     * @return
     */
    List<BizPurchaseOrderCert> getList(BizPurchaseOrderCert bizPurchaseOrderCert);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    BizPurchaseOrderCert getPurchaseOrderCertByHeadSid(String headId);

    void deleteByHeadSids(List<String> sids);
}
