package com.dcjet.cs.purchaseOrder.dao;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderHead;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizPurchaseOrderHead
* <AUTHOR>
* @date: 2025-7-11
*/
public interface BizPurchaseOrderHeadMapper extends Mapper<BizPurchaseOrderHead> {
    /**
     * 查询获取数据
     * @param bizPurchaseOrderHead
     * @return
     */
    List<BizPurchaseOrderHead> getList(BizPurchaseOrderHead bizPurchaseOrderHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<Map<String, String>> getCreateUserList(@Param("tradeCode")String tradeCode);

    List<SevenForeignContractHead> getForeignContractHeadListQty(BizPurchaseOrderHead bizPurchaseOrderHead);

    List<SevenForeignContractList> getForeignContractListQty(String headId);

    String getSerilNo(String contractNo);

    BizPurchaseOrderHead getDataByContract(String contractNo);
}
