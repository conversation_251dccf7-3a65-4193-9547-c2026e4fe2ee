package com.dcjet.cs.purchaseOrder.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Setter
@Getter
@Table(name = "t_biz_purchase_order_head")
public class BizPurchaseOrderHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 创建时间-开始
     */
	@Transient
	private String createTimeFrom;
	/**
     * 创建时间-结束
     */
	@Transient
    private String createTimeTo;
	/**
     * 创建人名称
     */
	@Column(name = "create_user_name")
	private  String createUserName;
	/**
     * 更新人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 合同号
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 进货单号
     */
	@Column(name = "purchase_order_no")
	private  String purchaseOrderNo;
	/**
     * 供应商
     */
	@Column(name = "supplier")
	private  String supplier;
	/**
     * 客户
     */
	@Column(name = "customer")
	private  String customer;
	/**
     * 客户地址
     */
	@Column(name = "customer_address")
	private  String customerAddress;
	/**
     * 贸易国别
     */
	@Column(name = "trade_country")
	private  String tradeCountry;
	/**
     * 经营单位
     */
	@Column(name = "business_enterprise")
	private  String businessEnterprise;
	/**
     * 目的地/港
     */
	@Column(name = "port_of_destination")
	private  String portOfDestination;
	/**
     * 付款方式
     */
	@Column(name = "payment_method")
	private  String paymentMethod;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 总金额
     */
	@Column(name = "total_amount")
	private  BigDecimal totalAmount;
	/**
     * 运输方式
     */
	@Column(name = "transport_mode")
	private  String transportMode;
	/**
     * 价格条款
     */
	@Column(name = "price_term")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@Column(name = "price_term_port")
	private  String priceTermPort;
	/**
     * 发货单位
     */
	@Column(name = "delivery_enterprise")
	private  String deliveryEnterprise;
	/**
     * 包装种类
     */
	@Column(name = "wrap_type")
	private  String wrapType;
	/**
     * 包装数量
     */
	@Column(name = "pack_num")
	private  BigDecimal packNum;
	/**
     * 发货单位所在地
     */
	@Column(name = "delivery_enterprise_address")
	private  String deliveryEnterpriseAddress;
	/**
     * 总毛重
     */
	@Column(name = "total_net_wt")
	private  BigDecimal totalNetWt;
	/**
     * 总净重
     */
	@Column(name = "total_gross_wt")
	private  BigDecimal totalGrossWt;
	/**
     * 总皮重
     */
	@Column(name = "total_tare")
	private  BigDecimal totalTare;
	/**
     * 发送报关
     */
	@Column(name = "send_declare")
	private  String sendDeclare;
	/**
     * 业务日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "business_date")
	private  Date businessDate;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	/**
     * 是否确认
     */
	@Column(name = "is_confirm")
	private  String isConfirm;
	/**
     * 是否保存
     */
	@Column(name = "is_save")
	private  String isSave;
	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;
	/**
     * 单据状态
     */
	@Column(name = "status")
	private  String status;
	/**
     * 审核状态
     */
	@Column(name = "appr_status")
	private  String apprStatus;
	/**
     * 发送财务系统
     */
	@Column(name = "send_finance")
	private  String sendFinance;
	/**
     * 是否红冲
     */
	@Column(name = "red_flush")
	private  String redFlush;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "purchase_mark")
	private  String purchaseMark;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "purchase_no_mark")
	private  String purchaseNoMark;
	/**
     * 发票号
     */
	@Column(name = "invoice_no")
	private  String invoiceNo;
	/**
     * 启运港
     */
	@Column(name = "port_of_departure")
	private  String portOfDeparture;
	/**
     * 船名航次
     */
	@Column(name = "vessel_voyage")
	private  String vesselVoyage;
	/**
     * 开航日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "sailing_date")
	private  Date sailingDate;
	/**
     * 作销日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "sales_date")
	private  Date salesDate;
	/**
     * 总数量
     */
	@Column(name = "total_quantity")
	private  BigDecimal totalQuantity;


	@Transient
	private String createrBy;

	@Transient
	private String createrUserName;

	@Transient
	private Date createrTime;

	@Transient
	private Date declarationDate;
	@Transient
	private String entryNo;
}
