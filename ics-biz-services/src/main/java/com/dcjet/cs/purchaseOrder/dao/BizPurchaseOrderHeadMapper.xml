<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.purchaseOrder.dao.BizPurchaseOrderHeadMapper">
    <resultMap id="bizPurchaseOrderHeadResultMap" type="com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="purchase_order_no" property="purchaseOrderNo" jdbcType="VARCHAR" />
		<result column="supplier" property="supplier" jdbcType="VARCHAR" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="customer_address" property="customerAddress" jdbcType="VARCHAR" />
		<result column="trade_country" property="tradeCountry" jdbcType="VARCHAR" />
		<result column="business_enterprise" property="businessEnterprise" jdbcType="VARCHAR" />
		<result column="port_of_destination" property="portOfDestination" jdbcType="VARCHAR" />
		<result column="payment_method" property="paymentMethod" jdbcType="VARCHAR" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
		<result column="transport_mode" property="transportMode" jdbcType="VARCHAR" />
		<result column="price_term" property="priceTerm" jdbcType="VARCHAR" />
		<result column="price_term_port" property="priceTermPort" jdbcType="VARCHAR" />
		<result column="delivery_enterprise" property="deliveryEnterprise" jdbcType="VARCHAR" />
		<result column="wrap_type" property="wrapType" jdbcType="VARCHAR" />
		<result column="pack_num" property="packNum" jdbcType="NUMERIC" />
		<result column="delivery_enterprise_address" property="deliveryEnterpriseAddress" jdbcType="VARCHAR" />
		<result column="total_net_wt" property="totalNetWt" jdbcType="NUMERIC" />
		<result column="total_gross_wt" property="totalGrossWt" jdbcType="NUMERIC" />
		<result column="total_tare" property="totalTare" jdbcType="NUMERIC" />
		<result column="send_declare" property="sendDeclare" jdbcType="VARCHAR" />
		<result column="business_date" property="businessDate" jdbcType="TIMESTAMP" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="is_confirm" property="isConfirm" jdbcType="VARCHAR" />
		<result column="is_save" property="isSave" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="appr_status" property="apprStatus" jdbcType="VARCHAR" />
		<result column="send_finance" property="sendFinance" jdbcType="VARCHAR" />
		<result column="red_flush" property="redFlush" jdbcType="VARCHAR" />
		<result column="purchase_mark" property="purchaseMark" jdbcType="VARCHAR" />
		<result column="purchase_no_mark" property="purchaseNoMark" jdbcType="VARCHAR" />
		<result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
		<result column="port_of_departure" property="portOfDeparture" jdbcType="VARCHAR" />
		<result column="vessel_voyage" property="vesselVoyage" jdbcType="VARCHAR" />
		<result column="sailing_date" property="sailingDate" jdbcType="TIMESTAMP" />
		<result column="sales_date" property="salesDate" jdbcType="TIMESTAMP" />
		<result column="total_quantity" property="totalQuantity" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
      t.sid
     ,COALESCE(t.update_by,t.create_by) as createrBy
     ,COALESCE(t.update_user_name,t.create_user_name) as createrUserName
     ,COALESCE(t.update_time,t.create_time) as createrTime
     ,t.create_by
     ,t.create_time
     ,t.create_user_name
     ,t.update_by
     ,t.update_time
     ,t.update_user_name
     ,t.trade_code
     ,t.sys_org_code
     ,t.extend1
     ,t.extend2
     ,t.extend3
     ,t.extend4
     ,t.extend5
     ,t.extend6
     ,t.extend7
     ,t.extend8
     ,t.extend9
     ,t.extend10
     ,t.business_type
     ,t.contract_no
     ,t.purchase_order_no
     ,t.supplier
     ,t.customer
     ,t.customer_address
     ,t.trade_country
     ,t.business_enterprise
     ,t.port_of_destination
     ,t.payment_method
     ,t.curr
     ,t.total_amount
     ,t.transport_mode
     ,t.price_term
     ,t.price_term_port
     ,t.delivery_enterprise
     ,t.wrap_type
     ,t.pack_num
     ,t.delivery_enterprise_address
     ,t.total_net_wt
     ,t.total_gross_wt
     ,t.total_tare
     ,t.send_declare
     ,t.business_date
     ,t.confirm_time
     ,t.is_confirm
     ,t.is_save
     ,t.note
     ,t.status
     ,t.appr_status
     ,t.send_finance
     ,t.red_flush
     ,t.purchase_mark
     ,t.purchase_no_mark
     ,t.invoice_no
     ,t.port_of_departure
     ,t.vessel_voyage
     ,t.sailing_date
     ,t.sales_date
     ,t.total_quantity
     ,u.entry_no
     ,u.DECLARATION_DATE
    </sql>
    <sql id="condition">
    <if test="createBy != null and createBy != ''">
		and create_by = #{createBy}
	</if>
        <if test="createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= to_date(#{createTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) < DATEADD(DAY, 1, to_date(#{createTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
    <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
	  and purchase_order_no like '%'|| #{purchaseOrderNo} || '%'
	</if>
    <if test="supplier != null and supplier != ''">
		and supplier = #{supplier}
	</if>
    <if test="status != null and status != ''">
		and status = #{status}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizPurchaseOrderHeadResultMap" parameterType="com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_purchase_order_head t
        left join T_BIZ_PURCHASE_ORDER_CERT u on t.sid = u.head_id
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getCreateUserList" resultType="java.util.Map">
        select
            distinct
            COALESCE(update_by,create_by) as "value"
                   ,COALESCE(update_user_name,create_user_name) as "label"
        from  t_biz_purchase_order_head t
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="getForeignContractHeadListQty" resultType="com.dcjet.cs.seven.model.SevenForeignContractHead">
        SELECT h.id,T.id AS tId, h.CONTRACT_NO,h.buyer, t.G_NAME,
        (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) AS qtya
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD h
        LEFT JOIN T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST t ON h.ID = t.HEAD_ID
        LEFT JOIN T_BIZ_PURCHASE_ORDER_LIST o ON t.id = o.CONTRACT_LIST_ID
        where h.DATA_STATUS = '1' and t.BODY_TYPE = 'slice'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO like '%'|| #{contractNo} || '%'
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and t.trade_code = #{tradeCode}
        </if>
        GROUP BY h.id,T.id, h.CONTRACT_NO,h.buyer,t.G_NAME,t.QTY
        HAVING (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) > 0
    </select>
    <select id="getForeignContractListQty" resultType="com.dcjet.cs.seven.model.SevenForeignContractList">
        SELECT t.ID,
               t.G_NAME ,
               t.G_MODEL,
               t.UNIT,
               COALESCE(t.QTY, 0) as QTY,
               COALESCE(t.UNIT_PRICE, 0) as UNIT_PRICE,
               (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) AS qtya
        FROM T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST t
                 LEFT JOIN T_BIZ_PURCHASE_ORDER_LIST o ON t.id = o.CONTRACT_LIST_ID
        WHERE t.HEAD_ID = #{headId} and t.BODY_TYPE = 'slice'
        GROUP BY t.ID, t.G_NAME ,t.G_MODEL,t.QTY,t.UNIT,t.UNIT_PRICE
        HAVING (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) > 0;
    </select>
    <select id="getSerilNo" resultType="java.lang.String">
        SELECT t.PURCHASE_ORDER_NO FROM t_biz_purchase_order_head t where t.CONTRACT_NO = #{contractNo} order BY t.PURCHASE_ORDER_NO DESC LIMIT 1
    </select>
    <select id="getDataByContract" resultType="com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderHead">
        SELECT
            t.sid
             ,t.create_by
             ,t.create_time
             ,t.create_user_name
             ,t.update_by
             ,t.update_time
             ,t.update_user_name
             ,t.trade_code
             ,t.sys_org_code
             ,t.extend1
             ,t.extend2
             ,t.extend3
             ,t.extend4
             ,t.extend5
             ,t.extend6
             ,t.extend7
             ,t.extend8
             ,t.extend9
             ,t.extend10
             ,t.business_type
             ,t.contract_no
             ,t.purchase_order_no
             ,t.supplier
             ,t.customer
             ,t.customer_address
             ,t.trade_country
             ,t.business_enterprise
             ,t.port_of_destination
             ,t.payment_method
             ,t.curr
             ,t.transport_mode
             ,t.price_term
             ,t.price_term_port
             ,t.delivery_enterprise
             ,t.wrap_type
             ,t.pack_num
             ,t.delivery_enterprise_address
             ,t.total_net_wt
             ,t.total_gross_wt
             ,t.total_tare
             ,t.send_declare
             ,t.business_date
             ,t.confirm_time
             ,t.is_confirm
             ,t.is_save
             ,t.note
             ,t.status
             ,t.appr_status
             ,t.send_finance
             ,t.red_flush
             ,t.purchase_mark
             ,t.purchase_no_mark
             ,t.invoice_no
             ,t.port_of_departure
             ,t.vessel_voyage
             ,t.sailing_date
             ,t.sales_date
             ,COALESCE(sum(l.qty),0) as total_quantity
             ,COALESCE(sum(l.amount),0) as total_amount
        FROM
        t_biz_purchase_order_head t
        left join t_biz_purchase_order_list l on t.sid = l.head_id
        where t.contract_no = #{contractNo}
        group by t.sid
               ,t.create_by
               ,t.create_time
               ,t.create_user_name
               ,t.update_by
               ,t.update_time
               ,t.update_user_name
               ,t.trade_code
               ,t.sys_org_code
               ,t.extend1
               ,t.extend2
               ,t.extend3
               ,t.extend4
               ,t.extend5
               ,t.extend6
               ,t.extend7
               ,t.extend8
               ,t.extend9
               ,t.extend10
               ,t.business_type
               ,t.contract_no
               ,t.purchase_order_no
               ,t.supplier
               ,t.customer
               ,t.customer_address
               ,t.trade_country
               ,t.business_enterprise
               ,t.port_of_destination
               ,t.payment_method
               ,t.curr
               ,t.total_amount
               ,t.transport_mode
               ,t.price_term
               ,t.price_term_port
               ,t.delivery_enterprise
               ,t.wrap_type
               ,t.pack_num
               ,t.delivery_enterprise_address
               ,t.total_net_wt
               ,t.total_gross_wt
               ,t.total_tare
               ,t.send_declare
               ,t.business_date
               ,t.confirm_time
               ,t.is_confirm
               ,t.is_save
               ,t.note
               ,t.status
               ,t.appr_status
               ,t.send_finance
               ,t.red_flush
               ,t.purchase_mark
               ,t.purchase_no_mark
               ,t.invoice_no
               ,t.port_of_departure
               ,t.vessel_voyage
               ,t.sailing_date
               ,t.sales_date
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_purchase_order_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
