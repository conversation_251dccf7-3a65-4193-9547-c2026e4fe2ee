package com.dcjet.cs.purchaseOrder.mapper;
import com.dcjet.cs.dto.purchaseOrder.*;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizPurchaseOrderListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizPurchaseOrderListDto toDto(BizPurchaseOrderList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizPurchaseOrderList toPo(BizPurchaseOrderListParam param);
    /**
     * 数据库原始数据更新
     * @param bizPurchaseOrderListParam
     * @param bizPurchaseOrderList
     */
    void updatePo(BizPurchaseOrderListParam bizPurchaseOrderListParam, @MappingTarget BizPurchaseOrderList bizPurchaseOrderList);
    default void patchPo(BizPurchaseOrderListParam bizPurchaseOrderListParam, BizPurchaseOrderList bizPurchaseOrderList) {
        // TODO 自行实现局部更新
    }
}
