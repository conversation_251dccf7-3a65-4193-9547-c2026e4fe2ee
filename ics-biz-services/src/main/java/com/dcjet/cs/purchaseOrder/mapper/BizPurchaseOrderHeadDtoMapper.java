package com.dcjet.cs.purchaseOrder.mapper;
import com.dcjet.cs.dto.purchaseOrder.*;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizPurchaseOrderHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizPurchaseOrderHeadDto toDto(BizPurchaseOrderHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizPurchaseOrderHead toPo(BizPurchaseOrderHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizPurchaseOrderHeadParam
     * @param bizPurchaseOrderHead
     */
    void updatePo(BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, @MappingTarget BizPurchaseOrderHead bizPurchaseOrderHead);
    default void patchPo(BizPurchaseOrderHeadParam bizPurchaseOrderHeadParam, BizPurchaseOrderHead bizPurchaseOrderHead) {
        // TODO 自行实现局部更新
    }
}
