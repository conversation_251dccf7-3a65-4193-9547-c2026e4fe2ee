package com.dcjet.cs.purchaseOrder.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Setter
@Getter
@Table(name = "T_BIZ_PURCHASE_ORDER_CERT")
public class BizPurchaseOrderCert implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "CREATE_USER_NAME")
	private  String createUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "EXTEND10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
     * 准运证编号
     */
	@Column(name = "TRANSPORT_PERMIT")
	private  String transportPermit;
	/**
     * 出货确认日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "SHIPMENT_CONFIRM_DATE")
	private  Date shipmentConfirmDate;
	/**
     * 报关单号
     */
	@Column(name = "ENTRY_NO")
	private  String entryNo;
	/**
     * 申报日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "DECLARATION_DATE")
	private  Date declarationDate;
	/**
     * 放行日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "RELEASE_DATE")
	private  Date releaseDate;
	/**
     * 唛头
     */
	@Column(name = "SHIPPING_MARK")
	private  String shippingMark;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 表头id
     */
	@Column(name = "HEAD_ID")
	private  String headId;
	/**
     * 分析单id
     */
	@Column(name = "ANALYSIS_ID")
	private  String analysisId;
}
