package com.dcjet.cs.purchaseOrder.dao;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderList;
import com.dcjet.cs.purchaseOrder.model.BizPurchaseOrderList;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizPurchaseOrderList
* <AUTHOR>
* @date: 2025-7-14
*/
public interface BizPurchaseOrderListMapper extends Mapper<BizPurchaseOrderList> {
    /**
     * 查询获取数据
     * @param bizPurchaseOrderList
     * @return
     */
    List<BizPurchaseOrderList> getList(BizPurchaseOrderList bizPurchaseOrderList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    SevenForeignContractList getPurchaseOrderListQty(String contractListId);

    List<BizPurchaseOrderList> getListByHeadId(String headId);

    void deleteByHeadSids(List<String> sids);

}
