package com.dcjet.cs.iEBusiness.mapper;
import com.dcjet.cs.dto.iEBusiness.*;
import com.dcjet.cs.iEBusiness.model.BizAgencyAgreement;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizAgencyAgreementDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizAgencyAgreementDto toDto(BizAgencyAgreement po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizAgencyAgreement toPo(BizAgencyAgreementParam param);
    /**
     * 数据库原始数据更新
     * @param bizAgencyAgreementParam
     * @param bizAgencyAgreement
     */
    void updatePo(BizAgencyAgreementParam bizAgencyAgreementParam, @MappingTarget BizAgencyAgreement bizAgencyAgreement);
    default void patchPo(BizAgencyAgreementParam bizAgencyAgreementParam, BizAgencyAgreement bizAgencyAgreement) {
        // TODO 自行实现局部更新
    }
}
