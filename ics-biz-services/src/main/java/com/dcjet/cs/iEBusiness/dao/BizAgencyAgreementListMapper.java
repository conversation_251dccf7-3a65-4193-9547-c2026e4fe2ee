package com.dcjet.cs.iEBusiness.dao;
import com.dcjet.cs.dto.dec.BizIListTotal;
import com.dcjet.cs.iEBusiness.model.BizAgencyAgreementList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;
/**
* generated by Generate dcits
* BizAgencyAgreementList
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizAgencyAgreementListMapper extends Mapper<BizAgencyAgreementList> {
    /**
     * 根据参数查询
     *
     * @param bizAgencyAgreementList
     * @return
     */
    List<BizAgencyAgreementList> getList(BizAgencyAgreementList bizAgencyAgreementList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据表头headId批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);
    /**
     * 根据表头headId查询是否存在表体数据
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);

    BizIListTotal getContractTotal(BizAgencyAgreementList bizAgencyAgreementList);
}
