package com.dcjet.cs.iEBusiness.mapper;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListDto;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListParam;
import com.dcjet.cs.iEBusiness.model.BizAgencyAgreementList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizAgencyAgreementListDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    BizAgencyAgreementListDto toDto(BizAgencyAgreementList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizAgencyAgreementList toPo(BizAgencyAgreementListParam param);
    void updatePo(BizAgencyAgreementListParam bizAgencyAgreementListParam, @MappingTarget BizAgencyAgreementList bizAgencyAgreementList);
}
