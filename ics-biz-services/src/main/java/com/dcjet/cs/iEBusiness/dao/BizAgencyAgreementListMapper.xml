<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.iEBusiness.dao.BizAgencyAgreementListMapper">
    <resultMap id="bizAgencyAgreementListResultMap" type="com.dcjet.cs.iEBusiness.model.BizAgencyAgreementList">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="goods_name" property="goodsName" jdbcType="VARCHAR" />
		<result column="product_model" property="productModel" jdbcType="VARCHAR" />
		<result column="quantity" property="quantity" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="unit_price" property="unitPrice" jdbcType="NUMERIC" />
		<result column="amount" property="amount" jdbcType="NUMERIC" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="i_e_mark" property="iEMark" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,goods_name
     ,product_model
     ,quantity
     ,unit
     ,unit_price
     ,amount
     ,remark
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,head_id
     ,parent_id
     ,i_e_mark
     ,trade_code
     ,sys_org_code
     ,create_by
     ,create_time
     ,update_by
     ,update_time
    </sql>
	<sql id="condition">
	<if test="headId != null and headId != ''">
		  head_id = #{headId}
	</if>
    <if test="parentId != null and parentId != ''">
		and parent_id = #{parentId}
	</if>
    <if test="iEMark != null and iEMark != ''">
		and i_e_mark = #{iEMark}
	</if>
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizAgencyAgreementListResultMap" parameterType="com.dcjet.cs.iEBusiness.model.BizAgencyAgreementList">
        select <include refid="Base_Column_List" />
        from t_biz_agency_agreement_list t
        <where>
            <!-- 用户Grid查询 and 条件-->
			<include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_agency_agreement_list t
		where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
              #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from t_biz_agency_agreement_list t
		where
		t.head_id
		in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from t_biz_agency_agreement_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <select id="getContractTotal" resultType="com.dcjet.cs.dto.dec.BizIListTotal">
        SELECT
            sum(t.quantity) as qtyTotal,
            sum(t.amount) as decTotal
        FROM
        t_biz_agency_agreement_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
</mapper>
