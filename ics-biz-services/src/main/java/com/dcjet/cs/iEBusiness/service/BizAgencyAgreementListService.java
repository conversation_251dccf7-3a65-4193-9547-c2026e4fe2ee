package com.dcjet.cs.iEBusiness.service;
import com.dcjet.cs.dto.dec.BizIListTotal;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListDto;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListParam;
import com.dcjet.cs.dto.iEBusiness.BizAgencyAgreementListExportParam;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListParam;
import com.dcjet.cs.iEBusiness.dao.BizAgencyAgreementListMapper;
import com.dcjet.cs.iEBusiness.mapper.BizAgencyAgreementListDtoMapper;
import com.dcjet.cs.iEBusiness.model.BizAgencyAgreementList;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizAgencyAgreementListService extends BaseService<BizAgencyAgreementList> {
    @Resource
    private BizAgencyAgreementListMapper bizAgencyAgreementListMapper;
    @Resource
    private BizAgencyAgreementListDtoMapper bizAgencyAgreementListDtoMapper;
    @Override
    public Mapper<BizAgencyAgreementList> getMapper() {
        return bizAgencyAgreementListMapper;
    }
    /**
     * 功能描述: grid分页查询
     *
     * @param bizAgencyAgreementListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizAgencyAgreementListDto>> selectAllPaged(BizAgencyAgreementListParam bizAgencyAgreementListParam, PageParam pageParam) {
        // 启用分页查询
        BizAgencyAgreementList bizAgencyAgreementList = bizAgencyAgreementListDtoMapper.toPo(bizAgencyAgreementListParam);
        Page<BizAgencyAgreementList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizAgencyAgreementListMapper.getList(bizAgencyAgreementList));
        List<BizAgencyAgreementListDto> bizAgencyAgreementListDtos = page.getResult().stream().map(head -> {
            BizAgencyAgreementListDto dto = bizAgencyAgreementListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<BizAgencyAgreementListDto>> paged = ResultObject.createInstance(bizAgencyAgreementListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<BizAgencyAgreementListDto> selectAll(BizAgencyAgreementListParam exportParam) {
        BizAgencyAgreementList bizAgencyAgreementList = bizAgencyAgreementListDtoMapper.toPo(exportParam);
        List<BizAgencyAgreementListDto> bizAgencyAgreementListDtos = new ArrayList<>();
        List<BizAgencyAgreementList> bizAgencyAgreementLists = bizAgencyAgreementListMapper.getList(bizAgencyAgreementList);
        if (CollectionUtils.isNotEmpty(bizAgencyAgreementLists)) {
            bizAgencyAgreementListDtos = bizAgencyAgreementLists.stream().map(item -> {
                BizAgencyAgreementListDto dto = bizAgencyAgreementListDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizAgencyAgreementListDtos;
    }
    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public BizAgencyAgreementListDto insert(BizAgencyAgreementListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        BizAgencyAgreementList bizAgencyAgreementList = bizAgencyAgreementListDtoMapper.toPo(model);
        bizAgencyAgreementList.setId(sid);
        bizAgencyAgreementList.setCreateBy(userInfo.getUserNo());
        bizAgencyAgreementList.setCreateTime(new Date());
        int insertStatus = bizAgencyAgreementListMapper.insert(bizAgencyAgreementList);
       return insertStatus > 0 ? bizAgencyAgreementListDtoMapper.toDto(bizAgencyAgreementList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizAgencyAgreementListParam
     * @param userInfo
     * @return
     */
    public BizAgencyAgreementListDto update(BizAgencyAgreementListParam bizAgencyAgreementListParam, UserInfoToken userInfo) {
        BizAgencyAgreementList bizAgencyAgreementList = bizAgencyAgreementListMapper.selectByPrimaryKey(bizAgencyAgreementListParam.getSid());
        bizAgencyAgreementListDtoMapper.updatePo(bizAgencyAgreementListParam, bizAgencyAgreementList);
        bizAgencyAgreementList.setUpdateBy(userInfo.getUserNo());
        bizAgencyAgreementList.setUpdateTime(new Date());
        if (bizAgencyAgreementList.getUnitPrice() != null && bizAgencyAgreementList.getQuantity() != null) {
            bizAgencyAgreementList.setAmount(bizAgencyAgreementList.getUnitPrice().multiply(bizAgencyAgreementList.getQuantity()));
        }
        // 更新数据
        int update = bizAgencyAgreementListMapper.updateByPrimaryKey(bizAgencyAgreementList);
         return update > 0 ? bizAgencyAgreementListDtoMapper.toDto(bizAgencyAgreementList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        bizAgencyAgreementListMapper.deleteBySids(sids);
    }
	public int getListNumByHeadIds(List<String> sids) {
        return bizAgencyAgreementListMapper.getListNumByHeadIds(sids);
    }

    public ResultObject<BizIContractListDto> getContractTotal(BizAgencyAgreementListParam bizAgencyAgreementListParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");

        BizAgencyAgreementList bizAgencyAgreementList = new BizAgencyAgreementList();
        bizAgencyAgreementList.setTradeCode(userInfo.getCompany());
        bizAgencyAgreementList.setHeadId(bizAgencyAgreementListParam.getHeadId());
        bizAgencyAgreementList.setIEMark(bizAgencyAgreementListParam.getIEMark());
        BizIListTotal bizIListTotal = bizAgencyAgreementListMapper.getContractTotal(bizAgencyAgreementList);
        resultObject.setData(bizIListTotal);
        return resultObject;
    }
}
