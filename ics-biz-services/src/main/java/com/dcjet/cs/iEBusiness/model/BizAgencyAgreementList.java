package com.dcjet.cs.iEBusiness.model;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Getter;
import lombok.Setter;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter
@Getter
@Table(name = "t_biz_agency_agreement_list")
public class BizAgencyAgreementList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 商品名称
     */
	@Column(name = "goods_name")
	private  String goodsName;
	/**
     * 产品型号
     */
	@Column(name = "product_model")
	private  String productModel;
	/**
     * 数量
     */
	@Column(name = "quantity")
	private  BigDecimal quantity;
	/**
     * 单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 单价
     */
	@Column(name = "unit_price")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Column(name = "amount")
	private  BigDecimal amount;
	/**
     * 备注
     */
	@Column(name = "remark")
	private  String remark;
	/**
     * 扩展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 表头id
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 上游id
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 进出口标识
     */
	@Column(name = "i_e_mark")
	@JsonProperty("iEMark")
	private  String iEMark;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
}
