package com.dcjet.cs.iEBusiness.dao;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.dto.iEBusiness.ForContractListDto;
import com.dcjet.cs.iEBusiness.model.BizAgencyAgreement;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* BizAgencyAgreement
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizAgencyAgreementMapper extends Mapper<BizAgencyAgreement> {
    /**
     * 查询获取数据
     * @param bizAgencyAgreement
     * @return
     */
    List<BizAgencyAgreement> getList(BizAgencyAgreement bizAgencyAgreement);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkAgreementNo(@Param("agreementNo") String agreementNo, @Param("id") String id);

    int checkCanDelBySids(List<String> sids);

    List<WorkFlowParam> getFlowList(List<String> ids);

    List<BizAgencyAgreement> getAeoList(BizAgencyAgreement bizAgencyAgreement);

    List<ForContractListDto> getForContractList(@Param("tradeCode") String tradeCode, @Param("contractNo") String contractNo);

    void insertListByForContract(@Param("id") String id, @Param("headId") String sid, @Param("userNo") String userNo);

    BigDecimal getContractAmount(@Param("id") String sid);
}
