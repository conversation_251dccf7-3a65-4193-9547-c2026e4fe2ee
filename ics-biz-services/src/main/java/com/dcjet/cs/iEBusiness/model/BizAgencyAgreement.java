package com.dcjet.cs.iEBusiness.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter
@Getter
@Table(name = "t_biz_agency_agreement")
public class BizAgencyAgreement implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 主键列表（用于批量操作）
     */
	@Transient
	private List<String> ids;
	/**
     * 业务类型
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 合同号
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 协议编号
     */
	@Column(name = "agreement_no")
	private  String agreementNo;
	/**
     * 客户
     */
	@Column(name = "customer")
	private  String customer;
	/**
     * 签约日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "signing_date")
	private  Date signingDate;
	/**
     * 签约日期-开始
     */
	@Transient
	private String signingDateFrom;
	/**
     * 签约日期-结束
     */
	@Transient
    private String signingDateTo;
	/**
     * 签约地点
     */
	@Column(name = "signing_place")
	private  String signingPlace;
	/**
     * 币种
     */
	@Column(name = "currency")
	private  String currency;
	/**
     * 合同金额
     */
	@Column(name = "contract_amount")
	private  BigDecimal contractAmount;
	/**
     * 代理费率%
     */
	@Column(name = "agency_rate")
	private  BigDecimal agencyRate;
	/**
     * 代理费用
     */
	@Column(name = "agency_fee")
	private  BigDecimal agencyFee;
	/**
     * 建议授权签约人
     */
	@Column(name = "suggested_signatory")
	private  String suggestedSignatory;
	/**
     * 协议条款
     */
	@Column(name = "agreement_terms")
	private  String agreementTerms;
	/**
     * 制单人
     */
	@Column(name = "maker")
	private  String maker;
	/**
     * 制单日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "make_date")
	private  Date makeDate;
	/**
     * 制单日期-开始
     */
	@Transient
	private String makeDateFrom;
	/**
     * 制单日期-结束
     */
	@Transient
    private String makeDateTo;
	/**
     * 单据状态
     */
	@Column(name = "bill_status")
	private  String billStatus;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	/**
     * 确认时间-开始
     */
	@Transient
	private String confirmTimeFrom;
	/**
     * 确认时间-结束
     */
	@Transient
    private String confirmTimeTo;
	/**
     * 审批状态
     */
	@Column(name = "approval_status")
	private  String approvalStatus;
	/**
     * 扩展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 父节点
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;

	/**
	 * 币种（英文），表体“出料加工”金额和
	 */
	@Transient
	private String totalAmountStr;
	@Transient
	private String totalAmountUpStr;

	/**
	 * 打印 合计
	 */
	@Transient
	private String sumStr;

	/**
	 * 打印 数量
	 */
	@Transient
	private String qtyStr;

}
