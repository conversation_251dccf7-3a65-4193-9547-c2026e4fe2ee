package com.dcjet.cs.dto.deliveryOrder;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 通关申报数据实体类
 */
@Getter
@Setter
public class DeliveryOrderDeclarationData {
    
    /**
     * 单据号
     */
    @JsonProperty("billNo")
    private String billNo;
    
    /**
     * 单据序号
     */
    @JsonProperty("billSerialNo")
    private Integer billSerialNo;
    
    /**
     * 保完税标志
     */
    @JsonProperty("bondMark")
    private String bondMark;
    
    /**
     * 物料类型
     */
    @JsonProperty("gMark")
    private String gMark;
    
    /**
     * 企业料号
     */
    @JsonProperty("facGNo")
    private String facGNo;
    
    /**
     * 数量
     */
    @JsonProperty("qtyErp")
    private BigDecimal qtyErp;
    
    /**
     * 单价
     */
    @JsonProperty("decPrice")
    private BigDecimal decPrice;
    
    /**
     * 总价
     */
    @JsonProperty("decTotal")
    private BigDecimal decTotal;
    
    /**
     * ERP交易单位
     */
    @JsonProperty("unitErp")
    private String unitErp;
    
    /**
     * 币制
     */
    @JsonProperty("curr")
    private String curr;
    
    /**
     * 净重
     */
    @JsonProperty("netWt")
    private BigDecimal netWt;
    
    /**
     * 毛重
     */
    @JsonProperty("grossWet")
    private String grossWet;
    
    /**
     * 最终目的国
     */
    @JsonProperty("destinationCountry")
    private String destinationCountry;
    /**
     * BOM版本号
     */
    @JsonProperty("bomVersion")
    private String bomVersion;
    /**
     * 销售订单号
     */
    @JsonProperty("SONumber")
    private String SONumber;

    /**
     * 销售订单行号
     */
    @JsonProperty("SOLineNumber")
    private String SOLineNumber;

    /**
     * 销售订单日期
     */
    @JsonProperty("SODate")
    private String SODate;

    /**
     * 发票号
     */
    @JsonProperty("invNo")
    private String invNo;
    
    /**
     * 发票日期
     */
    @JsonProperty("SOInvDate")
    private String SOInvDate;
    /**
     * 客户代码
     */
    @JsonProperty("supplierCode")
    private String supplierCode;
    /**
     * 出口方式
     */
    @JsonProperty("ieMode")
    private String ieMode;
    /**
     * 客户料号
     */
    @JsonProperty("supplierGNo")
    private String supplierGNo;
    /**
     * 客户订单号
     */
    @JsonProperty("supplierPONumber")
    private String supplierPONumber;
    /**
     * 客户订单行号
     */
    @JsonProperty("supplierPOLineNumber")
    private String supplierPOLineNumber;

    /**
     * 法定数量
     */
    @JsonProperty("firstQty")
    private String firstQty;

    /**
     * 法定第二数量
     */
    @JsonProperty("secondQty")
    private String secondQty;
    /**
     * 加工费单价
     */
    @JsonProperty("CMTPrice")
    private BigDecimal CMTPrice;
    /**
     * 加工费总价
     */
    @JsonProperty("CMTTotal")
    private BigDecimal CMTTotal;
    /**
     * 备注
     */
    @JsonProperty("note")
    private String note;

    /**
     * ERP创建时间
     */
    @JsonProperty("lastUpdateTime")
    private String lastUpdateTime;
    /**
     * 传输批次号
     */
    @JsonProperty("tempOwner")
    private String tempOwner;

    /**
     * 征免方式
     */
    @JsonProperty("dutyMode")
    private String dutyMode;
    
    /**
     * 境内货源地
     */
    @JsonProperty("districtCode")
    private String districtCode;
    
    /**
     * 境内目的地行政区划
     */
    @JsonProperty("districtPostCode")
    private String districtPostCode;
    
    /**
     * 原产国
     */
    @JsonProperty("originCountry")
    private String originCountry;
    
    /**
     * 报关单归并序号
     */
    @JsonProperty("entryGNo")
    private String entryGNo;
    
    /**
     * 备注1
     */
    @JsonProperty("remark1")
    private String remark1;
    
    /**
     * 备注2
     */
    @JsonProperty("remark2")
    private String remark2;
    
    /**
     * 备注3
     */
    @JsonProperty("remark3")
    private String remark3;
    
    /**
     * 入库关联单号
     */
    @JsonProperty("inOutRelNo")
    private String inOutRelNo;
    /**
     * 申报要素
     */
    @JsonProperty("gmodel")
    private String gmodel;
    /**
     * 申报单位
     */
    @JsonProperty("unit")
    private String unit;
    /**
     * 件数
     */
    @JsonProperty("packNum")
    private String packNum;
}
