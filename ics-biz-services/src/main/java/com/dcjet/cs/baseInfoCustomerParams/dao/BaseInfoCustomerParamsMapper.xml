<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper">

    <sql id="condition">
        <if test="tradeCode != null and tradeCode != ''">
            and TRADE_CODE = #{tradeCode}
        </if>
        <if test="paramsCode != null and paramsCode != ''">
            and PARAMS_CODE like concat(concat('%',#{paramsCode,jdbcType=VARCHAR}),'%')
        </if>
        <if test="paramsName != null and paramsName != ''">
            and PARAMS_NAME like concat(concat('%',#{paramsName,jdbcType=VARCHAR}),'%')
        </if>

        <if test="customParamCode != null and customParamCode != ''">
            and CUSTOM_PARAM_CODE = #{customParamCode}
        </if>
        <if test="customParamName != null and customParamName != ''">
            and CUSTOM_PARAM_NAME like concat(concat('%',#{customParamName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="paramsType != null and paramsType != ''">
            and PARAMS_TYPE = #{paramsType}
        </if>


    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList"  resultType="com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams" parameterType="com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams">
        SELECT *
        FROM
        T_BIZ_CUSTOMS_PARAMS t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_CUSTOMS_PARAMS t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getParamsCode" resultType="int" parameterType="map">
        SELECT COUNT(*) FROM T_BIZ_CUSTOMS_PARAMS t
        <where>
            and t.TRADE_CODE = #{tradeCode}
            <if test="sid != null and sid != ''">
                and t.SID != #{sid}
            </if>
            <if test="paramsCode != null and paramsCode != ''">
                and t.PARAMS_CODE = #{paramsCode}
            </if>
            <if test="paramsType != null and paramsType != ''">
                and t.PARAMS_TYPE = #{paramsType}
            </if>
        </where>
    </select>
    <select id="getParamsSelectByType"
            resultType="com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams">
        SELECT *
        FROM
        T_BIZ_CUSTOMS_PARAMS t
        <where>
            <if test="tradeCode != null and tradeCode != ''">
                and TRADE_CODE = #{tradeCode}
            </if>
            <if test="paramsType != null and paramsType != ''">
                and PARAMS_TYPE = #{paramsType}
            </if>
            <if test="businessType != null and businessType != ''">
                and BUSINESS_TYPE like concat(concat('%',#{businessType,jdbcType=VARCHAR}),'%')
            </if>
        </where>
    </select>
    <select id="getNameByCode" resultType="java.lang.String">
        SELECT PARAMS_NAME
        FROM T_BIZ_CUSTOMS_PARAMS t
    <where>
        <if test="paramsCode != null and paramsCode != ''">
            and t.PARAMS_CODE = #{paramsCode}
        </if>
        <if test="paramsType != null and paramsType != ''">
            and PARAMS_TYPE = #{paramsType}
        </if>
        <if test="businessType != null and businessType != ''">
            and BUSINESS_TYPE like concat(concat('%',#{businessType,jdbcType=VARCHAR}),'%')
        </if>
    </where>
    </select>

    <select id="getParamsSelectByTypes"
            resultType="com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams">
        select * from
            (select SID, BUSINESS_TYPE, PARAMS_TYPE, PARAMS_CODE, PARAMS_NAME,
                   CUSTOM_PARAM_CODE, UNIT_YONYOU, YY_CODE,
                   ROW_NUMBER() over (partition by PARAMS_CODE ORDER BY INSERT_TIME DESC) as RN
            from T_BIZ_CUSTOMS_PARAMS
            where TRADE_CODE = #{tradeCode}
            and UPPER(PARAMS_TYPE) in
            <foreach collection="types" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
            and REGEXP_LIKE(BUSINESS_TYPE, CONCAT('(^|,)', #{businessType}, '(,|$)'))) PARAMS
        where RN = 1
    </select>
</mapper>
