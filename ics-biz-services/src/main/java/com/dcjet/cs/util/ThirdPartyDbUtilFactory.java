package com.dcjet.cs.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 第三方数据库工具工厂类
 * 根据数据库类型创建相应的数据库工具实例
 */
@Slf4j
@Component
public class ThirdPartyDbUtilFactory {
    
    @Autowired
    private DmDatabaseUtil dmDatabaseUtil;
    
    @Autowired
    private OracleDatabaseUtil oracleDatabaseUtil;
    
    /**
     * 获取数据库工具实例
     * 
     * @param dbType 数据库类型，支持"DM"和"ORACLE"
     * @return 数据库工具实例
     */
    public ThirdPartyDbUtil getDbUtil(String dbType) {
        if (dbType == null || dbType.trim().isEmpty()) {
            log.warn("Database type is empty, using DM as default");
            return dmDatabaseUtil;
        }
        
        dbType = dbType.toUpperCase();
        
        switch (dbType) {
            case "DM":
                return dmDatabaseUtil;
            case "ORACLE":
                return oracleDatabaseUtil;
            default:
                log.warn("Unsupported database type: {}, using DM as default", dbType);
                return dmDatabaseUtil;
        }
    }
}
