package com.dcjet.cs.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.List;
import java.util.Map;

/**
 * Oracle数据库连接工具类
 */
@Slf4j
@Component("oracleDatabaseUtil")
public class OracleDatabaseUtil implements ThirdPartyDbUtil {

    private static final String ORACLE_DRIVER = "oracle.jdbc.OracleDriver";
    private static final String URL_TEMPLATE = "**************************";
    
    private String host;
    private String port;
    private String username;
    private String password;
    private String database; // Oracle的SID或服务名
    
    @Override
    public void init(String host, String port, String username, String password, String database) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.database = database;
        
        // 加载驱动
        try {
            Class.forName(ORACLE_DRIVER);
            log.info("Oracle JDBC driver loaded successfully");
        } catch (ClassNotFoundException e) {
            log.error("Failed to load Oracle JDBC driver", e);
            throw new RuntimeException("Failed to load Oracle JDBC driver", e);
        }
    }
    
    /**
     * 获取数据库连接
     * 
     * @return 数据库连接
     * @throws SQLException 连接异常
     */
    public Connection getConnection() throws SQLException {
        String url = String.format(URL_TEMPLATE, host, port, database);
        log.info("Connecting to Oracle database at {}", url);
        return DriverManager.getConnection(url, username, password);
    }
    
    @Override
    public int insert(String tableName, Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            log.warn("No data to insert");
            return 0;
        }
        
        StringBuilder columns = new StringBuilder();
        StringBuilder placeholders = new StringBuilder();
        
        for (String column : data.keySet()) {
            if (columns.length() > 0) {
                columns.append(", ");
                placeholders.append(", ");
            }
            columns.append(column);
            placeholders.append("?");
        }
        
        String sql = String.format("INSERT INTO %s (%s) VALUES (%s)", 
                tableName, columns.toString(), placeholders.toString());
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            int index = 1;
            for (Object value : data.values()) {
                stmt.setObject(index++, value);
            }
            
            log.info("Executing SQL: {}", sql);
            return stmt.executeUpdate();
            
        } catch (SQLException e) {
            log.error("Failed to insert data into table {}", tableName, e);
            throw new RuntimeException("Failed to insert data", e);
        }
    }
    
    @Override
    public int batchInsert(String tableName, List<Map<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("No data to insert");
            return 0;
        }
        
        Map<String, Object> firstRow = dataList.get(0);
        if (firstRow == null || firstRow.isEmpty()) {
            log.warn("First row has no columns, aborting insert.");
            return 0;
        }
        StringBuilder columns = new StringBuilder();
        StringBuilder placeholders = new StringBuilder();
        
        for (String column : firstRow.keySet()) {
            if (columns.length() > 0) {
                columns.append(", ");
                placeholders.append(", ");
            }
            columns.append(column);
            placeholders.append("?");
        }
        
        String sql = String.format("INSERT INTO %s (%s) VALUES (%s)", 
                tableName, columns.toString(), placeholders.toString());
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            for (Map<String, Object> data : dataList) {
                int index = 1;
                for (String column : firstRow.keySet()) {
                    stmt.setObject(index++, data.get(column));
                }
                stmt.addBatch();
            }
            
            log.info("Executing batch insert SQL: {}", sql);
            int[] results = stmt.executeBatch();
            
            int totalRows = 0;
            for (int result : results) {
                if (result > 0) {
                    totalRows += result;
                }
            }
            
            return totalRows;
            
        } catch (SQLException e) {
            log.error("Failed to batch insert data into table {}", tableName, e);
            throw new RuntimeException("Failed to batch insert data", e);
        }
    }
    
    @Override
    public <T> int insertEntity(String tableName, T entity) {
        Map<String, Object> data = EntityMapConverter.convertToMap(entity);
        return insert(tableName, data);
    }
    
    @Override
    public <T> int batchInsertEntities(String tableName, List<T> entities) {
        List<Map<String, Object>> dataList = EntityMapConverter.convertToMapList(entities);
        return batchInsert(tableName, dataList);
    }
    
    @Override
    public int executeUpdate(String sql, Object... params) {
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            
            log.info("Executing SQL: {}", sql);
            return stmt.executeUpdate();
            
        } catch (SQLException e) {
            log.error("Failed to execute SQL: {}", sql, e);
            throw new RuntimeException("Failed to execute SQL", e);
        }
    }

    @Override
    public long[] getSequenceNextValue(String sequenceName, int frequency) {
        if (StringUtils.isBlank(sequenceName) || frequency < 1) {
            return new long[0];
        }
        long[] sequenceNos = new long[frequency];
        int start = 0;
        String sequenceSql = String.format("select %s.NEXTVAL from dual CONNECT BY ROWNUM <= %d", sequenceName, frequency);
        try (Connection conn = getConnection()) {
            try (ResultSet resultSet = conn.prepareStatement(sequenceSql).executeQuery()) {
                while (resultSet.next()) {
                    sequenceNos[start++] = resultSet.getLong(1);
                }
            }
            return sequenceNos;
        } catch (SQLException e) {
            log.error("Failed to get sequence {} next value", sequenceSql, e);
            throw new RuntimeException("Failed to get sequence next value", e);
        }
    }
    
    @Override
    public String getDbType() {
        return "ORACLE";
    }
}