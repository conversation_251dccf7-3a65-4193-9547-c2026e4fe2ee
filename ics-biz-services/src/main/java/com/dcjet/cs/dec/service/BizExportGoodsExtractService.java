package com.dcjet.cs.dec.service;

import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dec.model.BizExportGoodsList;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;

public abstract class BizExportGoodsExtractService<T> extends BaseService<T> {

    /**
     * 设置出货管理表头信息
     * @param id 出货管理 主键Id
     * @param contractHead 合同表头信息
     * @param head 出货管理表头信息
     * @param userInfo 用户信息
     * @param serialNo 序号
     * @param client 客户信息
     * @param customerCode 客户编码
     */
    public void setExportGoodsHead(String id, BizENonStateAuxmatAggrContractHead contractHead, BizExportGoodsHead head, UserInfoToken userInfo, Integer serialNo, BizMerchant client, String customerCode) {
        head.setId(id);
        head.setTradeCode(userInfo.getCompany());
        head.setCreateBy(userInfo.getUserNo());
        head.setCreateTime(new Date());
        head.setInsertUserName(userInfo.getUserName());
        // 获取 客商信息 根据 客商编码
        head.setParentId(contractHead.getId());

        // 1	出货单号	字符型（60）	文本	是	用户录入 唯一性校验
        head.setExportNo(null);
        // 2	合同号  	字符型（60）	文本	是	<新增>操作带出，不允许修改
        head.setContractNo(contractHead.getContractNo());
        // 3	客户	字符型（200）	下拉框	是	<新增>操作带出-客户，不允许修改
        head.setCustomer(contractHead.getBuyer());
        // 4	客户地址	字符型（60）	文本	是	<新增>操作带出，允许修改 根据客户，关联【客商信息】带出“客商地址”
        head.setCustomerAddress(client.getMerchantAddress());
        // 5	供应商	字符型（200）	下拉框	是	<新增>操作带出-供应商，不允许修改
        head.setSupplier(contractHead.getSupplier());
        //  6	贸易国别	字符型（60）	文本	是	<新增>操作带出，允许修改 根据合同客户，关联【客商信息】带出“贸易国别”
        head.setTradeCountry(client.getTradeCountry());
        //  7	经营单位	字符型（200）	下拉框	是	【基础资料-客商信息】 //默认“中国烟草上海进出口有限责任公司”允许修改
        head.setManageUnit(Optional.ofNullable(customerCode).orElse("中国烟草上海进出口有限责任公司"));
        //  8	付款方式	字符型（50）	文本	否	<新增>操作带出-收汇方式 ，不允许修改
        head.setPaymentType(contractHead.getPaymentMethod());
        //  9	币种	字符型（10）	下拉框	是	<新增>操作带出 ，不允许修改
        head.setCurrency(contractHead.getCurr());
        //  10	运输方式	字符型（10）	下拉框	否	默认0海运，允许修改 系统参数0海运1空运2陆运 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setTransportType("0");
        //  11	价格条款	字符型（20）	下拉框	否	<新增>操作带出 ，不允许修改
        head.setPriceTerms(contractHead.getPriceTerm());
        //  12	价格条款对应港口	字符型（50）	下拉框	否	<新增>操作带出 ，不允许修改
        head.setPriceTermsPort(contractHead.getPriceTermPort());
        //  13	发货单位	字符型（60）	文本	是	<新增>操作带出-供应商，不允许修改
        head.setDeliveryUnit(contractHead.getSupplier());
        //  14	包装种类	字符型（30）	下拉框	否	【企业自定义参数-包装信息】 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setPackageType(null);
        //  15	包装数量   	数值型（10）	文本	否	用户录入 <新增>操作，根据合同号关联<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setPackageNum(null);
        //  16	发货单位所在地	字符型（50）	下拉框	否	企业自定义参数-城市 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setDeliveryUnitLocation(null);
        //  17	装运人SHIPPER	数值型（300）	文本	否	<新增>操作带出-根据供应商关联【客商信息】-装运人SHIPPER，可修改
        head.setShipper(client.getShipper());
        //  18	收货人CONSIGNEE	数值型（300）	文本	否	<新增>操作带出-根据合同客户关联【客商信息】-收货人CONSIGNEE，可修改
        head.setConsignee(client.getConsignee());
        //  19	通知人NOTIFY PARTY 	数值型（300）	文本	否	<新增>操作带出-根据客户关联【客商信息】-通知人NOTIFY PARTY ，可修改
        head.setNotifyParty(client.getNotifyParty());
        //  20	总毛重	数值型（19，6）	文本	否	用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setGrossWeight(null);
        //  21	总净重	数值型（19，6）	文本	否	<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setNetWeight(null);
        //  22	总皮重    	数值型（19，6）	文本	否	系统计算=总毛重-总净重，不允许修改
        head.setGrossWeight(null);
        //  23	装箱说明	数值型（200）	文本	否	用户录入
        head.setNetWeight(null);
        //  24	运输工具名称	数值型（50）	文本	否	用户录入
        head.setTransportationToolsName(null);
        //	开航日期	日期型（10）	日期控件	否	用户录入
        head.setSailingDate(null);
        //  25	唛头	数值型（300）	文本	否	<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setMark(null);
        //  26	装运港	字符型（50）	下拉框	否	企业自定义参数-城市 <新增>操作带出，允许修改
        head.setPortOfShipment(contractHead.getPortOfLoading());;
        //  27	目的地/港	字符型（50）	下拉框	否	企业自定义参数-城市 <新增>操作带出，允许修改 <新增>操作带出，允许修改
        head.setPortOfDestination(contractHead.getPortOfDestination());
        //  28	装运期限	日期型（10）	日期控件	否	用户录入  <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setShipmentDate(null);
        //  29	险别	字符型（100）	下拉框	否	企业自定义参数-保险类别 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setInsuranceType(null);
        //  30	保费币种 	字符型（10）	下拉框	否	海关参数自定义，显示为三位英文字母，默认为USD，允许修改
        head.setInsuranceCurrency("USD");
        //  31	投保加成%	数值型（19，6）	文本	否	默认1%，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setInsuranceAddRate(null);
        //  32	保费费率(%)	数值型（19，6）	文本	否	默认0.0267%,允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setInsuranceRate(null);
        //  33	保险费  	数值型（19，6）	文本	否	系统计算=表体金额汇总*（1+投保加成）*保费费率 不允许修改
        head.setInsuranceFee(null);
        //  34	投保人	字符型（200）	下拉框	是	【基础资料-客商信息】、【基础资料-客户信息】
        head.setInsurer(null);
        //  35	运费	数值型（19，6）	文本	否	用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setFreight(null);
        //  36	运费币种	字符型（10）	下拉框	否	海关参数自定义，显示为三位英文字母，默认为USD，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        head.setFreightCurrency(null);
        //  37	仓储地址	数值型（300）	文本	否	<新增>操作带出-根据委托方关联【客商信息】-仓储地址，可修改
        head.setWarehouseAddress(client.getWarehouseAddress());
        //  38	联系人	数值型（20）	文本	否	<新增>操作带出-根据委托方关联【客商信息】-联系人，可修改
        head.setContactPerson(client.getContactPerson());
        //  39	联系电话   	数值型（20）	文本	否	<新增>操作带出-根据委托方关联【客商信息】-联系电话可修改
        head.setContactPhone(client.getContactPhone());
        //  40	备注	字符型（200）	文本	否	用户录入
        head.setRemark(null);
        //  41	发送报关	字符型（10）	下拉框	是	0是1否 系统带出，不允许修改 数据新增时为1否，操作<发送报关>成功时，置为0是
        head.setSendCustoms("0");
        //  42	单据状态	字符型（10）	下拉框	否
        //      0编制：未点击“确认”功能按钮
        //      1确认：点击表头“确认”功能按钮，且成功提交
        //      2作废：点击列表“作废”功能按钮，且成功提交
        head.setDataState("0");
        //  43	确认时间	日期型（18）	日期控件	否	点击“确认”功能按钮，且成功提交的时间：yyyy-mm-dd hh:mm:ss 不允许修改，置灰
        head.setConfirmTime(null);

        head.setApprovalStatus("1");
    }


    /**
     * 设置出货信息表体
     * @param contractListItem 外商合同表体
     * @param goodsList 出货信息表体
     * @param sid 出货信息头ID
     * @param userInfo 用户信息
     */
    public void setExportGoodsList(BizENonStateAuxmatAggrContractList contractListItem, BizExportGoodsList goodsList, String sid, UserInfoToken userInfo) {
        goodsList.setId(UUID.randomUUID().toString());
        goodsList.setCreateTime(new Date());
        goodsList.setCreateBy(userInfo.getUserNo());
        goodsList.setInsertUserName(userInfo.getUserName());
        goodsList.setParentId(sid);
        goodsList.setExtend1(contractListItem.getId());
        goodsList.setDataState("0");
        goodsList.setTradeCode(userInfo.getCompany());


        // 1	商品名称	字符型（80）	文本	是	<新增>操作带出，不允许修改
        goodsList.setProductName(contractListItem.getGName());
        //  2	商品描述	字符型（100）	文本	否	<新增>操作带出，不允许修改
        // TODO 外商合同 未创建 待补充
        goodsList.setProductDesc(contractListItem.getGModel());
        //  3	规格	字符型（200）	文本	否	<新增>操作带出，不允许修改
        goodsList.setSpecification(contractListItem.getSpecifications());
        //  4	数量（卷）	数值型（19，6）	文本	是	<新增>操作带出，允许修改、
        // TODO 外商合同 未创建 待补充
        goodsList.setQtyJ(null);
        //右下角汇总显示
        //  5	数量（吨）	数值型（19，6）	文本	是	<新增>操作带出，允许修改 右下角汇总显示
        // TODO 外商合同 未创建 待补充
        goodsList.setQty(contractListItem.getQty());
        //  6	单位	字符型（20）	下拉框	是	<新增>操作带出，不允许修改
        goodsList.setUnit(contractListItem.getUnit());
        //  7	单价	数值型（19，8）	文本	是	<新增>操作带出-单价（吨），允许修改
        goodsList.setPrice(contractListItem.getUnitPrice());
        //  8	金额 	数值型（19，4）	文本	是	系统计算=数量*单价，不允许修改 修改数量或单价时，都须重新执行计算 右下角汇总显示
        goodsList.setAmount(contractListItem.getAmount());
        //9	起始箱号	数值型（10）	文本	是	默认为1，允许修改
        goodsList.setBoxStartNo(new BigDecimal("1"));
        //10	结束箱号	数值型（10）	文本	是	用户录入
        goodsList.setEndStartNo(null);
        //11	包装样式	字符型（50）	下拉框	是	企业自定义参数-包装信息
        goodsList.setPackageStyle(null);
        //12	毛重(KG)	数值型（19，4）	文本	是	用户录入 右下角汇总显示
        goodsList.setGrossWeight(null);
        //13	净重(KG)	数值型（19，4）	文本	是	用户录入 右下角汇总显示
        goodsList.setNetWeight(null);
        //14	皮重(KG)	数值型（19，4）	文本	是	系统计算=毛重-净重，不允许修改 右下角汇总显示
        goodsList.setGrossWeight(null);
        //15	长(M)	数值型（19，4）	文本	否	用户录入 右下角汇总显示
        goodsList.setELength(null);
        //16	宽(M)	数值型（19，4）	文本	否	用户录入 右下角汇总显示
        goodsList.setEWidth(null);
        //17	高(M) 	数值型（19，4）	文本	否	用户录入 右下角汇总显示
        goodsList.setEHeight(null);

    }
}
