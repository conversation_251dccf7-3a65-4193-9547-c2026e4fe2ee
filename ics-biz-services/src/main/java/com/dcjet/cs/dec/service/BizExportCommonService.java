package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizExportCommonMapper;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dto.dec.BizDecCommonParam;
import com.dcjet.cs.dto.dec.BizExportCommonKeyValue;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 第9条线-非国营贸易出口辅料-出货信息通用Service层
 */
@Slf4j
@Service
public class BizExportCommonService {

    private static final SimpleDateFormat MONTH_FORMAT = new SimpleDateFormat("yyyyMM");

    @Resource
    private BizExportCommonMapper bizExportCommonMapper;

    /**
     * 获取客户列表信息
     *
     * @param customerCode 客户代码
     * @param tradeCode    企业代码
     * @return 返回结果 Map<String, String> key:customerCode value:customerName
     */
    public List<Map<String, String>> getCustomerList(String customerCode, String tradeCode) {
        List<Map<String, String>> list = bizExportCommonMapper.getCustomerList(customerCode, tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("客户代码：{}，企业代码：{}，查询客户信息结果为空", customerCode, tradeCode);
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 获取当前企业币制信息
     *
     * @param tradeCode 企业代码
     * @return 返回结果 Map<String, String> key:currCode value:currName
     */
    public List<Map<String, String>> getCurrList(String tradeCode) {
        List<Map<String, String>> list = bizExportCommonMapper.getCurrList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码：{}，查询币制信息结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 获取价格条款
     *
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    public List<Map<String, String>> getPriceTermList(String tradeCode) {
        // 获取当前企业的供应商信息
        List<Map<String, String>> list = bizExportCommonMapper.getPriceTermList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码：{}，查询价格条款结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 获取包装信息
     *
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    public List<Map<String, String>> getPackageList(String tradeCode) {
        // 获取包装信息
        List<Map<String, String>> list = bizExportCommonMapper.getPackageList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码：{}，查询包装信息结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 获取城市信息
     *
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    public List<Map<String, String>> getCityList(String tradeCode) {
        // 获取城市信息
        List<Map<String, String>> list = bizExportCommonMapper.getCityList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码：{}，查询城市信息结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 获取保险类型信息
     *
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    public List<Map<String, String>> getInsuranceTypeList(String tradeCode) {
        // 获取保险币别信息
        List<Map<String, String>> list = bizExportCommonMapper.getInsuranceTypeList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码{}，查询保险币别结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 获取港口信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    public List<Map<String, String>> getPortList(String tradeCode) {
        // 获取港口信息
        List<Map<String, String>> list = bizExportCommonMapper.getPortList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码{}，查询港口结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 获取当前企业-出货信息表头-客户基础信息汇总
     */
    public List<Map<String, String>> getExportHeadCustomerList(String tradeCode) {
        // 获取当前客户下拉信息
        List<Map<String, String>> list = bizExportCommonMapper.getExportHeadCustomerList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码{}，查询客户基础信息结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 获取当前企业-出货信息表头-供应商基础信息汇总
     */
    public List<Map<String, String>> getExportHeadSupplierList(String tradeCode) {
        // 获取当前客户下拉信息
        List<Map<String, String>> list = bizExportCommonMapper.getExportHeadSupplierList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码{}，查询供应商基础信息结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }



    /**
     * 获取当前月汇率
     * @param month 格式：yyyyMM
     * @param tradeCode 企业代码
     * @param curr 货币代码
     * @return 根据当前月份+币种关联【企业自定义参数-企业汇率】取“汇率”栏位值，允许修改
     */
    public EnterpriseRate getCurrentMonthRate(String month, String tradeCode, String curr) {
        if(StringUtils.isEmpty(month)){
            month = MONTH_FORMAT.format(new Date());
        }
        return bizExportCommonMapper.getCurrentMonthRate(
                month,
                StringUtils.trimToEmpty(tradeCode),
                StringUtils.trimToEmpty(curr)
        );
    }


    /**
     * 获取单位汇率
     * @param tradeCode 企业代码
     * @return 单位汇率
     */
    public List<Map<String, String>> getUnitList(String tradeCode) {
        // 获取单位信息
        List<Map<String, String>> list = bizExportCommonMapper.getUnitList(tradeCode);
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码{}，查询单位结果为空", tradeCode);
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 根据 客商类型 + 常用标识 获取客户信息
     * @param param 查询参数
     * @param userInfoToken 用户信息
     * @return 客户信息列表
     */
    public List<Map<String, String>> getCustomerListByType(BizDecCommonParam param, UserInfoToken userInfoToken) {
        List<Map<String, String>> list = bizExportCommonMapper.getCustomerListByType(param.getMerchantType(),param.getCommonFlag(),userInfoToken.getCompany());
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码{}，查询客户信息结果为空", userInfoToken.getCompany());
            return new ArrayList<>();
        }
        return list;
    }


    /**
     * 根据 客商类型 + 常用标识 获取客户、供应商信息
     * @param param 查询参数
     * @param userInfoToken 用户信息
     * @return 客户、供应商信息列表
     */
    public List<Map<String, String>> getCustomerSupplierListByType(BizDecCommonParam param, UserInfoToken userInfoToken) {
        List<Map<String, String>> list = bizExportCommonMapper.getCustomerSupplierListByType(param.getCommonFlag(),userInfoToken.getCompany());
        if (CollectionUtils.isEmpty(list)) {
            log.error("企业代码{}，查询客户、供应商信息结果为空", userInfoToken.getCompany());
            return new ArrayList<>();
        }
        return list;
    }
}
