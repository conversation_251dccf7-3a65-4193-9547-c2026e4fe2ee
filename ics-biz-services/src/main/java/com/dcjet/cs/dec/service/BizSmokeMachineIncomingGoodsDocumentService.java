package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsDocumentMapper;
import com.dcjet.cs.dec.mapper.BizSmokeMachineIncomingGoodsDocumentDtoMapper;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsDocument;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsDocumentDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsDocumentParam;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizSmokeMachineIncomingGoodsDocument业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-04 21:29:58
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizSmokeMachineIncomingGoodsDocumentService extends BaseService<BizSmokeMachineIncomingGoodsDocument> {

    private static final Logger log = LoggerFactory.getLogger(BizSmokeMachineIncomingGoodsDocumentService.class);

    @Resource
    private BizSmokeMachineIncomingGoodsDocumentMapper bizSmokeMachineIncomingGoodsDocumentMapper;

    @Resource
    private BizSmokeMachineIncomingGoodsDocumentDtoMapper bizSmokeMachineIncomingGoodsDocumentDtoMapper;

    @Override
    public Mapper<BizSmokeMachineIncomingGoodsDocument> getMapper() {
        return bizSmokeMachineIncomingGoodsDocumentMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizSmokeMachineIncomingGoodsDocumentParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizSmokeMachineIncomingGoodsDocumentDto>> getListPaged(BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument = bizSmokeMachineIncomingGoodsDocumentDtoMapper.toPo(bizSmokeMachineIncomingGoodsDocumentParam);
        bizSmokeMachineIncomingGoodsDocument.setTradeCode(userInfo.getCompany());
        Page<BizSmokeMachineIncomingGoodsDocument> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizSmokeMachineIncomingGoodsDocumentMapper.getList( bizSmokeMachineIncomingGoodsDocument));
        // 将PO转为DTO返回给前端
        List<BizSmokeMachineIncomingGoodsDocumentDto> bizSmokeMachineIncomingGoodsDocumentDtoList = page.getResult().stream()
            .map(bizSmokeMachineIncomingGoodsDocumentDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizSmokeMachineIncomingGoodsDocumentDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizSmokeMachineIncomingGoodsDocumentParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsDocumentDto insert(BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument = bizSmokeMachineIncomingGoodsDocumentDtoMapper.toPo(bizSmokeMachineIncomingGoodsDocumentParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizSmokeMachineIncomingGoodsDocument.setId(sid);
        bizSmokeMachineIncomingGoodsDocument.setCreateBy(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsDocument.setInsertUserName(userInfo.getUserName());
        bizSmokeMachineIncomingGoodsDocument.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizSmokeMachineIncomingGoodsDocumentMapper.insert(bizSmokeMachineIncomingGoodsDocument);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizSmokeMachineIncomingGoodsDocumentDtoMapper.toDto(bizSmokeMachineIncomingGoodsDocument) : null;
    }

    /**
     * 修改记录
     *
     * @param bizSmokeMachineIncomingGoodsDocumentParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsDocumentDto update(BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument = bizSmokeMachineIncomingGoodsDocumentMapper.selectByPrimaryKey(bizSmokeMachineIncomingGoodsDocumentParam.getId());
        bizSmokeMachineIncomingGoodsDocumentDtoMapper.updatePo(bizSmokeMachineIncomingGoodsDocumentParam, bizSmokeMachineIncomingGoodsDocument);
        bizSmokeMachineIncomingGoodsDocument.setUpdateBy(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsDocument.setUpdateUserName(userInfo.getUserName());
        bizSmokeMachineIncomingGoodsDocument.setUpdateTime(new Date());

        // 更新数据
        int update = bizSmokeMachineIncomingGoodsDocumentMapper.updateByPrimaryKey(bizSmokeMachineIncomingGoodsDocument);
        return update > 0 ? bizSmokeMachineIncomingGoodsDocumentDtoMapper.toDto(bizSmokeMachineIncomingGoodsDocument) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizSmokeMachineIncomingGoodsDocumentMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizSmokeMachineIncomingGoodsDocumentDto> selectAll(BizSmokeMachineIncomingGoodsDocumentParam exportParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument = bizSmokeMachineIncomingGoodsDocumentDtoMapper.toPo(exportParam);
        bizSmokeMachineIncomingGoodsDocument.setTradeCode(userInfo.getCompany());
        List<BizSmokeMachineIncomingGoodsDocumentDto> bizSmokeMachineIncomingGoodsDocumentDtos = new ArrayList<>();
        List<BizSmokeMachineIncomingGoodsDocument> bizSmokeMachineIncomingGoodsDocumentLists = bizSmokeMachineIncomingGoodsDocumentMapper.getList(bizSmokeMachineIncomingGoodsDocument);
        if (CollectionUtils.isNotEmpty(bizSmokeMachineIncomingGoodsDocumentLists)) {
           bizSmokeMachineIncomingGoodsDocumentDtos = bizSmokeMachineIncomingGoodsDocumentLists.stream().map(head -> {
                    BizSmokeMachineIncomingGoodsDocumentDto dto =  bizSmokeMachineIncomingGoodsDocumentDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizSmokeMachineIncomingGoodsDocumentDtos;
    }

    public ResultObject<BizSmokeMachineIncomingGoodsDocumentDto> getDocumentByHeadId(String headId, UserInfoToken userInfo) {
        ResultObject<BizSmokeMachineIncomingGoodsDocumentDto> instance = ResultObject.createInstance(true,"获取成功！");
        if (StringUtils.isBlank(headId)) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空"));
        } else {
            BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument = bizSmokeMachineIncomingGoodsDocumentMapper.getDocumentByHeadId(headId);
            if (bizSmokeMachineIncomingGoodsDocument == null) {
               log.error("当前数据还未新增！");
            }else {
                BizSmokeMachineIncomingGoodsDocumentDto dto = bizSmokeMachineIncomingGoodsDocumentDtoMapper.toDto(bizSmokeMachineIncomingGoodsDocument);
                instance.setData(dto);
                instance.setSuccess(true);
            }
        }
        return instance;
    }

    public ResultObject updateAndInsert(BizSmokeMachineIncomingGoodsDocumentParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "保存成功");
        // 判断id是否存在
        String headId = param.getHeadId();
        BizSmokeMachineIncomingGoodsDocument po = bizSmokeMachineIncomingGoodsDocumentDtoMapper.toPo(param);
        BizSmokeMachineIncomingGoodsDocument document = bizSmokeMachineIncomingGoodsDocumentMapper.getDocumentByHeadId(headId);
        if (document == null) {
            // 新增数据
            po.setId(UUID.randomUUID().toString());
            po.setCreateBy(userInfo.getUserNo());
            po.setCreateBy(userInfo.getUserName());
            po.setCreateTime(new Date());
            bizSmokeMachineIncomingGoodsDocumentMapper.insert(po);
        }else {
            po.setId(document.getId());
            po.setUpdateBy(userInfo.getUserNo());
            po.setUpdateTime(new Date());
            po.setUpdateUserName(userInfo.getUserName());
            bizSmokeMachineIncomingGoodsDocumentMapper.updateByPrimaryKey(po);
        }
        return resultObject;
    }
}