package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizExportGoodsSellHead;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadDto;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizExportGoodsSellHeadDto
 *
 * <AUTHOR>
 * @date 2025-07-07 14:16:12
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizExportGoodsSellHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizExportGoodsSellHeadDto toDto(BizExportGoodsSellHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizExportGoodsSellHead toPo(BizExportGoodsSellHeadParam param);

    /**
     * 数据库原始数据更新
     * @param bizExportGoodsSellHeadParam
     * @param BizExportGoodsSellHead
     */
    void updatePo(BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam, @MappingTarget BizExportGoodsSellHead bizExportGoodsSellHead);

    default void patchPo(BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam , BizExportGoodsSellHead bizExportGoodsSellHead) {
        // TODO 自行实现局部更新
    }
}