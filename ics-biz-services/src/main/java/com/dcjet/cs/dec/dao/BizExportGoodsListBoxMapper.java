package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizExportGoodsListBox;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 第9条线-非国营贸易出口辅料-出货信息表体（商品信息）-装箱子表Mapper
 */
public interface BizExportGoodsListBoxMapper extends Mapper<BizExportGoodsListBox>{

    /**
     * 查询获取数据
     * @param bizExportGoodsListBox
     * @return
     */
    List<BizExportGoodsListBox> getList(BizExportGoodsListBox bizExportGoodsListBox);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);
}