package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxBoxDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxBoxParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizBpAnalyseOrderListBoxBoxDto
 *
 * <AUTHOR>
 * @date 2025-08-05 11:08:41
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizBpAnalyseOrderListBoxBoxDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizBpAnalyseOrderListBoxBoxDto toDto(BizBpAnalyseOrderListBoxBox po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizBpAnalyseOrderListBoxBox toPo(BizBpAnalyseOrderListBoxBoxParam param);

    /**
     * 数据库原始数据更新
     * @param bizBpAnalyseOrderListBoxBoxParam
     * @param BizBpAnalyseOrderListBoxBox
     */
    void updatePo(BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam, @MappingTarget BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox);

    default void patchPo(BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam , BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox) {
        // TODO 自行实现局部更新
    }
}