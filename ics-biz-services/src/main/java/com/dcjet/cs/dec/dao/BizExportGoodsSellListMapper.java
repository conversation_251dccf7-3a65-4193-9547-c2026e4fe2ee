package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizExportGoodsSellList;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListTotal;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 第9条线-非国营贸易出口辅料-外销发票表体Mapper
 */
public interface BizExportGoodsSellListMapper extends Mapper<BizExportGoodsSellList>{

    /**
     * 查询获取数据
     * @param bizExportGoodsSellList
     * @return
     */
    List<BizExportGoodsSellList> getList(BizExportGoodsSellList bizExportGoodsSellList);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    /**
     * 根据parent_id 删除 出货信息表体
     * @param parentId 出货信息表头ID
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteByParentId(@Param("parentId") String parentId);


    /**
     * 批量新增 数据
     * @param gList 出货信息表体列表
     * @return 返回执行成功行数，0为执行失败
     */
    int insertList(@Param("list") List<BizExportGoodsSellList> gList);


    /**
     * 获取出货信息表体数据统计
     * @param parentId 出货信息表头ID
     * @return 出货信息表体数据统计
     */
    BizExportGoodsSellListTotal getListTotal(@Param("parentId") String parentId);
}