package com.dcjet.cs.dec.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 汇总 进货信息表体数据
 */
@Getter
@Setter
public class InComingListSumTotal implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数量
     */
    private BigDecimal quantity;


    /**
     * 进口数量
     */
    private BigDecimal  inQuantity;

    /**
     * 金额
     */
    private BigDecimal amount;


    /**
     * 数量
     */
    private BigDecimal quantityStr;


    /**
     * 进口数量
     */
    private BigDecimal  inQuantityStr;

    /**
     * 金额
     */
    private BigDecimal amountStr;
}
