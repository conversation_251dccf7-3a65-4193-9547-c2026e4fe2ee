package com.dcjet.cs.dec.dao;


import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 第9条线-非国营贸易出口辅料-出货信息表头Mapper
 */
public interface BizExportGoodsHeadMapper extends Mapper<BizExportGoodsHead>{

    /**
     * 查询获取数据
     * @param bizExportGoodsHead
     * @return
     */
    List<BizExportGoodsHead> getList(BizExportGoodsHead bizExportGoodsHead);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    Integer checkAnalyseOrderCode(@Param("id") String id,
                                  @Param("exportNo") String exportNo,
                                  @Param("tradeCode") String tradeCode);

    BigDecimal getTotalAmount(@Param("id") String id);

    BigDecimal getListSumTotalById(@Param("id") String id);

    /**
     * 根据parent_id 查询表头id
     * @param parentId 外商合同表头ID
     * @return 出货信息表头ID
     */
    String getHeadIdByParentId(@Param("parentId") String parentId);

    /**
     * 作废数据
     * @param id 出货信息表头id
     * @param userNo 操作人账号
     * @param userName 操作人名称
     * @return 操作影响的行数
     */
    Integer cancel(@Param("id") String id,
                   @Param("userNo") String userNo,
                   @Param("userName") String userName);

    /**
     * 根据ID删除
     * @param id 出货信息表头id
     * @return 操作影响的行数
     */
    int deleteById(@Param("id") String id);



    int checkApprovalStatus(@Param("sids") List<String> sids, @Param("status") String status);

    List<WorkFlowParam> selectBySids(@Param("sids") List<String> sids);

    List<BizExportGoodsHead> getAeoList(BizExportGoodsHead head);

    Integer checkInvoiceIsConfirm(@Param("id") String id);

    List<BizExportGoodsHead> getListPagedToCustomerAccount(BizExportGoodsHead bizExportGoodsHead);

    /**
     * 获取出货信息 最大序号
     * @param contractNo 合同号
     * @param tradeCode 公司编码
     * @return 最大序号
     */
    Integer getMaxSerialNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);


    /**
     * 根据  合同号 + 商品名称 获取外商合同 商品名称 对应的数量总和
     * @param contractNo 合同号
     * @param gName 商品名称
     * @param tradeCode 公司编码
     * @return 商品名称 对应的数量总和
     */
    BigDecimal getGNameCountByContractNo(@Param("contractNo") String contractNo,
                                         @Param("gName") String gName,
                                         @Param("tradeCode") String tradeCode);

    /**
     * 根据  合同号 + 商品名称 获取出货信息 商品名称 对应的数量总和
     * @param contractNo 合同号
     * @param gName 商品名称
     * @param tradeCode 公司编码
     * @return 商品名称 对应的数量总和
     */
    BigDecimal getExportGNameCountByContractNo(@Param("contractNo") String contractNo,
                                               @Param("gName") String gName,
                                               @Param("tradeCode") String tradeCode);
}