package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizSmokeMachineIncomingGoodsListDto
 *
 * <AUTHOR>
 * @date 2025-07-03 16:10:01
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizSmokeMachineIncomingGoodsListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizSmokeMachineIncomingGoodsListDto toDto(BizSmokeMachineIncomingGoodsList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizSmokeMachineIncomingGoodsList toPo(BizSmokeMachineIncomingGoodsListParam param);

    /**
     * 数据库原始数据更新
     * @param bizSmokeMachineIncomingGoodsListParam
     * @param BizSmokeMachineIncomingGoodsList
     */
    void updatePo(BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, @MappingTarget BizSmokeMachineIncomingGoodsList bizSmokeMachineIncomingGoodsList);

    default void patchPo(BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam , BizSmokeMachineIncomingGoodsList bizSmokeMachineIncomingGoodsList) {
        // TODO 自行实现局部更新
    }
}