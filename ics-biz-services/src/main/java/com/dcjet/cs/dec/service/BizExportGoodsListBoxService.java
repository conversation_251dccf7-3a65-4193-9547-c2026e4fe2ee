package com.dcjet.cs.dec.service;



import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizENonStateAuxmatAggrContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.dcjet.cs.dec.dao.BizExportGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsListBoxMapper;
import com.dcjet.cs.dec.mapper.BizExportGoodsListBoxDtoMapper;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dec.model.BizExportGoodsListBox;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListDto;
import com.dcjet.cs.dto.dec.BizExportGoodsListBoxDto;
import com.dcjet.cs.dto.dec.BizExportGoodsListBoxParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizExportGoodsListBox业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 13:48:47
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizExportGoodsListBoxService extends BaseService<BizExportGoodsListBox> {

    private static final Logger log = LoggerFactory.getLogger(BizExportGoodsListBoxService.class);

    @Resource
    private BizExportGoodsListBoxMapper bizExportGoodsListBoxMapper;

    @Resource
    private BizExportGoodsListBoxDtoMapper bizExportGoodsListBoxDtoMapper;

    @Override
    public Mapper<BizExportGoodsListBox> getMapper() {
        return bizExportGoodsListBoxMapper;
    }

    @Resource
    private BizExportGoodsHeadMapper bizExportGoodsHeadMapper;

    @Resource
    private BizENonStateAuxmatAggrContractListMapper  bizENonStateAuxmatAggrContractListMapper;

    @Resource
    private BizENonStateAuxmatAggrContractListDtoMapper bizENonStateAuxmatAggrContractListDtoMapper;



    /**
     * 获取分页信息
     *
     * @param bizExportGoodsListBoxParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizExportGoodsListBoxDto>> getListPaged(BizExportGoodsListBoxParam bizExportGoodsListBoxParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizExportGoodsListBox bizExportGoodsListBox = bizExportGoodsListBoxDtoMapper.toPo(bizExportGoodsListBoxParam);
        bizExportGoodsListBox.setTradeCode(userInfo.getCompany());
        Page<BizExportGoodsListBox> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizExportGoodsListBoxMapper.getList( bizExportGoodsListBox));
        // 将PO转为DTO返回给前端
        List<BizExportGoodsListBoxDto> bizExportGoodsListBoxDtoList = page.getResult().stream()
            .map(bizExportGoodsListBoxDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizExportGoodsListBoxDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizExportGoodsListBoxParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsListBoxDto insert(BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        BizExportGoodsListBox bizExportGoodsListBox = bizExportGoodsListBoxDtoMapper.toPo(bizExportGoodsListBoxParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizExportGoodsListBox.setId(sid);
        bizExportGoodsListBox.setCreateBy(userInfo.getUserNo());
        bizExportGoodsListBox.setInsertUserName(userInfo.getUserName());
        bizExportGoodsListBox.setCreateTime(new Date());
        bizExportGoodsListBox.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizExportGoodsListBoxMapper.insert(bizExportGoodsListBox);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizExportGoodsListBoxDtoMapper.toDto(bizExportGoodsListBox) : null;
    }

    /**
     * 修改记录
     *
     * @param bizExportGoodsListBoxParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsListBoxDto update(BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        BizExportGoodsListBox bizExportGoodsListBox = bizExportGoodsListBoxMapper.selectByPrimaryKey(bizExportGoodsListBoxParam.getId());
        bizExportGoodsListBoxDtoMapper.updatePo(bizExportGoodsListBoxParam, bizExportGoodsListBox);
        bizExportGoodsListBox.setUpdateBy(userInfo.getUserNo());
        bizExportGoodsListBox.setUpdateUserName(userInfo.getUserName());
        bizExportGoodsListBox.setUpdateTime(new Date());

        // 更新数据
        int update = bizExportGoodsListBoxMapper.updateByPrimaryKey(bizExportGoodsListBox);
        return update > 0 ? bizExportGoodsListBoxDtoMapper.toDto(bizExportGoodsListBox) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizExportGoodsListBoxMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizExportGoodsListBoxDto> selectAll(BizExportGoodsListBoxParam exportParam, UserInfoToken userInfo) {
        BizExportGoodsListBox bizExportGoodsListBox = bizExportGoodsListBoxDtoMapper.toPo(exportParam);
        bizExportGoodsListBox.setTradeCode(userInfo.getCompany());
        List<BizExportGoodsListBoxDto> bizExportGoodsListBoxDtos = new ArrayList<>();
        List<BizExportGoodsListBox> bizExportGoodsListBoxLists = bizExportGoodsListBoxMapper.getList(bizExportGoodsListBox);
        if (CollectionUtils.isNotEmpty(bizExportGoodsListBoxLists)) {
           bizExportGoodsListBoxDtos = bizExportGoodsListBoxLists.stream().map(head -> {
                    BizExportGoodsListBoxDto dto =  bizExportGoodsListBoxDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizExportGoodsListBoxDtos;
    }


    /**
     * 功能描述:根据合同编号查询数据 可以提取的表体数据
     * @param bizExportGoodsListBoxParam 查询参数 主要是parent_id
     * @param userInfo 用户信息
     * @return <List<BizENonStateAuxmatAggrContractListDto>> 外商合同列表
     */
    public ResultObject<List<BizENonStateAuxmatAggrContractListDto>> getListByContractNo(BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        ResultObject<List<BizENonStateAuxmatAggrContractListDto>> resultObject = ResultObject.createInstance(true, "查询成功");
        if (StringUtils.isNotBlank(bizExportGoodsListBoxParam.getParentId())) {
            // 获取表头合同号
            BizExportGoodsHead bizExportGoodsListBox = bizExportGoodsHeadMapper.selectByPrimaryKey(bizExportGoodsListBoxParam.getParentId());
            if (bizExportGoodsListBox != null && StringUtils.isNotBlank(bizExportGoodsListBox.getContractNo())) {
                // 根据 合同号 + tradeCode 查找外商合同表体确认数据
                List<BizENonStateAuxmatAggrContractList>  list = bizENonStateAuxmatAggrContractListMapper.getListByContractNo(bizExportGoodsListBox.getContractNo(),userInfo.getCompany());
                List<BizENonStateAuxmatAggrContractListDto> collect = list.stream().map(
                        item -> {
                            BizENonStateAuxmatAggrContractListDto dto = bizENonStateAuxmatAggrContractListDtoMapper.toDto(item);
                            return dto;
                        }
                ).collect(Collectors.toList());
                resultObject.setData(collect);
            }
        }
        return resultObject;
    }
}