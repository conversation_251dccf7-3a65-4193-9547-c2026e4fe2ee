package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIPurchaseHeadMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseListBoxMapper;
import com.dcjet.cs.dec.mapper.BizIPurchaseListBoxDtoMapper;
import com.dcjet.cs.dec.mapper.BizIPurchaseListDtoMapper;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dec.model.BizIPurchaseListBox;
import com.dcjet.cs.dto.dec.*;

import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BizIPurchaseListBox业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-16 14:16:02
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIPurchaseListBoxService extends BaseService<BizIPurchaseListBox> {

    private static final Logger log = LoggerFactory.getLogger(BizIPurchaseListBoxService.class);

    @Resource
    private BizIPurchaseListBoxMapper bizIPurchaseListBoxMapper;

    @Resource
    private BizIPurchaseListBoxDtoMapper bizIPurchaseListBoxDtoMapper;

    @Override
    public Mapper<BizIPurchaseListBox> getMapper() {
        return bizIPurchaseListBoxMapper;
    }

    @Resource
    private BizIPurchaseListDtoMapper bizIPurchaseListDtoMapper;

    @Resource
    private BizIPurchaseHeadMapper bizIPurchaseHeadMapper;

    @Resource
    private BizIOrderHeadService bIzIOrderHeadService;




    /**
     * 获取分页信息
     *
     * @param bizIPurchaseListBoxParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIPurchaseListBoxDto>> getListPaged(BizIPurchaseListBoxParam bizIPurchaseListBoxParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizIPurchaseListBox bizIPurchaseListBox = bizIPurchaseListBoxDtoMapper.toPo(bizIPurchaseListBoxParam);
        bizIPurchaseListBox.setTradeCode(userInfo.getCompany());
        Page<BizIPurchaseListBox> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIPurchaseListBoxMapper.getList( bizIPurchaseListBox));
        // 将PO转为DTO返回给前端
        List<BizIPurchaseListBoxDto> bizIPurchaseListBoxDtoList = page.getResult().stream()
            .map(bizIPurchaseListBoxDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIPurchaseListBoxDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIPurchaseListBoxParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPurchaseListBoxDto insert(BizIPurchaseListBoxParam bizIPurchaseListBoxParam, UserInfoToken userInfo) {
        BizIPurchaseListBox bizIPurchaseListBox = bizIPurchaseListBoxDtoMapper.toPo(bizIPurchaseListBoxParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIPurchaseListBox.setSid(sid);
        bizIPurchaseListBox.setInsertUser(userInfo.getUserNo());
        bizIPurchaseListBox.setInsertTime(new Date());
        bizIPurchaseListBox.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizIPurchaseListBoxMapper.insert(bizIPurchaseListBox);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIPurchaseListBoxDtoMapper.toDto(bizIPurchaseListBox) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIPurchaseListBoxParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPurchaseListBoxDto update(BizIPurchaseListBoxParam bizIPurchaseListBoxParam, UserInfoToken userInfo) {
        BizIPurchaseListBox bizIPurchaseListBox = bizIPurchaseListBoxMapper.selectByPrimaryKey(bizIPurchaseListBoxParam.getSid());
        bizIPurchaseListBoxDtoMapper.updatePo(bizIPurchaseListBoxParam, bizIPurchaseListBox);
        bizIPurchaseListBox.setUpdateUser(userInfo.getUserNo());
        bizIPurchaseListBox.setUpdateTime(new Date());

        // 更新数据
        int update = bizIPurchaseListBoxMapper.updateByPrimaryKey(bizIPurchaseListBox);
        return update > 0 ? bizIPurchaseListBoxDtoMapper.toDto(bizIPurchaseListBox) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizIPurchaseListBoxMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIPurchaseListBoxDto> selectAll(BizIPurchaseListBoxParam exportParam, UserInfoToken userInfo) {
        BizIPurchaseListBox bizIPurchaseListBox = bizIPurchaseListBoxDtoMapper.toPo(exportParam);
        bizIPurchaseListBox.setTradeCode(userInfo.getCompany());
        List<BizIPurchaseListBoxDto> bizIPurchaseListBoxDtos = new ArrayList<>();
        List<BizIPurchaseListBox> bizIPurchaseListBoxLists = bizIPurchaseListBoxMapper.getList(bizIPurchaseListBox);
        if (CollectionUtils.isNotEmpty(bizIPurchaseListBoxLists)) {
           bizIPurchaseListBoxDtos = bizIPurchaseListBoxLists.stream().map(head -> {
                    BizIPurchaseListBoxDto dto =  bizIPurchaseListBoxDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIPurchaseListBoxDtos;
    }



    /**
     * 新增装箱子表数据
     * @param params 箱号+进货信息表体数据
     * @param userInfo 用户信息
     * @return ResultObject
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject addPushListBox(PushListBoxParams params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "新增成功！");
        // 1.检查进货信息表体 件数是否存在为空的情况
        String boxNo = params.getBoxNo();
        List<BizIPurchaseListParam> bizIPurchaseListBoxParams = params.getPurchaseList();
        if (CollectionUtils.isEmpty(bizIPurchaseListBoxParams)) {
            throw new ErrorException(400, "装箱子表数据不能为空!");
        }else {
            // 校验是否产生下游数据
            BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(bizIPurchaseListBoxParams.get(0).getHeadId());
            if (bizIPurchaseHead != null) {
                bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行维护箱号！");
            }

            for (BizIPurchaseListParam item : bizIPurchaseListBoxParams) {
                if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    throw new ErrorException(400, item.getProductGrade() +"-装箱数量存在为空或为0的数据!");
                }
            }
        }
        String headId = bizIPurchaseListBoxParams.get(0).getHeadId();
        // 处理数据
        bizIPurchaseListBoxParams.forEach(item -> {
            BizIPurchaseListBox boxList = bizIPurchaseListDtoMapper.toBoxList(item);
            // 将进货信息表头的sid复制给装箱子表的 listHeadSid
            boxList.setListHeadSid(boxList.getSid());
            // 设置装箱子表的箱号
            boxList.setBoxNo(boxNo);
            boxList.setTradeCode(userInfo.getCompany());
            boxList.setInsertUser(userInfo.getUserNo());
            boxList.setInsertUserName(userInfo.getUserName());
            boxList.setInsertTime(new Date());
            // 重置sid
            boxList.setSid(UUID.randomUUID().toString());
            // 新增装箱子表数据
            bizIPurchaseListBoxMapper.insert(boxList);
        });


        // 更新进货信息表体的 箱号 和 件数
        bizIPurchaseListBoxMapper.updatePurchaseListBoxAndQuantity(headId);
        return resultObject;
    }


    /**
     * 统计装箱子表的汇总数据
     * @param param 进货信息表头sid
     * @param userInfo 用户信息
     * @return ResultObject
     */
    public ResultObject getSumData(BizIPurchaseListBoxParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        String headId = param.getHeadId();
        if (StringUtils.isBlank(headId)) {
            throw new ErrorException(400, "表头sid不能为空!");
        }
        // 获取统计数据
        BizIPurchaseListBoxSumData sumData = bizIPurchaseListBoxMapper.getSumData(headId);
        resultObject.setData(sumData);
        return resultObject;
    }


    /**
     * 功能描述:批量修改箱号
     *
     * @param params 箱号+进货信息表体数据
     * @param userInfo 用户信息
     * @return ResultObject
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject batchUpdateBoxList(List<BizIPurchaseListBoxParam> params, UserInfoToken userInfo) {
        ResultObject instance = ResultObject.createInstance(true, "修改成功！");
        Assert.notNull(params, "请选择装箱子表数据!");
        if (CollectionUtils.isNotEmpty(params)) {
            // 校验是否产生下游数据
            BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(params.get(0).getHeadId());
            if (bizIPurchaseHead != null) {
                bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行维护箱号！");
            }
        }
        // 校验每一条数据
        for (int i = 0; i < params.size(); i++) {

            BizIPurchaseListBoxParam item = params.get(i);
                String message = item.canSubmit(i+1);
                if (StringUtils.isNotBlank(message)){
                    throw new ErrorException(400, XdoI18nUtil.t(message));
                }
                if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    throw new ErrorException(400, item.getProductGrade() +"-件数存在为空或为0的数据!");
                }
                // 循环更新表体数据
            BizIPurchaseListBox po = bizIPurchaseListBoxDtoMapper.toPo(item);
            bizIPurchaseListBoxMapper.updateByPrimaryKey(po);
        }

        // 表头重新重新统计箱号
        List<String> listHeadIds = params.stream().map(it -> it.getListHeadSid()).distinct().collect(Collectors.toList());
        // 表头重新统计装箱子表箱号
        for (String listHeadId : listHeadIds) {
            bizIPurchaseListBoxMapper.updatePurchaseListBoxBySid(listHeadId);
            // 重新统计件数
            bizIPurchaseListBoxMapper.updateJS(listHeadId);
        }


        return instance;
    }
}