package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Getter
@Setter
@Table(name = "T_BIZ_I_SELL_HEAD")
public class BizISellHead implements Serializable{
    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    @Column(name = "head_id")
    private String headId;


    // purchaseOrderNumber: '', // 进货单号
    @Column(name = "purchase_order_number")
    private String purchaseOrderNumber;

    //  purchasingUnit: '',      // 购货单位
    @Column(name = "purchasing_unit")
    private String purchasingUnit;

    //  sellingUnit: '',        // 销货单位
    @Column(name = "selling_unit")
    private String sellingUnit;

    //  taxRate: '',            // 税率%
    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    //  dateOfSale: '',         // 作销日期
    @Column(name = "date_of_sale")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateOfSale;

    //  remark: '',             // 备注
    @Column(name = "remark")
    private String remark;

    //  salesDocumentStatus: '',// 销售单据状态
    @Column(name = "sales_document_status")
    private String salesDocumentStatus;

    //  salesDataConfirmationTime: '', // 销售数据确认时间
    @Column(name = "sales_data_confirmation_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date salesDataConfirmationTime;

    //  sendUFida: '',          // 发送用友
    @Column(name = "send_ufida")
    private String sendUfida;

    //  drawer: '',             // 开票人
    @Column(name = "drawer")
    private String drawer;

    //  businessDate: ''        // 业务日期
    @Column(name = "business_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;




    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 制单时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;


    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    //合同号
    @Column(name = "CONTRACT_NO")
    private String contractNo;

    //客户
    @Column(name = "customer")
    private String customer;

    //发送财务系统
    @Column(name = "SEND_FINANCIAL")
    private String sendFinancial;

    //是否冲红
    @Column(name = "IS_FLUSH_RED")
    private String isFlushRed;

}