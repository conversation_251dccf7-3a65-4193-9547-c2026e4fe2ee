package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsTb;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsTbDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsTbParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizSmokeMachineIncomingGoodsTbDto
 *
 * <AUTHOR>
 * @date 2025-07-04 21:29:36
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizSmokeMachineIncomingGoodsTbDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizSmokeMachineIncomingGoodsTbDto toDto(BizSmokeMachineIncomingGoodsTb po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizSmokeMachineIncomingGoodsTb toPo(BizSmokeMachineIncomingGoodsTbParam param);

    /**
     * 数据库原始数据更新
     * @param bizSmokeMachineIncomingGoodsTbParam
     * @param BizSmokeMachineIncomingGoodsTb
     */
    void updatePo(BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, @MappingTarget BizSmokeMachineIncomingGoodsTb bizSmokeMachineIncomingGoodsTb);

    default void patchPo(BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam , BizSmokeMachineIncomingGoodsTb bizSmokeMachineIncomingGoodsTb) {
        // TODO 自行实现局部更新
    }
}