package com.dcjet.cs.dec.service;



import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListBoxBoxMapper;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListBoxMapper;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListMapper;
import com.dcjet.cs.dec.mapper.BizBpAnalyseOrderHeadDtoMapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderHead;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderList;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBox;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderHeadDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderHeadParam;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadCommonDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper;
import com.dcjet.cs.seven.dao.SevenForeignContractListMapper;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.dcjet.cs.util.ExcelMerger;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Delete;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizBpAnalyseOrderHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 17:08:03
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizBpAnalyseOrderHeadService extends BaseService<BizBpAnalyseOrderHead> {

    private static final Logger log = LoggerFactory.getLogger(BizBpAnalyseOrderHeadService.class);

    @Resource
    private BizBpAnalyseOrderHeadMapper bizBpAnalyseOrderHeadMapper;

    @Resource
    private BizBpAnalyseOrderHeadDtoMapper bizBpAnalyseOrderHeadDtoMapper;

    @Override
    public Mapper<BizBpAnalyseOrderHead> getMapper() {
        return bizBpAnalyseOrderHeadMapper;
    }


    @Resource
    private BizSmokeCommonService bizSmokeCommonService;


    @Resource
    private SevenForeignContractHeadMapper contractHeadMapper;

    @Resource
    private SevenForeignContractListMapper contractListMapper;

    @Resource
    private BizBpAnalyseOrderListBoxBoxMapper bizBpAnalyseOrderListBoxBoxMapper;

    @Resource
    private BizBpAnalyseOrderListMapper bizBpAnalyseOrderListMapper;

    @Resource
    private ExportService exportService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private BizBpAnalyseOrderListBoxMapper bizBpAnalyseOrderListBoxMapper;


    /**
     * 获取分页信息
     *
     * @param bizBpAnalyseOrderHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizBpAnalyseOrderHeadDto>> getListPaged(BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadDtoMapper.toPo(bizBpAnalyseOrderHeadParam);
        bizBpAnalyseOrderHead.setTradeCode(userInfo.getCompany());
        Page<BizBpAnalyseOrderHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizBpAnalyseOrderHeadMapper.getList( bizBpAnalyseOrderHead));
        // 将PO转为DTO返回给前端
        List<BizBpAnalyseOrderHeadDto> bizBpAnalyseOrderHeadDtoList = page.getResult().stream()
            .map(bizBpAnalyseOrderHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizBpAnalyseOrderHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizBpAnalyseOrderHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderHeadDto insert(BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadDtoMapper.toPo(bizBpAnalyseOrderHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizBpAnalyseOrderHead.setId(sid);
        bizBpAnalyseOrderHead.setCreateBy(userInfo.getUserNo());
        bizBpAnalyseOrderHead.setInsertUserName(userInfo.getUserName());
        bizBpAnalyseOrderHead.setCreateTime(new Date());
        bizBpAnalyseOrderHead.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizBpAnalyseOrderHeadMapper.insert(bizBpAnalyseOrderHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizBpAnalyseOrderHeadDtoMapper.toDto(bizBpAnalyseOrderHead) : null;
    }

    /**
     * 修改记录
     *
     * @param bizBpAnalyseOrderHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderHeadDto update(BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadMapper.selectByPrimaryKey(bizBpAnalyseOrderHeadParam.getId());
        bizBpAnalyseOrderHeadDtoMapper.updatePo(bizBpAnalyseOrderHeadParam, bizBpAnalyseOrderHead);
        bizBpAnalyseOrderHead.setUpdateBy(userInfo.getUserNo());
        bizBpAnalyseOrderHead.setUpdateUserName(userInfo.getUserName());
        bizBpAnalyseOrderHead.setUpdateTime(new Date());

        // 统计表体的总金额
        BigDecimal totalAmount = bizBpAnalyseOrderHeadMapper.getTotalAmountByHeadId(bizBpAnalyseOrderHead.getId());
        bizBpAnalyseOrderHead.setTotal(totalAmount);

        // 更新数据
        int update = bizBpAnalyseOrderHeadMapper.updateByPrimaryKey(bizBpAnalyseOrderHead);
        bizBpAnalyseOrderHead.setCreateBy(bizBpAnalyseOrderHead.getUpdateUserName());
        bizBpAnalyseOrderHead.setCreateTime(bizBpAnalyseOrderHead.getUpdateTime());
        return update > 0 ? bizBpAnalyseOrderHeadDtoMapper.toDto(bizBpAnalyseOrderHead) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    @DeleteMapping("/delete/{sids}")
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizBpAnalyseOrderHeadMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizBpAnalyseOrderHeadDto> selectAll(BizBpAnalyseOrderHeadParam exportParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadDtoMapper.toPo(exportParam);
        bizBpAnalyseOrderHead.setTradeCode(userInfo.getCompany());
        List<BizBpAnalyseOrderHeadDto> bizBpAnalyseOrderHeadDtos = new ArrayList<>();
        List<BizBpAnalyseOrderHead> bizBpAnalyseOrderHeadLists = bizBpAnalyseOrderHeadMapper.getList(bizBpAnalyseOrderHead);
        if (CollectionUtils.isNotEmpty(bizBpAnalyseOrderHeadLists)) {
           bizBpAnalyseOrderHeadDtos = bizBpAnalyseOrderHeadLists.stream().map(head -> {
                    BizBpAnalyseOrderHeadDto dto =  bizBpAnalyseOrderHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizBpAnalyseOrderHeadDtos;
    }


    public ResultObject<Integer> checkAnalyseOrderCode(BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
        ResultObject<Integer> resultObject = ResultObject.createInstance(true);
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadDtoMapper.toPo(bizBpAnalyseOrderHeadParam);
        Assert.hasText(bizBpAnalyseOrderHead.getTradeCode(), "分析单号不能为空");
        // 检查分析单编号是否已存在
        Integer i  = bizBpAnalyseOrderHeadMapper.checkAnalyseOrderCode(bizBpAnalyseOrderHead.getId(),bizBpAnalyseOrderHead.getAnalysisNo(),userInfo.getCompany());
        resultObject.setData(i);
        return resultObject;
    }


    public ResultObject getCommonKeyValueList(BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, UserInfoToken userInfo) {
       return bizSmokeCommonService.getListCommonKeyValueList(userInfo, Arrays.asList(BizSmokeCommonService.BizTypeEnum.COUNTRY,
                                                                                      BizSmokeCommonService.BizTypeEnum.CURR,
                                                                                      BizSmokeCommonService.BizTypeEnum.CUSTOMER));
    }

    public ResultObject getCommonSearchList(BizBpAnalyseOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject instance = ResultObject.createInstance(true,"获取成功");
        BizSmokeMachineIncomingGoodsHeadCommonDto dto = new BizSmokeMachineIncomingGoodsHeadCommonDto();
        // 获取当前表单用户的 客户
        List<Map<String, String>> customerList = bizBpAnalyseOrderHeadMapper.getCurrentCustomerList(userInfo.getCompany());
        dto.setCustomerList(customerList);

        // 获取当前用户的制单人
        List<Map<String, String>> createByList = bizBpAnalyseOrderHeadMapper.getCurrentCreateByList(userInfo.getCompany());
        dto.setCreateByList(createByList);


        //获取币制相关信息
        List<Map<String, String>> currList = bizSmokeCommonService.getCurrList(userInfo.getCompany());
        dto.setCurrList(currList);


        instance.setData(dto);
        return instance;
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirm(String id, UserInfoToken userInfo) {
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadMapper.selectByPrimaryKey(id);
        bizBpAnalyseOrderHead.setDataState("1");
        bizBpAnalyseOrderHead.setConfirmTime(new Date());
        // 判断表头分析单是否填写
        if (StringUtils.isBlank(bizBpAnalyseOrderHead.getAnalysisNo())) {
            throw new ErrorException(400,"请填写分析单号");
        }
        int i = bizBpAnalyseOrderHeadMapper.updateByPrimaryKey(bizBpAnalyseOrderHead);
        if (i == 1) {
            return ResultObject.createInstance(true,"确认成功");
        }else {
            return ResultObject.createInstance(false,"确认失败");
        }
    }

    public ResultObject getContractNoList(BizBpAnalyseOrderHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取外商合同列表成功");
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadDtoMapper.toPo(param);

        List<BizBpAnalyseOrderHead>  list = bizBpAnalyseOrderHeadMapper.getContractNoList(bizBpAnalyseOrderHead.getContractNo(),userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(list)) {
            List<BizBpAnalyseOrderHeadDto> dtoList = list.stream().map(head -> {
                BizBpAnalyseOrderHeadDto dto =  bizBpAnalyseOrderHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setData(dtoList);
        }
        return resultObject;
    }


    public ResultObject extractContract(BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "提取成功");
        // 判断ID是否为空
        if (param.getId() == null) {
            throw new ErrorException(400,"请选择需要提取的外商合同");
        }
        try {
            // 根据ID查询对应的外商合同
            SevenForeignContractHead head = contractHeadMapper.selectByPrimaryKey(param.getId());
            // 获取外商合同表体数据
            List<SevenForeignContractList> list = contractListMapper.getListByHeadId(param.getId(), userInfo.getCompany());


            // ======================================== 设置表头信息数据 ========================================
            String id = UUID.randomUUID().toString();
            BizBpAnalyseOrderHead bpHead = new BizBpAnalyseOrderHead();
            bpHead.setId(id);
            bpHead.setTradeCode(userInfo.getCompany());
            bpHead.setCreateTime(new Date());
            bpHead.setCreateBy(userInfo.getUserNo());
            bpHead.setInsertUserName(userInfo.getUserName());
            bpHead.setDataState("0");
            // 设置合同号
            bpHead.setContractNo(head.getContractNo());
            // 设置转运期限
            bpHead.setShipmentDeadline(head.getShipPeriodDate());
            // 设置外商合同表头ID
            bpHead.setExtend1(head.getId());
            // 设置币种
            bpHead.setCurr(head.getCurr());


            // ======================================== 设置表体信息数据 ========================================
            ArrayList<BizBpAnalyseOrderList> bpLists = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (SevenForeignContractList item : list) {
                    BizBpAnalyseOrderList bpList = new BizBpAnalyseOrderList();
                    bpList.setId(UUID.randomUUID().toString());
                    bpList.setCreateTime(new Date());
                    bpList.setCreateBy(userInfo.getUserNo());
                    bpList.setInsertUserName(userInfo.getUserName());
                    bpList.setDataState("0");
                    bpList.setParentId(id);

                    // 设置商品名称
                    bpList.setProductName(item.getGName());
                    // 设置产品型号
                    bpList.setProductModel(item.getGModel());
                    // 设置单位
                    bpList.setUnit(item.getUnit());
                    // 设置数量
                    bpList.setQuantity(item.getQty());
                    // 设置箱数
                    bpList.setBoxCount(item.getBoxNum());
                    // 设置毛重
                    bpList.setGrossWeight(item.getGrossWeight());

                    // 设置单价
                    bpList.setUnitPrice(item.getUnitPrice());
                    // 设置金额
                    bpList.setAmount(item.getMoneyAmount());
                    // 设置备注
                    bpList.setRemark(item.getNote());

                    bpLists.add(bpList);
                }
            }
            log.error("插入表头数据：{}", bpHead);
            log.error("插入表体数据：{}", bpLists);

            // 插入表头数据
            int i = bizBpAnalyseOrderHeadMapper.insert(bpHead);
            if (i > 0) {
                // 设置返回表头数据
                // 重新设置更新人
                bpHead.setCreateBy(bpHead.getInsertUserName());
                resultObject.setData(bpHead);
                // 插入表体数据
                if (CollectionUtils.isNotEmpty(bpLists)) {
                    for (BizBpAnalyseOrderList item : bpLists) {
                        int j = bizBpAnalyseOrderListMapper.insert(item);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(StringUtils.isEmpty(e.getMessage()) ? "提取失败" : e.getMessage());
        }
        return resultObject;
    }

    public String printAnalysis(BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) {
        String templateName = "7-分析单.xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizBpAnalyseOrderHead> heads = new ArrayList<>();
        List<BizBpAnalyseOrderList> lists = new ArrayList<>();
        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "分析单表头不能为空");
        }
        BizBpAnalyseOrderHead head = bizBpAnalyseOrderHeadMapper.selectByPrimaryKey(param.getId());
        head.setTradeCountry(pCodeHolder.getValue(PCodeType.COUNTRY, head.getTradeCountry()));
        head.setConsumeCountry(pCodeHolder.getValue(PCodeType.COUNTRY, head.getConsumeCountry()));
        head.setIsTransit("1".equals(head.getIsTransit()) ? "YES" : "NO");
        lists = bizBpAnalyseOrderListMapper.getListByHeadId(param.getId());
        //表体数据组装
        for (BizBpAnalyseOrderList list : lists) {
            // 安全获取各个字段值，避免空指针异常
            String productModel = list.getProductModel() != null ? list.getProductModel() : "";
            BigDecimal quantity = list.getQuantity() != null ? list.getQuantity() : BigDecimal.ZERO;
            BigDecimal grossWeight = list.getGrossWeight() != null ? list.getGrossWeight() : BigDecimal.ZERO;
            BigDecimal netWt = list.getNetWt() != null ? list.getNetWt() : BigDecimal.ZERO;

            // 计算单位重量，避免除零异常
            BigDecimal unitGrossWeight = BigDecimal.ZERO;
            BigDecimal unitNetWeight = BigDecimal.ZERO;
            BigDecimal unitTareWeight = BigDecimal.ZERO;

            if (quantity.compareTo(BigDecimal.ZERO) > 0) {
                unitGrossWeight = grossWeight.divide(quantity, 2, RoundingMode.HALF_UP);
                unitNetWeight = netWt.divide(quantity, 2, RoundingMode.HALF_UP);
                unitTareWeight = unitGrossWeight.subtract(unitNetWeight);
            }

            String remark = productModel + "\n"
                    + "LTR/SCF/090124/113\n"
                    + "NO.1-" + quantity + "\n"
                    + NumberFormatterUtils.formatNumber(unitGrossWeight) + "KGS "
                    + NumberFormatterUtils.formatNumber(unitNetWeight) + "KGS "
                    + NumberFormatterUtils.formatNumber(unitTareWeight) + "KGS";
            list.setRemark(remark);
            list.setProductModel("TOBACCO BY-PRODUCTS GRADE: " + list.getProductModel());
            list.setQuantityStr(NumberFormatterUtils.formatNumber(list.getQuantity()) + "CARTONS");
            list.setGrossWeightStr(NumberFormatterUtils.formatNumber(list.getGrossWeight()));
            list.setNetWtStr(NumberFormatterUtils.formatNumber(list.getNetWt()));
            list.setVolumeStr(NumberFormatterUtils.formatNumber(list.getVolume()));
            list.setUnitPriceStr(head.getCurr() + NumberFormatterUtils.formatNumber(list.getUnitPrice()));
            list.setAmountStr(head.getCurr() + NumberFormatterUtils.formatNumber(list.getAmount()));
        }

        //集装箱信息
        String packingSummaryInfo = bizBpAnalyseOrderHeadMapper.getPackingSummaryInfo(head.getId());
        head.setPackingSummaryInfo(packingSummaryInfo);

        String packingInfo = bizBpAnalyseOrderHeadMapper.getPackingInfo(head.getId());
        head.setPackingInfo(packingInfo);

        StringBuilder packingInfoDetail = new StringBuilder();
        int serialNo = 1;
        List<BizBpAnalyseOrderListBox> listByHeadId = bizBpAnalyseOrderListBoxMapper.getListByHeadId(param.getId());
        for (BizBpAnalyseOrderListBox box : listByHeadId) {
            //goodsNameStr格式 软中华 * 1 +软中华 * 2 +软中华 * 13
            String goodsNameStr = box.getProductName() != null ? box.getProductName() : "";

            // 判断商品名称的三种情况
            if (StringUtils.isBlank(goodsNameStr)) {
                // 空字符串情况，跳过处理
                continue;
            }

            // 按 + 号分割，获取所有商品项
            String[] productItems = goodsNameStr.split("\\+");

            if (productItems.length == 1) {
                // 情况1：只有一个商品项
                String[] split = productItems[0].trim().split("\\*");
                if (split.length >= 1) {
                    String productName = split[0].trim();
                    // 处理单个商品名称的逻辑
                    // 获取余数
                    BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox = new BizBpAnalyseOrderListBoxBox();
                    bizBpAnalyseOrderListBoxBox.setParentId(box.getId());
                    List<BizBpAnalyseOrderListBoxBox> list = bizBpAnalyseOrderListBoxBoxMapper.getList(bizBpAnalyseOrderListBoxBox);
                    if (CollectionUtils.isNotEmpty(list)) {
                        box.setRemainingBoxCount(list.get(0).getRemainingBoxCount());
                    }
                    packingInfoDetail.append(serialNo).append(".").append(handleSingleProduct(box, productName, split)).append("\n");
                    serialNo++;
                }
            } else {
                // 多个商品项，需要进一步判断是否为同一品名
                Set<String> uniqueProductNames = new HashSet<>();
                List<String[]> allProductSplits = new ArrayList<>();

                for (String item : productItems) {
                    String[] split = item.trim().split("\\*");
                    if (split.length >= 1) {
                        String productName = split[0].trim();
                        uniqueProductNames.add(productName);
                        allProductSplits.add(split);
                    }
                }

                if (uniqueProductNames.size() == 1) {
                    // 情况2：多个同一品名
                    String productName = uniqueProductNames.iterator().next();
                    // 处理多个同一品名的逻辑
                    packingInfoDetail.append(serialNo).append(".").append(handleSameProductMultiple(box, productName, allProductSplits)).append("\n");
                } else {
                    // 情况3：不同品名
                    // 处理不同品名的逻辑
                    packingInfoDetail.append(serialNo).append(".").append(handleDifferentProducts(box, uniqueProductNames, allProductSplits)).append("\n");
                }
                serialNo++;
            }
        }
        head.setPackingInfoDetail(packingInfoDetail.toString());
        SevenForeignContractHead sevenForeignContractHead = contractHeadMapper.selectByPrimaryKey(head.getExtend1());
        if (sevenForeignContractHead != null) {
            head.setPriceItem(sevenForeignContractHead.getPriceTerm());
        }
        heads.add(head);
        String exportFileName = exportService.export(heads, lists, fileName, templateName);
        return exportFileName;
    }

    /**
     * 处理单个商品名称的逻辑
     * @param productName 商品名称
     * @param split 分割后的数组（商品名称 * 数量）
     */
    private String handleSingleProduct(BizBpAnalyseOrderListBox box,String productName, String[] split) {
        // 实现单个商品名称的处理逻辑
        String name = "";
        log.info("处理单个商品：{}", productName);
        if (split.length >= 2) {
            String quantity = split[1].trim();
            log.info("商品名称：{}，数量：{}", productName, quantity);
            name = productName + " " +box.getContainerCount() +"*" + box.getContainerSpec() + "(装" + box.getBoxCount() + "箱);+余" + box.getRemainingBoxCount() + "箱";
        }
        return name;
    }

    /**
     * 处理多个同一品名的逻辑
     * @param productName 商品名称
     * @param allProductSplits 所有商品分割后的数组列表
     */
    private String handleSameProductMultiple(BizBpAnalyseOrderListBox box, String productName, List<String[]> allProductSplits) {
        //实现多个同一品名的处理逻辑
        log.info("处理多个同一品名：{}", productName);
        StringBuilder name = new StringBuilder(productName + " ");
        for (String[] split : allProductSplits) {
            if (split.length >= 2) {
                try {
                    int quantity = Integer.parseInt(split[1].trim());
                    name.append(box.getContainerCount()).append("*").append(box.getContainerSpec()).append("(装").append(quantity).append("箱);").append("+");
                    log.info("商品名称：{}，单项数量：{}", productName, quantity);
                } catch (NumberFormatException e) {
                    log.warn("数量格式错误：{}", split[1]);
                }
            }
        }
        if (name.length() > 0 && name.charAt(name.length() - 1) == '+') {
            name.deleteCharAt(name.length() - 1);
        }
        return name.toString();
    }

    /**
     * 处理不同品名的逻辑
     * @param uniqueProductNames 唯一商品名称集合
     * @param allProductSplits 所有商品分割后的数组列表
     */
    private String handleDifferentProducts(BizBpAnalyseOrderListBox box, Set<String> uniqueProductNames, List<String[]> allProductSplits) {
        // 实现不同品名的处理逻辑
        log.info("处理不同品名，共{}种商品", uniqueProductNames.size());
        StringBuilder name = new StringBuilder();
        int totalQuantity = 0;
        for (String productName : uniqueProductNames) {
            int productQuantity = 0;
            log.info("商品名称：{}", productName);
            for (String[] split : allProductSplits) {
                if (split.length >= 2) {
                    if (!productName.equals(split[0].trim())){
                        continue;
                    }
                    String quantity = split[1].trim();
                    totalQuantity = totalQuantity + Integer.parseInt(quantity);
                    productQuantity = productQuantity + Integer.parseInt(quantity);
                }
            }
            name.append(productName).append("(").append(productQuantity).append(")箱+");
        }
        if (name.length() > 0 && name.charAt(name.length() - 1) == '+') {
            name.deleteCharAt(name.length() - 1);
        }
        name.append("=").append(totalQuantity).append("箱").append(" ").append(box.getContainerCount()).append("*").append(box.getContainerSpec()).append("（装").append(totalQuantity).append("箱)");

        return name.toString();
    }

    public String printShipment(BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) {
        String templateName = "7-分析单-装运通知.xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizBpAnalyseOrderHead> heads = new ArrayList<>();
        List<BizBpAnalyseOrderList> lists = new ArrayList<>();
        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "分析单表头不能为空");
        }
        BizBpAnalyseOrderHead head = bizBpAnalyseOrderHeadMapper.selectByPrimaryKey(param.getId());
        lists = bizBpAnalyseOrderListMapper.getListByHeadId(param.getId());

        //集装箱信息
        String packingSummaryInfo = bizBpAnalyseOrderHeadMapper.getPackingSummaryInfo(head.getId());
        head.setPackingSummaryInfo(packingSummaryInfo);

        String packingInfo = bizBpAnalyseOrderHeadMapper.getPackingInfo(head.getId());
        head.setPackingInfo(packingInfo);

        StringBuilder packingInfoDetail = new StringBuilder();
        int serialNo = 1;
        List<BizBpAnalyseOrderListBox> listByHeadId = bizBpAnalyseOrderListBoxMapper.getListByHeadId(param.getId());
        //集装箱总数
        int containerCount = 0;
        for (BizBpAnalyseOrderListBox box : listByHeadId) {
            //goodsNameStr格式 软中华 * 1 +软中华 * 2 +软中华 * 13
            String goodsNameStr = box.getProductName() != null ? box.getProductName() : "";

            containerCount += box.getContainerCount().intValue();

            // 判断商品名称的三种情况
            if (StringUtils.isBlank(goodsNameStr)) {
                // 空字符串情况，跳过处理
                continue;
            }

            // 按 + 号分割，获取所有商品项
            String[] productItems = goodsNameStr.split("\\+");

            if (productItems.length == 1) {
                // 情况1：只有一个商品项
                String[] split = productItems[0].trim().split("\\*");
                if (split.length >= 1) {
                    String productName = split[0].trim();
                    // 处理单个商品名称的逻辑
                    packingInfoDetail.append(serialNo).append(".").append(handleSingleProduct(box, productName, split)).append("\n");
                    serialNo++;
                }
            } else {
                // 多个商品项，需要进一步判断是否为同一品名
                Set<String> uniqueProductNames = new HashSet<>();
                List<String[]> allProductSplits = new ArrayList<>();

                for (String item : productItems) {
                    String[] split = item.trim().split("\\*");
                    if (split.length >= 1) {
                        String productName = split[0].trim();
                        uniqueProductNames.add(productName);
                        allProductSplits.add(split);
                    }
                }

                if (uniqueProductNames.size() == 1) {
                    // 情况2：多个同一品名
                    String productName = uniqueProductNames.iterator().next();
                    // 处理多个同一品名的逻辑
                    packingInfoDetail.append(serialNo).append(".").append(handleSameProductMultiple(box, productName, allProductSplits)).append("\n");
                } else {
                    // 情况3：不同品名
                    // 处理不同品名的逻辑
                    packingInfoDetail.append(serialNo).append(".").append(handleDifferentProducts(box, uniqueProductNames, allProductSplits)).append("\n");
                }
                serialNo++;
            }
        }
        head.setPackingInfoDetail(packingInfoDetail.toString());
        head.setAnalysisNo("分析单号：" + head.getAnalysisNo());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd");
        head.setExtend10("装箱时间："+ (head.getPackingTime() != null ? sdf.format(head.getPackingTime()) : "") + (head.getPackingTimeTo() != null ? "-" + sdf.format(head.getPackingTimeTo()) : "")
                + "。 "+ containerCount +"个货柜，具体每天装多少只货柜，由货代公司与仓库联系安排。");
        heads.add(head);
        String exportFileName = exportService.export(heads, lists, fileName, templateName);
        return exportFileName;
    }

    public String printShipping(BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) throws IOException {
        String templateName = "7-分析单-装船电.xlsx";
        String templateNameBox = "7-分析单-装船电-集装箱.xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizBpAnalyseOrderHead> heads = new ArrayList<>();
        List<BizBpAnalyseOrderList> lists = new ArrayList<>();
        List<String> files = new ArrayList<>();
        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "分析单表头不能为空");
        }
        BizBpAnalyseOrderHead head = bizBpAnalyseOrderHeadMapper.selectByPrimaryKey(param.getId());
        if (head == null){
            throw new ErrorException(400, "分析单表头不存在");
        }
        lists = bizBpAnalyseOrderListMapper.getListByHeadId(param.getId());
        if (CollectionUtils.isEmpty(lists)){
            throw new ErrorException(400, "分析单表体不存在");
        }

        for (BizBpAnalyseOrderList list : lists) {
            if (list != null) {
                list.setBoxCountStr(NumberFormatterUtils.formatNumber(list.getBoxCount()) + "CARTONS");
                list.setGrossWeightStr(NumberFormatterUtils.formatNumber(list.getGrossWeight()) + "KGS(G.W.)");
                list.setNetWtStr(NumberFormatterUtils.formatNumber(list.getNetWt()) + "KGS(N.W.)");
            }
        }

        // 安全计算总箱数，过滤null对象和null值
        BigDecimal totalBoxCount = lists.stream()
                .filter(Objects::nonNull)
                .map(BizBpAnalyseOrderList::getBoxCount)
                .filter(Objects::nonNull)  // 过滤null的boxCount值
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 安全计算总毛重，过滤null对象和null值
        BigDecimal totalGrossWeight = lists.stream()
                .filter(Objects::nonNull)
                .map(BizBpAnalyseOrderList::getGrossWeight)
                .filter(Objects::nonNull)  // 过滤null的grossWeight值
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 安全计算总净重，过滤null对象和null值
        BigDecimal totalNetWt = lists.stream()
                .filter(Objects::nonNull)
                .map(BizBpAnalyseOrderList::getNetWt)
                .filter(Objects::nonNull)  // 过滤null的netWt值
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        head.setCreateTime(new Date());
        head.setTotalBoxCountStr(NumberFormatterUtils.formatNumber(totalBoxCount));
        head.setTotalGrossWeightStr(NumberFormatterUtils.formatNumber(totalGrossWeight));
        head.setTotalNetWtStr(NumberFormatterUtils.formatNumber(totalNetWt));
        //集装箱信息
        String packingSummaryInfo = bizBpAnalyseOrderHeadMapper.getPackingSummaryInfo(head.getId());
        head.setPackingSummaryInfo(packingSummaryInfo);
        heads.add(head);

        String exportFileName = exportService.export(heads, lists, fileName, templateName);
        files.add(exportFileName);
        //集装箱信息按货物组装
        List<BizBpAnalyseOrderListBox> boxLists = bizBpAnalyseOrderListBoxMapper.getListByHeadId(param.getId());

        // 处理boxLists数据为Map<String,List<BizBpAnalyseOrderListBox>>
        // key为商品名称，商品名称*后的数值存入箱数中
        Map<String, List<BizBpAnalyseOrderListBox>> productBoxMap = processBoxListsToMap(boxLists);
        productBoxMap.forEach((k, v) -> {
            String fileNameBox = UUID.randomUUID().toString() + ".xlsx";
            List<BizBpAnalyseOrderListBox> boxHeads = new ArrayList<>();
            List<BizBpAnalyseOrderListBox> boxList = new ArrayList<>();
            BizBpAnalyseOrderListBox boxHead = new BizBpAnalyseOrderListBox();
            boxHead.setProductName(k);
            boxHeads.add(boxHead);
            int countStart = 1;
            // 两条为一组处理
            for (int i = 0; i < v.size(); i += 2) {
                BizBpAnalyseOrderListBox box = new BizBpAnalyseOrderListBox();

                // 第一条数据赋值到主要字段
                BizBpAnalyseOrderListBox firstBox = v.get(i);
                box.setContainerNo(firstBox.getContainerNo());
                box.setContainerSpec(firstBox.getBoxCount() + "(" + firstBox.getContainerSpec() + ")");
                box.setBoxCount1(countStart + "-" + (countStart + firstBox.getBoxCount().intValue() - 1));
                countStart += firstBox.getBoxCount().intValue();

                // 如果存在第二条数据，赋值到扩展字段
                if (i + 1 < v.size()) {
                    BizBpAnalyseOrderListBox secondBox = v.get(i + 1);
                    box.setContainerSpec1(secondBox.getBoxCount() + "(" + secondBox.getContainerSpec() + ")");
                    box.setContainerNo1(secondBox.getContainerNo());
                    box.setBoxCount2(countStart + "-" + (countStart + secondBox.getBoxCount().intValue() - 1));
                    countStart += secondBox.getBoxCount().intValue();
                } else {
                    // 如果只有一条数据，扩展字段设为空或默认值
                    box.setContainerSpec1("");
                    box.setContainerNo1("");
                    box.setBoxCount2("");
                }

                boxList.add(box);
            }
            String exportFileName2 = exportService.export(boxHeads, boxList, fileNameBox, templateNameBox);
            files.add(exportFileName2);
        });


        //拼接导出文件
        ExcelMerger excelMerger=new ExcelMerger();
        return  excelMerger.appendToFirstFileSaveLast(files, true);
    }

    /**
     * 处理boxLists数据为Map格式
     * @param boxLists 原始箱子列表数据
     * @return Map<String,List<BizBpAnalyseOrderListBox>> key为商品名称，value为对应的箱子列表
     */
    private Map<String, List<BizBpAnalyseOrderListBox>> processBoxListsToMap(List<BizBpAnalyseOrderListBox> boxLists) {
        Map<String, List<BizBpAnalyseOrderListBox>> productBoxMap = new HashMap<>();

        if (CollectionUtils.isEmpty(boxLists)) {
            return productBoxMap;
        }

        for (BizBpAnalyseOrderListBox box : boxLists) {
            // 获取商品名称字符串，格式如：软中华 * 1 +软中华 * 2 +软中华 * 13
            String goodsNameStr = box.getProductName() != null ? box.getProductName() : "";

            if (StringUtils.isBlank(goodsNameStr)) {
                continue;
            }

            // 处理集装箱号的逗号分割逻辑
            String containerNoStr = box.getContainerNo() != null ? box.getContainerNo() : "";
            String[] containerNos = containerNoStr.split(",");

            // 按 + 号分割，获取所有商品项
            String[] productItems = goodsNameStr.split("\\+");

            // 为每个集装箱号创建对应的数据
            for (String containerNo : containerNos) {
                String singleContainerNo = containerNo.trim();

                for (String item : productItems) {
                    String trimmedItem = item.trim();
                    if (StringUtils.isBlank(trimmedItem)) {
                        continue;
                    }

                    // 按 * 号分割，获取商品名称和数量
                    String[] split = trimmedItem.split("\\*");
                    if (split.length >= 2) {
                        String productName = split[0].trim();
                        String quantityStr = split[1].trim();

                        if (StringUtils.isNotBlank(productName)) {
                            // 创建新的BizBpAnalyseOrderListBox对象，避免修改原对象
                            BizBpAnalyseOrderListBox newBox = createBoxCopy(box);

                            // 设置单个集装箱号
                            newBox.setContainerNo(singleContainerNo);

                            // 将商品名称*后的数值存入箱数中
                            try {
                                Integer boxCountInt = Integer.parseInt(quantityStr);
                                newBox.setBoxCount(new BigDecimal(boxCountInt));
                            } catch (NumberFormatException e) {
                                log.warn("解析箱数失败，商品：{}，数量字符串：{}", productName, quantityStr);
                                newBox.setBoxCount(BigDecimal.ZERO);
                            }

                            // 设置纯净的商品名称（不包含*和数量）
                            newBox.setProductName(productName);

                            // 按商品名称分组
                            productBoxMap.computeIfAbsent(productName, k -> new ArrayList<>()).add(newBox);

                            log.info("处理商品：{}，箱数：{}，集装箱号：{}", productName, newBox.getBoxCount(), singleContainerNo);
                        }
                    } else {
                        // 如果没有*分隔符，整个字符串作为商品名称，箱数设为0
                        String productName = trimmedItem;
                        BizBpAnalyseOrderListBox newBox = createBoxCopy(box);

                        // 设置单个集装箱号
                        newBox.setContainerNo(singleContainerNo);
                        newBox.setProductName(productName);
                        newBox.setBoxCount(BigDecimal.ZERO);

                        productBoxMap.computeIfAbsent(productName, k -> new ArrayList<>()).add(newBox);
                        log.info("处理商品（无箱数）：{}，集装箱号：{}", productName, singleContainerNo);
                    }
                }
            }
        }

        log.info("处理完成，共{}种商品", productBoxMap.size());
        return productBoxMap;
    }

    /**
     * 创建BizBpAnalyseOrderListBox对象的副本
     * @param original 原始对象
     * @return 副本对象
     */
    private BizBpAnalyseOrderListBox createBoxCopy(BizBpAnalyseOrderListBox original) {
        BizBpAnalyseOrderListBox copy = new BizBpAnalyseOrderListBox();

        // 复制所有相关字段（根据实际需要调整）
        copy.setId(original.getId());
        copy.setParentId(original.getParentId());  // 使用parentId而不是headId
        copy.setContainerSpec(original.getContainerSpec());
        copy.setContainerCount(original.getContainerCount());
        copy.setContainerNo(original.getContainerNo());
        copy.setRemainingBoxCount(original.getRemainingBoxCount());
        copy.setSerialNo(original.getSerialNo());
        copy.setNote(original.getNote());
        copy.setCreateBy(original.getCreateBy());
        copy.setCreateTime(original.getCreateTime());
        copy.setUpdateBy(original.getUpdateBy());
        copy.setUpdateTime(original.getUpdateTime());
        copy.setTradeCode(original.getTradeCode());
        copy.setBusinessType(original.getBusinessType());
        copy.setDataState(original.getDataState());
        copy.setVersionNo(original.getVersionNo());
        copy.setSysOrgCode(original.getSysOrgCode());
        copy.setInsertUserName(original.getInsertUserName());
        copy.setUpdateUserName(original.getUpdateUserName());
        // 复制扩展字段
        copy.setExtend1(original.getExtend1());
        copy.setExtend2(original.getExtend2());
        copy.setExtend3(original.getExtend3());
        copy.setExtend4(original.getExtend4());
        copy.setExtend5(original.getExtend5());
        copy.setExtend6(original.getExtend6());
        copy.setExtend7(original.getExtend7());
        copy.setExtend8(original.getExtend8());
        copy.setExtend9(original.getExtend9());
        copy.setExtend10(original.getExtend10());

        // 注意：不复制productName，因为我们要设置新的值
        // 注意：不复制boxCount，因为我们要设置新的值

        return copy;
    }

    public String printZxmd(BizBpAnalyseOrderHeadParam param, UserInfoToken userInfo) {
        String templateName = "biz-analyse-zxmd.xls";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizBpAnalyseOrderHead> heads = new ArrayList<>();
        List<BizBpAnalyseOrderList> lists = new ArrayList<>();
        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "分析单表头不能为空");
        }
        BizBpAnalyseOrderHead head = bizBpAnalyseOrderHeadMapper.selectByPrimaryKey(param.getId());
        if (head == null){
            throw new ErrorException(400, "分析单表头不存在");
        }
        lists = bizBpAnalyseOrderListMapper.getListByHeadId(param.getId());
        if (CollectionUtils.isEmpty(lists)){
            throw new ErrorException(400, "分析单表体不存在");
        }

        for (BizBpAnalyseOrderList list : lists) {
            //计算皮重=毛重-净重
            if (list.getGrossWeight()!=null&&list.getNetWt()!=null){
                list.setTareWeight(list.getGrossWeight().subtract(list.getNetWt()));
            }
        }

        heads.add(head);

        return exportService.export(heads, lists, fileName, templateName);
    }
}