package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizExportGoodsList;
import com.dcjet.cs.dto.dec.BizExportGoodsListDto;
import com.dcjet.cs.dto.dec.BizExportGoodsListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizExportGoodsListDto
 *
 * <AUTHOR>
 * @date 2025-07-07 13:22:20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizExportGoodsListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizExportGoodsListDto toDto(BizExportGoodsList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizExportGoodsList toPo(BizExportGoodsListParam param);

    /**
     * 数据库原始数据更新
     * @param bizExportGoodsListParam
     * @param BizExportGoodsList
     */
    void updatePo(BizExportGoodsListParam bizExportGoodsListParam, @MappingTarget BizExportGoodsList bizExportGoodsList);

    default void patchPo(BizExportGoodsListParam bizExportGoodsListParam , BizExportGoodsList bizExportGoodsList) {
        // TODO 自行实现局部更新
    }
}