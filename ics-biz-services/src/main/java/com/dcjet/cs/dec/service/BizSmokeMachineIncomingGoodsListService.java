package com.dcjet.cs.dec.service;


import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsListMapper;
import com.dcjet.cs.dec.mapper.BizSmokeMachineIncomingGoodsListDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListParam;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizSmokeMachineIncomingGoodsList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-03 16:10:01
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizSmokeMachineIncomingGoodsListService extends BaseService<BizSmokeMachineIncomingGoodsList> {

    private static final Logger log = LoggerFactory.getLogger(BizSmokeMachineIncomingGoodsListService.class);

    @Resource
    private BizSmokeMachineIncomingGoodsListMapper bizSmokeMachineIncomingGoodsListMapper;

    @Resource
    private BizSmokeMachineIncomingGoodsListDtoMapper bizSmokeMachineIncomingGoodsListDtoMapper;

    @Override
    public Mapper<BizSmokeMachineIncomingGoodsList> getMapper() {
        return bizSmokeMachineIncomingGoodsListMapper;
    }

    @Resource
    private BizSmokeCommonService bizSmokeCommonService;

    @Resource
    private BizSmokeMachineIncomingGoodsHeadMapper bizSmokeMachineIncomingGoodsHeadMapper;



    /**
     * 获取分页信息
     *
     * @param bizSmokeMachineIncomingGoodsListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizSmokeMachineIncomingGoodsListDto>> getListPaged(BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizSmokeMachineIncomingGoodsList bizSmokeMachineIncomingGoodsList = bizSmokeMachineIncomingGoodsListDtoMapper.toPo(bizSmokeMachineIncomingGoodsListParam);
        bizSmokeMachineIncomingGoodsList.setTradeCode(userInfo.getCompany());
        Page<BizSmokeMachineIncomingGoodsList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizSmokeMachineIncomingGoodsListMapper.getList( bizSmokeMachineIncomingGoodsList));
        // 将PO转为DTO返回给前端
        List<BizSmokeMachineIncomingGoodsListDto> bizSmokeMachineIncomingGoodsListDtoList = page.getResult().stream()
            .map(bizSmokeMachineIncomingGoodsListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizSmokeMachineIncomingGoodsListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizSmokeMachineIncomingGoodsListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsListDto insert(BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsList bizSmokeMachineIncomingGoodsList = bizSmokeMachineIncomingGoodsListDtoMapper.toPo(bizSmokeMachineIncomingGoodsListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizSmokeMachineIncomingGoodsList.setId(sid);
        bizSmokeMachineIncomingGoodsList.setCreateBy(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsList.setCreateTime(new Date());
        bizSmokeMachineIncomingGoodsList.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizSmokeMachineIncomingGoodsListMapper.insert(bizSmokeMachineIncomingGoodsList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizSmokeMachineIncomingGoodsListDtoMapper.toDto(bizSmokeMachineIncomingGoodsList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizSmokeMachineIncomingGoodsListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsListDto update(BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsList bizSmokeMachineIncomingGoodsList = bizSmokeMachineIncomingGoodsListMapper.selectByPrimaryKey(bizSmokeMachineIncomingGoodsListParam.getId());
        bizSmokeMachineIncomingGoodsListDtoMapper.updatePo(bizSmokeMachineIncomingGoodsListParam, bizSmokeMachineIncomingGoodsList);
        bizSmokeMachineIncomingGoodsList.setUpdateBy(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsList.setUpdateTime(new Date());

        // 更新数据
        int update = bizSmokeMachineIncomingGoodsListMapper.updateByPrimaryKey(bizSmokeMachineIncomingGoodsList);
        return update > 0 ? bizSmokeMachineIncomingGoodsListDtoMapper.toDto(bizSmokeMachineIncomingGoodsList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizSmokeMachineIncomingGoodsListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizSmokeMachineIncomingGoodsListDto> selectAll(BizSmokeMachineIncomingGoodsListParam exportParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsList bizSmokeMachineIncomingGoodsList = bizSmokeMachineIncomingGoodsListDtoMapper.toPo(exportParam);
        bizSmokeMachineIncomingGoodsList.setTradeCode(userInfo.getCompany());
        List<BizSmokeMachineIncomingGoodsListDto> bizSmokeMachineIncomingGoodsListDtos = new ArrayList<>();
        List<BizSmokeMachineIncomingGoodsList> bizSmokeMachineIncomingGoodsListLists = bizSmokeMachineIncomingGoodsListMapper.getList(bizSmokeMachineIncomingGoodsList);
        if (CollectionUtils.isNotEmpty(bizSmokeMachineIncomingGoodsListLists)) {
           bizSmokeMachineIncomingGoodsListDtos = bizSmokeMachineIncomingGoodsListLists.stream().map(head -> {
                    BizSmokeMachineIncomingGoodsListDto dto =  bizSmokeMachineIncomingGoodsListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizSmokeMachineIncomingGoodsListDtos;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject updateQuantity(BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsList po = bizSmokeMachineIncomingGoodsListDtoMapper.toPo(bizSmokeMachineIncomingGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());

        // 修改数量-校验单价是否为空
        if (po.getQuantity() == null) {
            throw new ErrorException(400, "数量不能为空");
        }
        // if (po.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
        //    throw new ErrorException(400, "数量不能为0");
        // }
        if (po.getUnitPrice() == null) {
            throw new ErrorException(400, "修改数量时，单价不能为空");
        }
        bizSmokeCommonService.computedUsdMoney(po,userInfo);

        int update = bizSmokeMachineIncomingGoodsListMapper.updateByPrimaryKey(po);


        // 更新表头总金额
        bizSmokeMachineIncomingGoodsHeadMapper.updateAccountById(po.getHeadId());
        if (update > 0) {
            return ResultObject.createInstance(true,"修改成功",po);
        }else{
            return ResultObject.createInstance(false, "修改失败");
        }
    }



    @Transactional(rollbackFor = Exception.class)
    public ResultObject updateCommonFiled(@Valid BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsList po = bizSmokeMachineIncomingGoodsListDtoMapper.toPo(bizSmokeMachineIncomingGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = bizSmokeMachineIncomingGoodsListMapper.updateByPrimaryKey(po);
        if (i > 0) {
            return ResultObject.createInstance(true,"修改成功",po);
        }else {
            return ResultObject.createInstance(false, "修改失败");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject updateUnitPrice(@Valid BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsList po = bizSmokeMachineIncomingGoodsListDtoMapper.toPo(bizSmokeMachineIncomingGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());


        // 单价不能为空
        if (po.getUnitPrice() == null) {
            throw new ErrorException(400, "单价不能为空");
        }

        // 校验数量是否为空
        if (po.getQuantity() == null) {
            throw new ErrorException(400, "数量不能为空");
        }
        bizSmokeCommonService.computedUsdMoney(po,userInfo);

        int update = bizSmokeMachineIncomingGoodsListMapper.updateByPrimaryKey(po);

        // 更新表头总金额
        bizSmokeMachineIncomingGoodsHeadMapper.updateAccountById(po.getHeadId());

        if (update > 0) {
            return ResultObject.createInstance(true,"修改成功",po);
        }else{
            return ResultObject.createInstance(false, "修改失败");
        }
    }




    public ResultObject getBizSmokeMachineIncomingGoodsListById(String id, BizSmokeMachineIncomingGoodsListParam bizSmokeMachineIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizSmokeMachineIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        if (StringUtils.isBlank(id)) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        BizSmokeMachineIncomingGoodsList po = bizSmokeMachineIncomingGoodsListMapper.selectByPrimaryKey(id);
        if (po == null) {
            throw new ErrorException(400, XdoI18nUtil.t("数据不存在"));
        }
        BizSmokeMachineIncomingGoodsListDto dto = bizSmokeMachineIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }
}