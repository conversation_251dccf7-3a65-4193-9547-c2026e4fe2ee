package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizExportGoodsSellListMapper;
import com.dcjet.cs.dec.mapper.BizExportGoodsSellListDtoMapper;
import com.dcjet.cs.dec.model.BizExportGoodsSellList;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadParam;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListDto;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListParam;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListTotal;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizExportGoodsSellList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 14:45:02
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizExportGoodsSellListService extends BaseService<BizExportGoodsSellList> {

    private static final Logger log = LoggerFactory.getLogger(BizExportGoodsSellListService.class);

    @Resource
    private BizExportGoodsSellListMapper bizExportGoodsSellListMapper;

    @Resource
    private BizExportGoodsSellListDtoMapper bizExportGoodsSellListDtoMapper;

    @Override
    public Mapper<BizExportGoodsSellList> getMapper() {
        return bizExportGoodsSellListMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizExportGoodsSellListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizExportGoodsSellListDto>> getListPaged(BizExportGoodsSellListParam bizExportGoodsSellListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizExportGoodsSellList bizExportGoodsSellList = bizExportGoodsSellListDtoMapper.toPo(bizExportGoodsSellListParam);
        bizExportGoodsSellList.setTradeCode(userInfo.getCompany());
        Page<BizExportGoodsSellList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizExportGoodsSellListMapper.getList( bizExportGoodsSellList));
        // 将PO转为DTO返回给前端
        List<BizExportGoodsSellListDto> bizExportGoodsSellListDtoList = page.getResult().stream()
            .map(bizExportGoodsSellListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizExportGoodsSellListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizExportGoodsSellListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsSellListDto insert(BizExportGoodsSellListParam bizExportGoodsSellListParam, UserInfoToken userInfo) {
        BizExportGoodsSellList bizExportGoodsSellList = bizExportGoodsSellListDtoMapper.toPo(bizExportGoodsSellListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizExportGoodsSellList.setId(sid);
        bizExportGoodsSellList.setCreateBy(userInfo.getUserNo());
        bizExportGoodsSellList.setInsertUserName(userInfo.getUserName());
        bizExportGoodsSellList.setCreateTime(new Date());
        bizExportGoodsSellList.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizExportGoodsSellListMapper.insert(bizExportGoodsSellList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizExportGoodsSellListDtoMapper.toDto(bizExportGoodsSellList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizExportGoodsSellListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsSellListDto update(BizExportGoodsSellListParam bizExportGoodsSellListParam, UserInfoToken userInfo) {
        BizExportGoodsSellList bizExportGoodsSellList = bizExportGoodsSellListMapper.selectByPrimaryKey(bizExportGoodsSellListParam.getId());
        bizExportGoodsSellListDtoMapper.updatePo(bizExportGoodsSellListParam, bizExportGoodsSellList);
        bizExportGoodsSellList.setUpdateBy(userInfo.getUserNo());
        bizExportGoodsSellList.setUpdateUserName(userInfo.getUserName());
        bizExportGoodsSellList.setUpdateTime(new Date());

        // 更新数据
        int update = bizExportGoodsSellListMapper.updateByPrimaryKey(bizExportGoodsSellList);
        return update > 0 ? bizExportGoodsSellListDtoMapper.toDto(bizExportGoodsSellList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizExportGoodsSellListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizExportGoodsSellListDto> selectAll(BizExportGoodsSellListParam exportParam, UserInfoToken userInfo) {
        BizExportGoodsSellList bizExportGoodsSellList = bizExportGoodsSellListDtoMapper.toPo(exportParam);
        bizExportGoodsSellList.setTradeCode(userInfo.getCompany());
        List<BizExportGoodsSellListDto> bizExportGoodsSellListDtos = new ArrayList<>();
        List<BizExportGoodsSellList> bizExportGoodsSellListLists = bizExportGoodsSellListMapper.getList(bizExportGoodsSellList);
        if (CollectionUtils.isNotEmpty(bizExportGoodsSellListLists)) {
           bizExportGoodsSellListDtos = bizExportGoodsSellListLists.stream().map(head -> {
                    BizExportGoodsSellListDto dto =  bizExportGoodsSellListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizExportGoodsSellListDtos;
    }

    public ResultObject<BizExportGoodsSellListTotal> getListTotal(BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功");
        String parentId = param.getParentId();
        BizExportGoodsSellListTotal total = new BizExportGoodsSellListTotal();
        if (StringUtils.isBlank(parentId)) {
            total.setAmountTotal(BigDecimal.ZERO);
            total.setQtyTotal(BigDecimal.ZERO);
            total.setNetWeightTotal(BigDecimal.ZERO);
            total.setGrossWeightTotal(BigDecimal.ZERO);
            resultObject.setData(total);
            return resultObject;
        }
        total = bizExportGoodsSellListMapper.getListTotal(parentId);
        resultObject.setData(total);
        return resultObject;
    }
}