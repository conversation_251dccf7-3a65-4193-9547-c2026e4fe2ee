package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListMapper;
import com.dcjet.cs.dec.mapper.BizBpAnalyseOrderListDtoMapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderList;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizBpAnalyseOrderList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 17:08:36
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizBpAnalyseOrderListService extends BaseService<BizBpAnalyseOrderList> {

    private static final Logger log = LoggerFactory.getLogger(BizBpAnalyseOrderListService.class);

    @Resource
    private BizBpAnalyseOrderListMapper bizBpAnalyseOrderListMapper;

    @Resource
    private BizBpAnalyseOrderListDtoMapper bizBpAnalyseOrderListDtoMapper;

    @Override
    public Mapper<BizBpAnalyseOrderList> getMapper() {
        return bizBpAnalyseOrderListMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizBpAnalyseOrderListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizBpAnalyseOrderListDto>> getListPaged(BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizBpAnalyseOrderList bizBpAnalyseOrderList = bizBpAnalyseOrderListDtoMapper.toPo(bizBpAnalyseOrderListParam);
        bizBpAnalyseOrderList.setTradeCode(userInfo.getCompany());
        Page<BizBpAnalyseOrderList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizBpAnalyseOrderListMapper.getList( bizBpAnalyseOrderList));
        // 将PO转为DTO返回给前端
        List<BizBpAnalyseOrderListDto> bizBpAnalyseOrderListDtoList = page.getResult().stream()
            .map(bizBpAnalyseOrderListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizBpAnalyseOrderListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizBpAnalyseOrderListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderListDto insert(BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderList bizBpAnalyseOrderList = bizBpAnalyseOrderListDtoMapper.toPo(bizBpAnalyseOrderListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizBpAnalyseOrderList.setId(sid);
        bizBpAnalyseOrderList.setCreateBy(userInfo.getUserNo());
        bizBpAnalyseOrderList.setInsertUserName(userInfo.getUserName());
        bizBpAnalyseOrderList.setCreateTime(new Date());
        bizBpAnalyseOrderList.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizBpAnalyseOrderListMapper.insert(bizBpAnalyseOrderList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizBpAnalyseOrderListDtoMapper.toDto(bizBpAnalyseOrderList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizBpAnalyseOrderListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderListDto update(BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderList bizBpAnalyseOrderList = bizBpAnalyseOrderListMapper.selectByPrimaryKey(bizBpAnalyseOrderListParam.getId());
        bizBpAnalyseOrderListDtoMapper.updatePo(bizBpAnalyseOrderListParam, bizBpAnalyseOrderList);
        bizBpAnalyseOrderList.setUpdateBy(userInfo.getUserNo());
        bizBpAnalyseOrderList.setUpdateUserName(userInfo.getUserName());
        bizBpAnalyseOrderList.setUpdateTime(new Date());

        // 更新数据
        int update = bizBpAnalyseOrderListMapper.updateByPrimaryKey(bizBpAnalyseOrderList);
        return update > 0 ? bizBpAnalyseOrderListDtoMapper.toDto(bizBpAnalyseOrderList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizBpAnalyseOrderListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizBpAnalyseOrderListDto> selectAll(BizBpAnalyseOrderListParam exportParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderList bizBpAnalyseOrderList = bizBpAnalyseOrderListDtoMapper.toPo(exportParam);
        bizBpAnalyseOrderList.setTradeCode(userInfo.getCompany());
        List<BizBpAnalyseOrderListDto> bizBpAnalyseOrderListDtos = new ArrayList<>();
        List<BizBpAnalyseOrderList> bizBpAnalyseOrderListLists = bizBpAnalyseOrderListMapper.getList(bizBpAnalyseOrderList);
        if (CollectionUtils.isNotEmpty(bizBpAnalyseOrderListLists)) {
           bizBpAnalyseOrderListDtos = bizBpAnalyseOrderListLists.stream().map(head -> {
                    BizBpAnalyseOrderListDto dto =  bizBpAnalyseOrderListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizBpAnalyseOrderListDtos;
    }

    public ResultObject<BizBpAnalyseOrderListDto> getListById(BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, UserInfoToken userInfo) {
        ResultObject<BizBpAnalyseOrderListDto> resultObject = ResultObject.createInstance(true, "获取成功");
        BizBpAnalyseOrderList BizBpAnalyseOrderList = bizBpAnalyseOrderListMapper.selectByPrimaryKey(bizBpAnalyseOrderListParam.getId());
        if (BizBpAnalyseOrderList != null) {
            BizBpAnalyseOrderListDto dto = bizBpAnalyseOrderListDtoMapper.toDto(BizBpAnalyseOrderList);
            resultObject.setData(dto);
        }
        return resultObject;
    }
}