package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizIncomingGoodsListMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsListDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import com.dcjet.cs.dec.model.InComingListSumTotal;
import com.dcjet.cs.dto.dec.BizBatchUpdateInvoiceNoParams;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListParam;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIncomingGoodsList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-05-23 13:32:21
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIncomingGoodsListService extends BaseService<BizIncomingGoodsList> {

    private static final Logger log = LoggerFactory.getLogger(BizIncomingGoodsListService.class);

    @Resource
    private BizIncomingGoodsListMapper bizIncomingGoodsListMapper;

    @Resource
    private BizIncomingGoodsListDtoMapper bizIncomingGoodsListDtoMapper;

    @Override
    public Mapper<BizIncomingGoodsList> getMapper() {
        return bizIncomingGoodsListMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizIncomingGoodsListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIncomingGoodsListDto>> getListPaged(BizIncomingGoodsListParam bizIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        bizIncomingGoodsList.setTradeCode(userInfo.getCompany());
        Page<BizIncomingGoodsList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIncomingGoodsListMapper.getList( bizIncomingGoodsList));
        // 将PO转为DTO返回给前端
        List<BizIncomingGoodsListDto> bizIncomingGoodsListDtoList = page.getResult().stream()
            .map(bizIncomingGoodsListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIncomingGoodsListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIncomingGoodsListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsListDto insert(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIncomingGoodsList.setId(sid);
        bizIncomingGoodsList.setCreateBy(userInfo.getUserNo());
        bizIncomingGoodsList.setCreateTime(new Date());
        bizIncomingGoodsList.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizIncomingGoodsListMapper.insert(bizIncomingGoodsList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIncomingGoodsListDtoMapper.toDto(bizIncomingGoodsList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIncomingGoodsListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsListDto update(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListMapper.selectByPrimaryKey(bizIncomingGoodsListParam.getId());
        bizIncomingGoodsListDtoMapper.updatePo(bizIncomingGoodsListParam, bizIncomingGoodsList);
        bizIncomingGoodsList.setUpdateBy(userInfo.getUserNo());
        bizIncomingGoodsList.setUpdateTime(new Date());

        // 更新数据
        int update = bizIncomingGoodsListMapper.updateByPrimaryKey(bizIncomingGoodsList);
        return update > 0 ? bizIncomingGoodsListDtoMapper.toDto(bizIncomingGoodsList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizIncomingGoodsListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIncomingGoodsListDto> selectAll(BizIncomingGoodsListParam exportParam, UserInfoToken userInfo) {
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListDtoMapper.toPo(exportParam);
        bizIncomingGoodsList.setTradeCode(userInfo.getCompany());
        List<BizIncomingGoodsListDto> bizIncomingGoodsListDtos = new ArrayList<>();
        List<BizIncomingGoodsList> bizIncomingGoodsListLists = bizIncomingGoodsListMapper.getList(bizIncomingGoodsList);
        if (CollectionUtils.isNotEmpty(bizIncomingGoodsListLists)) {
           bizIncomingGoodsListDtos = bizIncomingGoodsListLists.stream().map(head -> {
                    BizIncomingGoodsListDto dto =  bizIncomingGoodsListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIncomingGoodsListDtos;
    }

    public ResultObject<List<BizIncomingGoodsListDto>> getListSumByInvoice(BizIncomingGoodsListParam bizIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsListDto>> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizIncomingGoodsList po = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        if (StringUtils.isBlank(po.getHeadId())){
            throw new ErrorException(400, XdoI18nUtil.t("表头ID不能为空"));
        }


        String headId = po.getHeadId();
        String tradeCode = userInfo.getCompany();
        List<BizIncomingGoodsList> list = bizIncomingGoodsListMapper.getListSumByInvoice(headId,tradeCode);

        List<BizIncomingGoodsListDto> listDto = list.stream().map(head -> {
            BizIncomingGoodsListDto dto =  bizIncomingGoodsListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());

        resultObject.setData(listDto);

        return resultObject;
    }



    @Transactional(rollbackFor = Exception.class)
    public ResultObject batchUpdateInvoiceNo(BizBatchUpdateInvoiceNoParams bizIncomingGoodsListParam, UserInfoToken userInfo) {
        try {
            List<String> idList = bizIncomingGoodsListParam.getIds();
            String invoiceNo = bizIncomingGoodsListParam.getInvoiceNo();
            // 批量更新发票号
            if(CollectionUtils.isNotEmpty(idList)){
                bizIncomingGoodsListMapper.batchUpdateInvoiceNo(idList,invoiceNo,userInfo.getUserNo(),userInfo.getUserName());
            }
            return ResultObject.createInstance(true,"批量更新发票号成功！");
        }catch (Exception e){
            e.printStackTrace();
            throw new ErrorException(400, XdoI18nUtil.t("批量更新发票号失败"));
        }


    }

    public ResultObject<BizIncomingGoodsListDto> getIncomingGoodsListBySid(String id, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        if (StringUtils.isBlank(id)) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        BizIncomingGoodsList po = bizIncomingGoodsListMapper.selectByPrimaryKey(id);
        if (po == null) {
            throw new ErrorException(400, XdoI18nUtil.t("数据不存在"));
        }
        BizIncomingGoodsListDto dto = bizIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizIncomingGoodsListDto> updateInQuality(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(bizIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        if (bizIncomingGoodsListParam.getInQuantity() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("进口数量不能为空"));
        }else {
            if (bizIncomingGoodsListParam.getInQuantity().compareTo(BigDecimal.ZERO) == 0) {
                throw new ErrorException(400, XdoI18nUtil.t("进口数量不能为0"));
            }
        }

        // 系统计算=进口数量*单价
        // 允许修改，修改保存时，根据进口数量重新计算金额
        BizIncomingGoodsList po = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        // 重新计算金额
        BigDecimal systemAmount = po.getInQuantity().multiply(po.getUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
        po.setAmount(systemAmount);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = bizIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizIncomingGoodsListDto dto = bizIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizIncomingGoodsListDto> updateQuantity(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {

        ResultObject<BizIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(bizIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        if (bizIncomingGoodsListParam.getQuantity() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("数量不能为空"));
        }else {
            if (bizIncomingGoodsListParam.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                throw new ErrorException(400, XdoI18nUtil.t("数量不能为0"));
            }
        }
        // 系统计算=进口数量*单价
        // 允许修改，修改保存时，根据进口数量重新计算金额
        BizIncomingGoodsList po = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = bizIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizIncomingGoodsListDto dto = bizIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizIncomingGoodsListDto> updateAmount(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {

        ResultObject<BizIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(bizIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }

        if (bizIncomingGoodsListParam.getAmount() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("金额不能为空"));
        }else {
            if (bizIncomingGoodsListParam.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                throw new ErrorException(400, XdoI18nUtil.t("金额不能为0"));
            }
        }


        BizIncomingGoodsList po = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        // 重新计算金额时单价不能为空
        if (po.getUnitPrice() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("单价不能为空"));
        }else {
            if (po.getUnitPrice().compareTo(BigDecimal.ZERO) == 0) {
                throw new ErrorException(400, XdoI18nUtil.t("单价不能为0"));
            }
        }
        // 根据金额重新计算数量 保留六位小数
        BigDecimal quantity = po.getAmount().divide(po.getUnitPrice(), 6, BigDecimal.ROUND_HALF_UP);
        po.setInQuantity(quantity);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = bizIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizIncomingGoodsListDto dto = bizIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizIncomingGoodsListDto> updateInvoiceNo(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(bizIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        // 修改进口发票号
        if (StringUtils.isBlank(bizIncomingGoodsListParam.getInvoiceNo())){
            throw new ErrorException(400, XdoI18nUtil.t("进口发票号不能为空"));
        }
        BizIncomingGoodsList po = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = bizIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizIncomingGoodsListDto dto = bizIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject getSumTotalByHeadId(BizIncomingGoodsListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        String headId = param.getHeadId();
        if (StringUtils.isBlank(headId)) {
            return resultObject;
        }
        InComingListSumTotal total = bizIncomingGoodsListMapper.getSumTotalByHeadId(param.getHeadId());
        resultObject.setData(total);
        return resultObject;
    }
}