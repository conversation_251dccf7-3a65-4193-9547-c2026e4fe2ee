<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsListMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
        <result column="product_model" property="productModel" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="NUMERIC"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="unit_price" property="unitPrice" jdbcType="NUMERIC"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="delivery_date" property="deliveryDate" jdbcType="TIMESTAMP"/>
        <result column="total_usd" property="totalUsd" jdbcType="NUMERIC"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="in_quantity" property="inQuantity" jdbcType="NUMERIC"/>
        <result column="in_unit" property="inUnit" jdbcType="VARCHAR"/>
        <result column="curr" property="curr" jdbcType="VARCHAR"/>
        <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="contract_list_id" property="contractListId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.goods_name, 
            t.note, 
            t.product_model, 
            t.quantity, 
            t.unit, 
            t.unit_price, 
            t.amount, 
            t.delivery_date, 
            t.total_usd, 
            t.remarks, 
            t.head_id, 
            t.in_quantity, 
            t.in_unit, 
            t.curr, 
            t.invoice_no, 
            t.contract_list_id
    </sql>

    <sql id="condition">
        <if test="headId != null and headId  != ''">
            AND t.head_id LIKE '%' || #{headId} || '%'
        </if>
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_smoke_machine_incoming_goods_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_smoke_machine_incoming_goods_list t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectByHeadId" resultMap="BaseResultMap">
        select * from t_biz_smoke_machine_incoming_goods_list where head_id = #{headId}
    </select>
</mapper>