<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizBpAnalyseOrderListBoxBoxMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="container_spec" property="containerSpec" jdbcType="VARCHAR"/>
        <result column="container_count" property="containerCount" jdbcType="NUMERIC"/>
        <result column="container_no" property="containerNo" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="box_count" property="boxCount" jdbcType="NUMERIC"/>
        <result column="remaining_box_count" property="remainingBoxCount" jdbcType="NUMERIC"/>
        <result column="serial_no" property="serialNo" jdbcType="NUMERIC"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.container_spec, 
            t.container_count, 
            t.container_no, 
            t.product_name, 
            t.box_count, 
            t.remaining_box_count, 
            t.serial_no, 
            t.note
    </sql>

    <sql id="condition">
        <if test="parentId != null and parentId  != ''">
            AND t.parent_id  = #{parentId}
        </if>
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_bp_analyse_order_list_box_box t
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_bp_analyse_order_list_box_box t where t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>