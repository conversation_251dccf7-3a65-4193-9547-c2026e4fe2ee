<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizSmokeCommonMapper">

    <select id="getListCommonKeyValueList" resultType="java.util.Map">
        select
            distinct
            PARAMS_CODE as "value",
            PARAMS_NAME as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where
            PARAMS_TYPE = #{type}  and TRADE_CODE = #{tradeCode};
    </select>


    <select id="getCurrentMonthRate" resultType="com.dcjet.cs.params.model.EnterpriseRate">
        SELECT
            SID,
            TRADE_CODE,
            INSERT_USER,
            INSERT_TIME,
            INSERT_USER_NAME,
            UPDATE_USER,
            UPDATE_TIME,
            UPDATE_USER_NAME,
            PARAM_CODE,
            CURR,
            RATE,
            NOTE,
            EXTEND1,
            EXTEND2,
            EXTEND3,
            EXTEND4,
            EXTEND5,
            EXTEND6,
            EXTEND7,
            EXTEND8,
            EXTEND9,
            EXTEND10,
            MONTH,
            USD_RATE
        FROM T_BIZ_ENTERPRISE_RATE
        WHERE
            MONTH = #{month} AND TRADE_CODE = #{tradeCode} AND CURR = #{curr}
            LIMIT 1;
    </select>


    <select id="getListCommonCustomerKeyValueList" resultType="java.util.Map">
        select
            distinct
            MERCHANT_CODE as "value",
            MERCHANT_NAME_CN as "label"
        from T_BIZ_MERCHANT
        where TRADE_CODE =  #{tradeCode}
    </select>



    <select id="getListCommonCustomerCurrKeyValueList" resultType="java.util.Map">
        select
            distinct
            CUSTOM_PARAM_CODE as "value",
            PARAMS_NAME as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where
            PARAMS_TYPE = #{type}  and TRADE_CODE = #{tradeCode};
    </select>


    <select id="getListCommonCountryKeyValueList" resultType="java.util.Map">
        select
            distinct
            CUSTOM_PARAM_CODE as "value",
            CUSTOM_PARAM_NAME as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where
            PARAMS_TYPE = #{type}  and TRADE_CODE = #{tradeCode};
    </select>


    <select id="getInsuranceRateByType" resultType="java.math.BigDecimal">
        select INSURANCE_RATE from T_BIZ_TRANSCODE where BIZ_TYPE = #{type} and TRADE_CODE = #{tradeCode} order by CREATE_TIME desc limit 1;
    </select>

    <select id="getCustomerNameByCode" resultType="java.lang.String">
        select COALESCE(MERCHANT_NAME_CN,'')   from T_BIZ_MERCHANT where MERCHANT_CODE = #{customerCode}  and TRADE_CODE = #{tradeCode} limit 1;
    </select>

    <select id="getPriceNameByCode" resultType="java.lang.String">
        select COALESCE(PRICE_TERM,'') from T_BIZ_PRICE_TERMS where PARAM_CODE = #{priceCode} and TRADE_CODE = #{tradeCode} limit 1;
    </select>

    <select id="getTotalByHeadId" resultType="java.math.BigDecimal">
        select COALESCE(sum(MONEY_AMOUNT),0) from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST where HEAD_ID  = #{headId};
    </select>

    <select id="getCurrList" resultType="java.util.Map">
        select
            distinct CUSTOM_PARAM_CODE as "value",
                     PARAMS_NAME as "label"
        from
            T_BIZ_CUSTOMS_PARAMS
        where
            PARAMS_TYPE = 'CURR' and TRADE_CODE = #{tradeCode};
    </select>
    <select id="getListCommonInsuranceCategoryKeyValueList" resultType="java.util.Map">
        select
            distinct PARAM_CODE as "value",
                     INSURANCE_TYPE_CN as "label"
        from
            T_BIZ_INSURANCE_TYPE
        where
             TRADE_CODE = #{tradeCode};
    </select>
    <select id="getCustomerListByCodes" resultType="java.util.Map">
        select
            distinct MERCHANT_CODE as "value",
                     MERCHANT_NAME_CN as "label"
        from
            T_BIZ_MERCHANT
        where
            TRADE_CODE = #{tradeCode} and MERCHANT_CODE in
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
</mapper>