package com.dcjet.cs.dec.service;



import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListBoxBoxMapper;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListBoxMapper;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListMapper;
import com.dcjet.cs.dec.mapper.BizBpAnalyseOrderListBoxDtoMapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderHead;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderList;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBox;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxAddParam;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxParam;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizBpAnalyseOrderListBox业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 17:08:52
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizBpAnalyseOrderListBoxService extends BaseService<BizBpAnalyseOrderListBox> {

    private static final Logger log = LoggerFactory.getLogger(BizBpAnalyseOrderListBoxService.class);

    @Resource
    private BizBpAnalyseOrderListBoxMapper bizBpAnalyseOrderListBoxMapper;

    @Resource
    private BizBpAnalyseOrderListBoxDtoMapper bizBpAnalyseOrderListBoxDtoMapper;

    @Resource
    private BizBpAnalyseOrderListMapper bizBpAnalyseOrderListMapper;

    @Override
    public Mapper<BizBpAnalyseOrderListBox> getMapper() {
        return bizBpAnalyseOrderListBoxMapper;
    }


    @Resource
    private BizBpAnalyseOrderListBoxBoxMapper boxBoxMapper;

    @Resource
    private BizBpAnalyseOrderHeadMapper bizBpAnalyseOrderHeadMapper;


    /**
     * 获取分页信息
     *
     * @param bizBpAnalyseOrderListBoxParam 查询参数
     * @param pageParam                     分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizBpAnalyseOrderListBoxDto>> getListPaged(BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox = bizBpAnalyseOrderListBoxDtoMapper.toPo(bizBpAnalyseOrderListBoxParam);
        bizBpAnalyseOrderListBox.setTradeCode(userInfo.getCompany());
        Page<BizBpAnalyseOrderListBox> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizBpAnalyseOrderListBoxMapper.getList(bizBpAnalyseOrderListBox));
        // 将PO转为DTO返回给前端
        List<BizBpAnalyseOrderListBoxDto> bizBpAnalyseOrderListBoxDtoList = page.getResult().stream()
                .map(bizBpAnalyseOrderListBoxDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(bizBpAnalyseOrderListBoxDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizBpAnalyseOrderListBoxParam 插入参数
     * @param userInfo                      用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderListBoxDto insert(BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox = bizBpAnalyseOrderListBoxDtoMapper.toPo(bizBpAnalyseOrderListBoxParam);

        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizBpAnalyseOrderListBox.setId(sid);
        bizBpAnalyseOrderListBox.setCreateBy(userInfo.getUserNo());
        bizBpAnalyseOrderListBox.setInsertUserName(userInfo.getUserName());
        bizBpAnalyseOrderListBox.setCreateTime(new Date());
        bizBpAnalyseOrderListBox.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizBpAnalyseOrderListBoxMapper.insert(bizBpAnalyseOrderListBox);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizBpAnalyseOrderListBoxDtoMapper.toDto(bizBpAnalyseOrderListBox) : null;
    }

    /**
     * 修改记录
     *
     * @param bizBpAnalyseOrderListBoxParam 更新参数
     * @param userInfo                      用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderListBoxDto update(BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox = bizBpAnalyseOrderListBoxMapper.selectByPrimaryKey(bizBpAnalyseOrderListBoxParam.getId());
        bizBpAnalyseOrderListBoxDtoMapper.updatePo(bizBpAnalyseOrderListBoxParam, bizBpAnalyseOrderListBox);
        bizBpAnalyseOrderListBox.setUpdateBy(userInfo.getUserNo());
        bizBpAnalyseOrderListBox.setUpdateUserName(userInfo.getUserName());
        bizBpAnalyseOrderListBox.setUpdateTime(new Date());

        // 更新数据
        int update = bizBpAnalyseOrderListBoxMapper.updateByPrimaryKey(bizBpAnalyseOrderListBox);
        return update > 0 ? bizBpAnalyseOrderListBoxDtoMapper.toDto(bizBpAnalyseOrderListBox) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param headId   要删除的表头的ID
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, String parentId, UserInfoToken userInfo) {
        for (String sid : sids) {
            BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox = bizBpAnalyseOrderListBoxMapper.selectByPrimaryKey(sid);
            // 更新其他数据的剩余箱数
            bizBpAnalyseOrderListBoxMapper.deleteUpdateRemainingBoxCount(bizBpAnalyseOrderListBox);
        }
        bizBpAnalyseOrderListBoxMapper.deleteBySids(sids);
        // 获取当前表头的数据 对当前表体的序号进行重新排序
        List<BizBpAnalyseOrderListBox> list = bizBpAnalyseOrderListBoxMapper.getListByHeadId(parentId);
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox = list.get(i);
                bizBpAnalyseOrderListBox.setSerialNo(BigDecimal.valueOf(i + 1));
                bizBpAnalyseOrderListBox.setUpdateBy(userInfo.getUserNo());
                bizBpAnalyseOrderListBox.setUpdateUserName(userInfo.getUserName());
                bizBpAnalyseOrderListBox.setUpdateTime(new Date());
                bizBpAnalyseOrderListBoxMapper.updateByPrimaryKey(bizBpAnalyseOrderListBox);
            }
        }

        // 删除对应子表的子表
        bizBpAnalyseOrderListBoxMapper.deleteByParentIds(sids);

    }


    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizBpAnalyseOrderListBoxDto> selectAll(BizBpAnalyseOrderListBoxParam exportParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox = bizBpAnalyseOrderListBoxDtoMapper.toPo(exportParam);
        bizBpAnalyseOrderListBox.setTradeCode(userInfo.getCompany());
        List<BizBpAnalyseOrderListBoxDto> bizBpAnalyseOrderListBoxDtos = new ArrayList<>();
        List<BizBpAnalyseOrderListBox> bizBpAnalyseOrderListBoxLists = bizBpAnalyseOrderListBoxMapper.getList(bizBpAnalyseOrderListBox);
        if (CollectionUtils.isNotEmpty(bizBpAnalyseOrderListBoxLists)) {
            bizBpAnalyseOrderListBoxDtos = bizBpAnalyseOrderListBoxLists.stream().map(head -> {
                BizBpAnalyseOrderListBoxDto dto = bizBpAnalyseOrderListBoxDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizBpAnalyseOrderListBoxDtos;
    }


    public ResultObject<BigDecimal> getBoxCountByProductName(BizBpAnalyseOrderListBoxParam param, UserInfoToken userInfo) {
        ResultObject<BigDecimal> resultObject = ResultObject.createInstance(true, "获取成功");
        BizBpAnalyseOrderListBox po = bizBpAnalyseOrderListBoxDtoMapper.toPo(param);
        BigDecimal boxCount = bizBpAnalyseOrderListMapper.getBoxCountByProductName(po);
        log.info("原始单据对应的箱数：{}", boxCount);
        // 获取集装箱列表已经使用的数量
        // BigDecimal usedBoxCount = Optional.of(bizBpAnalyseOrderListBoxMapper.getUsedBoxCountByProductName(po)).orElse(BigDecimal.ZERO);
        BigDecimal usedBoxCount = Optional.of(bizBpAnalyseOrderListBoxMapper.getUsedBoxCountByProductName(po)).orElse(BigDecimal.ZERO);
        // 获取装箱列表已经使用集装箱
        BigDecimal usedContainerCount = Optional.of(bizBpAnalyseOrderListBoxMapper.getUsedContainerCountByProductName(po)).orElse(BigDecimal.ZERO);
        log.info("{}", po);
        log.info("{},装箱列表已经使用数量：{}", po.getProductName(), usedBoxCount);
        // 计算目前可以使用箱数
        if (boxCount != null) {
            boxCount = boxCount.subtract(usedBoxCount);
        }
        resultObject.setData(boxCount);
        return resultObject;
    }

    public ResultObject<List<Map<String, String>>> getProductNameListByHeadId(BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, UserInfoToken userInfo) {
        ResultObject<List<Map<String, String>>> resultObject = ResultObject.createInstance(true, "获取成功");
        BizBpAnalyseOrderListBox po = bizBpAnalyseOrderListBoxDtoMapper.toPo(bizBpAnalyseOrderListBoxParam);
        po.setTradeCode(userInfo.getCompany());
        // 根据表头ID获取分析单商品名称的去重汇总
        List<Map<String, String>> list = bizBpAnalyseOrderListMapper.getProductNameListByHeadId(po);
        // 遍历map 判断对应商品名称的剩余箱数是否 小于等于0 如果等于 从map中删除
        // 使用迭代器遍历，删除集合元素
        Iterator<Map<String, String>> iterator = list.iterator();
        while (iterator.hasNext()) {
            Map<String, String> map = iterator.next();
            String productName = map.get("value");
            int count = bizBpAnalyseOrderListBoxMapper.checkRemainingBoxCountByProductName(po.getParentId(), productName);
            if (count > 0) {
                // 删除集合元素
                iterator.remove();
            }
        }
        resultObject.setData(list);
        return resultObject;
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject insertContainerList(@Valid BizBpAnalyseOrderListBoxAddParam param, UserInfoToken userInfo) {

        List<BizBpAnalyseOrderListBox> bixList = new ArrayList<>();
        String parentId = param.getParentId();
        // 集装箱号
        String containerNo = param.getContainerNo();
        // 集装箱规格
        String containerSpec = param.getContainerSpec();
        // 集装箱数
        BigDecimal containerCount = param.getContainerCount();
        // 获取分析单表体数据
        // List<BizBpAnalyseOrderList> list = bizBpAnalyseOrderListMapper.getListByParentId(parentId);
        // 组装分析单表体数据 （商品名称 + 产品型号） 作为key  备注作为value
        // Map<String, String> map = list.stream().collect(Collectors.toMap(BizBpAnalyseOrderList::getProductNameAndGModel, BizBpAnalyseOrderList::getRemark));
        // 根究商品名称获取分析单表体备注汇总
        List<BizBpAnalyseOrderListBoxParam> list = param.getList();
        List<BizBpAnalyseOrderListBoxParam> boxList = param.getList();
        for (int i = 0; i < list.size(); i++) {
            BizBpAnalyseOrderListBoxParam boxParam = list.get(i);
            String productName = boxParam.getProductName();
            String note = bizBpAnalyseOrderListBoxMapper.getNoteByProductName(productName, parentId);
            boxParam.setNote(note);
        }
        // 获取当前集装箱列表的最大序号
        BigDecimal maxSerialNo = bizBpAnalyseOrderListBoxMapper.getMaxSerialNo(parentId);


        String boxItemId = UUID.randomUUID().toString();
        // 处理插入临时表id数据
        ArrayList<BizBpAnalyseOrderListBoxBox> insertBoxBoxList = new ArrayList<>();
        boxList.stream().forEach(item -> {
            BizBpAnalyseOrderListBoxBox boxPo = bizBpAnalyseOrderListBoxDtoMapper.toBoxPo(item);
            // 设置 集装箱号、集装箱规格、集装箱数
            boxPo.setContainerNo(containerNo);
            boxPo.setContainerSpec(containerSpec);
            boxPo.setContainerCount(containerCount);
            boxPo.setCreateBy(userInfo.getUserNo());
            boxPo.setInsertUserName(userInfo.getUserName());
            boxPo.setTradeCode(userInfo.getCompany());
            boxPo.setCreateTime(new Date());
            boxPo.setParentId(boxItemId);
            boxPo.setExtend1(parentId);
            insertBoxBoxList.add(boxPo);
        });


        // ===================================================== 第一种情况  【1个集装箱】【1个商品】 ===================================================
        // 对集装箱号按照,进行分隔

        List<String> containerNoList = Arrays.stream(containerNo.split(",")).collect(Collectors.toList());
        if (containerCount.compareTo(BigDecimal.ONE) == 0 && containerNoList.size() == 1) {
            //  从装箱信息界面带入。如果有多条商品，格式为：商品1*箱数+商品2*箱数
            // 按照商品名称分组，如果多个商品 拼接 商品名称 + 箱数
            Map<String, List<BizBpAnalyseOrderListBoxParam>> collect = list.stream().collect(Collectors.groupingBy(BizBpAnalyseOrderListBoxParam::getExtend1));
            // 循环遍历map
            for (Map.Entry<String, List<BizBpAnalyseOrderListBoxParam>> entry : collect.entrySet()) {
                // 获取商品名称
                String productName = "";
                // 获取商品数量
                BigDecimal productCount = BigDecimal.ZERO;
                // 获取商品箱数
                BigDecimal productBoxCount = BigDecimal.ZERO;
                // 剩余箱数
                BigDecimal remainingBoxCount = BigDecimal.ZERO;
                // 获取商品备注
                String productNote = "";
                // 获取商品列表
                List<BizBpAnalyseOrderListBoxParam> productList = entry.getValue();
                // 设置原始商品名称
                String beforeProductName = productList.get(0).getProductName();
                // 循环遍历商品列表
                for (int i = 0; i < productList.size(); i++) {
                    remainingBoxCount = productList.get(i).getRemainingBoxCount();
                    // 商品名称  =  商品名称 * 箱数
                    productName += productList.get(i).getProductName() + "\t*\t" + productList.get(i).getBoxCount() + " +";
                    // 获取商品数量
                    productCount = productCount.add(productList.get(i).getBoxCount());
                    // 获取商品箱数
                    productBoxCount = productBoxCount.add(productList.get(i).getBoxCount());
                    // 获取商品备注
                    productNote = productList.get(i).getNote();
                }
                // 去除最后一个 +
                productName = productName.substring(0, productName.length() - 1);
                // 插入数据
                BizBpAnalyseOrderListBox box = new BizBpAnalyseOrderListBox();
                box.setId(boxItemId);
                box.setCreateTime(new Date());
                box.setCreateBy(userInfo.getUserNo());
                box.setInsertUserName(userInfo.getUserName());
                box.setParentId(parentId);
                box.setTradeCode(userInfo.getCompany());

                box.setContainerNo(containerNo);
                box.setContainerSpec(containerSpec);
                box.setContainerCount(containerCount);
                box.setProductName(productName);
                box.setBoxCount(productBoxCount);
                box.setRemainingBoxCount(remainingBoxCount);
                box.setExtend1(beforeProductName);
                box.setNote(productNote);
                maxSerialNo = maxSerialNo.add(BigDecimal.ONE);
                box.setSerialNo(maxSerialNo);
                bixList.add(box);
            }
            // 插入数据
            if (CollectionUtils.isNotEmpty(bixList)) {
                bixList.forEach(item -> {
                    // 更新同名商品名称的剩余箱数
                    bizBpAnalyseOrderListBoxMapper.updateRemainingBoxCount(item);
                    // 将新数据插入集合
                    bizBpAnalyseOrderListBoxMapper.insert(item);
                });
            }

            // 插入数据
            if (CollectionUtils.isNotEmpty(insertBoxBoxList)) {
                insertBoxBoxList.forEach(item -> {
                    boxBoxMapper.insert(item);
                });
            }
        } else if (containerCount.compareTo(new BigDecimal(containerNoList.size())) == 0 && containerCount.compareTo(new BigDecimal(list.size())) == 0) {
            // 集装箱数量  和 集装箱号 相等 并且商品数量 相等
            // ===================================================== 第二种情况  【N个集装箱】 【N个箱号】【N个商品】 ===================================================
            //  从装箱信息界面带入。如果有多条商品，格式为：商品1*箱数+商品2*箱数
            // 按照商品名称分组，如果多个商品 拼接 商品名称 + 箱数
            Map<String, List<BizBpAnalyseOrderListBoxParam>> collect = list.stream().collect(Collectors.groupingBy(BizBpAnalyseOrderListBoxParam::getExtend1));
            // 循环遍历map
            for (Map.Entry<String, List<BizBpAnalyseOrderListBoxParam>> entry : collect.entrySet()) {
                // 获取商品名称
                String productName = "";
                // 获取商品数量
                BigDecimal productCount = BigDecimal.ZERO;
                // 获取商品箱数
                BigDecimal productBoxCount = BigDecimal.ZERO;
                // 剩余箱数
                BigDecimal remainingBoxCount = BigDecimal.ZERO;
                // 获取商品备注
                String productNote = "";
                // 获取商品列表
                List<BizBpAnalyseOrderListBoxParam> productList = entry.getValue();
                // 设置原始商品名称
                String beforeProductName = productList.get(0).getProductName();
                // 循环遍历商品列表
                for (int i = 0; i < productList.size(); i++) {
                    remainingBoxCount = productList.get(i).getRemainingBoxCount();
                    // 商品名称  =  商品名称 * 箱数
                    productName += productList.get(i).getProductName() + "\t*\t" + productList.get(i).getBoxCount() + " +";
                    // 获取商品数量
                    productCount = productCount.add(productList.get(i).getBoxCount());
                    // 获取商品箱数
                    productBoxCount = productBoxCount.add(productList.get(i).getBoxCount());
                    // 获取商品备注
                    productNote = productList.get(i).getNote();
                }
                // 去除最后一个 +
                productName = productName.substring(0, productName.length() - 1);
                // 插入数据
                BizBpAnalyseOrderListBox box = new BizBpAnalyseOrderListBox();
                box.setId(boxItemId);
                box.setCreateTime(new Date());
                box.setCreateBy(userInfo.getUserNo());
                box.setInsertUserName(userInfo.getUserName());
                box.setParentId(parentId);
                box.setTradeCode(userInfo.getCompany());

                box.setContainerNo(containerNo);
                box.setContainerSpec(containerSpec);
                box.setContainerCount(containerCount);
                box.setProductName(productName);
                box.setBoxCount(productBoxCount);
                box.setRemainingBoxCount(remainingBoxCount);
                box.setExtend1(beforeProductName);
                box.setNote(productNote);
                maxSerialNo = maxSerialNo.add(BigDecimal.ONE);
                box.setSerialNo(maxSerialNo);
                bixList.add(box);
            }
            // 插入数据
            if (CollectionUtils.isNotEmpty(bixList)) {
                bixList.forEach(item -> {
                    // 更新同名商品名称的剩余箱数
                    bizBpAnalyseOrderListBoxMapper.updateRemainingBoxCount(item);
                    // 将新数据插入集合
                    bizBpAnalyseOrderListBoxMapper.insert(item);
                });
            }

            // 处理集装箱列表数据
            for (int i = 0; i < insertBoxBoxList.size(); i++) {
                BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox = insertBoxBoxList.get(i);
                bizBpAnalyseOrderListBoxBox.setContainerNo(containerNoList.get(i));
                bizBpAnalyseOrderListBoxBox.setContainerCount(BigDecimal.ONE);
                boxBoxMapper.insert(bizBpAnalyseOrderListBoxBox);
            }

//            // 插入数据
//            if (CollectionUtils.isNotEmpty(insertBoxBoxList)) {
//                insertBoxBoxList.forEach(item -> {
//                    boxBoxMapper.insert(item);
//                });
//            }
        } else if (containerCount.compareTo(BigDecimal.ONE) == 0 && list.size() > 1) {
            // ===================================================== 第三种情况  【1个集装箱】 【1个箱号】【N个商品】 ===================================================
            // 集装箱数量  和 集装箱号 相等 并且商品数量 相等
            // ===================================================== 第二种情况  【N个集装箱】 【N个箱号】【N个商品】 ===================================================
            //  从装箱信息界面带入。如果有多条商品，格式为：商品1*箱数+商品2*箱数
            // 按照商品名称分组，如果多个商品 拼接 商品名称 + 箱数
            Map<String, List<BizBpAnalyseOrderListBoxParam>> collect = list.stream().collect(Collectors.groupingBy(BizBpAnalyseOrderListBoxParam::getExtend1));
            // 循环遍历map
            for (Map.Entry<String, List<BizBpAnalyseOrderListBoxParam>> entry : collect.entrySet()) {
                // 获取商品名称
                String productName = "";
                // 获取商品数量
                BigDecimal productCount = BigDecimal.ZERO;
                // 获取商品箱数
                BigDecimal productBoxCount = BigDecimal.ZERO;
                // 剩余箱数
                BigDecimal remainingBoxCount = BigDecimal.ZERO;
                // 获取商品备注
                String productNote = "";
                // 获取商品列表
                List<BizBpAnalyseOrderListBoxParam> productList = entry.getValue();
                // 设置原始商品名称
                String beforeProductName = productList.get(0).getProductName();
                // 循环遍历商品列表
                for (int i = 0; i < productList.size(); i++) {
                    remainingBoxCount = productList.get(i).getRemainingBoxCount();
                    // 商品名称  =  商品名称 * 箱数
                    productName += productList.get(i).getProductName() + "\t*\t" + productList.get(i).getBoxCount() + " +";
                    // 获取商品数量
                    productCount = productCount.add(productList.get(i).getBoxCount());
                    // 获取商品箱数
                    productBoxCount = productBoxCount.add(productList.get(i).getBoxCount());
                    // 获取商品备注
                    productNote = productList.get(i).getNote();
                }
                // 去除最后一个 +
                productName = productName.substring(0, productName.length() - 1);
                // 插入数据
                BizBpAnalyseOrderListBox box = new BizBpAnalyseOrderListBox();
                box.setId(boxItemId);
                box.setCreateTime(new Date());
                box.setCreateBy(userInfo.getUserNo());
                box.setInsertUserName(userInfo.getUserName());
                box.setParentId(parentId);
                box.setTradeCode(userInfo.getCompany());

                box.setContainerNo(containerNo);
                box.setContainerSpec(containerSpec);
                box.setContainerCount(containerCount);
                box.setProductName(productName);
                box.setBoxCount(productBoxCount);
                box.setRemainingBoxCount(remainingBoxCount);
                box.setExtend1(beforeProductName);
                box.setNote(productNote);
                maxSerialNo = maxSerialNo.add(BigDecimal.ONE);
                box.setSerialNo(maxSerialNo);
                bixList.add(box);
            }
            // 插入数据
            if (CollectionUtils.isNotEmpty(bixList)) {
                bixList.forEach(item -> {
                    // 更新同名商品名称的剩余箱数
                    bizBpAnalyseOrderListBoxMapper.updateRemainingBoxCount(item);
                    // 将新数据插入集合
                    bizBpAnalyseOrderListBoxMapper.insert(item);
                });
            }

            // 处理集装箱列表数据
            for (int i = 0; i < insertBoxBoxList.size(); i++) {
                BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox = insertBoxBoxList.get(i);
                bizBpAnalyseOrderListBoxBox.setContainerNo(containerNoList.get(i));
                bizBpAnalyseOrderListBoxBox.setContainerCount(BigDecimal.ONE);
                boxBoxMapper.insert(bizBpAnalyseOrderListBoxBox);
            }
        }else {
            throw new RuntimeException("数据录入不符合规范，请修改！");
        }

        return ResultObject.createInstance(true, "保存成功");
    }

    public ResultObject checkHeadId(BizBpAnalyseOrderListBoxParam param, UserInfoToken userInfo) {
        String parentId = param.getParentId();
        BizBpAnalyseOrderHead bizBpAnalyseOrderHead = bizBpAnalyseOrderHeadMapper.selectByPrimaryKey(parentId);
        // 判断分析单号是否为空
        if (StringUtils.isBlank(bizBpAnalyseOrderHead.getAnalysisNo())) {
            throw new ErrorException(400,"请先填写分析单号");
        }
        return ResultObject.createInstance(true,"分析单号存在");
    }
}