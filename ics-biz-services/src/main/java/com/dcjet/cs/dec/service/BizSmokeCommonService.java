package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizSmokeCommonMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsTbMapper;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsTb;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsListCommonDto;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 烟机设备通用 Service
 */
@Service
public class BizSmokeCommonService {

    private static final SimpleDateFormat MONTH_FORMAT = new SimpleDateFormat("yyyyMM");


    @Resource
    private BizSmokeCommonMapper bizSmokeCommonMapper;

    @Resource
    private BizSmokeMachineIncomingGoodsTbMapper bizSmokeMachineIncomingGoodsTbMapper;




    /**
     * 获取当前月汇率
     * @param month 格式：yyyyMM
     * @param tradeCode 企业代码
     * @param curr 货币代码
     * @return 根据当前月份+币种关联【企业自定义参数-企业汇率】取“汇率”栏位值，允许修改
     */
    public EnterpriseRate getCurrentMonthRate(String month, String tradeCode, String curr) {
        if(StringUtils.isEmpty(month)){
            month = MONTH_FORMAT.format(new Date());
        }
        return bizSmokeCommonMapper.getCurrentMonthRate(
                month,
                StringUtils.trimToEmpty(tradeCode),
                StringUtils.trimToEmpty(curr)
        );
    }


    /**
     * 获取进货单表体KeyValue键值对数据集合
     * @param userInfo 登录用户信息
     * @param types 参数类型集合
     * @return
     */
    public ResultObject getListCommonKeyValueList(UserInfoToken userInfo, List<BizTypeEnum> types) {
        ResultObject instance = ResultObject.createInstance(true);
        BizSmokeMachineIncomingGoodsListCommonDto dto = new BizSmokeMachineIncomingGoodsListCommonDto();
        if (CollectionUtils.isNotEmpty(types)) {
            for (BizTypeEnum type : types) {
                if (BizTypeEnum.UNIT.equals(type)) {
                    // 单位
                    List<Map<String, String>> list = bizSmokeCommonMapper.getListCommonKeyValueList(userInfo.getCompany(), type.name());
                    dto.setUnitList(list);
                }else if (BizTypeEnum.CURR.equals(type)) {
                    // 币制(获取自定义币制  USD  美元 不是标准 502 美元)
                    List<Map<String, String>> currList = bizSmokeCommonMapper.getListCommonCustomerCurrKeyValueList(userInfo.getCompany(), type.name());
                    dto.setCurrList(currList);
                }else if (BizTypeEnum.CUSTOMER.equals(type)) {
                    // 获取当前企业客户基础信息
                    List<Map<String, String>> customerList = bizSmokeCommonMapper.getListCommonCustomerKeyValueList(userInfo.getCompany());
                    dto.setCustomerList(customerList);
                }else if (BizTypeEnum.COUNTRY.equals(type)) {
                    // 获取国家信息
                    // 获取当前企业客户基础信息
                    List<Map<String, String>> countryList = bizSmokeCommonMapper.getListCommonCountryKeyValueList(userInfo.getCompany(), type.name());
                    dto.setCountryList(countryList);
                }else if (BizTypeEnum.INSURANCE_CATEGORY.equals(type)){
                    // 获取企业参数库 投保险别
                    List<Map<String, String>> insuranceCategoryList = bizSmokeCommonMapper.getListCommonInsuranceCategoryKeyValueList(userInfo.getCompany(), type.name());
                    dto.setInsuranceCategoryList(insuranceCategoryList);
                }
            }
        }

        instance.setMessage("获取成功！");
        instance.setData(dto);
        return instance;
    }

    /**
     * 获取 不同类型在划款参数维护的保险费率
     * @param type 类型
     * @param tradeCode 企业代码
     * @return 保险费率
     */
    public BigDecimal getInsuranceRateByType(String type, String tradeCode) {
        return bizSmokeCommonMapper.getInsuranceRateByType(type, tradeCode);
    }




    /**
     * 设置基础金额信息（投保单模块）
     * @param userInfo 用户信息
     * @param po 投保单数据
     * @param isEnter 是否回车 如果回车 覆盖原先的值
     */
    public void setBaseTBMoney(BizSmokeMachineIncomingGoodsTb po, Boolean isEnter,UserInfoToken userInfo) {
        // 投保加成
        BigDecimal insuranceBonusMoney = Optional.ofNullable(po.getInsurancePremiumRate()).orElse(BigDecimal.ZERO).divide(new BigDecimal(100));
        // 【投保加成%】
        // po.setInsurancePremiumRate(insuranceBonusMoney);
        // 进货单表体金额合计
        BigDecimal listSumTotal = bizSmokeMachineIncomingGoodsTbMapper.getPurchaseOrderSum(po.getHeadId());
        // 保险金额	进货单表体金额合计 *（1+投保加成%）
        BigDecimal insuranceMoney = listSumTotal.multiply(insuranceBonusMoney.add(BigDecimal.ONE));
        // 【保险金额】
        po.setInsuranceAmount(insuranceMoney.setScale(4, BigDecimal.ROUND_HALF_UP));
        // 保险费率 需求模糊（根据外商合同关联划款参数带出，允许修改） -> 先取最新的业务类型是 3 进口卷烟设备 的保险费率
        // 如果保险费率修改，可以计算出保费。保费修改，不要反算保险费率
        BigDecimal insuranceRate = po.getInsuranceRate();
        if (insuranceRate == null) {
            insuranceRate = getInsuranceRateByType("3", userInfo.getCompany());
        }
        if (po.getInsuranceRate() == null) {
            po.setInsuranceRate(insuranceRate);
        }
        if (insuranceRate == null) {
            throw new ErrorException(400, XdoI18nUtil.t("外商合同-划款参数-保险费率不能为空"));
        }
        // 【保险费率】
        po.setInsuranceRate(insuranceRate.setScale(4, BigDecimal.ROUND_HALF_UP));
        // 计算出保费 保费=保险金额*汇率*保险费率，允许修改

        // 获取汇率  从模块的汇率默认获取美元的
        EnterpriseRate currentMonthRate = getCurrentMonthRate("", userInfo.getCompany(), "USD");
        // if (StringUtils.isBlank(po.getCurrency())){
        //     throw new ErrorException(400, XdoI18nUtil.t("币种栏位为空，无法获取汇率"));
        // }
        if (currentMonthRate == null) {
            throw new ErrorException(400, XdoI18nUtil.t("币制 USD"+ " "+ "未获取到当月汇率"));
        }
        BigDecimal insurancePremium = insuranceMoney.multiply(insuranceRate).multiply(currentMonthRate.getUsdRate());
        if (po.getPremium() == null || isEnter == true) {
            // 【保费】
            po.setPremium(insurancePremium.setScale(2, BigDecimal.ROUND_HALF_UP));
        }
    }









    /**
     * 计算总金额 和 总折价美元
     * @param userInfo 用户信息
     * @param po 进货单表体数据
     */
    public void computedUsdMoney(BizSmokeMachineIncomingGoodsList po,UserInfoToken userInfo) {
        // 【金额】 根据 数量 * 单价 计算 金额
        BigDecimal totalMoney = po.getQuantity().multiply(po.getUnitPrice()).setScale(4, BigDecimal.ROUND_HALF_UP);
        po.setAmount(totalMoney);
        // 获取当月汇率
        EnterpriseRate enterpriseRate = getCurrentMonthRate("", userInfo.getCompany(), "USD");
        if (enterpriseRate == null) {
            throw new ErrorException(400, "当月汇率未维护！");
        }

        // 计算总折价美元
        BigDecimal totalDiscount = totalMoney.multiply(enterpriseRate.getUsdRate().divide(BigDecimal.valueOf(100), 4, BigDecimal.ROUND_HALF_UP));
        po.setTotalUsd(totalDiscount);
    }


    /**
     * 打印格式化小数
     * @param formatData 需要格式化数据
     * @return 格式化完成数据
     */
    public String formatDecimalPortion(BigDecimal formatData) {
        // 格式化小数部分
        DecimalFormat df2 = new DecimalFormat("#,##0.00");
        return df2.format(formatData);
    }


    /**
     * 根据客户代码获取客户名称
     * @param customerCode 客户代码
     * @param tradeCode 企业代码
     * @return 客户名称
     */
    public String getCustomerNameByCode(String customerCode, String tradeCode) {
        return bizSmokeCommonMapper.getCustomerNameByCode(customerCode, tradeCode);
    }


    /**
     * 根据价格条款code获取价格条款名称
     * @param priceCode 价格条款code
     * @param tradeCode 企业代码
     * @return 价格条款代码
     */
    public String getPriceNameByCode(String priceCode,String tradeCode) {
        return bizSmokeCommonMapper.getPriceNameByCode(priceCode, tradeCode);
    }


    /**
     * 批量根据代码 获取客户信息
     */
    public List<Map<String,String>> getCustomerListByCodes(List<String> customerCodes,String tradeCode) {
        return bizSmokeCommonMapper.getCustomerListByCodes(customerCodes, tradeCode);
    }


    /**
     * 第三条线 根据表头合同ID 获取表体金额汇总
     * @param headId 合同ID
     * @return 表体金额汇总
     */
    public BigDecimal getTotalByHeadId(String headId) {
        BigDecimal totalMoney = bizSmokeCommonMapper.getTotalByHeadId(headId);
        if (totalMoney == null || totalMoney.compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400, XdoI18nUtil.t("外商合同表体总金额为空，无法提取"));
        }
        return totalMoney;
    }

    public List<Map<String, String>> getCurrList(String tradeCode) {
        return bizSmokeCommonMapper.getCurrList(tradeCode);

    }


    /**
     * 常用枚举类型
     */
    public  enum BizTypeEnum {
        /**
         * 计量单位
         */
        UNIT,
        /**
         * 币制
         */
        CURR,
        /**
         * 客户信息
         */
        CUSTOMER,
        /**
         * 国家
         */
        COUNTRY,
        /**
         * 投保险别
         */
        INSURANCE_CATEGORY;
    }
}