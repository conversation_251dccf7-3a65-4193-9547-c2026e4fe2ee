<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizExportGoodsListBoxMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizExportGoodsListBox">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="start_box_no" property="startBoxNo" jdbcType="NUMERIC"/>
        <result column="end_box_no" property="endBoxNo" jdbcType="NUMERIC"/>
        <result column="product_desc" property="productDesc" jdbcType="VARCHAR"/>
        <result column="package_style" property="packageStyle" jdbcType="VARCHAR"/>
        <result column="gross_weight_box" property="grossWeightBox" jdbcType="NUMERIC"/>
        <result column="net_weight_box" property="netWeightBox" jdbcType="NUMERIC"/>
        <result column="tare_weight_box" property="tareWeightBox" jdbcType="NUMERIC"/>
        <result column="length_m" property="lengthM" jdbcType="NUMERIC"/>
        <result column="width_m" property="widthM" jdbcType="NUMERIC"/>
        <result column="height_m" property="heightM" jdbcType="NUMERIC"/>
        <result column="parent_list_id" property="parentListId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.start_box_no, 
            t.end_box_no, 
            t.product_desc, 
            t.package_style, 
            t.gross_weight_box, 
            t.net_weight_box, 
            t.tare_weight_box, 
            t.length_m, 
            t.width_m, 
            t.height_m, 
            t.parent_list_id
    </sql>

    <sql id="condition">
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizExportGoodsListBox">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_export_goods_list_box t
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_export_goods_list_box t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>