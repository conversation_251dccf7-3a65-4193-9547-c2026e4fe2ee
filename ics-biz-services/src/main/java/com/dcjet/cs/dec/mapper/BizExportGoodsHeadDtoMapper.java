package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dto.dec.BizExportGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizExportGoodsHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizExportGoodsHeadDto
 *
 * <AUTHOR>
 * @date 2025-07-07 11:18:39
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizExportGoodsHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizExportGoodsHeadDto toDto(BizExportGoodsHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizExportGoodsHead toPo(BizExportGoodsHeadParam param);

    /**
     * 数据库原始数据更新
     * @param bizExportGoodsHeadParam
     * @param BizExportGoodsHead
     */
    void updatePo(BizExportGoodsHeadParam bizExportGoodsHeadParam, @MappingTarget BizExportGoodsHead bizExportGoodsHead);

    default void patchPo(BizExportGoodsHeadParam bizExportGoodsHeadParam , BizExportGoodsHead bizExportGoodsHead) {
        // TODO 自行实现局部更新
    }
}