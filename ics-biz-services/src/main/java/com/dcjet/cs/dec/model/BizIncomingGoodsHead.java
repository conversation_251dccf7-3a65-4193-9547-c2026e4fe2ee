package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 进货管理-表头数据
 *
 * <AUTHOR>
 * @date 2025-05-22 15:28:59
 */
@Getter
@Setter
@Table(name = "T_BIZ_INCOMING_GOODS_HEAD")
public class BizIncomingGoodsHead implements Serializable{
    /*
        单据状态 进口管理-进货信息-表头-状态 使用的是 dataState 这个字段
     */
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     * 字符类型(40)
     * 必填
     */
    @Column(name = "ID")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "BUSINESS_TYPE")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "DATA_STATE")
    private String dataState;

    /**
     * 版本号
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "VERSION_NO")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "SYS_ORG_CODE")
    private String sysOrgCode;

    /**
     * 父级ID
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "PARENT_ID")
    private String parentId;

    /**
     * 创建人
     * 字符类型(100)
     * 必填
     */
    @Column(name = "CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "CREATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private String createTimeTo;

    /**
     * 更新人
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "UPDATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private Date updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "EXTEND10")
    private String extend10;

    /**
     * 合同号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "CONTRACT_NO")
    private String contractNo;

    /**
     * 进货单号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "PURCHASE_NO")
    private String purchaseNo;

    /**
     * 客户
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "CUSTOMER")
    private String customer;

    /**
     * 供应商
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "SUPPLIER")
    private String supplier;

    /**
     * 发票号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "INVOICE_NO")
    private String invoiceNo;

    /**
     * 启运港
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "PORT_OF_DEPARTURE")
    private String portOfDeparture;

    /**
     * 目的地/港
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "DESTINATION")
    private String destination;

    /**
     * 付款方式
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "PAYMENT_METHOD")
    private String paymentMethod;

    /**
     * 价格条款
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "PRICE_TERM")
    private String priceTerm;

    /**
     * 价格条款对应港口
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "PRICE_TERM_PORT")
    private String priceTermPort;

    /**
     * 船名航次
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "VESSEL_VOYAGE")
    private String vesselVoyage;

    /**
     * 开航日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "SAILING_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sailingDate;

    /**
     * 开航日期-开始时间
     */
    @Transient
    private Date sailingDateFrom;

    /**
     * 开航日期-结束时间
     */
    @Transient
    private Date sailingDateTo;

    /**
     * 预计到达日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "EXPECTED_ARRIVAL_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectedArrivalDate;

    /**
     * 预计到达日期-开始时间
     */
    @Transient
    private Date expectedArrivalDateFrom;

    /**
     * 预计到达日期-结束时间
     */
    @Transient
    private Date expectedArrivalDateTo;

    /**
     * 做销日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "SALES_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date salesDate;

    /**
     * 做销日期-开始时间
     */
    @Transient
    private Date salesDateFrom;

    /**
     * 做销日期-结束时间
     */
    @Transient
    private Date salesDateTo;

    /**
     * 合同金额
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "CONTRACT_AMOUNT")
    private BigDecimal contractAmount;

    /**
     * 保险费率%
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "INSURANCE_RATE")
    private BigDecimal insuranceRate;

    /**
     * 投保加成%
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "INSURANCE_MARKUP")
    private BigDecimal insuranceMarkup;

    /**
     * 制单人
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "DOCUMENT_CREATOR")
    private String documentCreator;

    /**
     * 制单日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "DOCUMENT_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date documentDate;

    /**
     * 制单日期-开始时间
     */
    @Transient
    private Date documentDateFrom;

    /**
     * 制单日期-结束时间
     */
    @Transient
    private Date documentDateTo;

    /**
     * 单据状态
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "DOCUMENT_STATUS")
    private String documentStatus;

    /**
     * 确认时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "CONFIRM_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    /**
     * 确认时间-开始时间
     */
    @Transient
    private Date confirmTimeFrom;

    /**
     * 确认时间-结束时间
     */
    @Transient
    private Date confirmTimeTo;

    /**
     * 审批状态
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "APPROVAL_STATUS")
    private String approvalStatus;

    /**
     * 签约日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "DATE_OF_CONTRACT")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateOfContract;

    /**
     * 签约日期-开始时间
     */
    @Transient
    private Date dateOfContractFrom;

    /**
     * 签约日期-结束时间
     */
    @Transient
    private Date dateOfContractTo;



    /**
     * 是否流入下一个节点
     * 数据库字段:IS_NEXT
     * 字符类型(1)
     */
    @Column(name = "IS_NEXT")
    private String isNext;


    /**
     * 购销合同号
     * 数据库字段:PURCHASE_CONTRACT_NO
     * 字符类型(60)
     */
    @Column(name = "PURCHASE_CONTRACT_NO")
    private String purchaseContractNo;


    /**
     * 报关单号
     * 数据库字段:ENTRY_NO
     * 字符类型(18)
     */
    @Column(name = "ENTRY_NO")
    private String entryNo;

    /**
     * 报关日期
     * 数据库字段:ENTRY_DATE
     * 日期类型(6)
     */
    @Column(name = "ENTRY_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 报关日期-开始时间
     */
    @Transient
    private Date entryDateFrom;

    /**
     * 报关日期-结束时间
     */
    @Transient
    private Date entryDateTo;

    //  salesDocumentStatus: '',// 销售单据状态
    @Column(name = "sales_document_status")
    private String salesDocumentStatus;



    /**
     * 序号
     * 数据库字段:SERIAL_NO
     * 数值类型(19,0)
     */
    @Column(name = "SERIAL_NO")
    private Integer serialNo;

    //
    //    SELL_CONTRACT_NO
    //
    //
    //
    //
    //            CANCEL_DATE
    //
    //
    //    NOTE

    /**
     * 购销合同号
     * 数据库字段:SELL_CONTRACT_NO
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "SELL_CONTRACT_NO")
    private String sellContractNo;


    /**
     * 作销日期
     * 数据库字段:CANCEL_DATE
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "CANCEL_DATE")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cancelDate;

    /**
     * 作销日期-开始时间
     */
    @Transient
    private String cancelDateFrom;

    /**
     * 作销日期-结束时间
     */
    @Transient
    private String cancelDateTo;


    /**
     * 备注
     * 数据库字段:NOTE
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "NOTE")
    private String note;





    /**
     * 许可证号
     */
    @Transient
    private String licenseNumber;

    /**
     * 准运证编号
     */
    @Transient
    private String permitNumber;

    /**
     * 销售发票号
     */
    @Transient
    private String sellInvoiceNo;

    /**
     * 销售数据状态
     * 0编制、1确认、2作废
     */
    @Transient
    private String sellStatus;

    // ALTER TABLE  "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD" ADD COLUMN IF NOT EXISTS "INSURANCE_CATEGORY" VARCHAR(200) NULL;
    //COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_CATEGORY"  IS '投保险别（企业自定义参数-保险类别 中文名称）';
    //COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_RATE" IS '费率%（数值型，保留4位小数）';
    //ALTER TABLE "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD" ADD COLUMN IF NOT EXISTS "INSURANCE_AMOUNT" NUMERIC(19,4) NULL;
    //COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_AMOUNT" IS '保额（数值型，保留4位小数）';
    //ALTER TABLE "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD" ADD COLUMN IF NOT EXISTS "INSURANCE_PREMIUM" NUMERIC(19,4) NULL;
    //COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_INCOMING_GOODS_HEAD"."INSURANCE_PREMIUM" IS '保费数（数值型，保留4位小数）';

    /**
     * 保险类别
     */
    @Column(name = "INSURANCE_CATEGORY")
    private String insuranceCategory;

    /**
     * 保额
     */
    @Column(name = "INSURANCE_AMOUNT")
    private BigDecimal insuranceAmount;

    /**
     * 保费数目
     */
    @Column(name = "INSURANCE_PREMIUM")
    private BigDecimal insurancePremium;

}