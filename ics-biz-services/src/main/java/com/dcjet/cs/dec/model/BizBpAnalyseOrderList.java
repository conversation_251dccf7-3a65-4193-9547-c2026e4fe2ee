package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import javax.validation.constraints.Digits;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * （第7条线）出料加工进口薄片-分析单表表体
 *
 * <AUTHOR>
 * @date 2025-07-12 20:50:39
 */
@Getter
@Setter
@Table(name = "t_biz_bp_analyse_order_list")
public class BizBpAnalyseOrderList implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     * 字符类型(160)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(240)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级id
     * 字符类型(160)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private String createTimeTo;

    /**
     * 更新人
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 商品名称
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "product_name")
    private String productName;

    /**
     * 产品型号
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "product_model")
    private String productModel;

    /**
     * 单位
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "unit")
    private String unit;

    /**
     * 数量
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * 箱数
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "box_count")
    private BigDecimal boxCount;

    /**
     * 毛重
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 单价
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 金额
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "amount")
    private BigDecimal amount;

    /**
     * 备注
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "remark")
    private String remark;


    /**
     * 净重
     * 数据库字段:net_wt
     * 数值类型(19,4)
     */
    @Column(name = "net_wt")
    private BigDecimal netWt;



    /**
     * 体积
     * 数据库字段:volume
     * 数值类型(19,4)
     */
    @Column(name = "volume")
    private BigDecimal volume;


    public String getProductNameAndGModel() {
        return productName + "-" + productModel;
    }

    @Override
    public String toString() {
    return "（第7条线）出料加工进口薄片-分析单表表体{" +
        "主键id='" + id + '\'' +
        "业务类型='" + businessType + '\'' +
        "数据状态='" + dataState + '\'' +
        "版本号='" + versionNo + '\'' +
        "企业10位编码='" + tradeCode + '\'' +
        "组织机构代码='" + sysOrgCode + '\'' +
        "父级id='" + parentId + '\'' +
        "创建人='" + createBy + '\'' +
        "创建时间='" + createTime + '\'' +
        "更新人='" + updateBy + '\'' +
        "更新时间='" + updateTime + '\'' +
        "插入用户名='" + insertUserName + '\'' +
        "更新用户名='" + updateUserName + '\'' +
        "扩展字段1='" + extend1 + '\'' +
        "扩展字段2='" + extend2 + '\'' +
        "扩展字段3='" + extend3 + '\'' +
        "扩展字段4='" + extend4 + '\'' +
        "扩展字段5='" + extend5 + '\'' +
        "扩展字段6='" + extend6 + '\'' +
        "扩展字段7='" + extend7 + '\'' +
        "扩展字段8='" + extend8 + '\'' +
        "扩展字段9='" + extend9 + '\'' +
        "扩展字段10='" + extend10 + '\'' +
        "商品名称='" + productName + '\'' +
        "产品型号='" + productModel + '\'' +
        "单位='" + unit + '\'' +
        "数量='" + quantity + '\'' +
        "箱数='" + boxCount + '\'' +
        "毛重='" + grossWeight + '\'' +
        "单价='" + unitPrice + '\'' +
        "金额='" + amount + '\'' +
        "备注='" + remark + '\'' +
        "净重='" + netWt + '\'' +
        "体积='" + volume + '\'' +

      '}';
    }

    /**
     * 打印导出字段
     */
    @Transient
    private String quantityStr;
    @Transient
    private String grossWeightStr;
    @Transient
    private String unitPriceStr;
    @Transient
    private String amountStr;
    @Transient
    private String netWtStr;
    @Transient
    private String volumeStr;
    @Transient
    private String boxCountStr;
    @Transient
    private BigDecimal tareWeight;
}