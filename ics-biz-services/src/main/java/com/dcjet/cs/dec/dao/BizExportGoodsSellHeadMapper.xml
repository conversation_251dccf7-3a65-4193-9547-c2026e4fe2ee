<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizExportGoodsSellHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizExportGoodsSellHead">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="export_no" property="exportNo" jdbcType="VARCHAR"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="invoice_customer" property="invoiceCustomer" jdbcType="VARCHAR"/>
        <result column="sales_customer" property="salesCustomer" jdbcType="VARCHAR"/>
        <result column="lc_no" property="lcNo" jdbcType="VARCHAR"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="total_amount" property="totalAmount" jdbcType="NUMERIC"/>
        <result column="mark" property="mark" jdbcType="VARCHAR"/>
        <result column="agent_rate" property="agentRate" jdbcType="NUMERIC"/>
        <result column="agent_fee_foreign" property="agentFeeForeign" jdbcType="NUMERIC"/>
        <result column="prepay_date" property="prepayDate" jdbcType="DATE"/>
        <result column="cancel_date" property="cancelDate" jdbcType="DATE"/>
        <result column="invoice_date" property="invoiceDate" jdbcType="DATE"/>
        <result column="exchange_rate" property="exchangeRate" jdbcType="NUMERIC"/>
        <result column="agent_fee_ex_tax" property="agentFeeExTax" jdbcType="NUMERIC"/>
        <result column="agent_tax" property="agentTax" jdbcType="NUMERIC"/>
        <result column="agent_fee_total" property="agentFeeTotal" jdbcType="NUMERIC"/>
        <result column="send_finance" property="sendFinance" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_red_flush" property="isRedFlush" jdbcType="VARCHAR"/>
        <result column="bill_status" property="billStatus" jdbcType="VARCHAR"/>
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.invoice_no, 
            t.export_no, 
            t.contract_no, 
            t.invoice_customer, 
            t.sales_customer, 
            t.lc_no, 
            t.currency, 
            t.total_amount, 
            t.mark, 
            t.agent_rate, 
            t.agent_fee_foreign, 
            t.prepay_date, 
            t.cancel_date, 
            t.invoice_date, 
            t.exchange_rate, 
            t.agent_fee_ex_tax, 
            t.agent_tax, 
            t.agent_fee_total, 
            t.send_finance, 
            t.remark, 
            t.is_red_flush, 
            t.bill_status, 
            t.confirm_time
    </sql>

    <sql id="condition">
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizExportGoodsSellHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_export_goods_sell_head t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getFormDataById" resultType="com.dcjet.cs.dec.model.BizExportGoodsSellHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_export_goods_sell_head t
        WHERE
            t.parent_id = #{parentId}
    </select>
    <select id="getDeleteSellHeadByParentId" resultType="java.lang.String">
        select t.id from t_biz_export_goods_sell_head t where t.parent_id = #{parentId}
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_export_goods_sell_head t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>