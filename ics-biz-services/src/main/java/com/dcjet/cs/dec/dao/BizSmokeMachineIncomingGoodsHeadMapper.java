package com.dcjet.cs.dec.dao;


import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dto.dec.BizSmokeHeadTotalDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.params.model.EnterpriseRate;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tk.mybatis.mapper.common.Mapper;

/**
 * （3）烟机设备-进货单-表头数据Mapper
 */
public interface BizSmokeMachineIncomingGoodsHeadMapper extends Mapper<BizSmokeMachineIncomingGoodsHead>{

    /**
     * 查询获取数据
     * @param bizSmokeMachineIncomingGoodsHead
     * @return
     */
    List<BizSmokeMachineIncomingGoodsHead> getList(BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead);
    List<BizSmokeMachineIncomingGoodsHead> getListByHtId(String htId);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    List<Map<String, String>> getCustomerList(BizSmokeMachineIncomingGoodsHeadParam params);

    List<Map<String, String>> getPortList(String tradeCode);

    List<Map<String, String>> getCurrentCustomerList(String tradeCode);

    List<Map<String, String>> getCurrentSupplierList(String tradeCode);

    List<Map<String, String>> getCurrentCreateByList(String tradeCode);

    Integer selectByPurchaseNo(@Param("purchaseNo") String purchaseNo, @Param("id") String id, @Param("tradeCode") String tradeCode);

    Integer updateAccountById(@Param("headId") String headId);

    List<ForeignContractHead> getExtractAuxmatContractList(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    BigDecimal getIncomingCountByContractNo(@Param("contractNo") String contractNoTemp, @Param("tradeCode") String tradeCode);

    Integer getMaxSerialNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    BigDecimal getContractListCount(@Param("contractNo") String contractNo, @Param("gName") String gName, @Param("tradeCode") String tradeCode);

    BigDecimal getSmokeListCount(@Param("contractNo") String contractNo, @Param("gName") String gName, @Param("tradeCode") String tradeCode);

    Integer deleteByHeadId(@Param("id") String sid);

    int cancelData(@Param("id") String id);

    int checkApprovalStatus(@Param("sids") List<String> sids, @Param("status") String status);

    List<WorkFlowParam> selectBySids(@Param("sids") List<String> sids);

    List<BizSmokeMachineIncomingGoodsHead> getAeoList(BizSmokeMachineIncomingGoodsHead head);

    BizSmokeHeadTotalDto getSumTotal(BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead);

}