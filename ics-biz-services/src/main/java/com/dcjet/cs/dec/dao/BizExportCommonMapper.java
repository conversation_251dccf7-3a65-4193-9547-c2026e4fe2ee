package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizBpAnalyseOrderHead;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dto.dec.BizExportCommonKeyValue;
import com.dcjet.cs.params.model.EnterpriseRate;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 第9条线-非国营贸易出口辅料-出货信息通用Mapper层
 */
public interface BizExportCommonMapper extends Mapper<BizExportGoodsHead>{


    /**
     * 获取客户列表信息
     * @param customerCode 客户代码
     * @param tradeCode 企业代码
     * @return 返回结果 Map<String,String> key:customerCode value:customerName
     */
    List<Map<String,String>> getCustomerList(@Param("customerCode") String customerCode,
                                             @Param("tradeCode") String tradeCode);


    /**
     * 获取当前企业币制信息
     * @param tradeCode 企业代码
     * @return 返回结果 Map<String,String> key:currCode value:currName
     */
    List<Map<String, String>> getCurrList(@Param("tradeCode") String tradeCode);

    /**
     * 获取价格条款
     * @param tradeCode  企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPriceTermList(@Param("tradeCode") String tradeCode);

    /**
     * 获取包装方式
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPackageList(@Param("tradeCode") String tradeCode);


    /**
     * 获取城市信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getCityList(@Param("tradeCode") String tradeCode);


    /**
     * 获取保险类别信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getInsuranceTypeList(@Param("tradeCode") String tradeCode);


    /**
     * 获取港口信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPortList(@Param("tradeCode") String tradeCode);


    /**
     * 获取当前企业-出货信息表头-客户基础信息汇总
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getExportHeadCustomerList(@Param("tradeCode") String tradeCode);

    /**
     * 获取当前企业-出货信息表头-供应商基础信息汇总
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getExportHeadSupplierList(@Param("tradeCode") String tradeCode);


    /**
     * 获取当月汇率信息
     * @param month 月份
     * @param tradeCode 企业代码
     * @param curr 币制
     * @return 返回结果
     */
    EnterpriseRate getCurrentMonthRate(@Param("month") String month,
                                       @Param("tradeCode") String tradeCode,
                                       @Param("curr") String curr);


    /**
     *
     * 获取单位信息
     * @param tradeCode 企业代码
     * @return 单位信息
     */
    List<Map<String, String>> getUnitList(@Param("tradeCode") String tradeCode);

    /**
     * 根据 客商类型 + 常用标识 获取客户信息
     * @param merchantType 客商类型
     * @param commonFlag 常用标识
     * @param tradeCode 企业代码
     * @return 客户信息列表
     */
    List<Map<String, String>> getCustomerListByType(@Param("merchantType") String merchantType,
                                                    @Param("commonFlag") String commonFlag,
                                                    @Param("tradeCode") String tradeCode);


    /**
     * 根据 常用标识 获取客户、供应商信息
     * @param commonFlag 常用标识
     * @param tradeCode 企业代码
     * @return 客户、供应商信息列表
     */
    List<Map<String, String>> getCustomerSupplierListByType(@Param("commonFlag") String commonFlag,
                                                            @Param("tradeCode") String tradeCode);
}