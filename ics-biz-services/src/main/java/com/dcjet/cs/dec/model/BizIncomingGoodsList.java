package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 进过管理-表体列表
 *
 * <AUTHOR>
 * @date 2025-05-23 13:32:21
 */
@Getter
@Setter
@Table(name = "t_biz_incoming_goods_list")
public class BizIncomingGoodsList implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     * 字符类型(80)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级id
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(100)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private Date createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private Date createTimeTo;

    /**
     * 更新人
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private Date updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 商品名称
     * 字符类型(160)
     * 非必填
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 规格
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "product_model")
    private String productModel;

    /**
     * 数量
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * 单位
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "unit")
    private String unit;

    /**
     * 单价
     * 数值类型(19,8)
     * 非必填
     */
    @Column(name = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 金额
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "amount")
    private BigDecimal amount;

    /**
     * 交货日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "delivery_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDate;

    /**
     * 交货日期-开始时间
     */
    @Transient
    private Date deliveryDateFrom;

    /**
     * 交货日期-结束时间
     */
    @Transient
    private Date deliveryDateTo;

    /**
     * 总价折美元
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "total_usd")
    private BigDecimal totalUsd;

    /**
     * 备注
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "remarks")
    private String remarks;

    /**
     * 表头head_id
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "head_id")
    private String headId;

    /**
     * 进口数量
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "in_quantity")
    private BigDecimal inQuantity;

    /**
     * 进口单位
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "in_unit")
    private String inUnit;

    /**
     * 币种
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "curr")
    private String curr;

    /**
     * 进口发票号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "invoice_no")
    private String invoiceNo;


    /**
     * 合同表体的ID
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "contract_list_id")
    private String contractListId;






}