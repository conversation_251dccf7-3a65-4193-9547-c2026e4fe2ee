<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsDocumentMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsDocument">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="permit_number" property="permitNumber" jdbcType="VARCHAR"/>
        <result column="arrival_date" property="arrivalDate" jdbcType="TIMESTAMP"/>
        <result column="entry_no" property="entryNo" jdbcType="VARCHAR"/>
        <result column="entry_date" property="entryDate" jdbcType="TIMESTAMP"/>
        <result column="release_date" property="releaseDate" jdbcType="TIMESTAMP"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.permit_number, 
            t.arrival_date, 
            t.entry_no, 
            t.entry_date, 
            t.release_date, 
            t.note, 
            t.head_id
    </sql>

    <sql id="condition">
        <if test="headId != null and headId  != ''">
            AND t.head_id LIKE '%' || #{headId} || '%'
        </if>
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsDocument">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_smoke_machine_incoming_goods_document t
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <select id="getDocumentByHeadId" resultType="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsDocument">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
        t_biz_smoke_machine_incoming_goods_document t
        WHERE
            t.head_id = #{headId}
        limit 1
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_smoke_machine_incoming_goods_document t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>