<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizExportGoodsSellListMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizExportGoodsSellList">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="goods_desc" property="goodsDesc" jdbcType="VARCHAR"/>
        <result column="invoice_name" property="invoiceName" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="NUMERIC"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="package_quantity" property="packageQuantity" jdbcType="NUMERIC"/>
        <result column="package_style" property="packageStyle" jdbcType="VARCHAR"/>
        <result column="unit_price" property="unitPrice" jdbcType="NUMERIC"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="gross_weight" property="grossWeight" jdbcType="NUMERIC"/>
        <result column="net_weight" property="netWeight" jdbcType="NUMERIC"/>
        <result column="parent_list_id" property="parentListId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.goods_name, 
            t.goods_desc, 
            t.invoice_name, 
            t.quantity, 
            t.unit, 
            t.package_quantity, 
            t.package_style, 
            t.unit_price, 
            t.amount, 
            t.gross_weight, 
            t.net_weight, 
            t.parent_list_id
    </sql>

    <sql id="condition">
        t.parent_id = #{parentId}
    </sql>

    <insert id="insertList">
        insert into t_biz_export_goods_sell_list (
            id,
            business_type,
            data_state,
            version_no,
            trade_code,
            sys_org_code,
            parent_id,
            create_by,
            create_time,
            update_by,
            update_time,
            insert_user_name,
            update_user_name,
            extend1,
            extend2,
            extend3,
            extend4,
            extend5,
            extend6,
            extend7,
            extend8,
            extend9,
            extend10,
            goods_name,
            goods_desc,
            invoice_name,
            quantity,
            unit,
            package_quantity,
            package_style,
            unit_price,
            amount,
            gross_weight,
            net_weight,
            parent_list_id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.businessType},
                #{item.dataState},
                #{item.versionNo},
                #{item.tradeCode},
                #{item.sysOrgCode},
                #{item.parentId},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.insertUserName},
                #{item.updateUserName},
                #{item.extend1},
                #{item.extend2},
                #{item.extend3},
                #{item.extend4},
                #{item.extend5},
                #{item.extend6},
                #{item.extend7},
                #{item.extend8},
                #{item.extend9},
                #{item.extend10},
                #{item.goodsName},
                #{item.goodsDesc},
                #{item.invoiceName},
                #{item.quantity},
                #{item.unit},
                #{item.packageQuantity},
                #{item.packageStyle},
                #{item.unitPrice},
                #{item.amount},
                #{item.grossWeight},
                #{item.netWeight},
                #{item.parentListId}
            )
        </foreach>
    </insert>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizExportGoodsSellList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_export_goods_sell_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getListTotal" resultType="com.dcjet.cs.dto.dec.BizExportGoodsSellListTotal">
        select
            coalesce(sum(t.quantity), 0) as qtyTotal,
            coalesce(sum(t.amount), 0) as amountTotal,
            coalesce(sum(t.gross_weight), 0) as grossWeightTotal,
            coalesce(sum(t.net_weight), 0) as netWeightTotal
        from
            t_biz_export_goods_sell_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_export_goods_sell_list t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByParentId">
        delete from t_biz_export_goods_sell_list where parent_id = #{parentId};
    </delete>
</mapper>