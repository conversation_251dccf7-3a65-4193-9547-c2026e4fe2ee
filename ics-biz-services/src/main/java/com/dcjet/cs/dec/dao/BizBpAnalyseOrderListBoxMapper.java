package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBox;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * （第7条线）出料加工进口薄片-装箱列表Mapper
 */
public interface BizBpAnalyseOrderListBoxMapper extends Mapper<BizBpAnalyseOrderListBox>{

    /**
     * 查询获取数据
     * @param bizBpAnalyseOrderListBox
     * @return
     */
    List<BizBpAnalyseOrderListBox> getList(BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    List<BizBpAnalyseOrderListBox> getListByHeadId(@Param("parentId") String parentId);

    String getNoteByProductName(@Param("productName") String productName, @Param("parentId") String parentId);

    BigDecimal getMaxSerialNo(@Param("parentId") String parentId);

    BigDecimal getUsedBoxCountByProductName(BizBpAnalyseOrderListBox po);

    int updateRemainingBoxCount(BizBpAnalyseOrderListBox item);

    int deleteUpdateRemainingBoxCount(BizBpAnalyseOrderListBox item);

    int checkRemainingBoxCountByProductName(@Param("parentId") String parentId, @Param("productName") String productName);

    BigDecimal getUsedContainerCountByProductName(BizBpAnalyseOrderListBox po);

    int deleteByParentIds(@Param("list") List<String> sids);
}