package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 第9条线-非国营贸易出口辅料-出货信息表头
 *
 * <AUTHOR>
 * @date 2025-07-07 11:18:39
 */
@Getter
@Setter
@Table(name = "t_biz_export_goods_head")
public class BizExportGoodsHead implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     * uuid
     * 必填
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 业务类型
     * 字符类型(240)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级id
     * uuid
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(200)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private String createTimeTo;

    /**
     * 更新人
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 出货单号
     * 字符类型(120)
     * 必填
     */
    @Column(name = "export_no")
    private String exportNo;

    /**
     * 合同号
     * 字符类型(120)
     * 必填
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 客户
     * 字符类型(400)
     * 必填
     */
    @Column(name = "customer")
    private String customer;

    /**
     * 客户地址
     * 字符类型(120)
     * 必填
     */
    @Column(name = "customer_address")
    private String customerAddress;

    /**
     * 供应商
     * 字符类型(400)
     * 必填
     */
    @Column(name = "supplier")
    private String supplier;

    /**
     * 贸易国别
     * 字符类型(120)
     * 必填
     */
    @Column(name = "trade_country")
    private String tradeCountry;

    /**
     * 经营单位
     * 字符类型(400)
     * 必填
     */
    @Column(name = "manage_unit")
    private String manageUnit;

    /**
     * 付款方式
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "payment_type")
    private String paymentType;

    /**
     * 币种
     * 字符类型(20)
     * 必填
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 运输方式
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "transport_type")
    private String transportType;

    /**
     * 价格条款
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "price_terms")
    private String priceTerms;

    /**
     * 价格条款对应港口
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "price_terms_port")
    private String priceTermsPort;

    /**
     * 发货单位
     * 字符类型(120)
     * 必填
     */
    @Column(name = "delivery_unit")
    private String deliveryUnit;

    /**
     * 包装种类
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "package_type")
    private String packageType;

    /**
     * 包装数量
     * 数值类型(10,0)
     * 非必填
     */
    @Column(name = "package_num")
    private BigDecimal packageNum;

    /**
     * 发货单位所在地
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "delivery_unit_location")
    private String deliveryUnitLocation;

    /**
     * 装运人shipper
     * 字符类型(600)
     * 非必填
     */
    @Column(name = "shipper")
    private String shipper;

    /**
     * 收货人consignee
     * 字符类型(600)
     * 非必填
     */
    @Column(name = "consignee")
    private String consignee;

    /**
     * 通知人notify party
     * 字符类型(600)
     * 非必填
     */
    @Column(name = "notify_party")
    private String notifyParty;

    /**
     * 总毛重
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 总净重
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "net_weight")
    private BigDecimal netWeight;

    /**
     * 总皮重
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "tare_weight")
    private BigDecimal tareWeight;

    /**
     * 唛头
     * 字符类型(600)
     * 非必填
     */
    @Column(name = "mark")
    private String mark;

    /**
     * 装运港
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "port_of_shipment")
    private String portOfShipment;

    /**
     * 目的地/港
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "port_of_destination")
    private String portOfDestination;

    /**
     * 装运期限
     * date
     * 非必填
     */
    @Column(name = "shipment_date")
    private Date shipmentDate;

    /**
     * 险别
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "insurance_type")
    private String insuranceType;

    /**
     * 保费币种
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "insurance_currency")
    private String insuranceCurrency;

    /**
     * 投保加成%
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "insurance_add_rate")
    private BigDecimal insuranceAddRate;

    /**
     * 保费费率(%)
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "insurance_rate")
    private BigDecimal insuranceRate;

    /**
     * 保险费
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "insurance_fee")
    private BigDecimal insuranceFee;

    /**
     * 投保人
     * 字符类型(400)
     * 必填
     */
    @Column(name = "insurer")
    private String insurer;

    /**
     * 运费
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "freight")
    private BigDecimal freight;

    /**
     * 运费币种
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "freight_currency")
    private String freightCurrency;

    /**
     * 仓储地址
     * 字符类型(600)
     * 非必填
     */
    @Column(name = "warehouse_address")
    private String warehouseAddress;

    /**
     * 联系人
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "contact_person")
    private String contactPerson;

    /**
     * 联系电话
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 备注
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 发送报关
     * 字符类型(20)
     * 必填
     */
    @Column(name = "send_customs")
    private String sendCustoms;

    /**
     * 确认时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "confirm_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    /**
     * 确认时间-开始时间
     */
    @Transient
    private String confirmTimeFrom;

    /**
     * 确认时间-结束时间
     */
    @Transient
    private String confirmTimeTo;


    /**
     * 准运证编号，用户录入
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "transport_permit_no")
    private String transportPermitNo;

    /**
     * 准运证申办日期，用户录入
     * 日期类型(0)
     * 非必填
     */
    @Column(name = "transport_permit_apply_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transportPermitApplyDate;

    /**
     * 准运证申办日期，用户录入-开始时间
     */
    @Transient
    private String transportPermitApplyDateFrom;

    /**
     * 准运证申办日期，用户录入-结束时间
     */
    @Transient
    private String transportPermitApplyDateTo;

    /**
     * 到货确认日期，用户录入
     * 日期类型(0)
     * 非必填
     */
    @Column(name = "arrival_confirm_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arrivalConfirmDate;

    /**
     * 到货确认日期，用户录入-开始时间
     */
    @Transient
    private String arrivalConfirmDateFrom;

    /**
     * 到货确认日期，用户录入-结束时间
     */
    @Transient
    private String arrivalConfirmDateTo;



    /**
     * 外销发票确认时间 确认时间
     * 字符类型(36)
     */
    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date invoiceConfirmTime;


    /**
     * 外销发票状态
     */
    @Transient
    private String invoiceDataState;


    /**
     * 外销发票 是否红冲
     */
    @Transient
    private String invoiceIsRedFlush;


    /**
     * 外销发票 发送财务系统
     */
    @Transient
    private String invoiceSendFinance;


    /**
     * 表体金额汇总
     */
    @Transient
    private BigDecimal listTotal;


    /**
     * 审核状态
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "APPROVAL_STATUS")
    private String approvalStatus;



    @Override
    public String toString() {
        return "第9条线-非国营贸易出口辅料-出货信息表头{" +
                "主键id='" + id + '\'' +
                "业务类型='" + businessType + '\'' +
                "数据状态='" + dataState + '\'' +
                "版本号='" + versionNo + '\'' +
                "企业10位编码='" + tradeCode + '\'' +
                "组织机构代码='" + sysOrgCode + '\'' +
                "父级id='" + parentId + '\'' +
                "创建人='" + createBy + '\'' +
                "创建时间='" + createTime + '\'' +
                "更新人='" + updateBy + '\'' +
                "更新时间='" + updateTime + '\'' +
                "插入用户名='" + insertUserName + '\'' +
                "更新用户名='" + updateUserName + '\'' +
                "扩展字段1='" + extend1 + '\'' +
                "扩展字段2='" + extend2 + '\'' +
                "扩展字段3='" + extend3 + '\'' +
                "扩展字段4='" + extend4 + '\'' +
                "扩展字段5='" + extend5 + '\'' +
                "扩展字段6='" + extend6 + '\'' +
                "扩展字段7='" + extend7 + '\'' +
                "扩展字段8='" + extend8 + '\'' +
                "扩展字段9='" + extend9 + '\'' +
                "扩展字段10='" + extend10 + '\'' +
                "出货单号='" + exportNo + '\'' +
                "合同号='" + contractNo + '\'' +
                "客户='" + customer + '\'' +
                "客户地址='" + customerAddress + '\'' +
                "供应商='" + supplier + '\'' +
                "贸易国别='" + tradeCountry + '\'' +
                "经营单位='" + manageUnit + '\'' +
                "付款方式='" + paymentType + '\'' +
                "币种='" + currency + '\'' +
                "运输方式='" + transportType + '\'' +
                "价格条款='" + priceTerms + '\'' +
                "价格条款对应港口='" + priceTermsPort + '\'' +
                "发货单位='" + deliveryUnit + '\'' +
                "包装种类='" + packageType + '\'' +
                "包装数量='" + packageNum + '\'' +
                "发货单位所在地='" + deliveryUnitLocation + '\'' +
                "装运人shipper='" + shipper + '\'' +
                "收货人consignee='" + consignee + '\'' +
                "通知人notify party='" + notifyParty + '\'' +
                "总毛重='" + grossWeight + '\'' +
                "总净重='" + netWeight + '\'' +
                "总皮重='" + tareWeight + '\'' +
                "唛头='" + mark + '\'' +
                "装运港='" + portOfShipment + '\'' +
                "目的地/港='" + portOfDestination + '\'' +
                "装运期限='" + shipmentDate + '\'' +
                "险别='" + insuranceType + '\'' +
                "保费币种='" + insuranceCurrency + '\'' +
                "投保加成%='" + insuranceAddRate + '\'' +
                "保费费率(%)='" + insuranceRate + '\'' +
                "保险费='" + insuranceFee + '\'' +
                "投保人='" + insurer + '\'' +
                "运费='" + freight + '\'' +
                "运费币种='" + freightCurrency + '\'' +
                "仓储地址='" + warehouseAddress + '\'' +
                "联系人='" + contactPerson + '\'' +
                "联系电话='" + contactPhone + '\'' +
                "备注='" + remark + '\'' +
                "发送报关='" + sendCustoms + '\'' +
                "确认时间='" + confirmTime + '\'' +
                "准运证编号，用户录入='" + transportPermitNo + '\'' +
                "准运证申办日期，用户录入='" + transportPermitApplyDate + '\'' +
                "到货确认日期，用户录入='" + arrivalConfirmDate + '\'' +
                '}';
    }
    @Transient
    private String purchaseNo;
    @Transient
    private String curr;
    @Transient
    private  BigDecimal decTotalToList;
    @Transient
    private  BigDecimal qtyToList;
    @Transient
    private  String unitToList;
    @Transient
    private  String merchandiseCategoriesToList;



    /**
     * 装箱说明
     */
    @Column(name = "BOX_DESC")
    private String boxDesc;


    /**
     * 运输工具名称
     */
    @Column(name = "TRANSPORTATION_TOOLS_NAME")
    private String transportationToolsName;

    /**
     * 开航日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "SAILING_DATE")
    private Date sailingDate;

    /**
     * 开航日期-开始时间
     */
    @Transient
    private String sailingDateFrom;

    /**
     * 开航日期-结束时间
     */
    @Transient
    private String sailingDateTo;


    /**
     * 序号
     */
    @Column(name = "SERIAL_NO")
    private BigDecimal serialNo;


}