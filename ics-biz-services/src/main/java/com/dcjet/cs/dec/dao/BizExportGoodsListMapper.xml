<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizExportGoodsListMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizExportGoodsList">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="product_desc" property="productDesc" jdbcType="VARCHAR"/>
        <result column="qty" property="qty" jdbcType="NUMERIC"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="NUMERIC"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="box_start_no" property="boxStartNo" jdbcType="NUMERIC"/>
        <result column="end_start_no" property="endStartNo" jdbcType="NUMERIC"/>
        <result column="package_style" property="packageStyle" jdbcType="VARCHAR"/>
        <result column="gross_weight" property="grossWeight" jdbcType="NUMERIC"/>
        <result column="net_weight" property="netWeight" jdbcType="NUMERIC"/>
        <result column="tare_weight" property="tareWeight" jdbcType="NUMERIC"/>
        <result column="e_length" property="eLength" jdbcType="NUMERIC"/>
        <result column="e_width" property="eWidth" jdbcType="NUMERIC"/>
        <result column="e_height" property="eHeight" jdbcType="NUMERIC"/>
        <result column="specification" property="specification" jdbcType="VARCHAR"/>
        <result column="qty_j" property="qtyJ" jdbcType="NUMERIC"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.product_name, 
            t.product_desc, 
            t.qty, 
            t.unit, 
            t.price, 
            t.amount, 
            t.box_start_no, 
            t.end_start_no, 
            t.package_style, 
            t.gross_weight, 
            t.net_weight, 
            t.tare_weight, 
            t.e_length,
            t.e_width, 
            t.e_height,
            t.specification,
            t.qty_j
    </sql>

    <sql id="condition">
        t.parent_id = #{parentId}
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizExportGoodsList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_export_goods_list t
        <where>
            <include refid="condition"></include>
        </where>
        order by create_time desc
    </select>
    <select id="getSummary" resultType="com.dcjet.cs.dto.dec.BizExportGoodsListSummaryDto">
        SELECT
            sum(t.qty) as qtyTotal,
            sum(t.amount) as totalAmount,
            sum(t.gross_weight) as grossTotal,
            sum(t.net_weight) as netTotal,
            sum(t.tare_weight) as traeTotal
        FROM
            t_biz_export_goods_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <select id="getListByHeadId" resultType="com.dcjet.cs.dec.model.BizExportGoodsList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_export_goods_list t
        <where>
            t.parent_id = #{parentId}
        </where>
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_export_goods_list t where t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>