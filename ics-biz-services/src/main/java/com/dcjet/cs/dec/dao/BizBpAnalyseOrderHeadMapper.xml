<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizBpAnalyseOrderHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizBpAnalyseOrderHead">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="receiver" property="receiver" jdbcType="VARCHAR"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="analysis_no" property="analysisNo" jdbcType="VARCHAR"/>
        <result column="trade_country" property="tradeCountry" jdbcType="VARCHAR"/>
        <result column="destination" property="destination" jdbcType="VARCHAR"/>
        <result column="consume_country" property="consumeCountry" jdbcType="VARCHAR"/>
        <result column="is_transit" property="isTransit" jdbcType="VARCHAR"/>
        <result column="shipment_deadline" property="shipmentDeadline" jdbcType="TIMESTAMP"/>
        <result column="shipper" property="shipper" jdbcType="VARCHAR"/>
        <result column="consignee" property="consignee" jdbcType="VARCHAR"/>
        <result column="notify_party" property="notifyParty" jdbcType="VARCHAR"/>
        <result column="freight" property="freight" jdbcType="VARCHAR"/>
        <result column="process_account_no" property="processAccountNo" jdbcType="VARCHAR"/>
        <result column="packing_time" property="packingTime" jdbcType="TIMESTAMP"/>
        <result column="packing_time_to" property="packingTimeTo" jdbcType="TIMESTAMP"/>
        <result column="warehouse_address" property="warehouseAddress" jdbcType="VARCHAR"/>
        <result column="ship_name_voyage" property="shipNameVoyage" jdbcType="VARCHAR"/>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"/>
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="bill_date" property="billDate" jdbcType="TIMESTAMP"/>
        <result column="appr_status" property="apprStatus" jdbcType="VARCHAR"/>
        <result column="curr" property="curr" jdbcType="VARCHAR"/>
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="total" property="total" jdbcType="NUMERIC"/>
        <result column="customer_code" property="customerCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            coalesce(t.update_user_name, t.insert_user_name) as create_by,
            coalesce(t.update_time, t.create_time) as create_time,
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.receiver, 
            t.contract_no, 
            t.analysis_no, 
            t.trade_country, 
            t.destination, 
            t.consume_country, 
            t.is_transit, 
            t.shipment_deadline, 
            t.shipper, 
            t.consignee, 
            t.notify_party, 
            t.freight, 
            t.process_account_no, 
            t.packing_time, 
            t.packing_time_to, 
            t.warehouse_address, 
            t.ship_name_voyage, 
            t.bill_no, 
            t.contact_phone, 
            t.bill_date,
            t.appr_status,
            t.curr,
            t.confirm_time,
            temp.total,
            t.customer_code
    </sql>

    <sql id="condition">

    <if test="dataState != null and dataState != ''">
        and t.data_state = #{dataState}
    </if>

    <if test="contractNo != null and contractNo != ''">
        and t.contract_no like '%' ||  #{contractNo} || '%'
    </if>

    <if test="analysisNo != null and analysisNo != ''">
        and t.analysis_no like '%' ||  #{analysisNo} || '%'
    </if>

    <if test="customerCode != null and customerCode != ''">
        and t.customer_code = #{customerCode}
    </if>
    <if test="createTimeFrom != null and createTimeFrom != ''">
        <![CDATA[ and coalesce(t.update_time,t.create_time) >= TO_DATE(#{createTimeFrom}, 'yyyy-mm-dd')]]>
    </if>
    <if test="createTimeTo != null and createTimeTo != ''">
        <![CDATA[ and coalesce(t.update_time,t.create_time) <  TO_DATE(#{createTimeTo}, 'yyyy-mm-dd') + 1 ]]>
    </if>

    <if test="shipmentDeadlineFrom != null and shipmentDeadlineFrom != ''">
        <![CDATA[ and t.shipment_deadline >= TO_DATE(#{shipmentDeadlineFrom}, 'yyyy-mm-dd')]]>
    </if>
    <if test="shipmentDeadlineTo != null and shipmentDeadlineTo != ''">
        <![CDATA[ and t.shipment_deadline <  TO_DATE(#{shipmentDeadlineTo}, 'yyyy-mm-dd') + 1 ]]>
    </if>



    <if test="createBy!= null and createBy!= ''">
        and coalesce(t.update_by,t.create_by)  = #{createBy}
    </if>


    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizBpAnalyseOrderHead">
        WITH temp_sum as (
            select
                parent_id as parent_id,
                sum(amount)  as total
            from  t_biz_bp_analyse_order_list  group by PARENT_ID
        )
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_bp_analyse_order_head t
        left join temp_sum temp on t.id = temp.parent_id
        <where>
            <include refid="condition"></include>
        </where>
        order by coalesce(t.update_time, t.create_time) desc
    </select>
    <select id="getListByContractId" resultType="com.dcjet.cs.dec.model.BizBpAnalyseOrderHead">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_biz_bp_analyse_order_head t where t.parent_id = #{contractId}
    </select>
    <select id="checkAnalyseOrderCode" resultType="java.lang.Integer">
        select
            count(1)
        from
            t_biz_bp_analyse_order_head t
        where t.analysis_no = #{analysisNo} and t.trade_code = #{tradeCode}
        <if test="id != null and id != ''">
            and t.id != #{id}
        </if>
    </select>
    <select id="getTotalAmountByHeadId" resultType="java.math.BigDecimal">
        select COALESCE(sum(QUANTITY),0) from T_BIZ_BP_ANALYSE_ORDER_LIST where parent_id = #{id}
    </select>
    <select id="getCurrentCustomerList" resultType="java.util.Map">
        select
            distinct MERCHANT_CODE as "value",
                     MERCHANT_NAME_CN as "label"
        from
            T_BIZ_MERCHANT
        where
            TRADE_CODE = #{tradeCode}
          and MERCHANT_CODE in (
            select
                DISTINCT CUSTOMER_CODE
            from
                t_biz_bp_analyse_order_head
            where trade_code = #{tradeCode}
        )
    </select>

    <select id="getCurrentCreateByList" resultType="java.util.Map">
        select distinct   coalesce(update_by, create_by) as "value",
                          coalesce(update_user_name, insert_user_name) as "label"
        from t_biz_bp_analyse_order_head
        where trade_code = #{tradeCode}
    </select>


    <select id="getContractNoList" resultType="com.dcjet.cs.dec.model.BizBpAnalyseOrderHead">

        select
            a.id as "id",
            a.contract_no as "contractNo",
            a.buyer as "customerCode",
            a.sign_date as "signDate",
            a.total_money as "totalMoney"
        from
            (
                select
                    h.ID,
                    h.CONTRACT_NO,
                    (
                        h.BUYER || ' ' || (
                            select
                                m.MERCHANT_NAME_CN
                            from
                                T_BIZ_MERCHANT m
                            where
                                m.MERCHANT_CODE = h.BUYER
                              and m.TRADE_CODE = h.TRADE_CODE
                            limit
                            1
                        )
                    ) as buyer,
                    h.SIGN_DATE,
                    (
                        select
                            COALESCE(sum(MONEY_AMOUNT), 0)
                        from
                            T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST l
                        where
                            l.HEAD_ID = h.ID
                    ) as total_money
                from
                     T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD h
                where
                    h.DATA_STATUS = '1' and h.TRADE_CODE = #{tradeCode}
                    <if test="contractNo!= null and contractNo!= ''">
                        and h.contract_no like '%' ||  #{contractNo} || '%'
                    </if>
            ) a
            where
                a.total_money > 0


    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_bp_analyse_order_list t where t.parent_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        delete from  t_biz_bp_analyse_order_list_box t where t.parent_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        delete from  t_biz_bp_analyse_order_head t where t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </delete>

    <select id="getPackingSummaryInfo" resultType="java.lang.String">
        select listagg(sum(CONTAINER_COUNT) || '*' || CONTAINER_SPEC, ';')  from T_BIZ_BP_ANALYSE_ORDER_LIST_BOX
        where PARENT_ID = #{id}
        group by CONTAINER_SPEC;
    </select>
    <select id="getPackingInfo" resultType="java.lang.String">
        select listagg(sum(CONTAINER_COUNT) || '*' || CONTAINER_SPEC || '装' ||sum(BOX_COUNT) ||'箱', ';')  from T_BIZ_BP_ANALYSE_ORDER_LIST_BOX
        where PARENT_ID = #{id}
        group by CONTAINER_SPEC;
    </select>
</mapper>