package com.dcjet.cs.dec.service;



import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizENonStateAuxmatAggrContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.dcjet.cs.dec.dao.BizExportGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsListMapper;
import com.dcjet.cs.dec.mapper.BizExportGoodsListDtoMapper;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dec.model.BizExportGoodsList;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListDto;
import com.dcjet.cs.dto.dec.*;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizExportGoodsList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 13:22:20
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizExportGoodsListService extends BizExportGoodsExtractService<BizExportGoodsList> {

    private static final Logger log = LoggerFactory.getLogger(BizExportGoodsListService.class);

    @Resource
    private BizExportGoodsListMapper bizExportGoodsListMapper;

    @Resource
    private BizExportGoodsListDtoMapper bizExportGoodsListDtoMapper;

    @Override
    public Mapper<BizExportGoodsList> getMapper() {
        return bizExportGoodsListMapper;
    }

    @Resource
    private BizExportGoodsHeadMapper goodsHeadMapper;



    @Resource
    private BizExportGoodsHeadMapper bizExportGoodsHeadMapper;

    @Resource
    private BizENonStateAuxmatAggrContractListMapper bizENonStateAuxmatAggrContractListMapper;

    @Resource
    private BizENonStateAuxmatAggrContractListDtoMapper bizENonStateAuxmatAggrContractListDtoMapper;


    /**
     * 获取分页信息
     *
     * @param bizExportGoodsListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizExportGoodsListDto>> getListPaged(BizExportGoodsListParam bizExportGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizExportGoodsList bizExportGoodsList = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        bizExportGoodsList.setTradeCode(userInfo.getCompany());
        Page<BizExportGoodsList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizExportGoodsListMapper.getList( bizExportGoodsList));
        // 将PO转为DTO返回给前端
        List<BizExportGoodsListDto> bizExportGoodsListDtoList = page.getResult().stream()
            .map(bizExportGoodsListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizExportGoodsListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizExportGoodsListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsListDto insert(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        BizExportGoodsList bizExportGoodsList = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizExportGoodsList.setId(sid);
        bizExportGoodsList.setCreateBy(userInfo.getUserNo());
        bizExportGoodsList.setInsertUserName(userInfo.getUserName());
        bizExportGoodsList.setCreateTime(new Date());
        bizExportGoodsList.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizExportGoodsListMapper.insert(bizExportGoodsList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizExportGoodsListDtoMapper.toDto(bizExportGoodsList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizExportGoodsListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsListDto update(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        BizExportGoodsList bizExportGoodsList = bizExportGoodsListMapper.selectByPrimaryKey(bizExportGoodsListParam.getId());
        bizExportGoodsListDtoMapper.updatePo(bizExportGoodsListParam, bizExportGoodsList);
        bizExportGoodsList.setUpdateBy(userInfo.getUserNo());
        bizExportGoodsList.setUpdateUserName(userInfo.getUserName());
        bizExportGoodsList.setUpdateTime(new Date());

        // 更新数据
        int update = bizExportGoodsListMapper.updateByPrimaryKey(bizExportGoodsList);
        return update > 0 ? bizExportGoodsListDtoMapper.toDto(bizExportGoodsList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizExportGoodsListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizExportGoodsListDto> selectAll(BizExportGoodsListParam exportParam, UserInfoToken userInfo) {
        BizExportGoodsList bizExportGoodsList = bizExportGoodsListDtoMapper.toPo(exportParam);
        bizExportGoodsList.setTradeCode(userInfo.getCompany());
        List<BizExportGoodsListDto> bizExportGoodsListDtos = new ArrayList<>();
        List<BizExportGoodsList> bizExportGoodsListLists = bizExportGoodsListMapper.getList(bizExportGoodsList);
        if (CollectionUtils.isNotEmpty(bizExportGoodsListLists)) {
           bizExportGoodsListDtos = bizExportGoodsListLists.stream().map(head -> {
                    BizExportGoodsListDto dto =  bizExportGoodsListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizExportGoodsListDtos;
    }

    public ResultObject<BizExportGoodsListDto> getListById(String id, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "查询成功");
        BizExportGoodsList bizExportGoodsList = bizExportGoodsListMapper.selectByPrimaryKey(id);
        if (bizExportGoodsList != null) {
            BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(bizExportGoodsList);
            resultObject.setData(dto);
        }
        return resultObject;
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizExportGoodsListDto> updateNumOrPrice(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        BizExportGoodsList bizExportGoodsList = bizExportGoodsListMapper.selectByPrimaryKey(bizExportGoodsListParam.getId());
        bizExportGoodsListDtoMapper.updatePo(bizExportGoodsListParam, bizExportGoodsList);
        // 判断数量是否为0或者null
        if (bizExportGoodsList.getQty() == null || bizExportGoodsList.getQty().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400,"数量不能为空或者0");
        }
        // 判断单价是否为0或者null
        if (bizExportGoodsList.getPrice() == null || bizExportGoodsList.getPrice().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400,"单价不能为空或者0");
        }

        // 计算金额
        bizExportGoodsList.setAmount(bizExportGoodsList.getQty().multiply(bizExportGoodsList.getPrice()).setScale(4, BigDecimal.ROUND_HALF_UP));


        bizExportGoodsList.setUpdateBy(userInfo.getUserNo());
        bizExportGoodsList.setUpdateUserName(userInfo.getUserName());
        bizExportGoodsList.setUpdateTime(new Date());
        int update = bizExportGoodsListMapper.updateByPrimaryKey(bizExportGoodsList);
        BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(bizExportGoodsList);
        return ResultObject.createInstance(true, "更新成功",dto);
    }



    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizExportGoodsListDto> updateStartBoxNo(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        // 校验参数
        if (bizExportGoodsListParam.getBoxStartNo() == null) {
            throw new ErrorException(400,"起始箱号不能为空");
        }
        // 修改
        BizExportGoodsList po = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateUserName(userInfo.getUserName());
        po.setUpdateTime(new Date());
        int update = bizExportGoodsListMapper.updateByPrimaryKey(po);
        BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(po);
        return ResultObject.createInstance(true, "更新成功",dto);
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizExportGoodsListDto> updateEndBoxNo( BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        // 校验参数
        if (bizExportGoodsListParam.getEndStartNo() == null) {
            throw new ErrorException(400,"结束箱号不能为空");
        }
        // 修改
        BizExportGoodsList po = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateUserName(userInfo.getUserName());
        po.setUpdateTime(new Date());
        int update = bizExportGoodsListMapper.updateByPrimaryKey(po);
        BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(po);
        return ResultObject.createInstance(true, "更新成功",dto);
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizExportGoodsListDto> updateGrossWeight(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        // 校验参数
        if (bizExportGoodsListParam.getGrossWeight() == null) {
            throw new ErrorException(400,"毛重不能为空");
        }
        // 修改
        BizExportGoodsList po = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateUserName(userInfo.getUserName());
        po.setUpdateTime(new Date());

        // 皮重 =  毛重 - 净重
        po.setTareWeight(po.getGrossWeight().subtract(Optional.ofNullable(po.getNetWeight()).orElse(BigDecimal.ZERO)));


        int update = bizExportGoodsListMapper.updateByPrimaryKey(po);
        BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(po);
        return ResultObject.createInstance(true, "更新成功",dto);
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizExportGoodsListDto> updateNetWeight(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        // 校验参数
        if (bizExportGoodsListParam.getNetWeight() == null) {
            throw new ErrorException(400,"净重不能为空");
        }
        // 修改
        BizExportGoodsList po = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateUserName(userInfo.getUserName());
        po.setUpdateTime(new Date());
        // 皮重 =  毛重 - 净重
        po.setTareWeight(Optional.ofNullable(po.getGrossWeight()).orElse(BigDecimal.ZERO).subtract(po.getNetWeight()));
        int update = bizExportGoodsListMapper.updateByPrimaryKey(po);
        BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(po);
        return ResultObject.createInstance(true, "更新成功",dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizExportGoodsListDto> updateGoodsLengthHeightWidth(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        // 修改
        BizExportGoodsList po = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateUserName(userInfo.getUserName());
        po.setUpdateTime(new Date());
        int update = bizExportGoodsListMapper.updateByPrimaryKey(po);
        BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(po);
        return ResultObject.createInstance(true, "更新成功",dto);
    }

    /**
     * 获取出货信息表体汇总信息
     * @param parentId 表头Id
     * @param userInfo 用户信息
     * @return 出货信息表体汇总信息
     */
    public ResultObject<BizExportGoodsListSummaryDto> getSummary(String parentId, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功");
        if(StringUtils.isBlank(parentId)){
            throw new ErrorException(400,"表头Id不能为空");
        }
        BizExportGoodsListSummaryDto summaryDto = bizExportGoodsListMapper.getSummary(parentId);
        resultObject.setData(summaryDto);
        return resultObject;
    }


    /**
     * 获取可以提取外商合同信息
     * @param param 可提取信息
     * @param userInfo 用户信息
     * @return 可提取外商合同信息
     */
    public ResultObject getExtractContractList(BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功");
        if(StringUtils.isBlank(param.getId())){
            throw new ErrorException(400,"表头Id不能为空");
        }
        // 合同号不能为空
        if(StringUtils.isBlank(param.getContractNo())){
            throw new ErrorException(400,"合同号不能为空");
        }

        // 根据合同号 + trade_Code 找出当前可以提取的数据，如果表体数量提取完了就不允许再次提取

        // 1.获取外商合同表体数据
        // 2.获取已经提取出货信息表体数据
        // 3.根据商品名称统计 判断当前商品的数量是否已经提取完毕，如果已经提取完毕，从集合中删除，如果没有提取完毕，将数量设置成剩余数量


        //        BizExportGoodsList po = bizExportGoodsListMapper.selectOne(new BizExportGoodsList().set(param.getContractNo()).setTradeCode(param.getTradeCode()));
        //        if(po == null){
        //            throw new ErrorException(400,"合同号 + trade_Code 不存在");
        //        }
        //        // 表体数量不能超过表头数量
        ////        if(po.getListNum().compareTo(po.getTotalNum()) > 0){
        ////            throw new ErrorException(400,"表体数量不能超过表头数量");
        ////        }














        return resultObject;
    }

    public ResultObject<BizExportGoodsListDto> updateQtyJ(BizExportGoodsListParam bizExportGoodsListParam, UserInfoToken userInfo) {
        // 校验参数
        if (bizExportGoodsListParam.getQtyJ() == null) {
            throw new ErrorException(400,"数量（卷）不能为空");
        }
        // 修改
        BizExportGoodsList po = bizExportGoodsListDtoMapper.toPo(bizExportGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateUserName(userInfo.getUserName());
        po.setUpdateTime(new Date());
        int update = bizExportGoodsListMapper.updateByPrimaryKey(po);
        BizExportGoodsListDto dto = bizExportGoodsListDtoMapper.toDto(po);
        return ResultObject.createInstance(true, "更新成功",dto);
    }

    /**
     * 功能描述:根据合同编号查询数据 可以提取的表体数据
     * @param bizExportGoodsListBoxParam 查询参数 主要是parent_id
     * @param userInfo 用户信息
     * @return <List<BizENonStateAuxmatAggrContractListDto>> 外商合同列表
     */
    public ResultObject<List<BizENonStateAuxmatAggrContractListDto>> getListByContractNo(BizExportGoodsListBoxParam bizExportGoodsListBoxParam, UserInfoToken userInfo) {
        ResultObject<List<BizENonStateAuxmatAggrContractListDto>> resultObject = ResultObject.createInstance(true, "查询成功");
        if (StringUtils.isNotBlank(bizExportGoodsListBoxParam.getParentId())) {
            // 获取表头合同号
            BizExportGoodsHead bizExportGoodsListBox = bizExportGoodsHeadMapper.selectByPrimaryKey(bizExportGoodsListBoxParam.getParentId());
            if (bizExportGoodsListBox != null && StringUtils.isNotBlank(bizExportGoodsListBox.getContractNo())) {
                // 根据 合同号 + tradeCode 查找外商合同表体确认数据
                List<BizENonStateAuxmatAggrContractList>  list = bizENonStateAuxmatAggrContractListMapper.getListByContractNo(bizExportGoodsListBox.getContractNo(),userInfo.getCompany());
                List<BizENonStateAuxmatAggrContractListDto> collect = list.stream().map(
                        item -> {
                            BizENonStateAuxmatAggrContractListDto dto = bizENonStateAuxmatAggrContractListDtoMapper.toDto(item);
                            return dto;
                        }
                ).collect(Collectors.toList());
                resultObject.setData(collect);
            }
        }
        return resultObject;
    }

    /**
     * 功能描述:根据合同编号查询数据 可以提取的表体数据
     * @param param  提取外商合同列表数据
     * @param userInfo 用户信息
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject extractContractList(ExtractContractListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "提取成功");
        if (CollectionUtils.isNotEmpty(param.getContractList())){
            try {
                List<BizENonStateAuxmatAggrContractList> list = param.getContractList().stream().map(
                        item -> {
                            BizENonStateAuxmatAggrContractList po = bizENonStateAuxmatAggrContractListDtoMapper.toPo(item);
                            return po;
                        }
                ).collect(Collectors.toList());
                String parentId = param.getParentId();
                for (BizENonStateAuxmatAggrContractList contractItem : list) {
                    BizExportGoodsList goodsList = new BizExportGoodsList();
                    setExportGoodsList(contractItem,goodsList,parentId,userInfo);
                    // 插入数据
                    bizExportGoodsListMapper.insert(goodsList);
                }
            } catch (Exception e) {
                throw new ErrorException(400,"新增明细失败");
            }
        }
        return resultObject;
    }
}