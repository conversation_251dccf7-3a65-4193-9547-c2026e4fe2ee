package com.dcjet.cs.dec.dao;


import com.dcjet.cs.params.model.EnterpriseRate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface BizSmokeCommonMapper {

    /**
     * 获取进货单表体 通用CodeValue键值对数据集合
     * @param tradeCode 企业代码
     * @param type 企业自定义参数类型
     * @return 返回List<Map<String,String>>
     */
    List<Map<String, String>> getListCommonKeyValueList(@Param("tradeCode") String tradeCode,@Param("type")String type);


    /**
     *  获取当前月汇率
     * @param month 格式：yyyyMM
     * @param tradeCode 企业代码
     * @param curr 货币代码
     * @return 根据当前月份+币种关联【企业自定义参数-企业汇率】取“汇率”栏位值，允许修改
     */
    EnterpriseRate getCurrentMonthRate(@Param("month") String month, @Param("tradeCode") String tradeCode, @Param("curr") String curr);


    /**
     * 获取当前企业 客户基础信息 通用CodeValue键值对数据集合
     * @param tradeCode 企业代码
     * @return 返回客户代码
     */
    List<Map<String, String>> getListCommonCustomerKeyValueList(@Param("tradeCode") String tradeCode);


    /**
     * 获取自定义币制 通用CodeValue键值对数据集合
     * @param tradeCode 企业代码
     * @param type 企业自定义参数类型
     * @return 返回List<Map<String,String>>
     */
    List<Map<String, String>> getListCommonCustomerCurrKeyValueList(@Param("tradeCode") String tradeCode, @Param("type") String type);

    BigDecimal getInsuranceRateByType(@Param("type") String type, @Param("tradeCode") String tradeCode);

    String getCustomerNameByCode(@Param("customerCode") String customerCode, @Param("tradeCode") String tradeCode);

    String getPriceNameByCode(@Param("priceCode") String priceCode, @Param("tradeCode") String tradeCode);

    BigDecimal getTotalByHeadId(@Param("headId") String headId);

    List<Map<String, String>> getListCommonCountryKeyValueList(@Param("tradeCode") String tradeCode, @Param("type") String type);

    List<Map<String, String>> getCurrList(@Param("tradeCode") String tradeCode);

    List<Map<String, String>> getListCommonInsuranceCategoryKeyValueList(@Param("tradeCode") String tradeCode, @Param("type") String type);

    List<Map<String, String>> getCustomerListByCodes(@Param("list") List<String> customerCodes, @Param("tradeCode") String tradeCode);
}
