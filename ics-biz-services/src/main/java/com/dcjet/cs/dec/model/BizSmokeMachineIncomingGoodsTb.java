package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 烟机设备-进货单-投保信息
 *
 * <AUTHOR>
 * @date 2025-07-04 21:29:36
 */
@Getter
@Setter
@Table(name = "t_biz_smoke_machine_incoming_goods_tb")
public class BizSmokeMachineIncomingGoodsTb implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     * 字符类型(80)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级ID
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(100)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * timestamp
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private String createTimeTo;

    /**
     * 更新人
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * timestamp
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 表头ID
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "head_id")
    private String headId;

    /**
     * 编号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "code_no")
    private String codeNo;

    /**
     * 保险公司
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "insurance_company")
    private String insuranceCompany;

    /**
     * 被保险人
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "insured_person")
    private String insuredPerson;

    /**
     * 发票抬头
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "invoice_title")
    private String invoiceTitle;

    /**
     * 运输工具名称
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "transport_name")
    private String transportName;

    /**
     * 开航日期
     * timestamp
     * 非必填
     */
    @Column(name = "departure_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date departureDate;

    /**
     * 开航日期-开始时间
     */
    @Transient
    private String departureDateFrom;

    /**
     * 开航日期-结束时间
     */
    @Transient
    private String departureDateTo;

    /**
     * 运输路线自
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "route_from")
    private String routeFrom;

    /**
     * 经
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "route_via")
    private String routeVia;

    /**
     * 至
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "route_to")
    private String routeTo;

    /**
     * 投保险别
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "insurance_type")
    private String insuranceType;

    /**
     * 币种
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 投保加成%
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "insurance_premium_rate")
    private BigDecimal insurancePremiumRate;

    /**
     * 保险金额
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "insurance_amount")
    private BigDecimal insuranceAmount;

    /**
     * 保险费率
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "insurance_rate")
    private BigDecimal insuranceRate;

    /**
     * 保费
     * 数值类型(19,2)
     * 非必填
     */
    @Column(name = "premium")
    private BigDecimal premium;

    /**
     * 投保日期
     * timestamp
     * 非必填
     */
    @Column(name = "insurance_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceDate;

    /**
     * 投保日期-开始时间
     */
    @Transient
    private String insuranceDateFrom;

    /**
     * 投保日期-结束时间
     */
    @Transient
    private String insuranceDateTo;

    /**
     * 备注
     * 字符类型(1000)
     * 非必填
     */
    @Column(name = "remark")
    private String remark;


    @Override
    public String toString() {
        return "烟机设备-进货单-投保信息{" +
                "主键ID='" + id + '\'' +
                "业务类型='" + businessType + '\'' +
                "数据状态='" + dataState + '\'' +
                "版本号='" + versionNo + '\'' +
                "企业10位编码='" + tradeCode + '\'' +
                "组织机构代码='" + sysOrgCode + '\'' +
                "父级ID='" + parentId + '\'' +
                "创建人='" + createBy + '\'' +
                "创建时间='" + createTime + '\'' +
                "更新人='" + updateBy + '\'' +
                "更新时间='" + updateTime + '\'' +
                "插入用户名='" + insertUserName + '\'' +
                "更新用户名='" + updateUserName + '\'' +
                "扩展字段1='" + extend1 + '\'' +
                "扩展字段2='" + extend2 + '\'' +
                "扩展字段3='" + extend3 + '\'' +
                "扩展字段4='" + extend4 + '\'' +
                "扩展字段5='" + extend5 + '\'' +
                "扩展字段6='" + extend6 + '\'' +
                "扩展字段7='" + extend7 + '\'' +
                "扩展字段8='" + extend8 + '\'' +
                "扩展字段9='" + extend9 + '\'' +
                "扩展字段10='" + extend10 + '\'' +
                "表头ID='" + headId + '\'' +
                "编号='" + codeNo + '\'' +
                "保险公司='" + insuranceCompany + '\'' +
                "被保险人='" + insuredPerson + '\'' +
                "发票抬头='" + invoiceTitle + '\'' +
                "运输工具名称='" + transportName + '\'' +
                "开航日期='" + departureDate + '\'' +
                "运输路线自='" + routeFrom + '\'' +
                "经='" + routeVia + '\'' +
                "至='" + routeTo + '\'' +
                "投保险别='" + insuranceType + '\'' +
                "币种='" + currency + '\'' +
                "投保加成%='" + insurancePremiumRate + '\'' +
                "保险金额='" + insuranceAmount + '\'' +
                "保险费率='" + insuranceRate + '\'' +
                "保费='" + premium + '\'' +
                "投保日期='" + insuranceDate + '\'' +
                "备注='" + remark + '\'' +
                '}';
    }
}