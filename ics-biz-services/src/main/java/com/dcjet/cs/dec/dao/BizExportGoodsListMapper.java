package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizExportGoodsList;
import com.dcjet.cs.dto.dec.BizExportGoodsListSummaryDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 第9条线-非国营贸易出口辅料-出货信息表体（商品信息）Mapper
 */
public interface BizExportGoodsListMapper extends Mapper<BizExportGoodsList>{


    /**
     * 查询获出货信息列表
     * @param bizExportGoodsList 查询条件对象，包含筛选条件字段（主要是parentId）
     * @return 商品信息实体对象列表，包含符合条件的所有表体商品数据
     */
    List<BizExportGoodsList> getList(BizExportGoodsList bizExportGoodsList);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    /**
     * 查询出货信息表体汇总信息
     * @param parentId 父级id
     * @return 出货信息表体汇总信息
     */
    BizExportGoodsListSummaryDto getSummary(@Param("parentId") String parentId);

    /**
     * 根据表头id查询表体数据
     * @param parentId 表头id
     * @return 表体数据列表
     */
    List<BizExportGoodsList> getListByHeadId(@Param("parentId") String parentId);

}