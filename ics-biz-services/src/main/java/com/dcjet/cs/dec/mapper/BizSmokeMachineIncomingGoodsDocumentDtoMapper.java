package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsDocument;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsDocumentDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsDocumentParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizSmokeMachineIncomingGoodsDocumentDto
 *
 * <AUTHOR>
 * @date 2025-07-04 21:29:58
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizSmokeMachineIncomingGoodsDocumentDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizSmokeMachineIncomingGoodsDocumentDto toDto(BizSmokeMachineIncomingGoodsDocument po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizSmokeMachineIncomingGoodsDocument toPo(BizSmokeMachineIncomingGoodsDocumentParam param);

    /**
     * 数据库原始数据更新
     * @param bizSmokeMachineIncomingGoodsDocumentParam
     * @param BizSmokeMachineIncomingGoodsDocument
     */
    void updatePo(BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam, @MappingTarget BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument);

    default void patchPo(BizSmokeMachineIncomingGoodsDocumentParam bizSmokeMachineIncomingGoodsDocumentParam , BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument) {
        // TODO 自行实现局部更新
    }
}