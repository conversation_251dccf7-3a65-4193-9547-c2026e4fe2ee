<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="DATA_STATE" property="dataState" jdbcType="VARCHAR"/>
        <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
        <result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR"/>
        <result column="PURCHASE_NO" property="purchaseNo" jdbcType="VARCHAR"/>
        <result column="CUSTOMER" property="customer" jdbcType="VARCHAR"/>
        <result column="SUPPLIER" property="supplier" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="PORT_OF_DEPARTURE" property="portOfDeparture" jdbcType="VARCHAR"/>
        <result column="DESTINATION" property="destination" jdbcType="VARCHAR"/>
        <result column="PAYMENT_METHOD" property="paymentMethod" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM" property="priceTerm" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM_PORT" property="priceTermPort" jdbcType="VARCHAR"/>
        <result column="VESSEL_VOYAGE" property="vesselVoyage" jdbcType="VARCHAR"/>
        <result column="SAILING_DATE" property="sailingDate" jdbcType="TIMESTAMP"/>
        <result column="EXPECTED_ARRIVAL_DATE" property="expectedArrivalDate" jdbcType="TIMESTAMP"/>
        <result column="SALES_DATE" property="salesDate" jdbcType="TIMESTAMP"/>
        <result column="CONTRACT_AMOUNT" property="contractAmount" jdbcType="NUMERIC"/>
        <result column="INSURANCE_RATE" property="insuranceRate" jdbcType="NUMERIC"/>
        <result column="INSURANCE_MARKUP" property="insuranceMarkup" jdbcType="NUMERIC"/>
        <result column="DOCUMENT_CREATOR" property="documentCreator" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_DATE" property="documentDate" jdbcType="TIMESTAMP"/>
        <result column="DOCUMENT_STATUS" property="documentStatus" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="APPROVAL_STATUS" property="approvalStatus" jdbcType="VARCHAR"/>
        <result column="DATE_OF_CONTRACT" property="dateOfContract" jdbcType="TIMESTAMP"/>
        <result column="IS_NEXT" property="isNext" jdbcType="VARCHAR"/>
        <result column="PURCHASE_CONTRACT_NO" property="purchaseContractNo" jdbcType="VARCHAR"/>
        <result column="ENTRY_NO" property="entryNo" jdbcType="VARCHAR"/>
        <result column="ENTRY_DATE" property="entryDate" jdbcType="TIMESTAMP"/>
        <result column="SERIAL_NO" property="serialNo" jdbcType="NUMERIC"/>
        <result column="SALES_DOCUMENT_STATUS" property="salesDocumentStatus" jdbcType="VARCHAR"/>
        <result column="SELL_CONTRACT_NO" property="sellContractNo" jdbcType="VARCHAR"/>
        <result column="CANCEL_DATE" property="cancelDate" jdbcType="TIMESTAMP"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.ID, 
            t.BUSINESS_TYPE, 
            t.DATA_STATE, 
            t.VERSION_NO, 
            t.TRADE_CODE, 
            t.SYS_ORG_CODE, 
            t.PARENT_ID, 
            COALESCE(t.UPDATE_USER_NAME, t.INSERT_USER_NAME) as "CREATE_BY",
            COALESCE(t.UPDATE_TIME,t.CREATE_TIME) as "CREATE_TIME",
            t.UPDATE_BY, 
            t.UPDATE_TIME, 
            t.INSERT_USER_NAME, 
            t.UPDATE_USER_NAME, 
            t.EXTEND1, 
            t.EXTEND2, 
            t.EXTEND3, 
            t.EXTEND4, 
            t.EXTEND5, 
            t.EXTEND6, 
            t.EXTEND7, 
            t.EXTEND8, 
            t.EXTEND9, 
            t.EXTEND10, 
            t.CONTRACT_NO, 
            t.PURCHASE_NO, 
            t.CUSTOMER, 
            t.SUPPLIER, 
            t.INVOICE_NO, 
            t.PORT_OF_DEPARTURE, 
            t.DESTINATION, 
            t.PAYMENT_METHOD, 
            t.PRICE_TERM, 
            t.PRICE_TERM_PORT, 
            t.VESSEL_VOYAGE, 
            t.SAILING_DATE, 
            t.EXPECTED_ARRIVAL_DATE, 
            t.SALES_DATE, 
            t.CONTRACT_AMOUNT, 
            t.INSURANCE_RATE, 
            t.INSURANCE_MARKUP, 
            t.DOCUMENT_CREATOR, 
            t.DOCUMENT_DATE, 
            t.DOCUMENT_STATUS, 
            t.CONFIRM_TIME, 
            t.APPROVAL_STATUS, 
            t.DATE_OF_CONTRACT, 
            t.IS_NEXT, 
            t.PURCHASE_CONTRACT_NO, 
            d.ENTRY_NO,
            d.ENTRY_DATE,
            t.SERIAL_NO, 
            t.SALES_DOCUMENT_STATUS, 
            t.SELL_CONTRACT_NO, 
            t.CANCEL_DATE, 
            t.NOTE
    </sql>

    <sql id="condition">
        t.trade_code = #{tradeCode}
        <choose>
            <when test="dataState != null and dataState != ''">
                AND t.data_state = #{dataState}
            </when>
            <otherwise>
                AND t.data_state != '2'
            </otherwise>
        </choose>

        <if test="contractNo != null and contractNo != ''">
            and t.contract_no like  '%' || #{contractNo} || '%'
        </if>

        <if test="purchaseNo!= null and purchaseNo!= ''">
            and t.purchase_no like  '%' || #{purchaseNo} || '%'
        </if>

        <if test="customer!= null and customer!= ''">
            and t.customer = #{customer}
        </if>

        <if test="supplier!= null and supplier!= ''">
            and t.supplier = #{supplier}
        </if>

        <if test="createBy != null and createBy != ''">
            and coalesce(t.update_user_name,t.insert_user_name) = #{createBy}
        </if>

        <if test="createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= TO_DATE(#{createTimeFrom}, 'yyyy-mm-dd')]]>
        </if>
        <if test="createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) <  TO_DATE(#{createTimeTo}, 'yyyy-mm-dd') + 1 ]]>
        </if>


         <if test="dateOfContractFrom != null and dateOfContractFrom != ''">
            <![CDATA[ AND t.date_of_contract >= TO_DATE(#{dateOfContractFrom,jdbcType=VARCHAR}, 'yyyy-mm-dd')]]>
        </if>
        <if test="dateOfContractTo!= null and dateOfContractTo!= ''">
            <![CDATA[ AND t.date_of_contract <  TO_DATE(#{dateOfContractTo,jdbcType=VARCHAR}, 'yyyy-mm-dd') + 1 ]]>
        </if>

        <if test="approvalStatus!= null and approvalStatus!= ''">
            and t.approval_status = #{approvalStatus}
        </if>

    </sql>

    <update id="updateAccountById">
        update T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD  t
            set CONTRACT_AMOUNT = round((
               select sum(l.amount) from T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST l where l.HEAD_ID = #{headId}
            ),4) where t.id = #{headId}
    </update>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_smoke_machine_incoming_goods_head t
        left join  t_biz_smoke_machine_incoming_goods_document d on d.head_id = t.id
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.UPDATE_TIME,t.CREATE_TIME) desc
    </select>
    <select id="getListByHtId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_biz_smoke_machine_incoming_goods_head t
        where t.EXTEND3 = #{htId}
    </select>
    <select id="getCustomerList" resultType="java.util.Map">
        select
            distinct
            MERCHANT_CODE as "value",
            MERCHANT_NAME_CN as "label"
        from  T_BIZ_MERCHANT
        where
            TRADE_CODE = #{tradeCode}
    </select>
    <select id="getPortList" parameterType="java.lang.String"  resultType="java.util.Map">
        select
            distinct
            params_code as "value",
            params_name as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'PORT'
          and TRADE_CODE = #{tradeCode}
    </select>
    <select id="getCurrentCustomerList" resultType="java.util.Map">
        select
            distinct MERCHANT_CODE as "value",
                     MERCHANT_NAME_CN as "label"
        from
            T_BIZ_MERCHANT
        where
            TRADE_CODE = #{tradeCode}
          and MERCHANT_CODE in (
            select
              DISTINCT CUSTOMER
            from
              T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD
            where trade_code = #{tradeCode}
          )
    </select>
    <select id="getCurrentSupplierList" resultType="java.util.Map">
        select
            distinct MERCHANT_CODE as "value",
                     MERCHANT_NAME_CN as "label"
        from
            T_BIZ_MERCHANT
        where
            TRADE_CODE = #{tradeCode}
          and MERCHANT_CODE in (
            select
              DISTINCT SUPPLIER
            from
              T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD
              where trade_code = #{tradeCode}
          )
    </select>


    <select id="getCurrentCreateByList" resultType="java.util.Map">
        select DISTINCT   COALESCE(UPDATE_USER_NAME, INSERT_USER_NAME) as "value",
                          COALESCE(UPDATE_USER_NAME, INSERT_USER_NAME) as "label"
        from T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD
        where TRADE_CODE = #{tradeCode}
    </select>


    <select id="selectByPurchaseNo" resultType="java.lang.Integer">
        select
            count(1)
        from
            T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD h
        where
            h.PURCHASE_NO = #{purchaseNo}
            and h.TRADE_CODE = #{tradeCode}
            and h.DATA_STATE != 2
            <if test = "id != null and id != ''" >
                and h.ID != #{id}
            </if>
    </select>
    <select id="getExtractAuxmatContractList" resultType="com.dcjet.cs.equipment.model.ForeignContractHead">
            select
                distinct h.id,
                         h.contract_no as contractNo,
                         h.sign_date as signDate,
                         h.buyer || ' ' || (select merchant_name_cn from t_biz_merchant q where h.buyer = q.merchant_code and h.trade_code = q.trade_code limit 1  )as buyer,
                         h.seller || '  ' || (select merchant_name_cn from t_biz_merchant q where h.seller = q.merchant_code and h.trade_code = q.trade_code limit 1  ) as seller,
                         (
                             select
                                 coalesce(sum(money_amount), 0)
                             from
                                 t_biz_i_machine_equip_foreign_contract_list l
                             where
                                 l.head_id = h.id
                         ) as totalMoney,
                         (
                             select
                                 coalesce(sum(qty), 0)
                             from
                                 t_biz_i_machine_equip_foreign_contract_list l
                             where
                                 l.head_id = h.id
                         ) as totalQty
            from
                t_biz_i_machine_equip_foreign_contract_head h
            where
                h.data_status = 1
                <if test="contractNo != null and contractNo != ''">
                    and h.contract_no like '%' || #{contractNo} || '%'
                </if>
            order by h.sign_date desc, h.contract_no
    </select>
    <select id="getIncomingCountByContractNo" resultType="java.math.BigDecimal">
        select sum(il.quantity)
        from t_biz_smoke_machine_incoming_goods_head ih
                 inner join t_biz_smoke_machine_incoming_goods_list il on ih.id = il.head_id
        where ih.contract_no = #{contractNo}
          and ih.trade_code = #{tradeCode}
          and ih.data_state != 2
    </select>
    <select id="getMaxSerialNo" resultType="java.lang.Integer">
        select coalesce(max(serial_no),0) as serialno  from t_biz_incoming_goods_head where  contract_no = #{contractNo} and trade_code = #{tradeCode};
    </select>

    <select id="getContractListCount" resultType="java.math.BigDecimal">
        select
            coalesce(sum(l.qty), 0)
        from
            t_biz_i_machine_equip_foreign_contract_list l
                left join t_biz_i_machine_equip_foreign_contract_head h on l.head_id = h.id
        where
              h.contract_no = #{contractNo}
          and h.trade_code = #{tradeCode}
          and l.g_name = #{gName}
          and h.data_status = 1
    </select>
    <select id="getSmokeListCount" resultType="java.math.BigDecimal">
        select
            coalesce(sum(l.quantity), 0)
        from
            t_biz_smoke_machine_incoming_goods_list l
                left join t_biz_smoke_machine_incoming_goods_head h on l.head_id = h.id
        where
              h.contract_no = #{contractNo}
          and h.trade_code = #{tradeCode}
          and l.goods_name = #{gName}
          and h.data_state = 1;
    </select>

    <select id="checkApprovalStatus" resultType="java.lang.Integer">
        select
            count(1)
        from
            t_biz_smoke_machine_incoming_goods_head
        where id in
        <foreach collection="sids"  item="item" open="(" separator="," close=")" >
            #{item}
        </foreach>
        and approval_status != #{status}
    </select>



    <select id="selectBySids" resultType="com.dcjet.cs.common.model.WorkFlowParam">
        select
            id as sid,
            extend2 as flowInstanceId
        from t_biz_smoke_machine_incoming_goods_head where id in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

    <select id="getAeoList" resultType="com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead">

        select
            <include refid="Base_Column_List"/>
        from
            t_biz_smoke_machine_incoming_goods_head t
        left join  t_biz_smoke_machine_incoming_goods_document d on d.head_id = t.id
        <where>
            <include refid="condition"></include>
            and t.id in
            <foreach collection="ids"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        </where>
        order by coalesce(t.update_time,t.create_time) desc

    </select>

    <select id="getSumTotal" resultType="com.dcjet.cs.dto.dec.BizSmokeHeadTotalDto">
        SELECT
            sum(t.contract_amount) as "amount"
        FROM
            t_biz_smoke_machine_incoming_goods_head t
            left join  t_biz_smoke_machine_incoming_goods_document d on d.head_id = t.id
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_smoke_machine_incoming_goods_head t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 级联删除 进货单数据  -->
    <delete id="deleteByHeadId">
        delete from t_biz_smoke_machine_incoming_goods_document where head_id = #{id};
        delete from t_biz_smoke_machine_incoming_goods_head where id = #{id};
        delete from t_biz_smoke_machine_incoming_goods_list where head_id = #{id};
        delete from t_biz_smoke_machine_incoming_goods_tb where head_id = #{id};
    </delete>

    <!--作废进货单数据 -->
    <delete id="cancelData">
        update t_biz_smoke_machine_incoming_goods_document set data_state = '2'  where head_id = #{id};
        update t_biz_smoke_machine_incoming_goods_head set data_state = '2' where id = #{id};
        update t_biz_smoke_machine_incoming_goods_list set data_state = '2' where head_id = #{id};
        update t_biz_smoke_machine_incoming_goods_tb set data_state = '2' where head_id = #{id};
    </delete>
</mapper>