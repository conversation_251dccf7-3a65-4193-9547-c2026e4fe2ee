package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderList;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizBpAnalyseOrderListDto
 *
 * <AUTHOR>
 * @date 2025-07-07 17:08:36
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizBpAnalyseOrderListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizBpAnalyseOrderListDto toDto(BizBpAnalyseOrderList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizBpAnalyseOrderList toPo(BizBpAnalyseOrderListParam param);

    /**
     * 数据库原始数据更新
     * @param bizBpAnalyseOrderListParam
     * @param BizBpAnalyseOrderList
     */
    void updatePo(BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam, @MappingTarget BizBpAnalyseOrderList bizBpAnalyseOrderList);

    default void patchPo(BizBpAnalyseOrderListParam bizBpAnalyseOrderListParam , BizBpAnalyseOrderList bizBpAnalyseOrderList) {
        // TODO 自行实现局部更新
    }
}