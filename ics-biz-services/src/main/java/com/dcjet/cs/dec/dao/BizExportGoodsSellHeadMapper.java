package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizExportGoodsSellHead;
import com.dcjet.cs.dec.model.BizExportGoodsSellList;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 第9条线-非国营贸易出口辅料-外销发票表头Mapper
 */
public interface BizExportGoodsSellHeadMapper extends Mapper<BizExportGoodsSellHead>{

    /**
     * 查询获取数据
     * @param bizExportGoodsSellHead
     * @return
     */
    List<BizExportGoodsSellHead> getList(BizExportGoodsSellHead bizExportGoodsSellHead);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    /**
     * 根据ID获取第9条线-非国营贸易出口辅料-外销发票表头数据
     * @param parentId 第9条线-非国营贸易出口辅料-外销发票表头ID
     * @return 第9条线-非国营贸易出口辅料-外销发票表头数据
     */
    BizExportGoodsSellHead getFormDataById(@Param("parentId") String parentId);

    /**
     * 根据parent_id 删除
     * @param parentId
     * @return
     */
    List<String> deleteByParentId(@Param("parentId") String parentId);

    /**
     * 根据parent_id 查询出货信息表头id
     * @param parentId 外商合同表头ID
     * @return 出货信息表头ID
     */
    List<String> getDeleteSellHeadByParentId(@Param("parentId") String parentId);
}