package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizExportGoodsListBox;
import com.dcjet.cs.dto.dec.BizExportGoodsListBoxDto;
import com.dcjet.cs.dto.dec.BizExportGoodsListBoxParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizExportGoodsListBoxDto
 *
 * <AUTHOR>
 * @date 2025-07-07 13:48:47
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizExportGoodsListBoxDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizExportGoodsListBoxDto toDto(BizExportGoodsListBox po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizExportGoodsListBox toPo(BizExportGoodsListBoxParam param);

    /**
     * 数据库原始数据更新
     * @param bizExportGoodsListBoxParam
     * @param BizExportGoodsListBox
     */
    void updatePo(BizExportGoodsListBoxParam bizExportGoodsListBoxParam, @MappingTarget BizExportGoodsListBox bizExportGoodsListBox);

    default void patchPo(BizExportGoodsListBoxParam bizExportGoodsListBoxParam , BizExportGoodsListBox bizExportGoodsListBox) {
        // TODO 自行实现局部更新
    }
}