package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizSmokeMachineIncomingGoodsHeadDto
 *
 * <AUTHOR>
 * @date 2025-06-30 13:51:46
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizSmokeMachineIncomingGoodsHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizSmokeMachineIncomingGoodsHeadDto toDto(BizSmokeMachineIncomingGoodsHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizSmokeMachineIncomingGoodsHead toPo(BizSmokeMachineIncomingGoodsHeadParam param);

    /**
     * 数据库原始数据更新
     * @param bizSmokeMachineIncomingGoodsHeadParam
     * @param BizSmokeMachineIncomingGoodsHead
     */
    void updatePo(BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam, @MappingTarget BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead);

    default void patchPo(BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam , BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead) {
        // TODO 自行实现局部更新
    }
}