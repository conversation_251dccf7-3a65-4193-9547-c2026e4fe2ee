package com.dcjet.cs.dec.service;


import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListBoxBoxMapper;
import com.dcjet.cs.dec.mapper.BizBpAnalyseOrderListBoxBoxDtoMapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxBoxDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxBoxParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizBpAnalyseOrderListBoxBox业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-08-05 11:08:41
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizBpAnalyseOrderListBoxBoxService extends BaseService<BizBpAnalyseOrderListBoxBox> {

    private static final Logger log = LoggerFactory.getLogger(BizBpAnalyseOrderListBoxBoxService.class);

    @Resource
    private BizBpAnalyseOrderListBoxBoxMapper bizBpAnalyseOrderListBoxBoxMapper;

    @Resource
    private BizBpAnalyseOrderListBoxBoxDtoMapper bizBpAnalyseOrderListBoxBoxDtoMapper;

    @Override
    public Mapper<BizBpAnalyseOrderListBoxBox> getMapper() {
        return bizBpAnalyseOrderListBoxBoxMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizBpAnalyseOrderListBoxBoxParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizBpAnalyseOrderListBoxBoxDto>> getListPaged(BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox = bizBpAnalyseOrderListBoxBoxDtoMapper.toPo(bizBpAnalyseOrderListBoxBoxParam);
        bizBpAnalyseOrderListBoxBox.setTradeCode(userInfo.getCompany());
        Page<BizBpAnalyseOrderListBoxBox> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizBpAnalyseOrderListBoxBoxMapper.getList(bizBpAnalyseOrderListBoxBox));
        // 将PO转为DTO返回给前端
        List<BizBpAnalyseOrderListBoxBoxDto> bizBpAnalyseOrderListBoxBoxDtoList = page.getResult().stream()
            .map(bizBpAnalyseOrderListBoxBoxDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizBpAnalyseOrderListBoxBoxDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizBpAnalyseOrderListBoxBoxParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderListBoxBoxDto insert(BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox = bizBpAnalyseOrderListBoxBoxDtoMapper.toPo(bizBpAnalyseOrderListBoxBoxParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizBpAnalyseOrderListBoxBox.setId(sid);
        bizBpAnalyseOrderListBoxBox.setCreateBy(userInfo.getUserNo());
        bizBpAnalyseOrderListBoxBox.setInsertUserName(userInfo.getUserName());
        bizBpAnalyseOrderListBoxBox.setCreateTime(new Date());
        bizBpAnalyseOrderListBoxBox.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizBpAnalyseOrderListBoxBoxMapper.insert(bizBpAnalyseOrderListBoxBox);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizBpAnalyseOrderListBoxBoxDtoMapper.toDto(bizBpAnalyseOrderListBoxBox) : null;
    }

    /**
     * 修改记录
     *
     * @param bizBpAnalyseOrderListBoxBoxParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizBpAnalyseOrderListBoxBoxDto update(BizBpAnalyseOrderListBoxBoxParam bizBpAnalyseOrderListBoxBoxParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox = bizBpAnalyseOrderListBoxBoxMapper.selectByPrimaryKey(bizBpAnalyseOrderListBoxBoxParam.getId());
        bizBpAnalyseOrderListBoxBoxDtoMapper.updatePo(bizBpAnalyseOrderListBoxBoxParam, bizBpAnalyseOrderListBoxBox);
        bizBpAnalyseOrderListBoxBox.setUpdateBy(userInfo.getUserNo());
        bizBpAnalyseOrderListBoxBox.setUpdateUserName(userInfo.getUserName());
        bizBpAnalyseOrderListBoxBox.setUpdateTime(new Date());

        // 更新数据
        int update = bizBpAnalyseOrderListBoxBoxMapper.updateByPrimaryKey(bizBpAnalyseOrderListBoxBox);
        return update > 0 ? bizBpAnalyseOrderListBoxBoxDtoMapper.toDto(bizBpAnalyseOrderListBoxBox) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizBpAnalyseOrderListBoxBoxMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizBpAnalyseOrderListBoxBoxDto> selectAll(BizBpAnalyseOrderListBoxBoxParam exportParam, UserInfoToken userInfo) {
        BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox = bizBpAnalyseOrderListBoxBoxDtoMapper.toPo(exportParam);
        bizBpAnalyseOrderListBoxBox.setTradeCode(userInfo.getCompany());
        List<BizBpAnalyseOrderListBoxBoxDto> bizBpAnalyseOrderListBoxBoxDtos = new ArrayList<>();
        List<BizBpAnalyseOrderListBoxBox> ******************************** = bizBpAnalyseOrderListBoxBoxMapper.getList(bizBpAnalyseOrderListBoxBox);
        if (CollectionUtils.isNotEmpty(********************************)) {
           bizBpAnalyseOrderListBoxBoxDtos = ********************************.stream().map(head -> {
                    BizBpAnalyseOrderListBoxBoxDto dto =  bizBpAnalyseOrderListBoxBoxDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizBpAnalyseOrderListBoxBoxDtos;
    }

}