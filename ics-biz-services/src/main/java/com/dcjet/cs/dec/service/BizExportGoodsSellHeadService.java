package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizExportGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsSellHeadMapper;
import com.dcjet.cs.dec.mapper.BizExportGoodsSellHeadDtoMapper;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dec.model.BizExportGoodsSellHead;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadDto;
import com.dcjet.cs.dto.dec.BizExportGoodsSellHeadParam;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListTotal;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizExportGoodsSellHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 14:16:12
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizExportGoodsSellHeadService extends BaseService<BizExportGoodsSellHead> {

    private static final Logger log = LoggerFactory.getLogger(BizExportGoodsSellHeadService.class);

    @Resource
    private BizExportGoodsSellHeadMapper bizExportGoodsSellHeadMapper;

    @Resource
    private BizExportGoodsSellHeadDtoMapper bizExportGoodsSellHeadDtoMapper;

    @Override
    public Mapper<BizExportGoodsSellHead> getMapper() {
        return bizExportGoodsSellHeadMapper;
    }

    @Resource
    private BizExportGoodsHeadMapper bizExportGoodsHeadMapper;



    /**
     * 获取分页信息
     *
     * @param bizExportGoodsSellHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizExportGoodsSellHeadDto>> getListPaged(BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizExportGoodsSellHead bizExportGoodsSellHead = bizExportGoodsSellHeadDtoMapper.toPo(bizExportGoodsSellHeadParam);
        bizExportGoodsSellHead.setTradeCode(userInfo.getCompany());
        Page<BizExportGoodsSellHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizExportGoodsSellHeadMapper.getList( bizExportGoodsSellHead));
        // 将PO转为DTO返回给前端
        List<BizExportGoodsSellHeadDto> bizExportGoodsSellHeadDtoList = page.getResult().stream()
            .map(bizExportGoodsSellHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizExportGoodsSellHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizExportGoodsSellHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsSellHeadDto insert(BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam, UserInfoToken userInfo) {
        BizExportGoodsSellHead bizExportGoodsSellHead = bizExportGoodsSellHeadDtoMapper.toPo(bizExportGoodsSellHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizExportGoodsSellHead.setId(sid);
        bizExportGoodsSellHead.setCreateBy(userInfo.getUserNo());
        bizExportGoodsSellHead.setInsertUserName(userInfo.getUserName());
        bizExportGoodsSellHead.setCreateTime(new Date());
        bizExportGoodsSellHead.setTradeCode(userInfo.getCompany());


        // 新增数据
        int insertStatus = bizExportGoodsSellHeadMapper.insert(bizExportGoodsSellHead);
        setReturnUser(bizExportGoodsSellHead);
        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizExportGoodsSellHeadDtoMapper.toDto(bizExportGoodsSellHead) : null;
    }

    /**
     * 修改记录
     *
     * @param bizExportGoodsSellHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsSellHeadDto update(BizExportGoodsSellHeadParam bizExportGoodsSellHeadParam, UserInfoToken userInfo) {
        BizExportGoodsSellHead bizExportGoodsSellHead = bizExportGoodsSellHeadMapper.selectByPrimaryKey(bizExportGoodsSellHeadParam.getId());
        bizExportGoodsSellHeadDtoMapper.updatePo(bizExportGoodsSellHeadParam, bizExportGoodsSellHead);
        bizExportGoodsSellHead.setUpdateBy(userInfo.getUserNo());
        bizExportGoodsSellHead.setUpdateUserName(userInfo.getUserName());
        bizExportGoodsSellHead.setUpdateTime(new Date());

        // 更新数据
        int update = bizExportGoodsSellHeadMapper.updateByPrimaryKey(bizExportGoodsSellHead);
        setReturnUser(bizExportGoodsSellHead);
        return update > 0 ? bizExportGoodsSellHeadDtoMapper.toDto(bizExportGoodsSellHead) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizExportGoodsSellHeadMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizExportGoodsSellHeadDto> selectAll(BizExportGoodsSellHeadParam exportParam, UserInfoToken userInfo) {
        BizExportGoodsSellHead bizExportGoodsSellHead = bizExportGoodsSellHeadDtoMapper.toPo(exportParam);
        bizExportGoodsSellHead.setTradeCode(userInfo.getCompany());
        List<BizExportGoodsSellHeadDto> bizExportGoodsSellHeadDtos = new ArrayList<>();
        List<BizExportGoodsSellHead> bizExportGoodsSellHeadLists = bizExportGoodsSellHeadMapper.getList(bizExportGoodsSellHead);
        if (CollectionUtils.isNotEmpty(bizExportGoodsSellHeadLists)) {
           bizExportGoodsSellHeadDtos = bizExportGoodsSellHeadLists.stream().map(head -> {
                    BizExportGoodsSellHeadDto dto =  bizExportGoodsSellHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizExportGoodsSellHeadDtos;
    }


    /**
     * 根据ID获取第9条线-非国营贸易出口辅料-外销发票表头数据
     * @param id 第9条线-非国营贸易出口辅料-外销发票表头ID
     * @param userInfo 用户信息
     * @return 第9条线-非国营贸易出口辅料-外销发票表头数据
     */
    public ResultObject<BizExportGoodsSellHeadDto> getFormDataById(String id, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功");
        BizExportGoodsSellHead bizExportGoodsSellHead = bizExportGoodsSellHeadMapper.getFormDataById(id);
        if (bizExportGoodsSellHead != null) {
            BizExportGoodsSellHeadDto dto = bizExportGoodsSellHeadDtoMapper.toDto(bizExportGoodsSellHead);
            resultObject.setData(dto);
        }
        return resultObject;
    }


    /**
     * 外销发票确认
     * @param param 确认参数
     * @param userInfo 用户信息
     * @return 确认结果
     */
    public ResultObject<BizExportGoodsSellHeadDto> confirm(BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "确认成功");
        BizExportGoodsSellHead po = bizExportGoodsSellHeadDtoMapper.toPo(param);
        // 更新确认状态
        // TODO 待其他功能开发完成后进行补充
        //  <确认>功能
        //  操作成功时
        //  1）根据合同号关联【合同与协议-分析单】“外销发票”区域，将“结束预警”栏位赋值为0是
        //  2）操作成功时，根据合同号关联【合同与协议-分析单】，将下表栏位值回写至“外销发票”区域
        //   1.汇率
        //   2.代理费（不含税金额）
        //   3.代理费税额
        //   4.代理费（价税合计）

        po.setDataState("1");
        po.setConfirmTime(new Date());

        bizExportGoodsSellHeadMapper.updateByPrimaryKey(po);
        setReturnUser(po);
        BizExportGoodsSellHeadDto dto = bizExportGoodsSellHeadDtoMapper.toDto(po);
        resultObject.setData(dto);

        return resultObject;
    }

    /**
     * 红冲
     * @param param  外销发票表头
     * @param userInfo 用户信息
     * @return 红冲结果
     */
    public ResultObject<BizExportGoodsSellHeadDto> redFlush(BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "红冲成功");
        BizExportGoodsSellHead po = bizExportGoodsSellHeadDtoMapper.toPo(param);
        // 更新红冲状态
        po.setIsRedFlush("1");
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());

        bizExportGoodsSellHeadMapper.updateByPrimaryKey(po);

        setReturnUser(po);

        BizExportGoodsSellHeadDto dto = bizExportGoodsSellHeadDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }


    public void setReturnUser(BizExportGoodsSellHead po){
        if (StringUtils.isNotBlank(po.getUpdateBy())){
            po.setCreateBy(po.getUpdateUserName());
        }else {
            po.setCreateBy(po.getInsertUserName());
        }
        if (po.getUpdateTime() !=null){
            po.setCreateTime(po.getUpdateTime());
        }else {
            po.setCreateTime(po.getCreateTime());
        }
    }


    public ResultObject<BizExportGoodsSellHeadDto> backOrder(BizExportGoodsSellHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "退单成功");
        BizExportGoodsSellHead po = bizExportGoodsSellHeadDtoMapper.toPo(param);
        // 判断表头是否处于审批中状态
        BizExportGoodsHead exportGoodsHead = bizExportGoodsHeadMapper.selectByPrimaryKey(po.getParentId());
        if (exportGoodsHead != null){
            if (exportGoodsHead.getApprovalStatus().equals("2")) {
                throw new ErrorException(400,"该单据当前正在内审中，无法操作");
            }
        }


        // 更新退单状态
        po.setDataState("0");
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        po.setConfirmTime(null);

        // 修改表头内审状态
        String parentId = po.getParentId();
        BizExportGoodsHead head = bizExportGoodsHeadMapper.selectByPrimaryKey(parentId);
        head.setApprovalStatus("1");
        bizExportGoodsHeadMapper.updateByPrimaryKeySelective(head);


        bizExportGoodsSellHeadMapper.updateByPrimaryKey(po);

        setReturnUser(po);

        BizExportGoodsSellHeadDto dto = bizExportGoodsSellHeadDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }



}