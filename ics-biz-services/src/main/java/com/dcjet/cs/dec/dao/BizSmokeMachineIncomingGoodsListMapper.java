package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进过管理-表体列表Mapper
 */
public interface BizSmokeMachineIncomingGoodsListMapper extends Mapper<BizSmokeMachineIncomingGoodsList>{

    /**
     * 查询获取数据
     * @param bizSmokeMachineIncomingGoodsList
     * @return
     */
    List<BizSmokeMachineIncomingGoodsList> getList(BizSmokeMachineIncomingGoodsList bizSmokeMachineIncomingGoodsList);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    List<BizSmokeMachineIncomingGoodsList> selectByHeadId(@Param("headId") String headId);
}