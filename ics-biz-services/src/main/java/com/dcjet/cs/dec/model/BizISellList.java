package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 进口管理-进货信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Getter
@Setter
@Table(name = "T_BIZ_I_SELL_LIST")
public class BizISellList implements Serializable {
    /**
     * 主键SID
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 唯一标识记录的主键
     */
    @Id
    @Column(name = "sid")
    private String sid;

    /**
     * 销售头SID
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 关联销售单表头的主键
     */
    @Column(name = "head_id")
    private String headId;

    /**
     * 销售合同号
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 对应销售合同的唯一编号
     */
    @Column(name = "sales_contract_number")
    private String salesContractNumber;

    /**
     * 销售发票号
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 与销售合同关联的发票编号
     */
    @Column(name = "sales_invoice_number")
    private String salesInvoiceNumber;

    /**
     * 商品名称
     * 字段类型: 字符类型(200)
     * 允许空值: 是
     * 说明: 贸易商品的中文全称
     */
    @Column(name = "trade_name")
    private String tradeName;

    /**
     * 单位
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 商品计量单位（如：箱、件、千克）
     */
    @Column(name = "unit")
    private String unit;

    /**
     * 数量
     * 字段类型: 数值类型(18,4)
     * 允许空值: 是
     * 说明: 商品交易数量，保留4位小数
     */
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * 不含税单价
     * 字段类型: 数值类型(18,4)
     * 允许空值: 是
     * 说明: 单件商品不含税价格，保留4位小数
     */
    @Column(name = "unit_price_excluding_tax")
    private BigDecimal unitPriceExcludingTax;

    /**
     * 税额
     * 字段类型: 数值类型(18,2)
     * 允许空值: 是
     * 说明: 当前商品对应的税费金额，保留2位小数
     */
    @Column(name = "amount_of_tax")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amountOfTax;

    /**
     * 不含税金额
     * 字段类型: 数值类型(18,2)
     * 允许空值: 是
     * 说明: 商品交易总金额（不含税），保留2位小数
     */
    @Column(name = "tax_not_included")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal taxNotIncluded;

    /**
     * 价税合计
     * 字段类型: 数值类型(18,2)
     * 允许空值: 是
     * 说明: 商品交易总金额（含税），保留2位小数
     */
    @Column(name = "total_value_tax")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal totalValueTax;

    /**
     * 插入人
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 创建记录的操作员账号
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 插入时间
     * 字段类型: 日期类型(6)
     * 允许空值: 是
     * 格式说明: yyyy-MM-dd HH:mm:ss
     * 时区说明: UTC+8
     * 说明: 记录首次创建的时间戳
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 插入人姓名
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 创建记录的操作员真实姓名
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 修改人
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 最后一次修改记录的操作员账号
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     * 字段类型: 日期类型(6)
     * 允许空值: 是
     * 格式说明: yyyy-MM-dd HH:mm:ss
     * 时区说明: UTC+8
     * 说明: 记录最后一次更新的时间戳
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 修改人姓名
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 最后一次修改记录的操作员真实姓名
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 公司代码
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 关联企业的唯一编码标识
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 选中的SID列表
     * 字段类型: 非持久化字段
     * 说明: 用于批量操作时存储多个记录的SID
     */
    @Transient
    private List<String> sids;

    /**
     * 进口数量
     * 字段类型: 数值类型(18,4)
     * 允许空值: 是
     * 说明: 进口商品的实际数量，保留4位小数
     */
    @Column(name = "I_COUNT")
    private BigDecimal iCount;

    /**
     * 进口单位
     * 字段类型: 字符类型(50)
     * 允许空值: 是
     * 说明: 进口商品的计量单位（如：吨、千克）
     */
    @Column(name = "I_UNIT")
    private String iUnit;

    /**
     * 总数量
     * 字段类型: 非持久化字段
     * 说明: 汇总计算的商品总数量，用于业务统计
     */
    @Transient
    private BigDecimal qtyTotal;
}