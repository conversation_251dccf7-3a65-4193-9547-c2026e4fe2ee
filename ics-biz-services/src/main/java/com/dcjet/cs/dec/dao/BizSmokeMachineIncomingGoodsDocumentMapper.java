package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsDocument;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进货管理-进货单-证件信息Mapper
 */
public interface BizSmokeMachineIncomingGoodsDocumentMapper extends Mapper<BizSmokeMachineIncomingGoodsDocument>{

    /**
     * 查询获取数据
     * @param bizSmokeMachineIncomingGoodsDocument
     * @return
     */
    List<BizSmokeMachineIncomingGoodsDocument> getList(BizSmokeMachineIncomingGoodsDocument bizSmokeMachineIncomingGoodsDocument);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    BizSmokeMachineIncomingGoodsDocument getDocumentByHeadId(@Param("headId") String headId);
}