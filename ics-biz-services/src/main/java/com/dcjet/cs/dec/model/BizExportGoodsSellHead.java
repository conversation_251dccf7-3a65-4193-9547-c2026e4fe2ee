package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 第9条线-非国营贸易出口辅料-外销发票表头
 *
 * <AUTHOR>
 * @date 2025-07-07 15:23:12
 */
@Getter
@Setter
@Table(name = "t_biz_export_goods_sell_head")
public class BizExportGoodsSellHead implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     * 字符类型(80)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(240)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级id
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(200)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private String createTimeTo;

    /**
     * 更新人
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 发票号
     * 字符类型(120)
     * 必填
     */
    @Column(name = "invoice_no")
    private String invoiceNo;

    /**
     * 出货单号
     * 字符类型(120)
     * 必填
     */
    @Column(name = "export_no")
    private String exportNo;

    /**
     * 合同号
     * 字符类型(120)
     * 必填
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 发票客户
     * 字符类型(400)
     * 必填
     */
    @Column(name = "invoice_customer")
    private String invoiceCustomer;

    /**
     * 销售客户
     * 字符类型(400)
     * 必填
     */
    @Column(name = "sales_customer")
    private String salesCustomer;

    /**
     * 信用证号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "lc_no")
    private String lcNo;

    /**
     * 币种
     * 字符类型(20)
     * 必填
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 合计金额
     * 数值类型(19,4)
     * 必填
     */
    @Column(name = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 唛头
     * 字符类型(600)
     * 非必填
     */
    @Column(name = "mark")
    private String mark;

    /**
     * 代理费率%
     * 数值类型(19,6)
     * 必填
     */
    @Column(name = "agent_rate")
    private BigDecimal agentRate;

    /**
     * 代理费（外币）
     * 数值类型(19,6)
     * 必填
     */
    @Column(name = "agent_fee_foreign")
    private BigDecimal agentFeeForeign;

    /**
     * 预收款日期
     * date
     * 非必填
     */
    @Column(name = "prepay_date")
    private Date prepayDate;

    /**
     * 作销日期
     * date
     * 非必填
     */
    @Column(name = "cancel_date")
    private Date cancelDate;

    /**
     * 发票日期
     * date
     * 非必填
     */
    @Column(name = "invoice_date")
    private Date invoiceDate;

    /**
     * 汇率
     * 数值类型(19,6)
     * 必填
     */
    @Column(name = "exchange_rate")
    private BigDecimal exchangeRate;

    /**
     * 代理费（不含税金额）
     * 数值类型(19,2)
     * 必填
     */
    @Column(name = "agent_fee_ex_tax")
    private BigDecimal agentFeeExTax;

    /**
     * 代理费税额
     * 数值类型(19,2)
     * 必填
     */
    @Column(name = "agent_tax")
    private BigDecimal agentTax;

    /**
     * 代理费（价税合计）
     * 数值类型(19,6)
     * 必填
     */
    @Column(name = "agent_fee_total")
    private BigDecimal agentFeeTotal;

    /**
     * 发送财务系统
     * 字符类型(20)
     * 必填
     */
    @Column(name = "send_finance")
    private String sendFinance;

    /**
     * 备注
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 是否红冲
     * 字符类型(20)
     * 必填
     */
    @Column(name = "is_red_flush")
    private String isRedFlush;

    /**
     * 单据状态
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "bill_status")
    private String billStatus;

    /**
     * 确认时间
     * 字符类型(36)
     * 非必填
     */
    @Column(name = "confirm_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;


}