package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * （第7条线）出料加工进口薄片-分析单表头
 *
 * <AUTHOR>
 * @date 2025-07-14 17:05:45
 */
@Getter
@Setter
@Table(name = "t_biz_bp_analyse_order_head")
public class BizBpAnalyseOrderHead implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     * 字符类型(160)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(240)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级id
     * 字符类型(160)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(200)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private String createTimeTo;

    /**
     * 更新人
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 收货人
     * 字符类型(2000)
     * 非必填
     */
    @Column(name = "receiver")
    private String receiver;

    /**
     * 合同号
     * 字符类型(120)
     * 必填
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 分析单号
     * 字符类型(120)
     * 必填
     */
    @Column(name = "analysis_no")
    private String analysisNo;

    /**
     * 贸易国别
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "trade_country")
    private String tradeCountry;

    /**
     * 目的地
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "destination")
    private String destination;

    /**
     * 消费国别
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "consume_country")
    private String consumeCountry;

    /**
     * 是否转运
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "is_transit")
    private String isTransit;

    /**
     * 装运期限
     * date
     * 非必填
     */
    @Column(name = "shipment_deadline")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shipmentDeadline;



    /**
     * 装运期限-开始时间
     */
    @Transient
    private String shipmentDeadlineFrom;


    /**
     * 装运期限-结束时间
     */
    @Transient
    private String shipmentDeadlineTo;

    /**
     * shipper
     * 字符类型(1000)
     * 非必填
     */
    @Column(name = "shipper")
    private String shipper;

    /**
     * consignee
     * 字符类型(1000)
     * 非必填
     */
    @Column(name = "consignee")
    private String consignee;

    /**
     * notify party
     * 字符类型(1000)
     * 非必填
     */
    @Column(name = "notify_party")
    private String notifyParty;

    /**
     * freight
     * 字符类型(1000)
     * 非必填
     */
    @Column(name = "freight")
    private String freight;

    /**
     * 出境加工账册编号
     * 字符类型(100)
     * 非必填
     */
    @Column(name = "process_account_no")
    private String processAccountNo;

    /**
     * 装箱时间
     * date
     * 非必填
     */
    @Column(name = "packing_time")
    private Date packingTime;

    /**
     * 至
     * date
     * 非必填
     */
    @Column(name = "packing_time_to")
    private Date packingTimeTo;

    /**
     * 仓库地址
     * 字符类型(1000)
     * 非必填
     */
    @Column(name = "warehouse_address")
    private String warehouseAddress;

    /**
     * 船名航次
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "ship_name_voyage")
    private String shipNameVoyage;

    /**
     * 提单编号
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "bill_no")
    private String billNo;

    /**
     * 联系人及电话
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 提单日期
     * date
     * 非必填
     */
    @Column(name = "bill_date")
    private Date billDate;

    /**
     * 审核状态
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "appr_status")
    private String apprStatus;

    /**
     * 币制
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "curr")
    private String curr;

    /**
     * 确认时间
     * timestamp
     * 非必填
     */
    @Column(name = "confirm_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    /**
     * 确认时间-开始时间
     */
    @Transient
    private String confirmTimeFrom;

    /**
     * 确认时间-结束时间
     */
    @Transient
    private String confirmTimeTo;

    /**
     * 表体金额汇总
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "total")
    private BigDecimal total;


    /**
     * 客户代码
     * 数据库字段:customer_code
     * 字符类型(200)
     */
    @Column(name = "customer_code")
    private String customerCode;





    @Transient
    private String totalMoney;



    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;


    @Override
    public String toString() {
        return "（第7条线）出料加工进口薄片-分析单表头{" +
                "主键id='" + id + '\'' +
                "业务类型='" + businessType + '\'' +
                "数据状态='" + dataState + '\'' +
                "版本号='" + versionNo + '\'' +
                "企业10位编码='" + tradeCode + '\'' +
                "组织机构代码='" + sysOrgCode + '\'' +
                "父级id='" + parentId + '\'' +
                "创建人='" + createBy + '\'' +
                "创建时间='" + createTime + '\'' +
                "更新人='" + updateBy + '\'' +
                "更新时间='" + updateTime + '\'' +
                "插入用户名='" + insertUserName + '\'' +
                "更新用户名='" + updateUserName + '\'' +
                "扩展字段1='" + extend1 + '\'' +
                "扩展字段2='" + extend2 + '\'' +
                "扩展字段3='" + extend3 + '\'' +
                "扩展字段4='" + extend4 + '\'' +
                "扩展字段5='" + extend5 + '\'' +
                "扩展字段6='" + extend6 + '\'' +
                "扩展字段7='" + extend7 + '\'' +
                "扩展字段8='" + extend8 + '\'' +
                "扩展字段9='" + extend9 + '\'' +
                "扩展字段10='" + extend10 + '\'' +
                "收货人='" + receiver + '\'' +
                "合同号='" + contractNo + '\'' +
                "分析单号='" + analysisNo + '\'' +
                "贸易国别='" + tradeCountry + '\'' +
                "目的地='" + destination + '\'' +
                "消费国别='" + consumeCountry + '\'' +
                "是否转运='" + isTransit + '\'' +
                "装运期限='" + shipmentDeadline + '\'' +
                "shipper='" + shipper + '\'' +
                "consignee='" + consignee + '\'' +
                "notify party='" + notifyParty + '\'' +
                "freight='" + freight + '\'' +
                "出境加工账册编号='" + processAccountNo + '\'' +
                "装箱时间='" + packingTime + '\'' +
                "至='" + packingTimeTo + '\'' +
                "仓库地址='" + warehouseAddress + '\'' +
                "船名航次='" + shipNameVoyage + '\'' +
                "提单编号='" + billNo + '\'' +
                "联系人及电话='" + contactPhone + '\'' +
                "提单日期='" + billDate + '\'' +
                "审核状态='" + apprStatus + '\'' +
                "币制='" + curr + '\'' +
                "确认时间='" + confirmTime + '\'' +
                "表体金额汇总='" + total + '\'' +
                "客户代码='" + customerCode + '\'' +
                '}';
    }

    /**
     * 打印字段
     */
    @Transient
    private String packingSummaryInfo;
    @Transient
    private String packingInfo;
    @Transient
    private String packingInfoDetail;
    @Transient
    private String priceItem;
    @Transient
    private String totalBoxCountStr;
    @Transient
    private String totalGrossWeightStr;
    @Transient
    private String totalNetWtStr;


}