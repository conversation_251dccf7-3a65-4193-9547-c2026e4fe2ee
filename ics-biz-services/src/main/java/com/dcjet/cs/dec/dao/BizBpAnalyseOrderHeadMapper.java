package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizBpAnalyseOrderHead;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tk.mybatis.mapper.common.Mapper;

/**
 * （第7条线）出料加工进口薄片-分析单表头Mapper
 */
public interface BizBpAnalyseOrderHeadMapper extends Mapper<BizBpAnalyseOrderHead>{

    /**
     * 查询获取数据
     * @param bizBpAnalyseOrderHead
     * @return
     */
    List<BizBpAnalyseOrderHead> getList(BizBpAnalyseOrderHead bizBpAnalyseOrderHead);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    //根据外商合同id查询分析单
    List<BizBpAnalyseOrderHead> getListByContractId(String contractId);

    Integer checkAnalyseOrderCode(
                                  @Param("id") String id,
                                  @Param("analysisNo") String analysisNo,
                                  @Param("tradeCode") String tradeCode
    );


    BigDecimal getTotalAmountByHeadId(@Param("id") String id);

    List<Map<String, String>> getCurrentCustomerList(@Param("tradeCode") String tradeCode);

    List<Map<String, String>> getCurrentCreateByList(@Param("tradeCode") String tradeCode);

    List<BizBpAnalyseOrderHead> getContractNoList(@Param("contractNo") String contractNo,
                                                  @Param("tradeCode")String tradeCode);

    String getPackingSummaryInfo(@Param("id") String id);

    String getPackingInfo(@Param("id") String id);
}