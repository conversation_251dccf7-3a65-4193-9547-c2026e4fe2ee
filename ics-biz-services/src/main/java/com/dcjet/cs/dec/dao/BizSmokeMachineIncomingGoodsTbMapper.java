package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsTb;
import com.dcjet.cs.dto.dec.GenerateYJTBFileData;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 烟机设备-进货单-投保信息Mapper
 */
public interface BizSmokeMachineIncomingGoodsTbMapper extends Mapper<BizSmokeMachineIncomingGoodsTb>{

    /**
     * 查询获取数据
     * @param bizSmokeMachineIncomingGoodsTb
     * @return
     */
    List<BizSmokeMachineIncomingGoodsTb> getList(BizSmokeMachineIncomingGoodsTb bizSmokeMachineIncomingGoodsTb);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    BizSmokeMachineIncomingGoodsTb getTBByHeadId(@Param("headId") String headId);

    BigDecimal getPurchaseOrderSum(@Param("headId") String headId);


    int deleteByHeadId(@Param("headId") String headId);

    String getListGoodsNameByHeadId(@Param("headId") String headId);

    GenerateYJTBFileData getSumTotal(@Param("headId") String headId);
}