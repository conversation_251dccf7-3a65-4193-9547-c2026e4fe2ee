package com.dcjet.cs.dec.service;


import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.attach.dao.AttachedMapper;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizENonStateAuxmatAggrContractHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dec.dao.BizExportGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsListMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsSellHeadMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsSellListMapper;
import com.dcjet.cs.dec.mapper.BizExportGoodsHeadDtoMapper;
import com.dcjet.cs.dec.model.*;
import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractHeadDto;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadDto;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.equipment.model.ForeignContractList;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.vo.HttpResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizExportGoodsHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-07 11:18:39
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizExportGoodsHeadService extends BizExportGoodsExtractService<BizExportGoodsHead> {

    private static final Logger log = LoggerFactory.getLogger(BizExportGoodsHeadService.class);

    @Resource
    private BizExportGoodsHeadMapper bizExportGoodsHeadMapper;

    @Resource
    private BizExportGoodsHeadDtoMapper bizExportGoodsHeadDtoMapper;

    @Override
    public Mapper<BizExportGoodsHead> getMapper() {
        return bizExportGoodsHeadMapper;
    }


    @Resource
    private BizExportCommonService bizExportCommonService;


    @Resource
    private BizExportGoodsListMapper bizExportGoodsListMapper;


    @Resource
    private BizExportGoodsSellHeadMapper  bizExportGoodsSellHeadMapper;

    @Resource
    private BizExportGoodsSellListMapper bizExportGoodsSellListMapper;

    @Resource
    private AttachedMapper attachedMapper;


    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;


    @Resource
    private CommonService commonService;

    @Resource
    private AeoAuditInfoService aeoAuditInfoService;

    @Resource
    private BizENonStateAuxmatAggrContractHeadDtoMapper exportContractHeadDtoMapper;


    /**
     * 第9条线 -> 外商合同表头Mapper
     */
    @Resource
    private BizENonStateAuxmatAggrContractHeadMapper  exportContractHeadMapper;

    /**
     * 第9条线 -> 外商合同表表体Mapper
     */
    @Resource
    private BizENonStateAuxmatAggrContractListMapper exportContractListMapper;


    /**
     * 客商信息Mapper
     */
    @Resource
    private BizMerchantMapper bizMerchantMapper;




    /**
     * 获取分页信息
     *
     * @param bizExportGoodsHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizExportGoodsHeadDto>> getListPaged(BizExportGoodsHeadParam bizExportGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadDtoMapper.toPo(bizExportGoodsHeadParam);
        bizExportGoodsHead.setTradeCode(userInfo.getCompany());
        Page<BizExportGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizExportGoodsHeadMapper.getList( bizExportGoodsHead));
        // 将PO转为DTO返回给前端
        List<BizExportGoodsHeadDto> bizExportGoodsHeadDtoList = page.getResult().stream()
            .map(bizExportGoodsHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizExportGoodsHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizExportGoodsHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsHeadDto insert(BizExportGoodsHeadParam bizExportGoodsHeadParam, UserInfoToken userInfo) {
        BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadDtoMapper.toPo(bizExportGoodsHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizExportGoodsHead.setId(sid);
        bizExportGoodsHead.setCreateBy(userInfo.getUserNo());
        bizExportGoodsHead.setInsertUserName(userInfo.getUserName());
        bizExportGoodsHead.setCreateTime(new Date());
        bizExportGoodsHead.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizExportGoodsHeadMapper.insert(bizExportGoodsHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizExportGoodsHeadDtoMapper.toDto(bizExportGoodsHead) : null;
    }

    /**
     * 修改记录
     *
     * @param bizExportGoodsHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsHeadDto update(BizExportGoodsHeadParam bizExportGoodsHeadParam, UserInfoToken userInfo) {
        try {
            BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadMapper.selectByPrimaryKey(bizExportGoodsHeadParam.getId());
            bizExportGoodsHeadDtoMapper.updatePo(bizExportGoodsHeadParam, bizExportGoodsHead);
            bizExportGoodsHead.setUpdateBy(userInfo.getUserNo());
            bizExportGoodsHead.setUpdateUserName(userInfo.getUserName());
            bizExportGoodsHead.setUpdateTime(new Date());

            // 更新保险费
            bizExportGoodsHead.setInsuranceFee(calculateInsurancePremiums(bizExportGoodsHead).setScale(4, BigDecimal.ROUND_HALF_UP));

            bizExportGoodsHead.setCreateBy(bizExportGoodsHead.getUpdateUserName());
            bizExportGoodsHead.setCreateTime(bizExportGoodsHead.getUpdateTime());

            // 更新数据
            int update = bizExportGoodsHeadMapper.updateByPrimaryKey(bizExportGoodsHead);
            return update > 0 ? bizExportGoodsHeadDtoMapper.toDto(bizExportGoodsHead) : null;
        } catch (DataIntegrityViolationException e){
            log.error("计算保费金额溢出：{}",e.getMessage());
            throw new ErrorException(400,"计算保费金额溢出");
        } catch (Exception e) {
            throw new RuntimeException("更新失败！",e);
        }
    }

    /**
     * 批量删除记录
     *
     * @param ids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> ids, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ErrorException(400,"请选择需要删除数据");
        }
        if (ids.size() != 1) {
            throw new ErrorException(400,"一次只能删除一条数据");
        }
        String id = ids.get(0);
        List<Attached> attachedList = attachedMapper.getAttachedDataByHeadId(ids);

        // 删除数据
        int i = bizExportGoodsHeadMapper.deleteById(id);
        // 循环删除附件
        if (CollectionUtils.isNotEmpty(attachedList)) {
            for (Attached attached : attachedList) {
                try {
                     // 删除附件
                     fileHandler.deleteFile(attached.getFileName());
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("删除附件异常！-{}",e.getMessage());
                }
            }
        }

    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizExportGoodsHeadDto> selectAll(BizExportGoodsHeadParam exportParam, UserInfoToken userInfo) {
        BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadDtoMapper.toPo(exportParam);
        bizExportGoodsHead.setTradeCode(userInfo.getCompany());
        List<BizExportGoodsHeadDto> bizExportGoodsHeadDtos = new ArrayList<>();
        List<BizExportGoodsHead> bizExportGoodsHeadLists = bizExportGoodsHeadMapper.getList(bizExportGoodsHead);
        if (CollectionUtils.isNotEmpty(bizExportGoodsHeadLists)) {
           bizExportGoodsHeadDtos = bizExportGoodsHeadLists.stream().map(head -> {
                    BizExportGoodsHeadDto dto =  bizExportGoodsHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizExportGoodsHeadDtos;
    }

    public ResultObject checkExportNo(BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject<Integer> resultObject = ResultObject.createInstance(true);
        BizExportGoodsHead head = bizExportGoodsHeadDtoMapper.toPo(param);
        Assert.hasText(head.getTradeCode(), "出货单号不能为空");
        // 校验出货单号是否存在
        Integer i  = bizExportGoodsHeadMapper.checkAnalyseOrderCode(head.getId(),head.getExportNo(),userInfo.getCompany());
        resultObject.setData(i);
        return resultObject;
    }

    /**
     * 功能描述:获取常用下拉列表
     *
     * @param param    入参
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject<BizExportGoodsHeadCommonDto> getCommonKeyValueList(BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject<BizExportGoodsHeadCommonDto> resultObject = ResultObject.createInstance(true,"获取成功");

        BizExportGoodsHeadCommonDto dto = new BizExportGoodsHeadCommonDto();
        String tradeCode = userInfo.getCompany();

        // 设置客户基础信息
        dto.setCustomerList(bizExportCommonService.getCustomerList("",tradeCode));
        // 设置币制信息
        dto.setCurrList(bizExportCommonService.getCurrList(tradeCode));
        // 价格条款列表
        dto.setPriceTermList(bizExportCommonService.getPriceTermList(tradeCode));
        // 设置包装信息
        dto.setPackageList(bizExportCommonService.getPackageList(tradeCode));
        // 设置城市信息
        dto.setCityList(bizExportCommonService.getCityList(tradeCode));
        // 设置保险类型信息
        dto.setInsuranceTypeList(bizExportCommonService.getInsuranceTypeList(tradeCode));
        // 设置港口信息
        dto.setPortList(bizExportCommonService.getPortList(tradeCode));
        // 设置出货单客户
        dto.setExportCustomerList(bizExportCommonService.getExportHeadCustomerList(tradeCode));
        // 设置出货单供应商
        dto.setExportSupplierList(bizExportCommonService.getExportHeadSupplierList(tradeCode));
        // 获取单价列表
        dto.setUnitList(bizExportCommonService.getUnitList(tradeCode));


        resultObject.setData(dto);
        return resultObject;
    }


    /**
     * 功能描述:计算保险费
     *  表体金额汇总 * （1+投保加成）* 保费费率
     *  投保加成如果为空 那么设置为0
     *  保费费率如果为空 那么设置为1
     *  计算保险费
     * @param head 出口信息表头
     * @return 保费
     */
    public BigDecimal calculateInsurancePremiums(BizExportGoodsHead head){
        log.info("================================== 计算保险费 ==================================");
        log.info("保费：表体金额汇总 * （1+投保加成）* 保费费率");
        // 表体金额汇总 * （1+投保加成）* 保费费率
        // 根据id获取表体金额汇总
        BigDecimal totalAmount = bizExportGoodsHeadMapper.getTotalAmount(head.getId());
        log.info("表体金额汇总：{}",totalAmount);
        // 投保加成如果为空 那么设置为0
        BigDecimal insuranceAddition = head.getInsuranceAddRate() == null ? BigDecimal.ZERO : head.getInsuranceAddRate().multiply(new BigDecimal(100));
        log.info("投保加成：{}",insuranceAddition);
        // 保费费率如果为空 那么设置为1
        BigDecimal insuranceRate = head.getInsuranceRate() == null? BigDecimal.ONE : head.getInsuranceRate().multiply(new BigDecimal(100));
        log.info("保费费率：{}",insuranceRate);
        // 计算保险费
        BigDecimal insurancePremiums = totalAmount.multiply(BigDecimal.ONE.add(insuranceAddition)).multiply(insuranceRate);
        log.info("保险费：{}",insurancePremiums);
        return insurancePremiums;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirm(BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "确认成功");
        if (StringUtils.isBlank(param.getId())) {
            throw new ErrorException(400,"单据ID不能为空");
        }
        String id = param.getId();
        // 获取表体数据
        List<BizExportGoodsList> list = bizExportGoodsListMapper.getListByHeadId(id);
        if (CollectionUtils.isEmpty(list)) {
            throw new ErrorException(400,"单据表体数据不存在");
        }
        BizExportGoodsHead head = bizExportGoodsHeadMapper.selectByPrimaryKey(id);

        // 确认数据
        BizExportGoodsSellHead gHead = new BizExportGoodsSellHead();
        List<BizExportGoodsSellList> gList = new ArrayList<>();

        String headId = UUID.randomUUID().toString();

        // 设置表头数据
        setHeadData(headId,gHead,head,userInfo);

        // 设置表体信息
        setListData(headId,userInfo,gList,list);

        // 批量插入数据
        // 先删除原先数据 (根据parent_id 找表头id 、先删除表体，再删除表头)
        // 获取需要删除的出货信息表头
        List<String> sids = bizExportGoodsSellHeadMapper.getDeleteSellHeadByParentId(id);
        if (CollectionUtils.isNotEmpty(sids)) {
            sids.forEach(item->{
                // 删除表体
                bizExportGoodsSellListMapper.deleteByParentId(item);
                // 删除表头
                bizExportGoodsSellHeadMapper.deleteByPrimaryKey(item);
            });

        }
        bizExportGoodsSellHeadMapper.insert(gHead);
        bizExportGoodsSellListMapper.insertList(gList);
        // gList.forEach(item->{
        //     bizExportGoodsSellListMapper.insert(item);
        // });

        // 更新出货信息表头
        head.setDataState("1");
        bizExportGoodsHeadMapper.updateByPrimaryKey(head);
        resultObject.setData(head);

        return resultObject;
    }



    /**
     * 设置表头数据
     * @param id 外销发票表头ID
     * @param gHead 外销发票表头数据
     * @param head 出货信息表头数据
     */
    public void setHeadData(String id,BizExportGoodsSellHead gHead, BizExportGoodsHead head,UserInfoToken userInfoToken) {
        gHead.setId(id);
        // 发票号【同出货单号，允许修改】
        gHead.setInvoiceNo(head.getExportNo());
        // 出货单号【确认时带出，不允许修改】
        gHead.setExportNo(head.getExportNo());
        // 合同号【确认时带出，不允许修改】
        gHead.setContractNo(head.getContractNo());
        // 发票客户【确认时带出-客户，不允许修改】
        gHead.setInvoiceCustomer(head.getCustomer());
        // 销售客户【确认时带出-供应商，不允许修改】
        gHead.setSalesCustomer(head.getSupplier());
        // 信用证号【用户录入】

        // 币种【确认时带出，不允许修改】
        gHead.setCurrency(head.getCurrency());
        // 合计金额【系统带出，汇总表体金额，不允许修改】
        BigDecimal listSumTotal = bizExportGoodsHeadMapper.getListSumTotalById(head.getId());
        log.info("出货信息表体金额汇总为：：{}",listSumTotal);
        gHead.setTotalAmount(listSumTotal);

        // 唛头【确认时带出，不允许修改】
        gHead.setMark(head.getMark());

        // TODO 代理费率%【确认时带出，合同号关联代理协议表头，不允许修改】
        // 目前先默认1
        gHead.setAgentRate(new BigDecimal(1));

        // 代理费（外币）【系统计算=合计金额*代理费率，不允许修改】
        // 如果代理费率为空 那么默认为1
        BigDecimal agentRate = gHead.getAgentRate();
        if (agentRate != null) {
            agentRate = agentRate.divide(new BigDecimal(100));
        }else {
            agentRate = BigDecimal.ONE;
        }
        gHead.setAgentFeeForeign(Optional.ofNullable(listSumTotal).orElse(BigDecimal.ZERO).multiply(agentRate));
        // 预收款日期【用户录入】


        // 作销日期【用户录入】


        // 发票日期【用户录入】

        // 汇率【企业汇率表取值，允许修改】
        EnterpriseRate currentMonthRate = bizExportCommonService.getCurrentMonthRate(null, userInfoToken.getCompany(), gHead.getCurrency());
        if (currentMonthRate == null) {
            throw new ErrorException(400, XdoI18nUtil.t("币制 " +  gHead.getCurrency() + " " + "未获取到当月汇率"));
        }
        log.info("企业汇率：{}",currentMonthRate);
        // 当月汇率 如果为空 就默认1
        BigDecimal currentRate = Optional.ofNullable(currentMonthRate.getUsdRate()).orElse(BigDecimal.ONE);
        gHead.setExchangeRate(currentRate);
        // -------------------- 代理费外币发生变化，下面三个都要变化 -------------------------------
        // 代理费（价税合计）【系统计算=代理费（外币）*汇率，不允许修改】
        BigDecimal agentFeeTotal = gHead.getAgentFeeForeign().multiply(currentRate);
        gHead.setAgentFeeTotal(agentFeeTotal);

        // 代理费税额【系统计算=代理费（价税合计）*0.06，不允许修改】
        BigDecimal agentFeeTax = agentFeeTotal.multiply(new BigDecimal(0.06));
        gHead.setAgentTax(agentFeeTax);


        // 代理费（不含税金额） 代理费（含税金额）-代理费税额
        BigDecimal agentFeeNoTax = agentFeeTotal.subtract(agentFeeTax);
        gHead.setAgentFeeExTax(agentFeeNoTax);



        // 发送财务系统【0是1否，默认为是，允许修改】
        gHead.setSendFinance("0");

        // 备注【用户录入】

        // 是否红冲【0是1否，新增默认为1否，红冲成功改为0是，不允许修改】
        gHead.setIsRedFlush("0");

        // 单据状态【0编制/1确认/2作废，不允许修改，置灰】
        gHead.setDataState("0");

        // 确认时间【确认按钮成功提交时间，不允许修改，置灰】

        // 制单人【自动识别最后操作人，显示登陆名，不允许修改，置灰】
        gHead.setCreateBy(userInfoToken.getUserNo());
        gHead.setInsertUserName(userInfoToken.getUserName());
        // 制单时间【系统自动生成最后操作时间，不允许修改，置灰】
        gHead.setCreateTime(new Date());
        gHead.setParentId(head.getId());

    }


    /**
     * 设置表体数据
     * @param headId 表头id
     * @param userInfoToken 用户信息
     * @param gList 导出发票表体数据
     * @param list 出货信息表体数据
     */
    private void setListData(String headId,UserInfoToken userInfoToken,List<BizExportGoodsSellList> gList, List<BizExportGoodsList> list) {
        for (BizExportGoodsList llList : list) {
            BizExportGoodsSellList ggList = new BizExportGoodsSellList();


            ggList.setId(UUID.randomUUID().toString());
            ggList.setParentId(headId);
            ggList.setCreateBy(userInfoToken.getUserNo());
            ggList.setCreateTime(new Date());
            ggList.setInsertUserName(userInfoToken.getUserName());
            ggList.setParentListId(llList.getId());


            // 商品名称
            ggList.setGoodsName(llList.getProductName());

            // 商品描述【确认时带出，不允许修改】
            ggList.setGoodsDesc(llList.getProductDesc());

            // TODO 开票名称【合同号+商品名称+商品描述关联合同与协议表体带出，不允许修改】


            // 数量【确认时带出，不允许修改，右下角汇总显示】
            ggList.setQuantity(llList.getQty());

            // 单位【确认时带出，不允许修改】
            ggList.setUnit(llList.getUnit());

            // 包装数量【装箱信息TAB-结束箱号，确认时带出，不允许修改】
            ggList.setPackageQuantity(llList.getEndStartNo());

            // 包装【装箱信息TAB-包装样式，确认时带出，不允许修改】
            ggList.setPackageStyle(llList.getPackageStyle());

            // 单价【确认时带出，不允许修改】
            ggList.setUnitPrice(llList.getPrice());

            // 金额【确认时带出，不允许修改，右下角汇总显示】
            ggList.setAmount(llList.getAmount());

            // 毛重【装箱信息TAB-毛重，右下角汇总显示】
            ggList.setGrossWeight(llList.getGrossWeight());

            // 净重【装箱信息TAB-净重，右下角汇总显示】
            ggList.setNetWeight(llList.getNetWeight());


            // 添加表体数据
            gList.add(ggList);
        }

    }


    /**
     * 修改准运与到货 不修改更新时间 更新人
     * @param bizExportGoodsHeadParam 导出商品头参数
     * @param userInfo 用户信息
     * @return 出货信息表头 DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public BizExportGoodsHeadDto updateApprovedShipmentArrival(BizExportGoodsHeadParam bizExportGoodsHeadParam, UserInfoToken userInfo) {
        try {
            BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadMapper.selectByPrimaryKey(bizExportGoodsHeadParam.getId());
            bizExportGoodsHeadDtoMapper.updatePo(bizExportGoodsHeadParam, bizExportGoodsHead);
            // 更新数据
            int update = bizExportGoodsHeadMapper.updateByPrimaryKey(bizExportGoodsHead);
            return update > 0 ? bizExportGoodsHeadDtoMapper.toDto(bizExportGoodsHead) : null;
        } catch (DataIntegrityViolationException e){
            log.error("计算保费金额溢出：{}",e.getMessage());
            throw new ErrorException(400,"计算保费金额溢出");
        } catch (Exception e) {
            throw new RuntimeException("更新失败！",e);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizExportGoodsHeadDto> cancel(String id, UserInfoToken userInfo) {
        ResultObject<BizExportGoodsHeadDto> resultObject = ResultObject.createInstance(true,"更新成功");
        if (StringUtils.isBlank(id)){
            throw new RuntimeException("主键ID不能为空");
        }
        try {
            // 更新出货信息表头、表体、外销发票表头、外销发票表体的ID
            bizExportGoodsHeadMapper.cancel(id,userInfo.getUserNo(),userInfo.getUserName());

        } catch (Exception e) {
            throw new RuntimeException("更新失败！",e);
        }
        return resultObject;
    }










    /**
     *
     * 第3条线 -> 烟机设备 -> 进货单发送您内审
     * @param param 进货单参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject sendAudit(BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审核成功"));
        try {
            BizExportGoodsHead head = bizExportGoodsHeadMapper.selectByPrimaryKey(param.getId());
            Assert.notNull(head, "进货单不存在，请刷新！");
            if (!"1".equals(head.getApprovalStatus())) {
                throw new ErrorException(400, "只有内审状态为 1 未审批的数据允许操作发送审批");
            }
            if (!"1".equals(head.getDataState())){
                throw new ErrorException(400, "只有单据全部确认，才允许操作发送审批");
            }
            // 校验外商发票是否确认
            Integer count = bizExportGoodsHeadMapper.checkInvoiceIsConfirm(head.getId());
            if (count > 0){
                throw new ErrorException(400, "只有单据全部确认，才允许操作发送审批");
            }
            if (!CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue().equals(head.getApprovalStatus())
                    && !CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue().equals(head.getApprovalStatus())){
                throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
            }
            // 调用发送审核 审批流
            // 解析返回信息
            NextNodeInfoBatchVo batchVo = commonService.startFlowBatch(param.getBusinessType(), param.getBillType(), param.getIds(), userInfo);
            // 记录flowInstanceId
            head.setExtend2(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));

            // 表头更新状态
            head.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
            head.setUpdateBy(userInfo.getUserNo());
            head.setUpdateTime(new Date());
            head.setUpdateUserName(userInfo.getUserName());
            bizExportGoodsHeadMapper.updateByPrimaryKey(head);

            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(head.getId());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
            return result;
        } catch (InterruptedException e) {
            log.error("发送内审失败：{}",e.getMessage());
            throw new RuntimeException(StringUtils.isBlank(e.getMessage())?"发送内审失败":e.getMessage());
        }
    }



    /**
     * 第3条线 -> 烟机设备 -> 内审通过
     * @param param 进货单参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject audit(BizExportGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核成功"));
        try {
            List<String> sids = param.getIds();
            if (CollectionUtils.isEmpty(sids)) {
                throw new ErrorException(400, "请选择要审核的数据！");
            }
            //校验审核状态
            if (bizExportGoodsHeadMapper.checkApprovalStatus(sids, CommonEnum.OrderApprStatusEnum.APPROVING.getValue()) > 0){
                throw new ErrorException(400, "存在不是<审批中>状态的数据，不允许操作！");
            }
            List<WorkFlowParam> flows = bizExportGoodsHeadMapper.selectBySids(sids);
            Map<String, String> flowInstanceMap = flows.stream().collect(Collectors.toMap(WorkFlowParam::getFlowInstanceId, WorkFlowParam::getSid));
            // 调用 审批通过-审批流
            // 解析 返回结果
            List<NextNodeInfoVo> nextNodeInfoVos = commonService.passBatch(flows, param.getApprMessage(), userInfo);

            for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
                BizExportGoodsHead head = bizExportGoodsHeadMapper.selectByPrimaryKey(flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId()));
                if (nextNodeInfoVo.isFinish()) {
                    head.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                    head.setUpdateBy(userInfo.getUserNo());
                    head.setUpdateTime(new Date());
                    head.setUpdateUserName(userInfo.getUserName());
                    bizExportGoodsHeadMapper.updateByPrimaryKey(head);
                    nextNodeInfoVo.setNodeName("审核通过");
                }
                //新增审核记录
                AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
                aeoAuditInfo.setBusinessSid(param.getIds().get(0));
                aeoAuditInfo.setApprNote(param.getApprMessage());
                aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName(), userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
            }
            return result;
        } catch (InterruptedException e) {
            log.error("内审失败：{}",e.getMessage());
            throw new RuntimeException(StringUtils.isBlank(e.getMessage())?"内审失败":e.getMessage());
        }
    }

    /**
     * 第3条线 -> 烟机设备 -> 内审退回
     * @param param 进货单参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject reject(BizExportGoodsHeadParam param, UserInfoToken userInfo) {

        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核退回成功"));
        // 只有待审核数据才能操作
        if (bizExportGoodsHeadMapper.checkApprovalStatus(param.getIds(), CommonEnum.OrderApprStatusEnum.APPROVING.getValue()) > 0){
            throw new ErrorException(400, "存在不是<审批中>状态的数据，不允许操作！");
        }


        try {
            List<WorkFlowParam> flows = bizExportGoodsHeadMapper.selectBySids(param.getIds());
            // 审核退回 对接审批流
            // 解析 返回结果
            List<NextNodeInfoVo> nextNodeInfoVos = commonService.rejectBatch(flows, param.getApprMessage(), userInfo);

            for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
                //回退到发起人
                BizExportGoodsHead head = bizExportGoodsHeadMapper.selectByPrimaryKey(param.getIds().get(0));

                head.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
                head.setUpdateBy(userInfo.getUserNo());
                head.setUpdateTime(new Date());
                head.setUpdateUserName(userInfo.getUserName());
                bizExportGoodsHeadMapper.updateByPrimaryKey(head);

                //新增审核记录
                AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
                aeoAuditInfo.setBusinessSid(param.getIds().get(0));
                aeoAuditInfo.setApprNote(param.getApprMessage());
                aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
            }
        } catch (Exception e) {
            log.error("内审退回失败：{}",e.getMessage());
            throw new RuntimeException(StringUtils.isBlank(e.getMessage())?"内审退回失败":e.getMessage(),e);
        }
        return result;
    }




    /**
     * 第3条线 -> 烟机设备 -> 查询内审列表
     * @param param 进货单参数
     * @param pageParam 分页参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject<List<BizExportGoodsHeadDto>> getAeoListPaged(BizExportGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalListByParam(param.getBusinessType(), CommonEnum.WORKFLOW_CONSTANT_ENUM.getValue(param.getApprovalStatus()));
        List<String> ids = (List<String>) result.getResult();

        // 如果ids为空，直接返回空分页结果
        if (ids == null || ids.isEmpty()) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }

        param.setIds(ids);

        param.setTradeCode(userInfo.getCompany());

        // 启用分页查询
        BizExportGoodsHead head = bizExportGoodsHeadDtoMapper.toPo(param);
        Page<BizExportGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizExportGoodsHeadMapper.getAeoList(head));
        List<BizExportGoodsHeadDto> headDtos = page.getResult().stream()
                .map(bizExportGoodsHeadDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(headDtos, (int) page.getTotal(), page.getPageNum());
    }


    public ResultObject<List<BizExportGoodsHeadDto>> getListPagedToCustomerAccount(BizExportGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadDtoMapper.toPo(param);
        bizExportGoodsHead.setTradeCode(userInfo.getCompany());
        Page<BizExportGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizExportGoodsHeadMapper.getListPagedToCustomerAccount(bizExportGoodsHead));
        // 将PO转为DTO返回给前端
        List<BizExportGoodsHeadDto> bizExportGoodsHeadDtoList = page.getResult().stream()
                .map(bizExportGoodsHeadDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(bizExportGoodsHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }


    /**
     * 第9条线 -> 出口管理 -> 获取可提取的外商合同
     * 列表显示
     *  1.合同号
     *  2.签约日期
     *  3.供应商
     *  4.客户
     *
     * @param params 出口管理参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> getExtractContractList(BizExportGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> resultObject = ResultObject.createInstance(true, "获取成功！");
        String contractNo = params.getContractNo();
        List<BizENonStateAuxmatAggrContractHead> tempList = exportContractHeadMapper.getExtractAuxmatContractList(contractNo,userInfo.getCompany());
        tempList.removeIf(item -> {
            // 过滤掉抄数量的合同
            String contractNoTemp = item.getContractNo();
            String tradeCode = userInfo.getCompany();
            // 获取 出货信息已经提取的 合同表体 总数量
            BigDecimal count = exportContractHeadMapper.getExportExtractCountByContractNo(contractNoTemp,tradeCode);
            if (count == null){
                count = new BigDecimal(0);
            }
            // 如果total数量大于count数量，则不进行移除
            if (item.getTotalQty().compareTo(count) > 0){
                return false;
            }
            return true;
        });


        if(CollectionUtils.isEmpty(tempList)){
            resultObject.setMessage("未查询到相关合同信息");
            return resultObject;
        }else {
            List<BizENonStateAuxmatAggrContractHeadDto> collect = tempList.stream().map(it -> {
                BizENonStateAuxmatAggrContractHeadDto dto = exportContractHeadDtoMapper.toDto(it);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setData(collect);
        }
        return resultObject;
    }



    /**
     * 第9条线 -> 出口管理 -> 获取可提取的外商合同
     * 提取
     * @param params 出口管理参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject extractContract(BizExportGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        try {
            // 获取合同的ID
            String id = params.getId();
            String contractNo = params.getContractNo();
            if (StringUtils.isBlank(id)){
                throw new ErrorException(400, "合同ID不能为空");
            }

            // 获取外商合同表头信息
            BizENonStateAuxmatAggrContractHead contractHead = exportContractHeadMapper.selectByPrimaryKey(id);
            // 获取出口合同表体信息
            List<BizENonStateAuxmatAggrContractList> contractList = exportContractListMapper.getListByHeadId(id, userInfo.getCompany());


            log.error("表头信息：{}",contractHead);
            contractList.forEach(it -> {
                log.error("表体信息：{}",it);
            });

            // 获取当前合同的最大序号
            Integer serialNo = bizExportGoodsHeadMapper.getMaxSerialNo(contractNo,userInfo.getCompany());
            if (serialNo == null){
                serialNo = 1;
            }

            // 设置提取信息
            BizExportGoodsExtractHeadList headList = new BizExportGoodsExtractHeadList();
            headList.setHead(new BizExportGoodsHead());
            headList.setHeadList(new ArrayList<BizExportGoodsList>());
            // 提取表头信息
            String sid = UUID.randomUUID().toString().replaceAll("-", "");
            // 获取 客商信息 根据 客商编码
            BizMerchant client = bizMerchantMapper.getCustomerByCode(contractHead.getBuyer(),userInfo.getCompany());
            String customerCode = bizMerchantMapper.getCodeByName("中国烟草上海进出口有限责任公司",userInfo.getCompany());
            // 获取币制信息
            // 设置表头信息
            setExportGoodsHead(sid,contractHead,headList.getHead(),userInfo,serialNo,client,customerCode);
            // 提取外商表体信息
            contractList.forEach(contractListItem -> {
                BizExportGoodsList goodsList = new BizExportGoodsList();
                // 需要汇总，当前非作废状态下合同，商品名称，对应的数量总和 ，和 进货管理当前合同提取的商品数量总和 ，如果前者大于后者，则可以提取
                // 根据合同号+商品名称，获取外商合同表体总数量
                BigDecimal count = bizExportGoodsHeadMapper.getGNameCountByContractNo(contractNo,contractListItem.getGName(),userInfo.getCompany());
                // 获取当前合同非作废状态下，商品名称，对应的数量总和
                BigDecimal count2 = bizExportGoodsHeadMapper.getExportGNameCountByContractNo(contractNo,contractListItem.getGName(),userInfo.getCompany());
                // 如果前者大于后者，则可以提取
                if(count.compareTo(count2) > 0){
                    // 提取表体信息
                    setExportGoodsList(contractListItem,goodsList,sid,userInfo);
                    // 提取表体信息
                    headList.getHeadList().add(goodsList);
                }
            });

            // 插入表头表体信息
            bizExportGoodsHeadMapper.insert(headList.getHead());
            // 重置表头返回信息
            headList.getHead().setCreateBy(userInfo.getUserName());
            // 插入表体信息
            headList.getHeadList().forEach(it -> {
                bizExportGoodsListMapper.insert(it);
            });
            // 返回信息
            resultObject.setData(headList);
            return resultObject;
        } catch (ErrorException e) {
            log.error("提取外商合同错误：{}",e.getMessage());
            throw new RuntimeException("提取外商合同错误:" + e.getMessage());
        }
    }

        //
        //
        //
        //    /**
        //     * 设置出货管理表头信息
        //     * @param id 出货管理 主键Id
        //     * @param contractHead 合同表头信息
        //     * @param head 出货管理表头信息
        //     * @param userInfo 用户信息
        //     * @param serialNo 序号
        //     */
        //    public void setExportGoodsHead(String id, BizENonStateAuxmatAggrContractHead contractHead, BizExportGoodsHead head, UserInfoToken userInfo, Integer serialNo) {
        //        head.setId(id);
        //        head.setTradeCode(userInfo.getCompany());
        //        head.setCreateBy(userInfo.getUserNo());
        //        head.setCreateTime(new Date());
        //        head.setInsertUserName(userInfo.getUserName());
        //
        //        head.setParentId(contractHead.getId());
        //
        //        // 1	出货单号	字符型（60）	文本	是	用户录入 唯一性校验
        //        head.setExportNo(null);
        //        // 2	合同号  	字符型（60）	文本	是	<新增>操作带出，不允许修改
        //        head.setContractNo(contractHead.getContractNo());
        //        // 3	客户	字符型（200）	下拉框	是	<新增>操作带出-客户，不允许修改
        //        head.setCustomer(contractHead.getBuyer());
        //        // 4	客户地址	字符型（60）	文本	是	<新增>操作带出，允许修改 根据客户，关联【客商信息】带出“客商地址”
        //        head.setCustomerAddress(client.getMerchantAddress());
        //        // 5	供应商	字符型（200）	下拉框	是	<新增>操作带出-供应商，不允许修改
        //        head.setSupplier(contractHead.getSupplier());
        //        //  6	贸易国别	字符型（60）	文本	是	<新增>操作带出，允许修改 根据合同客户，关联【客商信息】带出“贸易国别”
        //        head.setTradeCountry(client.getTradeCountry());
        //        //  7	经营单位	字符型（200）	下拉框	是	【基础资料-客商信息】 //默认“中国烟草上海进出口有限责任公司”允许修改
        //        head.setManageUnit(Optional.ofNullable(customerCode).orElse("中国烟草上海进出口有限责任公司"));
        //        //  8	付款方式	字符型（50）	文本	否	<新增>操作带出-收汇方式 ，不允许修改
        //        head.setPaymentType(contractHead.getPaymentMethod());
        //        //  9	币种	字符型（10）	下拉框	是	<新增>操作带出 ，不允许修改
        //        head.setCurrency(contractHead.getCurr());
        //        //  10	运输方式	字符型（10）	下拉框	否	默认0海运，允许修改 系统参数0海运1空运2陆运 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setTransportType("0");
        //        //  11	价格条款	字符型（20）	下拉框	否	<新增>操作带出 ，不允许修改
        //        head.setPriceTerms(contractHead.getPriceTerm());
        //        //  12	价格条款对应港口	字符型（50）	下拉框	否	<新增>操作带出 ，不允许修改
        //        head.setPriceTermsPort(contractHead.getPriceTermPort());
        //        //  13	发货单位	字符型（60）	文本	是	<新增>操作带出-供应商，不允许修改
        //        head.setDeliveryUnit(contractHead.getSupplier());
        //        //  14	包装种类	字符型（30）	下拉框	否	【企业自定义参数-包装信息】 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setPackageType(null);
        //        //  15	包装数量   	数值型（10）	文本	否	用户录入 <新增>操作，根据合同号关联<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setPackageNum(null);
        //        //  16	发货单位所在地	字符型（50）	下拉框	否	企业自定义参数-城市 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setDeliveryUnitLocation(null);
        //        //  17	装运人SHIPPER	数值型（300）	文本	否	<新增>操作带出-根据供应商关联【客商信息】-装运人SHIPPER，可修改
        //        head.setShipper(client.getShipper());
        //        //  18	收货人CONSIGNEE	数值型（300）	文本	否	<新增>操作带出-根据合同客户关联【客商信息】-收货人CONSIGNEE，可修改
        //        head.setConsignee(client.getConsignee());
        //        //  19	通知人NOTIFY PARTY 	数值型（300）	文本	否	<新增>操作带出-根据客户关联【客商信息】-通知人NOTIFY PARTY ，可修改
        //        head.setNotifyParty(client.getNotifyParty());
        //        //  20	总毛重	数值型（19，6）	文本	否	用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setGrossWeight(null);
        //        //  21	总净重	数值型（19，6）	文本	否	<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setNetWeight(null);
        //        //  22	总皮重    	数值型（19，6）	文本	否	系统计算=总毛重-总净重，不允许修改
        //        head.setGrossWeight(null);
        //        //  23	装箱说明	数值型（200）	文本	否	用户录入
        //        head.setNetWeight(null);
        //        //  24	运输工具名称	数值型（50）	文本	否	用户录入
        //        head.setTransportationToolsName(null);
        //        //	开航日期	日期型（10）	日期控件	否	用户录入
        //        head.setSailingDate(null);
        //        //  25	唛头	数值型（300）	文本	否	<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setMark(null);
        //        //  26	装运港	字符型（50）	下拉框	否	企业自定义参数-城市 <新增>操作带出，允许修改
        //        head.setPortOfShipment(contractHead.getPortOfLoading());;
        //        //  27	目的地/港	字符型（50）	下拉框	否	企业自定义参数-城市 <新增>操作带出，允许修改 <新增>操作带出，允许修改
        //        head.setPortOfDestination(contractHead.getPortOfDestination());
        //        //  28	装运期限	日期型（10）	日期控件	否	用户录入  <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setShipmentDate(null);
        //        //  29	险别	字符型（100）	下拉框	否	企业自定义参数-保险类别 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setInsuranceType(null);
        //        //  30	保费币种 	字符型（10）	下拉框	否	海关参数自定义，显示为三位英文字母，默认为USD，允许修改
        //        head.setInsuranceCurrency("USD");
        //        //  31	投保加成%	数值型（19，6）	文本	否	默认1%，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setInsuranceAddRate(null);
        //        //  32	保费费率(%)	数值型（19，6）	文本	否	默认0.0267%,允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setInsuranceRate(null);
        //        //  33	保险费  	数值型（19，6）	文本	否	系统计算=表体金额汇总*（1+投保加成）*保费费率 不允许修改
        //        head.setInsuranceFee(null);
        //        //  34	投保人	字符型（200）	下拉框	是	【基础资料-客商信息】、【基础资料-客户信息】
        //        head.setInsurer(null);
        //        //  35	运费	数值型（19，6）	文本	否	用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setFreight(null);
        //        //  36	运费币种	字符型（10）	下拉框	否	海关参数自定义，显示为三位英文字母，默认为USD，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改
        //        // TODO 优先从分析单带出，带不出默认0 分析单实体目前默认没有
        //        head.setFreightCurrency(null);
        //        //  37	仓储地址	数值型（300）	文本	否	<新增>操作带出-根据委托方关联【客商信息】-仓储地址，可修改
        //        head.setWarehouseAddress(client.getWarehouseAddress());
        //        //  38	联系人	数值型（20）	文本	否	<新增>操作带出-根据委托方关联【客商信息】-联系人，可修改
        //        head.setContactPerson(client.getContactPerson());
        //        //  39	联系电话   	数值型（20）	文本	否	<新增>操作带出-根据委托方关联【客商信息】-联系电话可修改
        //        head.setContactPhone(client.getContactPhone());
        //        //  40	备注	字符型（200）	文本	否	用户录入
        //        head.setRemark(null);
        //        //  41	发送报关	字符型（10）	下拉框	是	0是1否 系统带出，不允许修改 数据新增时为1否，操作<发送报关>成功时，置为0是
        //        head.setSendCustoms("0");
        //        //  42	单据状态	字符型（10）	下拉框	否
        //        //      0编制：未点击“确认”功能按钮
        //        //      1确认：点击表头“确认”功能按钮，且成功提交
        //        //      2作废：点击列表“作废”功能按钮，且成功提交
        //        head.setDataState("0");
        //        //  43	确认时间	日期型（18）	日期控件	否	点击“确认”功能按钮，且成功提交的时间：yyyy-mm-dd hh:mm:ss 不允许修改，置灰
        //        head.setConfirmTime(null);
        //
        //        head.setApprovalStatus("1");
        //
        //        head.setSerialNo(BigDecimal.valueOf(serialNo));
        //    }
        //
        //
        //    /**
        //     * 设置出货信息表体
        //     * @param contractListItem 外商合同表体
        //     * @param goodsList 出货信息表体
        //     * @param sid 出货信息头ID
        //     * @param userInfo 用户信息
        //     */
        //    private void setExportGoodsList(BizENonStateAuxmatAggrContractList contractListItem, BizExportGoodsList goodsList, String sid, UserInfoToken userInfo) {
        //        goodsList.setId(UUID.randomUUID().toString());
        //        goodsList.setCreateTime(new Date());
        //        goodsList.setCreateBy(userInfo.getUserNo());
        //        goodsList.setInsertUserName(userInfo.getUserName());
        //        goodsList.setParentId(sid);
        //        goodsList.setExtend1(contractListItem.getId());
        //        goodsList.setDataState("0");
        //        goodsList.setTradeCode(userInfo.getCompany());
        //
        //
        //        // 1	商品名称	字符型（80）	文本	是	<新增>操作带出，不允许修改
        //        goodsList.setProductName(contractListItem.getGName());
        //        //  2	商品描述	字符型（100）	文本	否	<新增>操作带出，不允许修改
        //        // TODO 外商合同 未创建 待补充
        //        goodsList.setProductDesc(contractListItem.getGModel());
        //        //  3	规格	字符型（200）	文本	否	<新增>操作带出，不允许修改
        //        goodsList.setSpecification(contractListItem.getSpecifications());
        //        //  4	数量（卷）	数值型（19，6）	文本	是	<新增>操作带出，允许修改、
        //        // TODO 外商合同 未创建 待补充
        //        goodsList.setQtyJ(null);
        //        //右下角汇总显示
        //        //  5	数量（吨）	数值型（19，6）	文本	是	<新增>操作带出，允许修改 右下角汇总显示
        //        // TODO 外商合同 未创建 待补充
        //        goodsList.setQty(contractListItem.getQty());
        //        //  6	单位	字符型（20）	下拉框	是	<新增>操作带出，不允许修改
        //        goodsList.setUnit(contractListItem.getUnit());
        //        //  7	单价	数值型（19，8）	文本	是	<新增>操作带出-单价（吨），允许修改
        //        goodsList.setPrice(contractListItem.getUnitPrice());
        //        //  8	金额 	数值型（19，4）	文本	是	系统计算=数量*单价，不允许修改 修改数量或单价时，都须重新执行计算 右下角汇总显示
        //        goodsList.setAmount(contractListItem.getAmount());
        //        //9	起始箱号	数值型（10）	文本	是	默认为1，允许修改
        //        goodsList.setBoxStartNo(new BigDecimal("1"));
        //        //10	结束箱号	数值型（10）	文本	是	用户录入
        //        goodsList.setEndStartNo(null);
        //        //11	包装样式	字符型（50）	下拉框	是	企业自定义参数-包装信息
        //        goodsList.setPackageStyle(null);
        //        //12	毛重(KG)	数值型（19，4）	文本	是	用户录入 右下角汇总显示
        //        goodsList.setGrossWeight(null);
        //        //13	净重(KG)	数值型（19，4）	文本	是	用户录入 右下角汇总显示
        //        goodsList.setNetWeight(null);
        //        //14	皮重(KG)	数值型（19，4）	文本	是	系统计算=毛重-净重，不允许修改 右下角汇总显示
        //        goodsList.setGrossWeight(null);
        //        //15	长(M)	数值型（19，4）	文本	否	用户录入 右下角汇总显示
        //        goodsList.setELength(null);
        //        //16	宽(M)	数值型（19，4）	文本	否	用户录入 右下角汇总显示
        //        goodsList.setEWidth(null);
        //        //17	高(M) 	数值型（19，4）	文本	否	用户录入 右下角汇总显示
        //        goodsList.setEHeight(null);
        //
        //    }
}