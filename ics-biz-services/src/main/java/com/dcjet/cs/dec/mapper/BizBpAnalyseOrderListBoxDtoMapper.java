package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBox;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderListBoxParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizBpAnalyseOrderListBoxDto
 *
 * <AUTHOR>
 * @date 2025-07-07 17:08:52
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizBpAnalyseOrderListBoxDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizBpAnalyseOrderListBoxDto toDto(BizBpAnalyseOrderListBox po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizBpAnalyseOrderListBox toPo(BizBpAnalyseOrderListBoxParam param);


    BizBpAnalyseOrderListBoxBox  toBoxPo(BizBpAnalyseOrderListBoxParam param);

    /**
     * 数据库原始数据更新
     * @param bizBpAnalyseOrderListBoxParam
     * @param BizBpAnalyseOrderListBox
     */
    void updatePo(BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam, @MappingTarget BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox);

    default void patchPo(BizBpAnalyseOrderListBoxParam bizBpAnalyseOrderListBoxParam , BizBpAnalyseOrderListBox bizBpAnalyseOrderListBox) {
        // TODO 自行实现局部更新
    }
}