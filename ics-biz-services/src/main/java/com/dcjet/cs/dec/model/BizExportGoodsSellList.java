package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 第9条线-非国营贸易出口辅料-外销发票表体
 *
 * <AUTHOR>
 * @date 2025-07-07 14:45:02
 */
@Getter
@Setter
@Table(name = "t_biz_export_goods_sell_list")
public class BizExportGoodsSellList implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     * 字符类型(160)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(240)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 企业10位编码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 组织机构代码
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级id
     * 字符类型(160)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(200)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private String createTimeTo;

    /**
     * 更新人
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(800)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 商品名称，出货信息操作<确认>时带出，不允许修改
     * 字符类型(160)
     * 必填
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 商品描述，出货信息操作<确认>时带出，不允许修改
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "goods_desc")
    private String goodsDesc;

    /**
     * 开票名称，合同号+商品名称+商品描述关联合同与协议表体带出-机打发票名称，不允许修改
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "invoice_name")
    private String invoiceName;

    /**
     * 数量，出货信息操作<确认>时带出，不允许修改，右下角汇总显示
     * 数值类型(19,6)
     * 必填
     */
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * 单位，出货信息操作<确认>时带出，不允许修改
     * 字符类型(40)
     * 必填
     */
    @Column(name = "unit")
    private String unit;

    /**
     * 包装数量，出货信息操作<确认>时带出【装箱信息tab】-结束箱号，不允许修改
     * 数值类型(10,0)
     * 必填
     */
    @Column(name = "package_quantity")
    private BigDecimal packageQuantity;

    /**
     * 包装，出货信息操作<确认>时带出【装箱信息tab】-包装样式，不允许修改
     * 字符类型(100)
     * 必填
     */
    @Column(name = "package_style")
    private String packageStyle;

    /**
     * 单价，出货信息操作<确认>时带出，不允许修改
     * 数值类型(19,8)
     * 必填
     */
    @Column(name = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 金额，出货信息操作<确认>时带出，不允许修改，右下角汇总显示
     * 数值类型(19,4)
     * 必填
     */
    @Column(name = "amount")
    private BigDecimal amount;

    /**
     * 毛重，装箱信息tab-毛重，右下角汇总显示
     * 数值类型(19,4)
     * 必填
     */
    @Column(name = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 净重，装箱信息tab-净重，右下角汇总显示
     * 数值类型(19,4)
     * 必填
     */
    @Column(name = "net_weight")
    private BigDecimal netWeight;

    /**
     * 进货单表体sid
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "parent_list_id")
    private String parentListId;


}