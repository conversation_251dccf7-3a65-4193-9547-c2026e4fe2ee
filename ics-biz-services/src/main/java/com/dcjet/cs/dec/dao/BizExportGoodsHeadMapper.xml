<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizExportGoodsHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizExportGoodsHead">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="export_no" property="exportNo" jdbcType="VARCHAR"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="customer" property="customer" jdbcType="VARCHAR"/>
        <result column="customer_address" property="customerAddress" jdbcType="VARCHAR"/>
        <result column="supplier" property="supplier" jdbcType="VARCHAR"/>
        <result column="trade_country" property="tradeCountry" jdbcType="VARCHAR"/>
        <result column="manage_unit" property="manageUnit" jdbcType="VARCHAR"/>
        <result column="payment_type" property="paymentType" jdbcType="VARCHAR"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="transport_type" property="transportType" jdbcType="VARCHAR"/>
        <result column="price_terms" property="priceTerms" jdbcType="VARCHAR"/>
        <result column="price_terms_port" property="priceTermsPort" jdbcType="VARCHAR"/>
        <result column="delivery_unit" property="deliveryUnit" jdbcType="VARCHAR"/>
        <result column="package_type" property="packageType" jdbcType="VARCHAR"/>
        <result column="package_num" property="packageNum" jdbcType="NUMERIC"/>
        <result column="delivery_unit_location" property="deliveryUnitLocation" jdbcType="VARCHAR"/>
        <result column="shipper" property="shipper" jdbcType="VARCHAR"/>
        <result column="consignee" property="consignee" jdbcType="VARCHAR"/>
        <result column="notify_party" property="notifyParty" jdbcType="VARCHAR"/>
        <result column="gross_weight" property="grossWeight" jdbcType="NUMERIC"/>
        <result column="net_weight" property="netWeight" jdbcType="NUMERIC"/>
        <result column="tare_weight" property="tareWeight" jdbcType="NUMERIC"/>
        <result column="mark" property="mark" jdbcType="VARCHAR"/>
        <result column="port_of_shipment" property="portOfShipment" jdbcType="VARCHAR"/>
        <result column="port_of_destination" property="portOfDestination" jdbcType="VARCHAR"/>
        <result column="shipment_date" property="shipmentDate" jdbcType="DATE"/>
        <result column="insurance_type" property="insuranceType" jdbcType="VARCHAR"/>
        <result column="insurance_currency" property="insuranceCurrency" jdbcType="VARCHAR"/>
        <result column="insurance_add_rate" property="insuranceAddRate" jdbcType="NUMERIC"/>
        <result column="insurance_rate" property="insuranceRate" jdbcType="NUMERIC"/>
        <result column="insurance_fee" property="insuranceFee" jdbcType="NUMERIC"/>
        <result column="insurer" property="insurer" jdbcType="VARCHAR"/>
        <result column="freight" property="freight" jdbcType="NUMERIC"/>
        <result column="freight_currency" property="freightCurrency" jdbcType="VARCHAR"/>
        <result column="warehouse_address" property="warehouseAddress" jdbcType="VARCHAR"/>
        <result column="contact_person" property="contactPerson" jdbcType="VARCHAR"/>
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="send_customs" property="sendCustoms" jdbcType="VARCHAR"/>
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="transport_permit_no" property="transportPermitNo" jdbcType="VARCHAR"/>
        <result column="transport_permit_apply_date" property="transportPermitApplyDate" jdbcType="DATE"/>
        <result column="arrival_confirm_date" property="arrivalConfirmDate" jdbcType="DATE"/>
        <result column="invoiceConfirmTime" property="invoiceConfirmTime" jdbcType="DATE"/>
        <result column="invoiceDataState" property="invoiceDataState" jdbcType="VARCHAR"/>
        <result column="invoiceIsRedFlush" property="invoiceIsRedFlush" jdbcType="VARCHAR"/>
        <result column="invoiceSendFinance" property="invoiceSendFinance" jdbcType="VARCHAR"/>
        <result column="listTotal" property="listTotal" jdbcType="NUMERIC"/>
        <result column="approval_status" property="approvalStatus" jdbcType="VARCHAR"/>
        <result column="box_desc" property="boxDesc" javaType="String" jdbcType="VARCHAR"/>
        <result column="transportation_tools_name" property="transportationToolsName" javaType="String" jdbcType="VARCHAR"/>
        <result column="sailing_date" property="sailingDate" javaType="Date" jdbcType="DATE"/>
        <result column="serial_no" property="serialNo"  jdbcType="NUMERIC"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.export_no, 
            t.contract_no, 
            t.customer, 
            t.customer_address, 
            t.supplier, 
            t.trade_country, 
            t.manage_unit, 
            t.payment_type, 
            t.currency, 
            t.transport_type, 
            t.price_terms, 
            t.price_terms_port, 
            t.delivery_unit, 
            t.package_type, 
            t.package_num, 
            t.delivery_unit_location, 
            t.shipper, 
            t.consignee, 
            t.notify_party, 
            t.gross_weight, 
            t.net_weight, 
            t.tare_weight, 
            t.mark, 
            t.port_of_shipment, 
            t.port_of_destination, 
            t.shipment_date, 
            t.insurance_type, 
            t.insurance_currency, 
            t.insurance_add_rate, 
            t.insurance_rate, 
            t.insurance_fee, 
            t.insurer, 
            t.freight, 
            t.freight_currency, 
            t.warehouse_address, 
            t.contact_person, 
            t.contact_phone, 
            t.remark, 
            t.send_customs, 
            t.confirm_time,
            t.transport_permit_no,
            t.transport_permit_apply_date,
            t.arrival_confirm_datel,
            t.approval_status,
            t.box_desc,
            t.transportation_tools_name,
            t.sailing_date,
            t.serial_no
    </sql>


    <sql id="Base_Invoice_Column_List">
            t.id,
            t.business_type,
            t.data_state,
            t.version_no,
            t.trade_code,
            t.sys_org_code,
            t.parent_id,
            coalesce(t.update_user_name, t.insert_user_name) as create_by,
            coalesce(t.update_time, t.create_time) as create_time,
            t.update_by,
            t.update_time,
            t.insert_user_name,
            t.update_user_name,
            t.extend1,
            t.extend2,
            t.extend3,
            t.extend4,
            t.extend5,
            t.extend6,
            t.extend7,
            t.extend8,
            t.extend9,
            t.extend10,
            t.export_no,
            t.contract_no,
            t.customer,
            t.customer_address,
            t.supplier,
            t.trade_country,
            t.manage_unit,
            t.payment_type,
            t.currency,
            t.transport_type,
            t.price_terms,
            t.price_terms_port,
            t.delivery_unit,
            t.package_type,
            t.package_num,
            t.delivery_unit_location,
            t.shipper,
            t.consignee,
            t.notify_party,
            t.gross_weight,
            t.net_weight,
            t.tare_weight,
            t.mark,
            t.port_of_shipment,
            t.port_of_destination,
            t.shipment_date,
            t.insurance_type,
            t.insurance_currency,
            t.insurance_add_rate,
            t.insurance_rate,
            t.insurance_fee,
            t.insurer,
            t.freight,
            t.freight_currency,
            t.warehouse_address,
            t.contact_person,
            t.contact_phone,
            t.remark,
            t.send_customs,
            t.confirm_time,
            t.transport_permit_no,
            t.transport_permit_apply_date,
            t.arrival_confirm_date,
            case
                when t.data_state = '2'  then '2'
                else coalesce(s.data_state, '0')
            end as "invoiceDataState",
            coalesce(s.is_red_flush, '0') as "invoiceIsRedFlush",
            coalesce(s.send_finance, '0') as "invoiceSendFinance",
            s.confirm_time  as "invoiceConfirmTime",
            (select total from l_temp temp where temp.parent_id = t.id) as "listTotal",
            t.approval_status,
            t.box_desc,
            t.transportation_tools_name,
            t.sailing_date,
            t.serial_no
    </sql>

    <!--
        1	出货单号	文本	出货信息
        2	合同号	文本	出货信息
        3	供应商	下拉框	出货信息
        4	客户	下拉框	出货信息
        5	出货单据状态	下拉框	出货信息
        6	外销发票状态	下拉框	外销发票
        7	制单人	文本	出货信息
        8	制单日期起	日期	出货信息
        9	制单日期止	日期	出货信息
    -->
    <sql id="condition">
        t.trade_code = #{tradeCode}
        <choose>
            <when test="dataState != null and dataState != ''">
                AND t.data_state = #{dataState}
            </when>
            <otherwise>
                AND t.data_state != '2'
            </otherwise>
        </choose>

        <if test="exportNo != null and exportNo != ''">
            and t.export_no like '%' || #{exportNo} || '%'
        </if>
        <if test="contractNo != null and contractNo != ''">
            and t.contract_no like '%' || #{contractNo} || '%'
        </if>
        <if test="customer != null and customer != ''">
            and t.customer = #{customer}
        </if>
        <if test="supplier != null and supplier != ''">
            and t.supplier = #{supplier}
        </if>
        <if test="invoiceDataState != null and invoiceDataState != ''">
            and s.data_state = #{invoiceDataState}
        </if>
        <if test="dataState != null and dataState != ''">
            and t.data_state = #{dataState}
        </if>
        <if test="createBy != null and createBy != ''">
            and coalesce(t.update_user_name, t.insert_user_name) = #{createBy}
        </if>
        <if test="createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= TO_DATE(#{createTimeFrom}, 'yyyy-mm-dd')]]>
        </if>
        <if test="createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) <  TO_DATE(#{createTimeTo}, 'yyyy-mm-dd') + 1 ]]>
        </if>

         <if test="approvalStatus != null and approvalStatus != ''">
            and t.approval_status = #{approvalStatus}
        </if>
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizExportGoodsHead">
        with l_temp as (
            select
                  parent_id as parent_id,
                  sum(amount) as total
            from t_biz_export_goods_list
            where coalesce(parent_id,'')  != ''
            group by parent_id
        )
        SELECT
            <include refid="Base_Invoice_Column_List"/>
        FROM
            t_biz_export_goods_head t
        left join   t_biz_export_goods_sell_head s on t.id = s.parent_id
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="checkAnalyseOrderCode" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        from
            t_biz_export_goods_head t
        where
        t.export_no = #{exportNo} and t.trade_code = #{tradeCode}
        <if test="id != null and id != ''">
             and    t.id != #{id}
        </if>

    </select>

    <select id="getTotalAmount" resultType="java.math.BigDecimal">
        select COALESCE(sum(AMOUNT),0) from T_BIZ_EXPORT_GOODS_LIST where parent_id = #{id}
    </select>
    <select id="getListSumTotalById" resultType="java.math.BigDecimal">
        select
            COALESCE(sum(AMOUNT),0)
        from
            T_BIZ_EXPORT_GOODS_LIST
        where PARENT_ID =#{id}

    </select>


    <select id="getHeadIdByParentId" resultType="java.lang.String">
        select parent_id from t_biz_export_goods_head where id = #{parentId}
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_export_goods_head t where t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--    作废数据 -->
    <delete id="cancel" parameterType="java.lang.String">
        update t_biz_export_goods_head set data_state = '2',update_by = #{userNo},update_user_name = #{userName},update_time = now() where id = #{id};
        update t_biz_export_goods_list set data_state = '2',update_by = #{userNo},update_user_name = #{userName},update_time = now() where parent_id = #{id};
        update t_biz_export_goods_sell_head set data_state = '2',update_by = #{userNo},update_user_name = #{userName},update_time = now() where parent_id = #{id};
        update t_biz_export_goods_sell_list set data_state = '2',update_by = #{userNo},update_user_name = #{userName},update_time = now() where parent_id in (
            select id from t_biz_export_goods_sell_head where parent_id = #{id}
        );
    </delete>

    <!-- 删除数据 -->
    <delete id="deleteById">
        delete from t_biz_export_goods_head where id = #{id};
        delete from t_biz_export_goods_list where parent_id = #{id};
        delete from t_biz_export_goods_sell_head where parent_id = #{id};
        delete from t_biz_export_goods_sell_list where parent_id in (
            select id from t_biz_export_goods_sell_head where parent_id = #{id}
        );
        delete from t_attached where head_id = #{id};
    </delete>




    <select id="checkApprovalStatus" resultType="java.lang.Integer">
        select
            count(1)
        from
            t_biz_export_goods_head
        where id in
        <foreach collection="sids"  item="item" open="(" separator="," close=")" >
            #{item}
        </foreach>
        and approval_status != #{status}
    </select>



    <select id="selectBySids" resultType="com.dcjet.cs.common.model.WorkFlowParam">
        select
            id as sid,
            extend2 as flowInstanceId
        from t_biz_export_goods_head where id in
        <foreach collection="sids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

    <select id="getAeoList" resultType="com.dcjet.cs.dec.model.BizExportGoodsHead">

        select
            <include refid="Base_Invoice_Column_List"/>
        from
            t_biz_export_goods_head t
        <where>
            <include refid="condition"></include>
            and t.id in
            <foreach collection="ids"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        </where>
        order by coalesce(t.update_time,t.create_time) desc

    </select>
    <select id="getListPagedToCustomerAccount" resultType="com.dcjet.cs.dec.model.BizExportGoodsHead">
        SELECT t.id,t.export_no AS purchaseNo,t.contract_no,t.CUSTOMER,t.currency AS curr,t.CREATE_TIME,
               sum(tl.amount) AS decTotalToList, sum(tl.qty) AS qtyToList, max(tl.unit) AS unitToList, max(tm.MERCHANDISE_CATEGORIES) AS merchandiseCategoriesToList
        FROM T_BIZ_EXPORT_GOODS_HEAD t
                LEFT JOIN T_BIZ_EXPORT_GOODS_LIST tl ON t.id = tl.parent_id
                LEFT JOIN T_BIZ_E_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD th ON th.id = t.parent_id
                LEFT JOIN T_BIZ_MATERIAL_INFORMATION tm ON tm.g_name = tl.product_name
        WHERE NOT EXISTS (SELECT 1 FROM T_BIZ_CUSTOMER_ACCOUNT tc WHERE tc.PURCHASE_NO_MARK = th.id AND tc.PURCHASE_MARK = '1' AND tc.STATUS != '2' )
          AND NOT EXISTS (SELECT 1 FROM T_BIZ_CUSTOMER_ACCOUNT tc WHERE tc.PURCHASE_NO_MARK = t.id AND tc.PURCHASE_MARK = '2' AND tc.STATUS != '2' )
          AND NOT EXISTS (SELECT 1 FROM T_BIZ_CUSTOMER_ACCOUNT tc WHERE t.contract_no LIKE '%'|| tc.CONTRACT_NO || '%' AND tc.PURCHASE_MARK = '2' AND tc.STATUS != '2' )
          AND t.DATA_STATE = '1' AND th.STATUS = '1'
        <if test="contractNo != null and contractNo != ''">
            and t.CONTRACT_NO like '%'|| #{contractNo} || '%'
        </if>
        <if test="purchaseNo != null and purchaseNo != ''">
            and t.purchase_no like '%'|| #{purchaseNo} || '%'
        </if>
        <if test="businessType != null and businessType != ''">
            and t.BUSINESS_TYPE = #{businessType}
        </if>
        GROUP BY t.id,t.export_no,t.CUSTOMER,t.contract_no,t.currency,t.CREATE_TIME ORDER BY t.create_time DESC
    </select>


    <select id="checkInvoiceIsConfirm" resultType="java.lang.Integer">
        select count(1) from T_BIZ_EXPORT_GOODS_SELL_HEAD where PARENT_ID = #{id} and DATA_STATE != '1'
    </select>



    <select id="getMaxSerialNo" resultType="java.lang.Integer">
        SELECT
            COALESCE(MAX(t.serial_no),0) as serial_no
        FROM
            t_biz_export_goods_head t
        WHERE
            t.contract_no = #{contractNo} and t.trade_code = #{tradeCode}
     </select>

     <select id="getGNameCountByContractNo" resultType="java.math.BigDecimal">
         SELECT
             COALESCE(SUM(tl.qty),0) as qty
         FROM
             t_biz_e_non_state_auxmat_aggr_contract_list  tl
         WHERE
             tl.head_id in (
                 select t.id from t_biz_e_non_state_auxmat_aggr_contract_head  t where t.contract_no = #{contractNo} and t.trade_code = #{tradeCode} and t.status = '1'
             ) and tl.g_name = #{gName}
     </select>


    <select id="getExportGNameCountByContractNo" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(tl.qty),0) as qty
        FROM
            t_biz_export_goods_list  tl
        WHERE
            tl.parent_id in (
                select t.id from t_biz_export_goods_head  t where t.contract_no = #{contractNo} and t.trade_code = #{tradeCode} and t.data_state = '1'
            ) and tl.product_name = #{gName}
    </select>

</mapper>