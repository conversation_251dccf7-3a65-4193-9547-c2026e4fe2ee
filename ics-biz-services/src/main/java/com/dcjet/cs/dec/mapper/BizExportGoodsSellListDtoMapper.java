package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizExportGoodsSellList;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListDto;
import com.dcjet.cs.dto.dec.BizExportGoodsSellListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizExportGoodsSellListDto
 *
 * <AUTHOR>
 * @date 2025-07-07 14:45:02
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizExportGoodsSellListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizExportGoodsSellListDto toDto(BizExportGoodsSellList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizExportGoodsSellList toPo(BizExportGoodsSellListParam param);

    /**
     * 数据库原始数据更新
     * @param bizExportGoodsSellListParam
     * @param BizExportGoodsSellList
     */
    void updatePo(BizExportGoodsSellListParam bizExportGoodsSellListParam, @MappingTarget BizExportGoodsSellList bizExportGoodsSellList);

    default void patchPo(BizExportGoodsSellListParam bizExportGoodsSellListParam , BizExportGoodsSellList bizExportGoodsSellList) {
        // TODO 自行实现局部更新
    }
}