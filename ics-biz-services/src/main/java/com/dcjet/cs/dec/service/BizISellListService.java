package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizISellHeadMapper;
import com.dcjet.cs.dec.dao.BizISellListMapper;
import com.dcjet.cs.dec.mapper.BizISellListDtoMapper;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.model.BizIOrderList;
import com.dcjet.cs.dec.model.BizISellHead;
import com.dcjet.cs.dec.model.BizISellList;
import com.dcjet.cs.dto.dec.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * BizISellList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizISellListService extends BaseService<BizISellList> {

    private static final Logger log = LoggerFactory.getLogger(BizISellListService.class);

    @Resource
    private BizISellListMapper bizISellListMapper;

    @Resource
    private BizISellHeadMapper bizISellHeadMapper;

    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;

    @Resource
    private BizISellListDtoMapper BizISellListDtoMapper;

    @Override
    public Mapper<BizISellList> getMapper() {
        return bizISellListMapper;
    }



    /**
     * 获取分页信息
     *
     * @param BizISellListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizISellList>> getListPaged(BizISellListParam BizISellListParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizISellList BizISellList = BizISellListDtoMapper.toPo(BizISellListParam);
        BizISellList.setTradeCode(userInfo.getCompany());
        Page<BizISellList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizISellListMapper.getList( BizISellList));

        return ResultObject.createInstance(page.getResult(), (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param BizISellListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizISellListDto insert(BizISellListParam BizISellListParam, UserInfoToken userInfo) {
        BizISellList BizISellList = BizISellListDtoMapper.toPo(BizISellListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        BizISellList.setSid(sid);
        BizISellList.setInsertUser(userInfo.getUserNo());
        BizISellList.setInsertTime(new Date());
        BizISellList.setTradeCode(userInfo.getCompany());
        BizISellList.setInsertUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = bizISellListMapper.insert(BizISellList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? BizISellListDtoMapper.toDto(BizISellList) : null;
    }

    /**
     * 修改记录
     *
     * @param BizISellListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizISellListDto update(BizISellListParam BizISellListParam, UserInfoToken userInfo) {
        BizISellList bizISellList = bizISellListMapper.selectByPrimaryKey(BizISellListParam.getSid());
        BizISellListDtoMapper.updatePo(BizISellListParam, bizISellList);
        BizISellHead bizISellHead = bizISellHeadMapper.selectByPrimaryKey(bizISellList.getHeadId());
        if(bizISellHead.getSalesDocumentStatus().equals("1")){
            throw new ErrorException(400, XdoI18nUtil.t("确认状态无法编辑!"));
        }

        bizISellList.setUpdateUser(userInfo.getUserNo());
        bizISellList.setUpdateTime(new Date());
        bizISellList.setUpdateUserName(userInfo.getUserName());

        // 计算税额
        // 税额=表体“不含税金额”* 表头-税率“税率”
        BigDecimal taxRate = bizISellHead.getTaxRate();
        if (taxRate != null){
            BigDecimal taxNotIncluded = bizISellList.getTaxNotIncluded();
            BigDecimal amountOfTax = taxNotIncluded.multiply(taxRate).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
            bizISellList.setAmountOfTax(amountOfTax);
        }



        //计算价税合计 税额+不含税金额
        bizISellList.setTotalValueTax(bizISellList.getAmountOfTax() != null ? bizISellList.getAmountOfTax().add(bizISellList.getTaxNotIncluded() != null ? bizISellList.getTaxNotIncluded() : BigDecimal.ZERO) : BigDecimal.ZERO.add(bizISellList.getTaxNotIncluded() != null ? bizISellList.getTaxNotIncluded() : BigDecimal.ZERO));


        // 更新数据
        int update = bizISellListMapper.updateByPrimaryKey(bizISellList);
        return update > 0 ? BizISellListDtoMapper.toDto(bizISellList) : null;
    }



    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param BizISellListParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    public ResultObject<BizISellListDto> getPurchaseHeadByOrderSid(BizISellListParam BizISellListParam, UserInfoToken userInfo) {
        ResultObject<BizISellListDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizISellList BizISellListList = bizISellListMapper.getEditDataByHeadId(BizISellListParam.getHeadId());
        if (BizISellListList != null) {
            BizISellListDto BizISellListDto = BizISellListDtoMapper.toDto(BizISellListList);
            resultObject.setData(BizISellListDto);
        }
        return resultObject;
    }

    public ResultObject updateSellList(BizISellListParam bizISellListParam, UserInfoToken userInfo) {
        ResultObject<BizISellListDto> resultObject = ResultObject.createInstance(true,"修改成功！");
        List<String> sids = bizISellListParam.getSids();
        List<BizISellList> list = bizISellListMapper.getListBySids(new BizISellList(){{
            setSids(sids);
        }});
        //批量修改
        for (BizISellList bizISellList : list) {
            bizISellList.setSalesContractNumber(bizISellListParam.getSalesContractNumber());
            bizISellList.setSalesInvoiceNumber(bizISellListParam.getSalesInvoiceNumber());
            bizISellList.setUpdateTime(new Date());
            bizISellList.setUpdateUser(userInfo.getUserNo());
            bizISellListMapper.updateByPrimaryKey(bizISellList);
        }
        String headId = list.get(0).getHeadId();
        BizISellHead bizISellHead = bizISellHeadMapper.selectByPrimaryKey(headId);
        List<BizISellList> select = bizISellListMapper.select(new BizISellList() {{
            setHeadId(headId);
        }});
        String headIdHead = bizISellHead.getHeadId();
        //汇总销售发票号/销售合同号
        String collect = select.stream().map(BizISellList::getSalesContractNumber).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(", "));
        String collect1 = select.stream().map(BizISellList::getSalesInvoiceNumber).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(", "));
        //回填表头销售发票号/销售合同号
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(headIdHead);
        bizIOrderHead.setSalesInvoiceNo(collect1);
        bizIOrderHead.setSalesContractNo(collect);
        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);

        return resultObject;
    }

    public ResultObject getSumDataByInvoiceSell(BizISellListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取汇总数据成功！");
        // Assert.hasLength(param.getHeadId(), "headId不能为空!");
        if (StringUtils.isBlank(param.getHeadId())) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空!"));
        }
        // 获取汇总数据
        BizISellList sumData = bizISellListMapper.getSumDataByInvoiceSell(param.getHeadId());
        resultObject.setData(sumData);
        return resultObject;
    }

    public ResultObject getSumDataByInvoiceSellSummary(BizISellListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取汇总数据成功！");
        // Assert.hasLength(param.getHeadId(), "headId不能为空!");
        if (StringUtils.isBlank(param.getHeadId())) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空!"));
        }
        // 获取汇总数据
        List<BizISellList> sumData = bizISellListMapper.getSumDataByInvoiceSellSummary(param.getHeadId());
        resultObject.setData(sumData);
        return resultObject;
    }

    /**
     * 更新税额属性
     * @param param 销售信息表体
     * @param userInfo 用户信息
     * @return 结果
     */
    public ResultObject updateAmountOfTax(BizISellListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "更新税额属性成功！");
        BizISellList po = BizISellListDtoMapper.toPo(param);

        po.setUpdateUser(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        bizISellListMapper.updateByPrimaryKey(po);

        BizISellListDto dto = BizISellListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }
}