package com.dcjet.cs.dec.service;


import com.aspose.cells.License;
import com.aspose.cells.SaveFormat;
import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsTbMapper;
import com.dcjet.cs.dec.mapper.BizSmokeMachineIncomingGoodsTbDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsDocument;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsTb;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.util.customexport.CustomFunction;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xxl.job.core.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import xdoi18n.XdoI18nUtil;

/**
 * BizSmokeMachineIncomingGoodsTb业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-07-04 21:29:36
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizSmokeMachineIncomingGoodsTbService extends BaseService<BizSmokeMachineIncomingGoodsTb> {

    private static final Logger log = LoggerFactory.getLogger(BizSmokeMachineIncomingGoodsTbService.class);

    @Resource
    private BizSmokeMachineIncomingGoodsTbMapper bizSmokeMachineIncomingGoodsTbMapper;

    @Resource
    private BizSmokeMachineIncomingGoodsTbDtoMapper bizSmokeMachineIncomingGoodsTbDtoMapper;

    @Override
    public Mapper<BizSmokeMachineIncomingGoodsTb> getMapper() {
        return bizSmokeMachineIncomingGoodsTbMapper;
    }


    @Resource
    private BizSmokeCommonService bizSmokeCommonService;

    @Resource
    private BizSmokeMachineIncomingGoodsHeadMapper bizSmokeMachineIncomingGoodsHeadMapper;


    @Value("${dc.export.template:}")
    private String templatePath;



    /**
     * 获取分页信息
     *
     * @param bizSmokeMachineIncomingGoodsTbParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizSmokeMachineIncomingGoodsTbDto>> getListPaged(BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizSmokeMachineIncomingGoodsTb bizSmokeMachineIncomingGoodsTb = bizSmokeMachineIncomingGoodsTbDtoMapper.toPo(bizSmokeMachineIncomingGoodsTbParam);
        bizSmokeMachineIncomingGoodsTb.setTradeCode(userInfo.getCompany());
        Page<BizSmokeMachineIncomingGoodsTb> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizSmokeMachineIncomingGoodsTbMapper.getList( bizSmokeMachineIncomingGoodsTb));
        // 将PO转为DTO返回给前端
        List<BizSmokeMachineIncomingGoodsTbDto> bizSmokeMachineIncomingGoodsTbDtoList = page.getResult().stream()
            .map(bizSmokeMachineIncomingGoodsTbDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizSmokeMachineIncomingGoodsTbDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizSmokeMachineIncomingGoodsTbParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsTbDto insert(BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsTb bizSmokeMachineIncomingGoodsTb = bizSmokeMachineIncomingGoodsTbDtoMapper.toPo(bizSmokeMachineIncomingGoodsTbParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizSmokeMachineIncomingGoodsTb.setId(sid);
        bizSmokeMachineIncomingGoodsTb.setCreateBy(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsTb.setInsertUserName(userInfo.getUserName());
        bizSmokeMachineIncomingGoodsTb.setCreateTime(new Date());
        bizSmokeMachineIncomingGoodsTb.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizSmokeMachineIncomingGoodsTbMapper.insert(bizSmokeMachineIncomingGoodsTb);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizSmokeMachineIncomingGoodsTbDtoMapper.toDto(bizSmokeMachineIncomingGoodsTb) : null;
    }

    /**
     * 修改记录
     *
     * @param bizSmokeMachineIncomingGoodsTbParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsTbDto update(BizSmokeMachineIncomingGoodsTbParam bizSmokeMachineIncomingGoodsTbParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsTb bizSmokeMachineIncomingGoodsTb = bizSmokeMachineIncomingGoodsTbMapper.selectByPrimaryKey(bizSmokeMachineIncomingGoodsTbParam.getId());
        bizSmokeMachineIncomingGoodsTbDtoMapper.updatePo(bizSmokeMachineIncomingGoodsTbParam, bizSmokeMachineIncomingGoodsTb);
        bizSmokeMachineIncomingGoodsTb.setUpdateBy(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsTb.setUpdateUserName(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsTb.setUpdateTime(new Date());

        // 更新数据
        int update = bizSmokeMachineIncomingGoodsTbMapper.updateByPrimaryKey(bizSmokeMachineIncomingGoodsTb);
        return update > 0 ? bizSmokeMachineIncomingGoodsTbDtoMapper.toDto(bizSmokeMachineIncomingGoodsTb) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizSmokeMachineIncomingGoodsTbMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizSmokeMachineIncomingGoodsTbDto> selectAll(BizSmokeMachineIncomingGoodsTbParam exportParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsTb bizSmokeMachineIncomingGoodsTb = bizSmokeMachineIncomingGoodsTbDtoMapper.toPo(exportParam);
        bizSmokeMachineIncomingGoodsTb.setTradeCode(userInfo.getCompany());
        List<BizSmokeMachineIncomingGoodsTbDto> bizSmokeMachineIncomingGoodsTbDtos = new ArrayList<>();
        List<BizSmokeMachineIncomingGoodsTb> bizSmokeMachineIncomingGoodsTbLists = bizSmokeMachineIncomingGoodsTbMapper.getList(bizSmokeMachineIncomingGoodsTb);
        if (CollectionUtils.isNotEmpty(bizSmokeMachineIncomingGoodsTbLists)) {
           bizSmokeMachineIncomingGoodsTbDtos = bizSmokeMachineIncomingGoodsTbLists.stream().map(head -> {
                    BizSmokeMachineIncomingGoodsTbDto dto =  bizSmokeMachineIncomingGoodsTbDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizSmokeMachineIncomingGoodsTbDtos;
    }

    public ResultObject<BizSmokeMachineIncomingGoodsTbDto> getTBByHeadId(String headId, UserInfoToken userInfo) {
        ResultObject<BizSmokeMachineIncomingGoodsTbDto> instance = ResultObject.createInstance(true,"获取成功！");
        if (StringUtils.isBlank(headId)) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空"));
        } else {
            BizSmokeMachineIncomingGoodsTb po = bizSmokeMachineIncomingGoodsTbMapper.getTBByHeadId(headId);
            if (po == null) {
                log.error("当前数据还未新增！");
            }else {
                BizSmokeMachineIncomingGoodsTbDto dto = bizSmokeMachineIncomingGoodsTbDtoMapper.toDto(po);
                instance.setData(dto);
                instance.setSuccess(true);
            }
        }
        return instance;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject updateAndInsert(BizSmokeMachineIncomingGoodsTbParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "保存成功");
        // 判断id是否存在
        String headId = param.getHeadId();
        BizSmokeMachineIncomingGoodsTb po = bizSmokeMachineIncomingGoodsTbDtoMapper.toPo(param);
        BizSmokeMachineIncomingGoodsTb tb = bizSmokeMachineIncomingGoodsTbMapper.getTBByHeadId(headId);
        if (tb == null) {
            // 新增数据
            po.setId(UUID.randomUUID().toString());
            po.setCreateBy(userInfo.getUserNo());
            po.setCreateBy(userInfo.getUserName());
            po.setCreateTime(new Date());
            bizSmokeMachineIncomingGoodsTbMapper.insert(po);
        }else {
            // 确认的时候 默认生成，所以这里只有更新
            po.setId(tb.getId());
            po.setUpdateBy(userInfo.getUserNo());
            po.setUpdateTime(new Date());
            po.setUpdateUserName(userInfo.getUserName());

            // 设置基础金额信息
            bizSmokeCommonService.setBaseTBMoney(po,false,userInfo);
            log.error(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 进货单-投保信息保存信息为： <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< ");
            log.error("{}",po);

            resultObject.setData(po);
            bizSmokeMachineIncomingGoodsTbMapper.updateByPrimaryKey(po);
        }
        return resultObject;
    }




    public ResultObject calcPremium(BizSmokeMachineIncomingGoodsTbParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "计算成功");
        BizSmokeMachineIncomingGoodsTb po = bizSmokeMachineIncomingGoodsTbDtoMapper.toPo(param);
        bizSmokeCommonService.setBaseTBMoney(po,true,userInfo);
        resultObject.setData(po);
        return resultObject;
    }


    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity generateTB(GenerateYJTBParams param, UserInfoToken userInfo) {
        // 单个打印

        String id = param.getId();
        String headId = param.getHeadId();
        GenerateYJTBFileData fileData = new GenerateYJTBFileData();
        // 获取表头数据
        // DecLicenceHeadGenerate head = decLicenceGenerateFileCommonService.getGenerateDecLicenceHead(sid,userInfo);
        // 根据id获取进货单表头信息
        BizSmokeMachineIncomingGoodsHead head = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(headId);
        BizSmokeMachineIncomingGoodsTb tBData = bizSmokeMachineIncomingGoodsTbMapper.selectByPrimaryKey(id);

        // 【单据生成人】 取值【投保信息】
        fileData.setCreateBy("  " + (StringUtils.isNotBlank(tBData.getUpdateUserName())?tBData.getUpdateUserName():tBData.getInsertUserName()));
        // 【被保险人】 取值 【投保信息】
        // 根据客户代码查找客户名称
        if (StringUtils.isNotBlank(tBData.getInsuredPerson())) {
            String customerName1 = bizSmokeCommonService.getCustomerNameByCode(tBData.getInsuredPerson(),userInfo.getCompany());
            fileData.setInsuredPerson("被保险人："+Optional.ofNullable(customerName1).orElse(""));
        }
        // 【编号】 取值 【投保信息】
        fileData.setCode("编号："+Optional.ofNullable(tBData.getCodeNo()).orElse(""));
        // 【货物名称】 取值 【进货单表体商品名称去重汇总】
        String cigarettePaper = bizSmokeMachineIncomingGoodsTbMapper.getListGoodsNameByHeadId(headId);
        fileData.setCigarettePaper(Optional.ofNullable(cigarettePaper).orElse(""));
        // 【包装及数量】、【发票金额】 取值 【进货单表体数量汇总】
        GenerateYJTBFileData tempTotal = bizSmokeMachineIncomingGoodsTbMapper.getSumTotal(headId);
        if (tempTotal != null){
            // 格式化字符 整数部分增加千分位字符
            DecimalFormat df = new DecimalFormat("#,###");
            // 格式化小数部分
            DecimalFormat df2 = new DecimalFormat("#,##0.00");
            if (StringUtils.isNotBlank(tempTotal.getTotalQuantity())){
                fileData.setTotalQuantity(df.format(new BigDecimal(tempTotal.getTotalQuantity())) + " SET");
            }
            if (StringUtils.isNotBlank(tempTotal.getTotalAmount())){
                fileData.setTotalAmount(Optional.ofNullable(tBData.getCurrency()).orElse("")+df2.format(new BigDecimal(tempTotal.getTotalAmount())));
            }
        }
        // 【价格条件】  取值 【进货单表头】
        String priceName = bizSmokeCommonService.getPriceNameByCode(head.getPriceTerm(), userInfo.getCompany());
        fileData.setPriceTerms(Optional.ofNullable(head.getPriceTerm()).orElse(""));
        // 【合同号】 取值 【进货单表头合同号】
        fileData.setContractNo(Optional.ofNullable(head.getContractNo()).orElse(""));

        // 【运输工具】 取值 【投保信息】
        fileData.setTransportName(Optional.ofNullable(tBData.getTransportName()).orElse(""));

        // 【开航日期】 取值 【投保信息】
        // 开航日期格式化成 yyyy-MM-dd
        if (tBData.getDepartureDate()!= null){
            fileData.setGoVesselDate("\r\r\r\r\r"+ DateUtil.format(tBData.getDepartureDate(),"yyyy-MM-dd"));
        }

        // 【运输路线】 取值 【投保信息】
        // 自
        fileData.setPortDeparture("自"+"  "+Optional.ofNullable(tBData.getRouteFrom()).orElse(""));
        // 经
        fileData.setRouteVia("经"+"  "+Optional.ofNullable(tBData.getRouteVia()).orElse(""));
        // 至
        fileData.setRouteTo("至"+"  "+Optional.ofNullable(tBData.getRouteTo()).orElse(""));

        // 【投保险别】 取值 【投保信息】
        fileData.setInsuranceType(Optional.ofNullable(tBData.getInsuranceType()).orElse(""));

        // 【费率】 取值 【投保信息】
        // 将费率转为字符串 后面加%
        if (tBData.getInsuranceRate()!= null){
            // 将bigDecimal 转为字符串
            // String str = tBData.getInsuranceRate().toString();
            // 格式化小数部分
            // str = getString(str, tBData);
            fileData.setInsuranceRate(bizSmokeCommonService.formatDecimalPortion(tBData.getInsuranceRate())+"%");
        }

        // 【保额】 取值 【投保信息】
        if (tBData.getInsuranceAmount()!= null){
            // 将bigDecimal 转为字符串
            // String str = tBData.getInsuranceAmount().toString();
            // str = getString(str, tBData);
            fileData.setInsuranceAmount(Optional.ofNullable(tBData.getCurrency()).orElse("")+bizSmokeCommonService.formatDecimalPortion(tBData.getInsuranceAmount()));
        }


        // 【保费】 取值 【投保信息】
        if (tBData.getPremium()!= null){
            // 将bigDecimal 转为字符串
            // String str = tBData.getPremium().toString();
            fileData.setPremium(bizSmokeCommonService.formatDecimalPortion(tBData.getPremium()));
        }
        // 【当前日期】
        fileData.setCurrentDate("      "+DateUtil.format(new Date(),"yyyy年MM月dd日"));

        // 【发票抬头】 取值 【投保信息】
        if (StringUtils.isNotBlank(tBData.getInvoiceTitle())){
            // 根据客户代码查找客户名称
            String customerName2 = bizSmokeCommonService.getCustomerNameByCode(tBData.getInvoiceTitle(),userInfo.getCompany());
            fileData.setInvoiceTitle("发票抬头："+Optional.ofNullable(customerName2).orElse(""));
        }

        // 【备注】 取值 【投保信息】
        fileData.setRemark(Optional.ofNullable(tBData.getRemark()).orElse(""));


        try {
            Boolean isPdf = "pdf".equals(param.getType());
            // 获取bytes
            byte[] bytes =  licenceGenerateViewPdf(fileData, "yj_tb_order.xlsx", isPdf);
            HttpHeaders h = new HttpHeaders();
            h.setContentDispositionFormData("attachment", URLEncoder.encode("投保单("+head.getPurchaseNo()+")."+param.getType(), CommonVariable.UTF8));
            h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(bytes, h, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException("生成投保单错误预览错误！",e);
        }
    }







    /**
     * 许可证生成预览PDF
     * @param head 黏贴表头数据
     * @param tempFileName 模板存放的路径
     * @param isExportPdf 是否生成pdf
     * @return 文件的byte流
     * @throws Exception
     */
    public  byte[] licenceGenerateViewPdf(GenerateYJTBFileData head,
                                          String tempFileName,
                                          Boolean isExportPdf) throws Exception {
        tempFileName = templatePath + tempFileName;

        // 验证License
        if (!getLicense()) {
            return null;
        }

        Workbook wb = null;
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            wb = new Workbook(tempFileName);
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(wb);
            designer.setCalculateFormula(true);

            List<GenerateYJTBFileData> headList = new ArrayList<>();
            headList.add(head);
            designer.setDataSource("Head", headList);
            // designer.setDataSource("List", listMap.get("decLicenceList"));


            designer.process();
            designer.getWorkbook().calculateFormula(true, new CustomFunction());

            int saveFormat = getSaveFormat(tempFileName, isExportPdf);
            wb.save(os, saveFormat);
            os.close();
        } catch (Exception ex) {
            throw ex;
        } finally {
            if (wb != null) {
                wb.dispose();
            }
        }
        return os.toByteArray();
    }



    /**
     * 获取license
     *
     * @return
     */
    public static boolean getLicense() {
        boolean result = false;
        try {
            InputStream is = ExportService.class.getClassLoader().getResourceAsStream("/files/license.xml");
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 根据文件名获取保存选项
     *
     * @param tempFileName
     * @return
     */
    private static int getSaveFormat(String tempFileName, Boolean isExportPdf) {
        if (isExportPdf) {
            return SaveFormat.PDF;
        }
        String extName = tempFileName.substring(tempFileName.lastIndexOf('.') + 1);
        switch (extName) {
            case "xls":
                return SaveFormat.EXCEL_97_TO_2003;
            case "xlsx":
                return SaveFormat.XLSX;
            case "xlsm":
                return SaveFormat.XLSM;
        }
        return SaveFormat.EXCEL_97_TO_2003;
    }




}