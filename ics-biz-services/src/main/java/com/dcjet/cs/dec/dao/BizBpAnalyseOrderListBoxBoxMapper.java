package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBoxBox;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * （第7条线）出料加工进口薄片-装箱列表-子表的子表Mapper
 */
public interface BizBpAnalyseOrderListBoxBoxMapper extends Mapper<BizBpAnalyseOrderListBoxBox>{

    /**
     * 查询获取数据
     * @param bizBpAnalyseOrderListBoxBox
     * @return
     */
    List<BizBpAnalyseOrderListBoxBox> getList(BizBpAnalyseOrderListBoxBox bizBpAnalyseOrderListBoxBox);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);
}