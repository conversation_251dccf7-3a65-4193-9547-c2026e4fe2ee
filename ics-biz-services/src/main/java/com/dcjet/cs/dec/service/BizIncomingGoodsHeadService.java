package com.dcjet.cs.dec.service;


import com.aspose.cells.License;
import com.aspose.cells.SaveFormat;
import com.aspose.cells.Workbook;
import com.aspose.cells.WorkbookDesigner;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatForContractHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;

import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizISellHeadMapper;
import com.dcjet.cs.dec.dao.BizISellListMapper;
import com.dcjet.cs.dec.dao.BizIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizIncomingGoodsListMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsHeadDtoMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsListDtoMapper;
import com.dcjet.cs.dec.model.*;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dec.model.*;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.util.customexport.CustomFunction;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xxl.job.core.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * TBizIncomingGoodsHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-05-22 15:28:59
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIncomingGoodsHeadService extends BaseService<BizIncomingGoodsHead> {

    private static final Logger log = LoggerFactory.getLogger(BizIncomingGoodsHeadService.class);

    @Resource
    private BizIncomingGoodsHeadMapper tBizIncomingGoodsHeadMapper;

    @Resource
    private BizIncomingGoodsHeadDtoMapper tBizIncomingGoodsHeadDtoMapper;
    @Autowired
    private BizIncomingGoodsListService bizIncomingGoodsListService;

    @Resource
    private BizISellHeadMapper bizISellHeadMapper;

    @Resource
    private BizISellListMapper bizISellListMapper;

    @Override
    public Mapper<BizIncomingGoodsHead> getMapper() {
        return tBizIncomingGoodsHeadMapper;
    }


    @Resource
    private BizIAuxmatForContractHeadDtoMapper bizIAuxmatForContractHeadDtoMapper;

    @Resource
    private BizIAuxmatForContractHeadMapper bizIAuxmatForContractHeadMapper;

    @Resource
    private BizIAuxmatForContractListMapper bizIAuxmatForContractListMapper;

    @Resource
    private BizIncomingGoodsListMapper bizIncomingGoodsListMapper;

    @Resource
    private BizIncomingGoodsListDtoMapper bizIncomingGoodsListDtoMapper;

    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;


    @Resource
    private BizSmokeCommonService bizSmokeCommonService;

    @Value("${dc.export.template:}")
    private String templatePath;

    @Value("${dc.export.temp:}")
    private String tempPath;


    /**
     * 获取分页信息
     *
     * @param tBizIncomingGoodsHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIncomingGoodsHeadDto>> getListPaged(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(tBizIncomingGoodsHeadParam);
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        Page<BizIncomingGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> tBizIncomingGoodsHeadMapper.getList( tBizIncomingGoodsHead));
        // 将PO转为DTO返回给前端
        List<BizIncomingGoodsHeadDto> tBizIncomingGoodsHeadDtoList = page.getResult().stream()
            .map(tBizIncomingGoodsHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(tBizIncomingGoodsHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }


    public ResultObject<List<BizIncomingGoodsHeadList>> getListPagedNew(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(tBizIncomingGoodsHeadParam);
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        Page<BizIncomingGoodsHeadList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> tBizIncomingGoodsHeadMapper.getListPagedNew( tBizIncomingGoodsHead));
        return ResultObject.createInstance(page, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param tBizIncomingGoodsHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsHeadDto insert(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(tBizIncomingGoodsHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        tBizIncomingGoodsHead.setId(sid);
        tBizIncomingGoodsHead.setCreateBy(userInfo.getUserNo());
        tBizIncomingGoodsHead.setCreateTime(new Date());
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        tBizIncomingGoodsHead.setSalesDocumentStatus("-1");

        // 新增数据
        int insertStatus = tBizIncomingGoodsHeadMapper.insert(tBizIncomingGoodsHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? tBizIncomingGoodsHeadDtoMapper.toDto(tBizIncomingGoodsHead) : null;
    }

    /**
     * 修改记录
     *
     * @param tBizIncomingGoodsHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsHeadDto update(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(tBizIncomingGoodsHeadParam.getId());
        tBizIncomingGoodsHeadDtoMapper.updatePo(tBizIncomingGoodsHeadParam, tBizIncomingGoodsHead);
        tBizIncomingGoodsHead.setUpdateBy(userInfo.getUserNo());
        tBizIncomingGoodsHead.setUpdateUserName(userInfo.getUserName());
        tBizIncomingGoodsHead.setUpdateTime(new Date());
        tBizIncomingGoodsHead.setDocumentCreator(userInfo.getUserName());

        // 校验进货单号
        if (StringUtils.isNotBlank(tBizIncomingGoodsHead.getPurchaseNo())){
            // 检验进货单号是否已经存在
            Integer count = tBizIncomingGoodsHeadMapper.selectByPurchaseNo(tBizIncomingGoodsHead.getPurchaseNo(),tBizIncomingGoodsHead.getId(),userInfo.getCompany());
            if (count > 0){
                throw new ErrorException(400, XdoI18nUtil.t("进货单号已经存在！"));
            }
            // 判断进货单号长度是否大于2位
            if (tBizIncomingGoodsHead.getPurchaseNo().length() <= 2) {
                throw new ErrorException(400, XdoI18nUtil.t("进货单号格式不正确！"));
            }
            // 判断最后两位是否是两位流水号
            String substring = tBizIncomingGoodsHead.getPurchaseNo().substring(tBizIncomingGoodsHead.getPurchaseNo().length() - 2);

            // 判断订单号最后两位是否可以转为数字
            try {
                // 获取字符串最后两位
                // 将最后两位设置为序号
                Integer bigDecimal = new Integer(substring);
                tBizIncomingGoodsHead.setSerialNo(bigDecimal);
            }   catch (NumberFormatException e) {
                throw new ErrorException(400, XdoI18nUtil.t("进货单号格式不正确！"));
            }
        }









        // 更新数据
        int update = tBizIncomingGoodsHeadMapper.updateByPrimaryKey(tBizIncomingGoodsHead);


        List<BizIncomingGoodsListParam> incomingList = tBizIncomingGoodsHeadParam.getIncomingList();


        // 校验表体数据

        // 更新表体数据
        if (CollectionUtils.isNotEmpty(incomingList)){
            // 循环更新数据
            for (int i = 0; i < incomingList.size(); i++) {
                BizIncomingGoodsListParam item = incomingList.get(i);
                String message = item.canSubmit(i+1);
                if (StringUtils.isNotBlank(message)){
                    throw new ErrorException(400, XdoI18nUtil.t(message));
                }



                // 自动计算订单表体总值
                // 自动计算总价值
                // 如果金额为空 则自动计算
                if (item.getAmount() == null &&item.getInQuantity() != null && item.getUnitPrice()!= null){
                    item.setAmount(item.getInQuantity().multiply(item.getUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                // 如果金额不为空，那么根据金额 + 单价自动计算数量
                if (item.getAmount() != null &&  item.getUnitPrice()!= null){
                    item.setInQuantity(item.getAmount().divide(item.getUnitPrice(), 10, BigDecimal.ROUND_HALF_UP).setScale(6, BigDecimal.ROUND_HALF_UP));
                }
                // 循环更新表体数据
                BizIncomingGoodsList po = bizIncomingGoodsListDtoMapper.toPo(item);
                bizIncomingGoodsListMapper.updateByPrimaryKey(po);

            }
        }

        // 重新设置信息
        if (StringUtils.isNotBlank(tBizIncomingGoodsHead.getUpdateUserName())){
            tBizIncomingGoodsHead.setCreateBy(tBizIncomingGoodsHead.getUpdateUserName());
        }else {
            tBizIncomingGoodsHead.setCreateBy(tBizIncomingGoodsHead.getInsertUserName());
        }
        // 设置更新时间
        if (tBizIncomingGoodsHead.getUpdateTime() != null){
            tBizIncomingGoodsHead.setCreateTime(tBizIncomingGoodsHead.getUpdateTime());
        }else {
            tBizIncomingGoodsHead.setCreateTime(tBizIncomingGoodsHead.getCreateTime());
        }
        return update > 0 ? tBizIncomingGoodsHeadDtoMapper.toDto(tBizIncomingGoodsHead) : null;
    }


    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        // 删除表头信息，一次只能删除一个
        if(sids.size() != 1){
            throw new ErrorException(400, "只能选择一条数据进行删除！");
        }
        String sid = sids.get(0);
        BizIncomingGoodsHead bizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(sid);
        // 仅编制状态数据允许删除
        if (!bizIncomingGoodsHead.getDataState().equals("0")){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }

        // 判断下游仓库管理是否存在提取数据记录
        int count = bizISellHeadMapper.checkIsExtract(bizIncomingGoodsHead.getPurchaseNo(),userInfo.getCompany());
        if (count > 0){
            throw new ErrorException(400, "仓库管理中存在该票进货单号，不允许删除");
        }
        // 删除表头信息
        tBizIncomingGoodsHeadMapper.deleteBySids(sids);
        // 删除表体信息
        bizIncomingGoodsListMapper.deleteByHeadId(sid);
        // 删除证件信息
        tBizIncomingGoodsHeadMapper.deleteDocumentByHeadId(sid);
        // 删除销售信息表体数据
        tBizIncomingGoodsHeadMapper.deleteSellListByHeadId(sid);
        // 删除销售信息表头数据
        tBizIncomingGoodsHeadMapper.deleteSellHeadByHeadId(sid);

    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIncomingGoodsHeadDto> selectAll(BizIncomingGoodsHeadParam exportParam, UserInfoToken userInfo) {
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(exportParam);
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        List<BizIncomingGoodsHeadDto> tBizIncomingGoodsHeadDtos = new ArrayList<>();
        List<BizIncomingGoodsHead> tBizIncomingGoodsHeadLists = tBizIncomingGoodsHeadMapper.getList(tBizIncomingGoodsHead);
        if (CollectionUtils.isNotEmpty(tBizIncomingGoodsHeadLists)) {
           tBizIncomingGoodsHeadDtos = tBizIncomingGoodsHeadLists.stream().map(head -> {
                    BizIncomingGoodsHeadDto dto =  tBizIncomingGoodsHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return tBizIncomingGoodsHeadDtos;
    }


    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIncomingGoodsHeadList> selectAllNew(BizIncomingGoodsHeadParam exportParam, UserInfoToken userInfo) {
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(exportParam);
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        List<BizIncomingGoodsHeadList> listPagedNew = tBizIncomingGoodsHeadMapper.getListPagedNew(tBizIncomingGoodsHead);
        return listPagedNew;
    }



    /**
     * 进货管理-获取供应商列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getSupplierList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 进货管理-获取港口列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getPortList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getPortList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关港口信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 获取币制信息 (下拉框)
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getCurrList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        params.setTradeCode(userInfo.getCompany());
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getCurrList(params);
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关币制信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 进货管理-获取单位列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getUnitList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        params.setTradeCode(userInfo.getCompany());
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getUnitList(params);
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关单位信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 进货管理-获取价格条款
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getPriceTermList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getPriceTermList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关价格条款信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }


    /**
     * 进货管理 确认
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizIncomingGoodsHeadDto> confirmIncomingGoods(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsHeadDto> resultObject = ResultObject.createInstance(true, "确认成功！");
        BizIncomingGoodsHead po = tBizIncomingGoodsHeadDtoMapper.toPo(params);

        // 设置确认信息
        po.setConfirmTime(new Date());
        po.setDataState("1");
        po.setDocumentStatus("1");
        po.setIsNext("1");
        initSaleReturnData(params.getId(),userInfo);
        po.setSalesDocumentStatus("0");
        // 更新
        int update = tBizIncomingGoodsHeadMapper.updateByPrimaryKey(po);
        if (update <= 0){
            resultObject.setMessage("确认失败！");
            return resultObject;
        }
        BizIncomingGoodsHeadDto dto = tBizIncomingGoodsHeadDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<List<BizIAuxmatForContractHeadDto>> getExtractContractInfo(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject<List<BizIAuxmatForContractHeadDto>> resultObject = ResultObject.createInstance(true, "获取成功！");
        String contractNo = params.getContractNo();
        List<BizIAuxmatForContractHead> tempList = tBizIncomingGoodsHeadMapper.getExtractAuxmatContractList(contractNo,userInfo.getCompany());
        tempList.removeIf(item -> {
            // 过滤掉抄数量的合同
            String contractNoTemp = item.getContractNo();
            String tradeCode = userInfo.getCompany();
            // 获取当前合同提取的商品数量总和
            BigDecimal count = tBizIncomingGoodsHeadMapper.getIncomingCountByContract(contractNoTemp,tradeCode);
            if (count == null){
                count = new BigDecimal(0);
            }
            // 如果total数量大于count数量，则不进行移除
            if (item.getTotalQuantity().compareTo(count) > 0){
                return false;
            }
            return true;
        });


//        List<BizIAuxmatForContractHead> list = tBizIncomingGoodsHeadMapper.getExtractContractInfo(contractNo,userInfo.getCompany());
        if(CollectionUtils.isEmpty(tempList)){
            resultObject.setMessage("未查询到相关合同信息");
            return resultObject;
        }else {
            List<BizIAuxmatForContractHeadDto> collect = tempList.stream().map(it -> {
                BizIAuxmatForContractHeadDto dto = bizIAuxmatForContractHeadDtoMapper.toDto(it);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setData(collect);
        }
        return resultObject;
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject extractContract(BizIAuxmatForContractHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        // 获取合同的ID
        String id = params.getId();
        String contractNo = params.getContractNo();
        if (StringUtils.isBlank(id)){
            throw new ErrorException(400, "合同ID不能为空");
        }

        // 获取外商合同表头信息
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(id);
        // 获取外商合同表体信息
        List<BizIAuxmatForContractList> bizIAuxmatForContractList = bizIAuxmatForContractListMapper.getBizIAuxmatForContractListByHeadid(id);


        log.error("表头信息：{}",bizIAuxmatForContractHead);
        bizIAuxmatForContractList.forEach(it -> {
            log.error("表体信息：{}",it);
        });


        // 提取外商合同表头信息

        // 获取当前合同的最大序号
        Integer serialNo = tBizIncomingGoodsHeadMapper.getCurrentContractNoMaxSerial(contractNo,userInfo.getCompany());
        if (serialNo == null){
            serialNo = 1;
        }

        BizIncomingGoodsExtractHeadList headList = new BizIncomingGoodsExtractHeadList();
        headList.setHead(new BizIncomingGoodsHead());
        headList.setHeadList(new ArrayList<BizIncomingGoodsList>());
        // 提取表头信息
        String sid = UUID.randomUUID().toString().replaceAll("-", "");
        setContractHeadToIncomingGoodsHead(sid,bizIAuxmatForContractHead,headList.getHead(),userInfo,serialNo);
        String curr = bizIAuxmatForContractHead.getCurrency();
        // 提取外商表体信息
        bizIAuxmatForContractList.forEach(it -> {
            BizIncomingGoodsList goodsList = new BizIncomingGoodsList();
            // 需要汇总，当前非作废状态下合同，商品名称，对应的数量总和 ，和 进货管理当前合同提取的商品数量总和 ，如果前者大于后者，则可以提取
            // 获取当前合同提取的商品数量总和
            BigDecimal count = tBizIncomingGoodsHeadMapper.getAuxmatCurrentContractNoSum(contractNo,it.getProductName(),userInfo.getCompany(),it.getSpecification());
            log.error("外商合同 商品名称：{}，商品规格：{}，对应的数量总和：{}",it.getProductName(),it.getSpecification(),count);
            // 获取当前合同非作废状态下，商品名称，对应的数量总和
            BigDecimal count2 = tBizIncomingGoodsHeadMapper.getIncomingGoodsHeadSum(contractNo,it.getProductName(),userInfo.getCompany(),it.getSpecification());
            log.error("进口合同 商品名称：{}，商品规格：{}，对应的数量总和：{}",it.getProductName(),it.getSpecification(),count2);
            // 如果前者大于后者，则可以提取
            if(count.compareTo(count2) > 0){
                // 提取表体信息
                setContractListToIncomingGoodsList(it,goodsList,sid,curr,userInfo);
                // 提取表体信息
                headList.getHeadList().add(goodsList);
            }
        });

        // 插入表头表体信息
        tBizIncomingGoodsHeadMapper.insert(headList.getHead());
        // 重置表头返回信息
        headList.getHead().setCreateBy(userInfo.getUserName());
        // 插入表体信息
        headList.getHeadList().forEach(it -> {
            bizIncomingGoodsListMapper.insert(it);
        });


        // 返回信息
        resultObject.setData(headList);

        return resultObject;
    }


    /**
     * 将外商合同表头信息赋值到进货信息表头
     * @param contractHead 外商合同表头
     * @param incomingGoodsHead 进货信息表头
     * @param serialNo xuhao
     */
    public void setContractHeadToIncomingGoodsHead(
                                                   String id,
                                                   BizIAuxmatForContractHead contractHead,
                                                   BizIncomingGoodsHead incomingGoodsHead,
                                                   UserInfoToken userInfoToken,
                                                   Integer serialNo) {

        incomingGoodsHead.setId(id);

        // 设置当前表头序号
        Integer currentSerialNo = serialNo + 1;
        incomingGoodsHead.setSerialNo(currentSerialNo);
        // 将当前序号格式化成两位数字字符串 比如 1  格式化成 01
        String formatSerialNo = String.format("%02d", currentSerialNo);
        // 进货单号：系统带出，按外商合同号+2位流水号
        // 外商合同字段：contractNo -> 进货表头字段：purchaseNo
        incomingGoodsHead.setPurchaseNo(contractHead.getContractNo() + formatSerialNo);

        // 合同号：<新增>操作带出，不允许修改
        // 外商合同字段：contractNo -> 进货表头字段：contractNo
        incomingGoodsHead.setContractNo(contractHead.getContractNo());

        // 供应商：<新增>操作带出，不允许修改
        // 外商合同字段：supplierName -> 进货表头字段：supplier
        incomingGoodsHead.setSupplier(contractHead.getSupplierName());

        // 购销合同  指【购销合同】表头购销合同号
        incomingGoodsHead.setSellContractNo(contractHead.getExtend1());

        // 起运港：允许修改，<新增>操作带出外商合同的装运港
        // 外商合同字段：portOfShipment -> 进货表头字段：portOfDeparture
        incomingGoodsHead.setPortOfDeparture(contractHead.getPortOfShipment());

        // 目的地/港：允许修改，<新增>操作带出外商合同的目的港
        // 外商合同字段：portOfDestination -> 进货表头字段：destination
        incomingGoodsHead.setDestination(contractHead.getPortOfDestination());

        // 付款方式：<新增>操作带出外商合同付款方式，不允许修改
        // 外商合同字段：paymentMethod -> 进货表头字段：paymentMethod
        incomingGoodsHead.setPaymentMethod(contractHead.getPaymentMethod());

        // 【作销日期】 用户录入
        incomingGoodsHead.setCancelDate(null);

        // 业务类型：业务类型
        // 外商合同字段：businessType -> 进货表头字段：businessType
        incomingGoodsHead.setBusinessType(contractHead.getBusinessType());

        // 企业代码：业务编码
        // 外商合同字段：tradeCode -> 进货表头字段：tradeCode
        incomingGoodsHead.setTradeCode(userInfoToken.getCompany());

        // 组织机构代码：所属机构编码
        // 外商合同字段：sysOrgCode -> 进货表头字段：sysOrgCode
        incomingGoodsHead.setSysOrgCode(userInfoToken.getCompany());

        // 创建人：创建人
        // 外商合同字段：createBy -> 进货表头字段：createBy
        incomingGoodsHead.setCreateBy(userInfoToken.getUserNo());

        // 创建时间：创建时间
        // 外商合同字段：createTime -> 进货表头字段：createTime
        incomingGoodsHead.setCreateTime(new Date());

        // 创建人姓名：创建人姓名
        // 外商合同字段：insertUserName -> 进货表头字段：insertUserName
        incomingGoodsHead.setInsertUserName(userInfoToken.getUserName());

        // 制单人：使用当前登录用户或创建人
        // 外商合同字段：insertUserName -> 进货表头字段：documentCreator
        incomingGoodsHead.setDocumentCreator(userInfoToken.getUserName());

        // 制单时间：当前时间
        // 进货表头字段：documentDate
        incomingGoodsHead.setDocumentDate(new Date());

        // 单据状态：默认为0编制状态
        // 进货表头字段：documentStatus
        incomingGoodsHead.setDocumentStatus("0");

        // 数据状态：默认为正常状态
        // 进货表头字段：dataState
        incomingGoodsHead.setDataState("0");

        // 版本号：版本号
        // 外商合同字段：versionNo -> 进货表头字段：versionNo
        incomingGoodsHead.setVersionNo("1");

        // 价格条款	　	　	　	根据任一表体商品名称+规格，关联匹配【报价表信息】取值，允许修改
        // 外商合同字段：priceTerm -> 进货表头字段：priceTerm
        //
        String priceTerm = tBizIncomingGoodsHeadMapper.getPriceTerm(contractHead.getId());
        incomingGoodsHead.setPriceTerm(priceTerm);
        // 价格条款对应港口
        incomingGoodsHead.setPriceTermPort("1");



        // TODO 待添加 销售状态

    }



    /**
     * 将外商合同表体信息赋值到进货信息表体
     * @param contractList 外商合同表体
     * @param incomingGoodsList 进货信息表体
     * @param incomingGoodsHeadId 进货信息表头ID
     * @param currency 币种（从合同表头获取）
     */
    public void setContractListToIncomingGoodsList(BizIAuxmatForContractList contractList,
                                                   BizIncomingGoodsList incomingGoodsList,
                                                   String incomingGoodsHeadId,
                                                   String currency,
                                                   UserInfoToken userInfoToken) {

        incomingGoodsList.setId(UUID.randomUUID().toString());

        // 商品名称：<新增>操作带出，不允许修改，置灰
        // 外商合同字段：productName -> 进货表体字段：goodsName
        incomingGoodsList.setGoodsName(contractList.getProductName());

        // 规格：<新增>操作带出，不允许修改，置灰
        // 外商合同字段：specification -> 进货表体字段：productModel
        incomingGoodsList.setProductModel(contractList.getSpecification());

        // 进口数量：<新增>操作带出，允许修改
        // 外商合同字段：importQuantity -> 进货表体字段：inQuantity
        incomingGoodsList.setInQuantity(contractList.getImportQuantity());

        // 进口单位：<新增>操作带出，不允许修改，置灰
        // 外商合同字段：importUnit -> 进货表体字段：inUnit
        incomingGoodsList.setInUnit(contractList.getImportUnit());

        // 数量：允许修改，<新增>操作带出
        // 外商合同字段：quantity -> 进货表体字段：quantity
        incomingGoodsList.setQuantity(contractList.getQuantity());

        // 单位：<新增>操作带出，不允许修改，置灰
        // 外商合同字段：unit -> 进货表体字段：unit
        incomingGoodsList.setUnit(contractList.getUnit());

        // 币种：<新增>操作带出-合同表头币种，不允许修改，置灰
        // 合同表头字段：currency -> 进货表体字段：curr
        incomingGoodsList.setCurr(currency);

        // 单价：<新增>操作带出，不允许修改，置灰
        // 外商合同字段：unitPrice -> 进货表体字段：unitPrice
        incomingGoodsList.setUnitPrice(contractList.getUnitPrice());

        // 金额：系统计算=进口数量*单价
        // 外商合同字段：amount -> 进货表体字段：amount（需要重新计算）
        if (contractList.getImportQuantity() != null && contractList.getUnitPrice() != null) {
            BigDecimal calculatedAmount = contractList.getImportQuantity().multiply(contractList.getUnitPrice()).setScale(2,BigDecimal.ROUND_HALF_UP);
            incomingGoodsList.setAmount(calculatedAmount);
        } else {
            incomingGoodsList.setAmount(contractList.getAmount().setScale(2,BigDecimal.ROUND_HALF_UP));
        }

        // // 交货日期：发货日期
        // // 外商合同字段：deliveryDate -> 进货表体字段：deliveryDate
        // incomingGoodsList.setDeliveryDate(contractList.getDeliveryDate());

        // // 总价折美元：总值折美元
        // // 外商合同字段：usdTotal -> 进货表体字段：totalUsd
        // incomingGoodsList.setTotalUsd(contractList.getUsdTotal());

        // 关联进货信息表头ID
        // 进货表体字段：headId
        incomingGoodsList.setHeadId(incomingGoodsHeadId);

        // 合同表体ID：保存外商合同表体的ID，用于追溯
        // 外商合同字段：id -> 进货表体字段：contractListId
        incomingGoodsList.setContractListId(contractList.getId());

        // 业务类型：业务类型
        // 外商合同字段：无此字段，可从表头获取 -> 进货表体字段：businessType
        // incomingGoodsList.setBusinessType(businessType); // 需要从表头传入

        // 企业代码：业务编码
        // 外商合同字段：tradeCode -> 进货表体字段：tradeCode
        incomingGoodsList.setTradeCode(userInfoToken.getCompany());

        // 组织机构代码：所属机构编码
        // 外商合同字段：sysOrgCode -> 进货表体字段：sysOrgCode
        incomingGoodsList.setSysOrgCode(userInfoToken.getCompany());

        // 创建人：创建人
        // 外商合同字段：createBy -> 进货表体字段：createBy
        incomingGoodsList.setCreateBy(userInfoToken.getUserNo());

        // 创建时间：创建时间
        // 外商合同字段：createTime -> 进货表体字段：createTime
        incomingGoodsList.setCreateTime(new Date());

        // 创建人姓名：创建人姓名
        // 外商合同字段：insertUserName -> 进货表体字段：insertUserName
        incomingGoodsList.setInsertUserName(userInfoToken.getUserName());

        // 数据状态：默认为正常状态
        // 进货表体字段：dataState
        incomingGoodsList.setDataState("1");

        // 版本号：版本号
        // 外商合同字段：versionNo -> 进货表体字段：versionNo
        incomingGoodsList.setVersionNo("1");

        // 扩展字段复制（如果需要）
        incomingGoodsList.setExtend1(contractList.getExtend1());
        incomingGoodsList.setExtend2(contractList.getExtend2());
        incomingGoodsList.setExtend3(contractList.getExtend3());
        incomingGoodsList.setExtend4(contractList.getExtend4());
        incomingGoodsList.setExtend5(contractList.getExtend5());
        incomingGoodsList.setExtend6(contractList.getExtend6());
        incomingGoodsList.setExtend7(contractList.getExtend7());
        incomingGoodsList.setExtend8(contractList.getExtend8());
        incomingGoodsList.setExtend9(contractList.getExtend9());
        incomingGoodsList.setExtend10(contractList.getExtend10());

        // 进口发票号码：用户录入，初始为空
        // 进货表体字段：invoiceNo（需要用户后续录入）
        incomingGoodsList.setInvoiceNo(""); // 用户录入
    }

    /**
     * 重新计算金额（当进口数量或单价变更时调用）
     * @param incomingGoodsList 进货信息表体
     */
    public void recalculateAmount(BizIncomingGoodsList incomingGoodsList) {
        if (incomingGoodsList.getInQuantity() != null && incomingGoodsList.getUnitPrice() != null) {
            BigDecimal calculatedAmount = incomingGoodsList.getInQuantity().multiply(incomingGoodsList.getUnitPrice());
            incomingGoodsList.setAmount(calculatedAmount);
        }
    }

    public ResultObject<BizIncomingGoodsHeadDto> getHeadInfoById(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsHeadDto> resultObject = ResultObject.createInstance(true, "获取成功！");
        BizIncomingGoodsHead bizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(params.getId());
        // 重新设置信息
        if (StringUtils.isNotBlank(bizIncomingGoodsHead.getUpdateUserName())){
            bizIncomingGoodsHead.setCreateBy(bizIncomingGoodsHead.getUpdateUserName());
        }else {
            bizIncomingGoodsHead.setCreateBy(bizIncomingGoodsHead.getInsertUserName());
        }
        // 设置更新时间
        if (bizIncomingGoodsHead.getUpdateTime() != null){
            bizIncomingGoodsHead.setCreateTime(bizIncomingGoodsHead.getUpdateTime());
        }else {
            bizIncomingGoodsHead.setCreateTime(bizIncomingGoodsHead.getCreateTime());
        }

        if (bizIncomingGoodsHead == null){
            resultObject.setMessage("未查询到相关数据");
            return resultObject;
        }else {
            BizIncomingGoodsHeadDto dto = tBizIncomingGoodsHeadDtoMapper.toDto(bizIncomingGoodsHead);
            resultObject.setData(dto);
        }
        return resultObject;
    }


    public ResultObject<List<Map<String,String>>> getSupplierListDistinct(BizIAuxmatForContractHeadParam params, UserInfoToken userInfo) {
        ResultObject<List<Map<String,String>>> resultObject = ResultObject.createInstance(true, "获取成功！");
        List<Map<String,String>>  map  = tBizIncomingGoodsHeadMapper.getOrderSupplierListDistinct(userInfo.getCompany());
        if (map.isEmpty()){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }else {
            resultObject.setData(map);
        }
        return resultObject;
    }

    //销售数据初始化
    public void initSaleReturnData(String sid, UserInfoToken userInfo) {
        //删除已有销售信息回单
        deleteMessage(sid,userInfo);
        //初始化销售信息信息
        String sellSid = UUID.randomUUID().toString();
        int i = bizISellHeadMapper.insertIncomingHeadList(sellSid,sid,userInfo);
        if (i == 0){
            throw new ErrorException(400, "初始化销售信息失败");
        }else {
            // 重新计算销售信息表头数据
            List<BizIncomingGoodsList> goodsListList = bizISellListMapper.getListByHeadId(sid);
            for (BizIncomingGoodsList good : goodsListList){
                // 重新计算
                // 不含税单价(unitPriceExcludingTax)	 根据商品名称+规格关联【报价表信息】取
                BigDecimal noTaxPrice = Optional.ofNullable(bizISellListMapper.getPrice(good.getGoodsName(),good.getProductModel())).orElse(BigDecimal.ZERO);
                log.info("商品名称：{}，规格型号：{}，不含税单价：{}",good.getGoodsName(),good.getProductModel(),noTaxPrice);
                // 物料信息税率(amountOfTax)	 	             系统计算=不含税金额*表体商品名称关联【物料信息】的税率
                BigDecimal tax = Optional.ofNullable(bizISellListMapper.getTax(good.getGoodsName(), userInfo.getCompany())).orElse(BigDecimal.ZERO);
                log.info("商品名称：{}，物料信息税率：{}",good.getGoodsName(),tax);
                // 不含税金额(taxNotIncluded)	         系统计算=数量*不含税单价
                BigDecimal noTaxAmount = Optional.ofNullable(good.getQuantity()).orElse(BigDecimal.ZERO).multiply(noTaxPrice);
                log.info("商品名称：{}，数量：{}，不含税金额：{}",good.getGoodsName(),good.getQuantity(),noTaxAmount);
                // 价税合计(totalValueTax)	         系统计算=税额+不含税金额
                BigDecimal totalValueTax = Optional.ofNullable(noTaxAmount).orElse(BigDecimal.ZERO).add(tax);
                // log.info("价税合计：{}",totalValueTax);
                BizISellList bizISellList = new BizISellList();
                bizISellList.setSid(UUID.randomUUID().toString());
                bizISellList.setHeadId(sellSid);
                bizISellList.setTradeName(good.getGoodsName());
                bizISellList.setUnit(good.getUnit());
                bizISellList.setQuantity(good.getQuantity());
                bizISellList.setICount(good.getInQuantity());
                bizISellList.setIUnit(good.getInUnit());
                bizISellList.setUnitPriceExcludingTax(noTaxPrice);
                if (tax.compareTo(BigDecimal.ZERO) == 0){
                    log.info("税率为设置，税额等于不含税金额");
                    bizISellList.setAmountOfTax(noTaxAmount);
                }else {
                    log.info("税率设置了，税额等于 不含税金额*表体商品名称关联【物料信息】的税率");
                    // 税率小数点移动两位
                    bizISellList.setAmountOfTax(noTaxAmount.multiply(tax.divide(new BigDecimal(100))));
                }
                bizISellList.setTaxNotIncluded(noTaxAmount);
                // 重新设置价税合计  = 税额 + 不含税金额
                bizISellList.setTotalValueTax(Optional.ofNullable(bizISellList.getAmountOfTax()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(bizISellList.getTaxNotIncluded()).orElse(BigDecimal.ZERO)));
                // bizISellList.setTotalValueTax(totalValueTax);
                bizISellList.setInsertUser(userInfo.getUserNo());
                bizISellList.setInsertUserName(userInfo.getUserName());
                bizISellList.setInsertTime(new Date());
                bizISellList.setTradeCode(userInfo.getCompany());
                bizISellListMapper.insert(bizISellList);
            }
        }

        //“客户”栏位取订货通知表头的客户
        BizIncomingGoodsHead bizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(sid);
        String contractNo = bizIncomingGoodsHead.getContractNo();
        String customer = bizIContractHeadMapper.getCustomerByContractNo(contractNo);
        // 更新 修改销售信息表头 客户基础信息 默认值赋值
        BizISellHead bizISellHead = bizISellHeadMapper.selectByPrimaryKey(sellSid);
        bizISellHead.setCustomer(customer);
//        String buyerCode = bizIContractHeadMapper.getBuyerCodeByName("上海烟草贸易中心有限公司", userInfo.getCompany());
//        if (StringUtils.isNotBlank(buyerCode)) {
//            bizISellHead.setCustomer(buyerCode);
//        }else {
//            bizISellHead.setCustomer("上海烟草贸易中心有限公司");
//        }
        bizISellHeadMapper.updateByPrimaryKey(bizISellHead);
    }

    public void deleteMessage(String sid, UserInfoToken userInfo) {

        List<BizISellHead> select = bizISellHeadMapper.select(new BizISellHead() {{
            setHeadId(sid);
        }});
        if (!select.isEmpty()) {
            BizISellHead bizISellHead = select.get(0);
            bizISellHeadMapper.deleteByPrimaryKey(bizISellHead.getSid());
            bizISellListMapper.delete(new BizISellList(){{
                setHeadId(bizISellHead.getSid());
            }});
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject cancel(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        BizIncomingGoodsHead bizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(params.getId());
        // 作废数据  进货信息表头 、 销售信息表头 、 证件信息表头
        ResultObject resultObject = ResultObject.createInstance(true, "作废成功！");
        if (StringUtils.isBlank(params.getId())){
            throw new ErrorException(400, "进货信息ID不能为空");
        }
        // 判断是否为作废状态，如果是作废状态无需操作
        if (StringUtils.isNotBlank(bizIncomingGoodsHead.getDataState()) && bizIncomingGoodsHead.getDataState().equals("2")) {
            throw new ErrorException(400, "该票进货单已经作废，无需再次作废");
        }
        // 判断下游仓库管理是否存在提取数据记录
        int count = bizISellHeadMapper.checkIsExtract(bizIncomingGoodsHead.getPurchaseNo(),userInfo.getCompany());
        if (count > 0){
            throw new ErrorException(400, "仓库管理中存在该票进货单号，不允许作废");
        }
        String id = params.getId();
        int i = tBizIncomingGoodsHeadMapper.cancelData(id);
        return resultObject;
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject returnOrder(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        BizIncomingGoodsHead bizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(params.getId());
        // 修改销售信息表头 数据状态变为 0 编制
        ResultObject resultObject = ResultObject.createInstance(true, "退单成功！");
        if (StringUtils.isBlank(params.getId())){
            throw new ErrorException(400, "进货信息ID不能为空");
        }
        // 判断是否生成销售信息
        Integer count = tBizIncomingGoodsHeadMapper.checkIsGenerate(params.getId());
        if (count == 0){
            throw new ErrorException(400, "未生成销售信息，无法退单");
        }

        // 作废状态不允许退单
        if (StringUtils.isNotBlank(bizIncomingGoodsHead.getDataState()) && "2".equals(bizIncomingGoodsHead.getDataState())){
            throw new ErrorException(400, "作废状态不允许退单");
        }



        String id = params.getId();
        // TODO 待确认？？？？ 如果要修改进货单据状态，是否要删除销售信息？？？？
        // 1、将当前进货单表头状态修改为：0 编制
        // 2、删除销售信息表头、表体
        bizIncomingGoodsHead.setDataState("0");
        bizIncomingGoodsHead.setSellStatus("0");
        bizIncomingGoodsHead.setIsNext("0");
        bizIncomingGoodsHead.setSellInvoiceNo("");
        bizIncomingGoodsHead.setUpdateBy(userInfo.getUserNo());
        bizIncomingGoodsHead.setConfirmTime(null);
        bizIncomingGoodsHead.setUpdateTime(new Date());
        bizIncomingGoodsHead.setUpdateUserName(userInfo.getUserName());
        tBizIncomingGoodsHeadMapper.updateByPrimaryKey(bizIncomingGoodsHead);
        // 删除进货单表头表体数据
        tBizIncomingGoodsHeadMapper.deleteSellHeadAndListByHeadId(id);
        // int i = tBizIncomingGoodsHeadMapper.returnOrder(id);
        return resultObject;
    }

    public ResultObject redFlush(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        // 修改销售信息表头 数据状态变为 0 编制
        ResultObject resultObject = ResultObject.createInstance(true, "退单成功！");
        if (StringUtils.isBlank(params.getId())){
            throw new ErrorException(400, "进货信息ID不能为空");
        }
        // 判断是否生成销售信息
        Integer count = tBizIncomingGoodsHeadMapper.checkIsGenerate(params.getId());
        if (count == 0){
            throw new ErrorException(400, "未生成销售信息，无法退单");
        }
        String id = params.getId();
        int i = tBizIncomingGoodsHeadMapper.redFlush(id);
        return resultObject;
    }



    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity generateTB(GenerateTBParams param, UserInfoToken userInfo) {
        // 单个打印
        List<String> sids = param.getIds();
        if (sids.size() > 0 && sids.size() == 1){
            String sid = sids.get(0);
            GenerateTBFileData generateTBFileData = new GenerateTBFileData();
            // 获取表头数据
            // DecLicenceHeadGenerate head = decLicenceGenerateFileCommonService.getGenerateDecLicenceHead(sid,userInfo);
            // 根据id获取进货单表头信息
            BizIncomingGoodsHead head = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(sid);
            generateTBFileData.setCreateBy("  " + (StringUtils.isNotBlank(head.getUpdateUserName())?head.getUpdateUserName():head.getInsertUserName()));
            generateTBFileData.setCode("编号："+head.getPurchaseNo());
            String cigarettePaper = tBizIncomingGoodsHeadMapper.getCigarettePaper(head.getId());
            generateTBFileData.setCigarettePaper(Optional.ofNullable(cigarettePaper).orElse(""));
            GenerateTBFileData tempTotal = tBizIncomingGoodsHeadMapper.getSumTotal(head.getId());
            if (tempTotal != null){
                // 格式化字符 整数部分增加千分位字符
                DecimalFormat df = new DecimalFormat("#,###");
                // 格式化小数部分
                DecimalFormat df2 = new DecimalFormat("#,##0.00");
                if (StringUtils.isNotBlank(tempTotal.getTotalQuantity())){
                    generateTBFileData.setTotalQuantity(df.format(new BigDecimal(tempTotal.getTotalQuantity())) + " MT");
                }
                if (StringUtils.isNotBlank(tempTotal.getTotalAmount())){
                    generateTBFileData.setTotalAmount("USD"+df2.format(new BigDecimal(tempTotal.getTotalAmount())));
                }
            }
            setPremiumInformation(head,generateTBFileData,userInfo);
            generateTBFileData.setPriceTerms(Optional.ofNullable(head.getPriceTerm()).orElse(""));
            generateTBFileData.setContractNo(Optional.ofNullable(head.getContractNo()).orElse(""));
            generateTBFileData.setVessel("  "+Optional.ofNullable(head.getVesselVoyage()).orElse(""));
            // 开航日期格式化成 yyyy-MM-dd
            if (head.getSailingDate()!= null){
                generateTBFileData.setGoVesselDate("\r\r\r\r\r"+DateUtil.format(head.getSailingDate(),"yyyy-MM-dd"));
            }
            // 运输路线取值 起运港英文 getPortOfDeparture
            String port = tBizIncomingGoodsHeadMapper.getPortByCode(head.getPortOfDeparture(),userInfo.getCompany());
            generateTBFileData.setPortDeparture("自"+"  "+Optional.ofNullable(port).orElse(""));

            // 当前日期
            generateTBFileData.setCurrentDate("      "+DateUtil.format(new Date(),"yyyy年MM月dd日"));
            try {
                Boolean isPdf = "pdf".equals(param.getType());
                // 获取bytes
                byte[] bytes =  licenceGenerateViewPdf(generateTBFileData, "tb_order.xlsx", isPdf);
                HttpHeaders h = new HttpHeaders();
                h.setContentDispositionFormData("attachment", URLEncoder.encode("投保单("+head.getPurchaseNo()+")."+param.getType(), CommonVariable.UTF8));
                h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                return new ResponseEntity<>(bytes, h, HttpStatus.OK);
            } catch (Exception e) {
                throw new RuntimeException("生成许可证预览错误！",e);
            }
        }else {
            // 多个数据
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

            try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
                for (String sid : sids) {
                    GenerateTBFileData generateTBFileData = new GenerateTBFileData();
                    // 获取表头数据
                    // DecLicenceHeadGenerate head = decLicenceGenerateFileCommonService.getGenerateDecLicenceHead(sid,userInfo);
                    // 根据id获取进货单表头信息
                    BizIncomingGoodsHead head = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(sid);
                    generateTBFileData.setCreateBy("  " + (StringUtils.isNotBlank(head.getUpdateUserName())?head.getUpdateUserName():head.getInsertUserName()));
                    generateTBFileData.setCode("编号："+head.getPurchaseNo());
                    String cigarettePaper = tBizIncomingGoodsHeadMapper.getCigarettePaper(head.getId());
                    generateTBFileData.setCigarettePaper(Optional.ofNullable(cigarettePaper).orElse(""));
                    GenerateTBFileData tempTotal = tBizIncomingGoodsHeadMapper.getSumTotal(head.getId());
                    if (tempTotal != null){
                        // 格式化字符 整数部分增加千分位字符
                        DecimalFormat df = new DecimalFormat("#,###");
                        // 格式化小数部分
                        DecimalFormat df2 = new DecimalFormat("#,##0.00");
                        if (StringUtils.isNotBlank(tempTotal.getTotalQuantity())){
                            generateTBFileData.setTotalQuantity(df.format(new BigDecimal(tempTotal.getTotalQuantity())) + " MT");
                        }
                        if (StringUtils.isNotBlank(tempTotal.getTotalAmount())){
                            generateTBFileData.setTotalAmount("USD"+df2.format(new BigDecimal(tempTotal.getTotalAmount())));
                        }
                    }
                    setPremiumInformation(head,generateTBFileData,userInfo);
                    generateTBFileData.setPriceTerms(Optional.ofNullable(head.getPriceTerm()).orElse(""));
                    generateTBFileData.setContractNo(Optional.ofNullable(head.getContractNo()).orElse(""));
                    generateTBFileData.setVessel("  "+Optional.ofNullable(head.getVesselVoyage()).orElse(""));
                    // 开航日期格式化成 yyyy-MM-dd
                    if (head.getSailingDate()!= null){
                        generateTBFileData.setGoVesselDate("\r\r\r\r"+DateUtil.format(head.getSailingDate(),"yyyy-MM-dd"));
                    }
                    generateTBFileData.setPortDeparture("自"+"  "+Optional.ofNullable(head.getPortOfDeparture()).orElse(""));

                    // 当前日期
                    generateTBFileData.setCurrentDate("      "+DateUtil.format(new Date(),"yyyy年MM月dd日"));

                    // 获取bytes
                    // 判断 type是否为pdf
                    Boolean isPdf = "pdf".equals(param.getType());
                    byte[] bytes =  licenceGenerateViewPdf(generateTBFileData, "tb_order.xlsx", isPdf);

                    // 对文件进行重命名
                    String fileName = "投保单(" + head.getPurchaseNo() + ")."+param.getType();
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zipOutputStream.putNextEntry(zipEntry);
                    zipOutputStream.write(bytes, 0, bytes.length);
                    zipOutputStream.closeEntry();
                }
                zipOutputStream.finish(); // 完成压缩
            } catch (IOException e) {
                throw new RuntimeException("压缩文件异常！", e);
            } catch (Exception e) {
                throw new RuntimeException("生成预览压缩文件异常！", e);
            }

            HttpHeaders headers = new HttpHeaders();
            try {
                headers.setContentDispositionFormData("attachment", URLEncoder.encode("投保单合集（" + userInfo.getUserNo() + ").zip", CommonVariable.UTF8));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(byteArrayOutputStream.toByteArray(), headers, HttpStatus.OK);
        }
    }



    /**
     * 许可证生成预览PDF
     * @param head 黏贴表头数据
     * @param tempFileName 模板存放的路径
     * @param isExportPdf 是否生成pdf
     * @return 文件的byte流
     * @throws Exception
     */
    public  byte[] licenceGenerateViewPdf(GenerateTBFileData head,
                                          String tempFileName,
                                          Boolean isExportPdf) throws Exception {
        tempFileName = templatePath + tempFileName;

        // 验证License
        if (!getLicense()) {
            return null;
        }

        Workbook wb = null;
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            wb = new Workbook(tempFileName);
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(wb);
            designer.setCalculateFormula(true);

             List<GenerateTBFileData> headList = new ArrayList<>();
             headList.add(head);
             designer.setDataSource("Head", headList);
            // designer.setDataSource("List", listMap.get("decLicenceList"));


            designer.process();
            designer.getWorkbook().calculateFormula(true, new CustomFunction());

            int saveFormat = getSaveFormat(tempFileName, isExportPdf);
            wb.save(os, saveFormat);
            os.close();
        } catch (Exception ex) {
            throw ex;
        } finally {
            if (wb != null) {
                wb.dispose();
            }
        }
        return os.toByteArray();
    }



    /**
     * 获取license
     *
     * @return
     */
    public static boolean getLicense() {
        boolean result = false;
        try {
            InputStream is = ExportService.class.getClassLoader().getResourceAsStream("/files/license.xml");
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 根据文件名获取保存选项
     *
     * @param tempFileName
     * @return
     */
    private static int getSaveFormat(String tempFileName, Boolean isExportPdf) {
        if (isExportPdf) {
            return SaveFormat.PDF;
        }
        String extName = tempFileName.substring(tempFileName.lastIndexOf('.') + 1);
        switch (extName) {
            case "xls":
                return SaveFormat.EXCEL_97_TO_2003;
            case "xlsx":
                return SaveFormat.XLSX;
            case "xlsm":
                return SaveFormat.XLSM;
        }
        return SaveFormat.EXCEL_97_TO_2003;
    }

    public ResultObject<List<Map<String, String>>> getInsuranceCategoryList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = bizSmokeCommonService.getListCommonKeyValueList(userInfo, Arrays.asList(BizSmokeCommonService.BizTypeEnum.INSURANCE_CATEGORY));
        resultObject.setMessage("获取成功");
        return resultObject;
    }


    private void setPremiumInformation(BizIncomingGoodsHead head,GenerateTBFileData generateTBFileData, UserInfoToken userInfo) {
        // 投保险别 ALL RISKS, WAR RISKS AND STRIKES, WAREHOUSE CLAUSE
        // 根据投保险别找对应代码
        if (StringUtils.isNotBlank(head.getInsuranceCategory())){
            String insuranceName = bizIncomingGoodsListMapper.getInsuranceName(head.getInsuranceCategory(),userInfo.getCompany());
            generateTBFileData.setInsuranceCategory(insuranceName);
        }
        // 费率 0.0219%
        // 格式化费率 保留四位小数 最后添加 %
        if (head.getInsuranceRate() != null){
            DecimalFormat df = new DecimalFormat("#,##0.0000");
            generateTBFileData.setInsuranceRate(df.format(head.getInsuranceRate()) + "%");
        }
        // 保额 JPY160,369,000.00
        if (head.getInsuranceAmount() != null){
            DecimalFormat df = new DecimalFormat("#,##0.00");
            generateTBFileData.setInsuranceMoney("USD"+ df.format(head.getInsuranceAmount()));
        }
        // 保费 35,120.81
        if (head.getInsurancePremium() != null){
            DecimalFormat df = new DecimalFormat("#,##0.00");
            generateTBFileData.setInsurancePremium(df.format(head.getInsurancePremium()));
        }
    }
}