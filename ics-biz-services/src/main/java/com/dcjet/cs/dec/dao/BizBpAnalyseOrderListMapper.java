package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizBpAnalyseOrderList;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderListBox;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import tk.mybatis.mapper.common.Mapper;

/**
 * （第7条线）出料加工进口薄片-分析单表表体Mapper
 */
public interface BizBpAnalyseOrderListMapper extends Mapper<BizBpAnalyseOrderList>{

    /**
     * 查询获取数据
     * @param bizBpAnalyseOrderList
     * @return
     */
    List<BizBpAnalyseOrderList> getList(BizBpAnalyseOrderList bizBpAnalyseOrderList);
    List<BizBpAnalyseOrderList> getListByHeadId(String headId);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    BigDecimal getBoxCountByProductName(BizBpAnalyseOrderListBox po);

    List<Map<String, String>> getProductNameListByHeadId(BizBpAnalyseOrderListBox po);

    List<BizBpAnalyseOrderList> getListByParentId(@Param("parentId") String parentId);

}