package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


/**
 * 进货信息查询结果实体类
 */
@Data
public class BizIncomingGoodsHeadList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 进货单号
     */
    private String purchaseNo;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 进口发票号码（多个用逗号分隔）
     */
    private String invoiceNo;

    /**
     * 许可证号
     */
    private String licenseNumber;

    /**
     * 准运证编号
     */
    private String permitNumber;

    /**
     * 销售发票号
     */
    private String sellInvoiceNo;

    /**
     * 销售数据状态
     * 0编制、1确认、2作废
     */
    private String sellStatus;

    /**
     * 进货单据状态
     * 0编制、1确认、2作废
     */
    private String dataState;

    /**
     * 制单人
     */
    private String createBy;

    /**
     * 制单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    /**
     * 是否流入下一个节点
     */
    private String isNext;

    @Override
    public String toString() {
        return "BizIncomingGoodsHeadList{" +
                "id='" + id + '\'' +
                ", purchaseNo='" + purchaseNo + '\'' +
                ", supplier='" + supplier + '\'' +
                ", invoiceNo='" + invoiceNo + '\'' +
                ", licenseNumber='" + licenseNumber + '\'' +
                ", permitNumber='" + permitNumber + '\'' +
                ", sellInvoiceNo='" + sellInvoiceNo + '\'' +
                ", sellStatus='" + sellStatus + '\'' +
                ", dataState='" + dataState + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}