package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderHead;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderHeadDto;
import com.dcjet.cs.dto.dec.BizBpAnalyseOrderHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizBpAnalyseOrderHeadDto
 *
 * <AUTHOR>
 * @date 2025-07-07 17:08:03
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizBpAnalyseOrderHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizBpAnalyseOrderHeadDto toDto(BizBpAnalyseOrderHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizBpAnalyseOrderHead toPo(BizBpAnalyseOrderHeadParam param);

    /**
     * 数据库原始数据更新
     * @param bizBpAnalyseOrderHeadParam
     * @param BizBpAnalyseOrderHead
     */
    void updatePo(BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam, @MappingTarget BizBpAnalyseOrderHead bizBpAnalyseOrderHead);

    default void patchPo(BizBpAnalyseOrderHeadParam bizBpAnalyseOrderHeadParam , BizBpAnalyseOrderHead bizBpAnalyseOrderHead) {
        // TODO 自行实现局部更新
    }
}