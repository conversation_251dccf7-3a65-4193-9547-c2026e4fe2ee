package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIPurchaseHeadMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseListMapper;
import com.dcjet.cs.dec.mapper.BizIPurchaseHeadDtoMapper;
import com.dcjet.cs.dec.mapper.BizIPurchaseListDtoMapper;
import com.dcjet.cs.dec.model.BizIOrderList;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dto.dec.BizIOrderListParam;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadDto;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadParam;

import com.dcjet.cs.dto.dec.BizIPurchaseListParam;
import com.xdo.common.I18n.example.Example;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIPurchaseHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIPurchaseHeadService extends BaseService<BizIPurchaseHead> {

    private static final Logger log = LoggerFactory.getLogger(BizIPurchaseHeadService.class);

    @Resource
    private BizIPurchaseHeadMapper bizIPurchaseHeadMapper;

    @Resource
    private BizIPurchaseHeadDtoMapper bizIPurchaseHeadDtoMapper;

    @Override
    public Mapper<BizIPurchaseHead> getMapper() {
        return bizIPurchaseHeadMapper;
    }

    @Resource
    private BizIOrderHeadService bIzIOrderHeadService;


    @Resource
    private BizIPurchaseListMapper purchaseListMapper;

    @Resource
    private BizIPurchaseListDtoMapper purchaseListDtoMapper;


    /**
     * 获取分页信息
     *
     * @param bizIPurchaseHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIPurchaseHeadDto>> getListPaged(BizIPurchaseHeadParam bizIPurchaseHeadParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadDtoMapper.toPo(bizIPurchaseHeadParam);
        bizIPurchaseHead.setTradeCode(userInfo.getCompany());
        Page<BizIPurchaseHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIPurchaseHeadMapper.getList( bizIPurchaseHead));
        // 将PO转为DTO返回给前端
        List<BizIPurchaseHeadDto> bizIPurchaseHeadDtoList = page.getResult().stream()
            .map(bizIPurchaseHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIPurchaseHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIPurchaseHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPurchaseHeadDto insert(BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadDtoMapper.toPo(bizIPurchaseHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIPurchaseHead.setSid(sid);
        bizIPurchaseHead.setInsertUser(userInfo.getUserNo());
        bizIPurchaseHead.setInsertTime(new Date());
        bizIPurchaseHead.setTradeCode(userInfo.getCompany());
        bizIPurchaseHead.setInsertUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = bizIPurchaseHeadMapper.insert(bizIPurchaseHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIPurchaseHeadDtoMapper.toDto(bizIPurchaseHead) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIPurchaseHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPurchaseHeadDto update(BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(bizIPurchaseHeadParam.getSid());
        bizIPurchaseHeadDtoMapper.updatePo(bizIPurchaseHeadParam, bizIPurchaseHead);
        bizIPurchaseHead.setUpdateUser(userInfo.getUserNo());
        bizIPurchaseHead.setUpdateTime(new Date());
        bizIPurchaseHead.setUpdateUserName(userInfo.getUserName());
        // TODO 校验进货单号是否已经存在
         int j = bizIPurchaseHeadMapper.getOrderNoIsExists(bizIPurchaseHead.getPurchaseOrderNo(),bizIPurchaseHead.getSid(),userInfo.getCompany());
         if (j>0){
             throw new ErrorException(400, XdoI18nUtil.t("进货单号已存在"));
         }
        // 校验是否产生下游数据
        bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑！");


        // 更新数据
        int update = bizIPurchaseHeadMapper.updateByPrimaryKey(bizIPurchaseHead);
        // 更新订单信息  表头的进货订单号
        String headId = bizIPurchaseHead.getHeadId();
        String purchaseOrderNo = bizIPurchaseHead.getPurchaseOrderNo();
        bizIPurchaseHeadMapper.updateOderHeadPurchaseNo(purchaseOrderNo,headId);


        // 更新进货信息表体数据
        List<BizIPurchaseListParam> listParams = bizIPurchaseHeadParam.getPurchaseListParams();
        // 更新表体数据
        if (CollectionUtils.isNotEmpty(listParams)){
            // 循环更新数据
            for (int i = 0; i < listParams.size(); i++) {
                BizIPurchaseListParam item = listParams.get(i);
                String message = item.canSubmit(i+1);
                if (StringUtils.isNotBlank(message)){
                    throw new ErrorException(400, XdoI18nUtil.t(message));
                }
                // 循环更新表体数据
                BizIPurchaseList po = purchaseListDtoMapper.toPo(item);

                // 计算金额
                // 总价为空，数量不为空
                if (po.getDecTotal() == null && po.getQty() != null){
                    // 根据 单价、数量计算总价
                    updateOrderListByQty(po,userInfo,i+1);
                }else if (po.getDecTotal() != null && po.getQty() == null){
                    // 根据 总价、单价计算数量
                    updateOrderListByTotal(po,userInfo,i+1);
                }else if (po.getDecTotal() != null && po.getQty() != null){
                    // 根据 总价、数量 计算单价
                    updateOrderListByPriceAndTotal(po,userInfo,i+1);
                }else if (po.getDecTotal() == null && po.getQty() == null){
                    throw new ErrorException(400, XdoI18nUtil.t("第"+(i+1)+"行数据不符合规范:数量和总值不能同时为空!"));
                }

                purchaseListMapper.updateByPrimaryKey(po);

                // 如果进口发票号不为空，更新装箱子表的进口发票号
                if (StringUtils.isNotBlank(item.getInvoiceNo())){
                    purchaseListMapper.updateBoxInvoiceNo(item.getInvoiceNo(),po.getSid());
                }
            }
        }


        return update > 0 ? bizIPurchaseHeadDtoMapper.toDto(bizIPurchaseHead) : null;
    }


    /**
     * 【※  进货单表头表体同步保存时 （总价值为空，数量不为空），根据 数量 * 单价 计算总值 ※】
     * @param po 进货信息表体
     * @param userInfo 用户信息
     */
    public void updateOrderListByQty(BizIPurchaseList po,UserInfoToken userInfo,int index) {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>> 批量更新订单信息表体【无总价】 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(po.getHeadId());
        if (bizIPurchaseHead != null) {
            bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        }
        // 数量不能为空
        if (po.getQty() == null || po.getQty().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400, XdoI18nUtil.t("第"+index+"行数据不符合规范:数量不能为空或者为0!"));
        }

        po.setUpdateUser(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        // 1.修改数量 重新计算总价
        BigDecimal totalPrice = po.getQty().multiply(po.getDecPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
        log.info("[总价：{}]", totalPrice);
        po.setDecTotal(totalPrice);
        // 2.重新计算折扣金额 总值*折扣率%
        if (po.getDiscountRate() != null) {
            BigDecimal discountRate = po.getDiscountRate();
            if(discountRate.compareTo(BigDecimal.ZERO) == 0) {
                // 折扣率为0是，折扣金额为0
                // discountRate = BigDecimal.ONE;
                discountRate = BigDecimal.ZERO;
                log.info("[折扣率：{}]", discountRate);
                po.setPaymentAmount(totalPrice);
                po.setDiscountAmount(BigDecimal.ZERO);
            }else {
                log.info("[折扣率：{}]", discountRate);
                discountRate = discountRate.divide(BigDecimal.valueOf(100), 8, BigDecimal.ROUND_HALF_UP);
                BigDecimal discountTotal = totalPrice.multiply(discountRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[折扣金额：{}]", discountTotal);
                po.setDiscountAmount(discountTotal);
                // 3.重新贷款金额 总值-折扣金额
                BigDecimal loanTotal = totalPrice.subtract(discountTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[贷款金额：{}]", loanTotal);
                po.setPaymentAmount(loanTotal);
            }
        }
    }


    /**
     * 【※  进货单表头表体同步保存时 （总价值不为空，数量为空），根据 总价 / 单价 = 计算数量 ※】
     * @param po 进货信息表体
     * @param userInfo 用户信息
     */
    public void updateOrderListByTotal(BizIPurchaseList po,UserInfoToken userInfo,int index) {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>> 批量更新订单信息表体【无数量】 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(po.getHeadId());
        if (bizIPurchaseHead != null) {
            bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        }
        // 数量不能为空
        if (po.getDecTotal() == null || po.getDecTotal().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400, XdoI18nUtil.t("第"+index+"行数据不符合规范:总值不能为空或者为0!"));
        }

        po.setUpdateUser(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        // 1.修改总价  重新计算数量，数量小数位数最大6位数
        BigDecimal decPrice = po.getDecTotal().divide(po.getDecPrice(), 6, BigDecimal.ROUND_HALF_UP);
        log.info("[单价为：{}]", decPrice);
        po.setDecPrice(decPrice);
        // 总价
        BigDecimal totalPrice = po.getDecTotal();
        // 2.重新计算折扣金额 总值*折扣率%
        if (po.getDiscountRate() != null) {
            BigDecimal discountRate = po.getDiscountRate();
            if(discountRate.compareTo(BigDecimal.ZERO) == 0) {
                // 折扣率为0是，折扣金额为0
                // discountRate = BigDecimal.ONE;
                discountRate = BigDecimal.ZERO;
                log.info("[折扣率：{}]", discountRate);
                po.setPaymentAmount(totalPrice);
                po.setDiscountAmount(BigDecimal.ZERO);
            }else {
                log.info("[折扣率：{}]", discountRate);
                discountRate = discountRate.divide(BigDecimal.valueOf(100), 8, BigDecimal.ROUND_HALF_UP);
                BigDecimal discountTotal = totalPrice.multiply(discountRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[折扣金额：{}]", discountTotal);
                po.setDiscountAmount(discountTotal);
                // 3.重新贷款金额 总值-折扣金额
                BigDecimal loanTotal = totalPrice.subtract(discountTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[贷款金额：{}]", loanTotal);
                po.setPaymentAmount(loanTotal);
            }
        }
    }

    /**
     * 【※  进货单表头表体同步保存时 （总价，数量不为空），根据 总价 / 数量 重新计算单价 ※】
     * @param po 进货信息表体
     * @param userInfo 用户信息
     */
    public void updateOrderListByPriceAndTotal(BizIPurchaseList po, UserInfoToken userInfo,int index) {
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(po.getHeadId());
        if (bizIPurchaseHead != null) {
            bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        }
        po.setUpdateUser(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        // 总价值不能为空
        if (po.getDecTotal() == null || po.getDecTotal().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400, XdoI18nUtil.t("第"+index+"行数据不符合规范:总值不能为空或者为0!"));
        }
        // 数量
        if (po.getQty() == null || po.getQty().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400, XdoI18nUtil.t("第"+index+"行数据不符合规范:数量不能为空或者为0!"));
        }

        // 根据总价和数量 重新计算单价
        BigDecimal totalPrice = po.getDecTotal();
        BigDecimal decPrice = totalPrice.divide(po.getQty(), 5, BigDecimal.ROUND_HALF_UP);
        po.setDecPrice(decPrice);

        // 重新计算折扣金额
        if (po.getDiscountRate()!= null) {
            BigDecimal discountRate = po.getDiscountRate();
            if(discountRate.compareTo(BigDecimal.ZERO) == 0) {
                discountRate = BigDecimal.ZERO;
                log.info("[折扣率：{}]", discountRate);
                po.setDiscountAmount(BigDecimal.ZERO);
                po.setPaymentAmount(totalPrice);
            }else {
                log.info("[折扣率：{}]", discountRate);
                discountRate = discountRate.divide(BigDecimal.valueOf(100), 8, BigDecimal.ROUND_HALF_UP);
                BigDecimal discountTotal = totalPrice.multiply(discountRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[折扣金额：{}]", discountTotal);
                po.setDiscountAmount(discountTotal);
                // 重新计算贷款金额
                BigDecimal loanTotal = totalPrice.subtract(discountTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[贷款金额：{}]", loanTotal);
                po.setPaymentAmount(loanTotal);
            }
        }
    }





    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizIPurchaseHeadMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIPurchaseHeadDto> selectAll(BizIPurchaseHeadParam exportParam, UserInfoToken userInfo) {
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadDtoMapper.toPo(exportParam);
        bizIPurchaseHead.setTradeCode(userInfo.getCompany());
        List<BizIPurchaseHeadDto> bizIPurchaseHeadDtos = new ArrayList<>();
        List<BizIPurchaseHead> bizIPurchaseHeadLists = bizIPurchaseHeadMapper.getList(bizIPurchaseHead);
        if (CollectionUtils.isNotEmpty(bizIPurchaseHeadLists)) {
           bizIPurchaseHeadDtos = bizIPurchaseHeadLists.stream().map(head -> {
                    BizIPurchaseHeadDto dto =  bizIPurchaseHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIPurchaseHeadDtos;
    }


    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param bizIPurchaseHeadParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    public ResultObject<BizIPurchaseHeadDto> getPurchaseHeadByOrderSid(BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIPurchaseHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizIPurchaseHead bizIPurchaseHeadList = bizIPurchaseHeadMapper.getEditDataByHeadId(bizIPurchaseHeadParam.getHeadId());
        if (bizIPurchaseHeadList != null) {
            BizIPurchaseHeadDto bizIPurchaseHeadDto = bizIPurchaseHeadDtoMapper.toDto(bizIPurchaseHeadList);
            resultObject.setData(bizIPurchaseHeadDto);
        }
        return resultObject;
    }

    /**
     * 更新报关信息
     * @param bizIPurchaseHeadParam
     * @param userInfo
     * @return
     */
    public BizIPurchaseHeadDto updateEntryInfo(BizIPurchaseHeadParam bizIPurchaseHeadParam, UserInfoToken userInfo) {
        BizIPurchaseHead  bizIPurchaseHead = bizIPurchaseHeadDtoMapper.toPo(bizIPurchaseHeadParam);
        bizIPurchaseHead.setUpdateUser(userInfo.getUserNo());
        bizIPurchaseHead.setUpdateTime(new Date());
        bizIPurchaseHead.setUpdateUserName(userInfo.getUserName());
        bizIPurchaseHeadMapper.updateByPrimaryKeySelective(bizIPurchaseHead);
        return bizIPurchaseHeadDtoMapper.toDto(bizIPurchaseHead);
    }
}