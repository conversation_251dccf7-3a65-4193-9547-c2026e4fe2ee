<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizExportCommonMapper">


    <select id="getCustomerList" resultType="java.util.Map">
        select
            distinct
            merchant_code as "value",
            merchant_name_cn as "label"
        from
            t_biz_merchant
        where
            trade_code = #{tradeCode}
            <if test="customerCode != null and customerCode != ''">
                and merchant_code = #{customerCode}
            </if>
    </select>


    <select id="getCurrList" resultType="java.util.Map">
        select
            distinct
            custom_param_code as "value",
            params_name as "label"
        from
            t_biz_customs_params
        where
            params_type = 'CURR'
          and trade_code = #{tradeCode}
    </select>


    <select id="getPriceTermList" resultType="java.util.Map">
        select
            distinct
            param_code as "value",
            price_term as "label"
        from
            t_biz_price_terms
        where
            trade_code =  #{tradeCode}
    </select>


    <select id="getPackageList" resultType="java.util.Map">
        select
            distinct
            param_code as "value",
            pack_unit_cn_name as "label"
        from
            t_biz_package_info
        where
            param_code is not null and trade_code = #{tradeCode}
    </select>


    <select id="getCityList" resultType="java.util.Map">
        select
            distinct
            param_code as "value",
            city_cn_name as "label"
        from
            T_BIZ_CITY
        where
            param_code is not null and trade_code = #{tradeCode}
    </select>


    <select id="getInsuranceTypeList" resultType="java.util.Map">
        select
            distinct
            param_code as "value",
            insurance_type_cn as "label"
        from
            t_biz_insurance_type
        where trade_code = #{tradeCode}
    </select>


    <select id="getPortList" resultType="java.util.Map">
        select
            distinct
            params_code as "value",
            params_name as "label"
        from
            t_biz_customs_params
        where
            params_type = 'PORT'
          and trade_code = #{tradeCode}
    </select>


    <select id="getExportHeadCustomerList" resultType="java.util.Map">
        select
            distinct
            h.customer as "value",
            tbm.merchant_name_cn as "label"
        from t_biz_export_goods_head h
        inner join  t_biz_merchant tbm on  h.customer = tbm.merchant_code  and h.trade_code = tbm.trade_code
        where h.trade_code = #{tradeCode}
    </select>


    <select id="getExportHeadSupplierList" resultType="java.util.Map">
        select
            distinct
            h.supplier as "value",
            tbm.merchant_name_cn as "label"
        from t_biz_export_goods_head h
                 inner join  t_biz_merchant tbm on  h.supplier = tbm.merchant_code  and h.trade_code = tbm.trade_code
        where h.trade_code = #{tradeCode}
    </select>
    <select id="getCurrentMonthRate" resultType="com.dcjet.cs.params.model.EnterpriseRate">
        SELECT
            SID,
            TRADE_CODE,
            INSERT_USER,
            INSERT_TIME,
            INSERT_USER_NAME,
            UPDATE_USER,
            UPDATE_TIME,
            UPDATE_USER_NAME,
            PARAM_CODE,
            CURR,
            RATE,
            NOTE,
            EXTEND1,
            EXTEND2,
            EXTEND3,
            EXTEND4,
            EXTEND5,
            EXTEND6,
            EXTEND7,
            EXTEND8,
            EXTEND9,
            EXTEND10,
            MONTH,
            USD_RATE
        FROM T_BIZ_ENTERPRISE_RATE
        WHERE
            MONTH = #{month} AND TRADE_CODE = #{tradeCode} AND CURR = #{curr}
            LIMIT 1;
    </select>
    <select id="getUnitList" resultType="java.util.Map">
        select
            distinct
            params_code as "value",
            params_name as "label"
        from
            t_biz_customs_params
        where
            params_type = 'UNIT'
          and trade_code = #{tradeCode}
    </select>


    <select id="getCustomerListByType" resultType="java.util.Map">
        select
            distinct
            merchant_code as "value",
            merchant_name_cn as "label"
        from
            t_biz_merchant
        where
            trade_code = #{tradeCode}
            and merchant_type = #{merchantType}
            and common_flag  like '%' ||  #{commonFlag} || '%'
    </select>
    <select id="getCustomerSupplierListByType" resultType="java.util.Map">
        select
            distinct
            merchant_code as "value",
            merchant_name_cn as "label"
        from
            t_biz_merchant
        where
            trade_code = #{tradeCode}
            and merchant_type in ('0','1')
            and common_flag  like '%' ||  #{commonFlag} || '%'
    </select>
</mapper>