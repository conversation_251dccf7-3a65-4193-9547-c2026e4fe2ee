package com.dcjet.cs.dec.service;



import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsListMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsTbMapper;
import com.dcjet.cs.dec.mapper.BizSmokeMachineIncomingGoodsHeadDtoMapper;
import com.dcjet.cs.dec.mapper.BizSmokeMachineIncomingGoodsListDtoMapper;
import com.dcjet.cs.dec.model.*;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadDto;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractListDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadParam;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.dao.ForeignContractListMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractHeadDtoMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractListDtoMapper;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.equipment.model.ForeignContractList;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.vo.HttpResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizSmokeMachineIncomingGoodsHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-06-30 13:51:46
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizSmokeMachineIncomingGoodsHeadService extends BaseService<BizSmokeMachineIncomingGoodsHead> {

    private static final Logger log = LoggerFactory.getLogger(BizSmokeMachineIncomingGoodsHeadService.class);

    @Resource
    private BizSmokeMachineIncomingGoodsHeadMapper bizSmokeMachineIncomingGoodsHeadMapper;

    @Resource
    private BizSmokeMachineIncomingGoodsHeadDtoMapper bizSmokeMachineIncomingGoodsHeadDtoMapper;

    @Override
    public Mapper<BizSmokeMachineIncomingGoodsHead> getMapper() {
        return bizSmokeMachineIncomingGoodsHeadMapper;
    }

    @Resource
    private BizSmokeCommonService bizSmokeCommonService;

    @Resource
    private BizSmokeMachineIncomingGoodsTbMapper  bizSmokeMachineIncomingGoodsTbMapper;


    @Resource
    private BizSmokeMachineIncomingGoodsListMapper bizSmokeMachineIncomingGoodsListMapper;

    @Resource
    private BizSmokeMachineIncomingGoodsListDtoMapper bizSmokeMachineIncomingGoodsListDtoMapper;

    @Resource
    private ForeignContractHeadDtoMapper foreignContractHeadDtoMapper;

    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;

    @Resource
    private ForeignContractListMapper foreignContractListMapper;

    @Resource
    private ForeignContractListDtoMapper foreignContractListDtoMapper;


    @Resource
    private CommonService commonService;

    @Resource
    private AeoAuditInfoService aeoAuditInfoService;





    /**
     * 获取分页信息
     *
     * @param bizSmokeMachineIncomingGoodsHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizSmokeMachineIncomingGoodsHeadDto>> getListPaged(BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // BizSmokeMachineIncomingGoodsHeadDorpListDto bizSmokeMachineIncomingGoodsHeadDorpListDto = new BizSmokeMachineIncomingGoodsHeadDorpListDto();
        // 启用分页查询
        BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadDtoMapper.toPo(bizSmokeMachineIncomingGoodsHeadParam);
        bizSmokeMachineIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        Page<BizSmokeMachineIncomingGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizSmokeMachineIncomingGoodsHeadMapper.getList( bizSmokeMachineIncomingGoodsHead));
        // 将PO转为DTO返回给前端

//        // 获取当前客户的列表，List<String>
//        if (!page.getResult().isEmpty()) {
//            List<BizSmokeMachineIncomingGoodsHead> list = bizSmokeMachineIncomingGoodsHeadMapper.getList(new BizSmokeMachineIncomingGoodsHead() {{
//                setTradeCode(userInfo.getCompany());
//            }});
//            List<String> cList = list.stream()
//                                                 .map(BizSmokeMachineIncomingGoodsHead::getCustomer)
//                                                 .distinct()
//                                                 .collect(Collectors.toList());
//            List<String> sList = list.stream()
//                                                 .map(BizSmokeMachineIncomingGoodsHead::getSupplier)
//                                                 .distinct()
//                                                 .collect(Collectors.toList());
//            List<Map<String, String>> customerList = bizSmokeCommonService.getCustomerListByCodes(cList, userInfo.getCompany());
//            List<Map<String, String>> supplierList = bizSmokeCommonService.getCustomerListByCodes(sList, userInfo.getCompany());
//            bizSmokeMachineIncomingGoodsHeadDorpListDto.setCustomerList(customerList);
//            bizSmokeMachineIncomingGoodsHeadDorpListDto.setSupplierList(supplierList);
//        }


        List<BizSmokeMachineIncomingGoodsHeadDto> bizSmokeMachineIncomingGoodsHeadDtoList = page.getResult().stream()
            .map(bizSmokeMachineIncomingGoodsHeadDtoMapper::toDto)
            .collect(Collectors.toList());
        // bizSmokeMachineIncomingGoodsHeadDorpListDto.setList(bizSmokeMachineIncomingGoodsHeadDtoList);

        return ResultObject.createInstance(bizSmokeMachineIncomingGoodsHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizSmokeMachineIncomingGoodsHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsHeadDto insert(BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadDtoMapper.toPo(bizSmokeMachineIncomingGoodsHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizSmokeMachineIncomingGoodsHead.setId(sid);
        bizSmokeMachineIncomingGoodsHead.setCreateBy(userInfo.getUserNo());
        bizSmokeMachineIncomingGoodsHead.setCreateTime(new Date());
        bizSmokeMachineIncomingGoodsHead.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizSmokeMachineIncomingGoodsHeadMapper.insert(bizSmokeMachineIncomingGoodsHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizSmokeMachineIncomingGoodsHeadDtoMapper.toDto(bizSmokeMachineIncomingGoodsHead) : null;
    }

    /**
     * 修改记录
     *
     * @param bizSmokeMachineIncomingGoodsHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizSmokeMachineIncomingGoodsHeadDto update(BizSmokeMachineIncomingGoodsHeadParam bizSmokeMachineIncomingGoodsHeadParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsHead headData = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(bizSmokeMachineIncomingGoodsHeadParam.getId());
        bizSmokeMachineIncomingGoodsHeadDtoMapper.updatePo(bizSmokeMachineIncomingGoodsHeadParam, headData);
        headData.setUpdateBy(userInfo.getUserNo());
        headData.setUpdateTime(new Date());
        if (StringUtils.isNotBlank(headData.getApprovalStatus()) && headData.getApprovalStatus().equals("2")){
            // 待审核数据不允许编辑
            throw new ErrorException(400, XdoI18nUtil.t("待审核数据，不允许编辑！"));
        }
        // 每次编辑后 修改审批状态
        headData.setApprovalStatus("1");

        // 校验进货单号
        if (StringUtils.isNotBlank(headData.getPurchaseNo())){
            // 检验进货单号是否已经存在
            Integer count = bizSmokeMachineIncomingGoodsHeadMapper.selectByPurchaseNo(headData.getPurchaseNo(),headData.getId(),userInfo.getCompany());
            if (count > 0){
                throw new ErrorException(400, XdoI18nUtil.t("进货单号已经存在！"));
            }
            // 判断进货单号长度是否大于2位
            if (headData.getPurchaseNo().length() <= 2) {
                throw new ErrorException(400, XdoI18nUtil.t("进货单号格式不正确！"));
            }
            // 判断最后两位是否是两位流水号
            String substring = headData.getPurchaseNo().substring(headData.getPurchaseNo().length() - 2);

            // 判断订单号最后两位是否可以转为数字
            try {
                // 获取字符串最后两位
                // 将最后两位设置为序号
                Integer bigDecimal = new Integer(substring);
                headData.setSerialNo(bigDecimal);
            }   catch (NumberFormatException e) {
                throw new ErrorException(400, XdoI18nUtil.t("进货单号格式不正确！"));
            }
        }





        // 校验表体数据
        List<BizSmokeMachineIncomingGoodsListParam> listData = bizSmokeMachineIncomingGoodsHeadParam.getIncomingList();
        // 更新表体数据
        if (CollectionUtils.isNotEmpty(listData)){
            // 循环更新数据
            for (int i = 0; i < listData.size(); i++) {
                BizSmokeMachineIncomingGoodsListParam item = listData.get(i);
                String message = item.canSubmit(i+1);
                if (StringUtils.isNotBlank(message)){
                    throw new ErrorException(400, XdoI18nUtil.t(message));
                }


                // 【数量 * 单价 = 金额】  【金额 * 汇率 = 总折价美元】
                // 校验数量和单价是否正确
                if (item.getQuantity() == null || item.getUnitPrice() == null){
                    throw new ErrorException(400, XdoI18nUtil.t("第"+(i+1)+"行数据：数量/单价不能为空"));
                }
                // 循环更新表体数据
                BizSmokeMachineIncomingGoodsList po = bizSmokeMachineIncomingGoodsListDtoMapper.toPo(item);
                // 计算金额
                bizSmokeCommonService.computedUsdMoney(po,userInfo);
                // 更新表体数据
                bizSmokeMachineIncomingGoodsListMapper.updateByPrimaryKey(po);
            }
        }




        // 重新生成投保信息
        BizSmokeMachineIncomingGoodsTb tb = new BizSmokeMachineIncomingGoodsTb();


        // 保存时，不在生成投保单，确认时生成投保单
        // setConfirmTBData(headData,tb, userInfo);

        // 重新设置信息
        if (StringUtils.isNotBlank(headData.getUpdateUserName())){
            headData.setCreateBy(headData.getUpdateUserName());
        }else {
            headData.setCreateBy(headData.getInsertUserName());
        }
        // 设置更新时间
        if (headData.getUpdateTime() != null){
            headData.setCreateTime(headData.getUpdateTime());
        }else {
            headData.setCreateTime(headData.getCreateTime());
        }

        // 更新表头数据
        int update = bizSmokeMachineIncomingGoodsHeadMapper.updateByPrimaryKey(headData);
        bizSmokeMachineIncomingGoodsHeadMapper.updateAccountById(headData.getId());
        BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(headData);
        return update > 0 ? bizSmokeMachineIncomingGoodsHeadDtoMapper.toDto(bizSmokeMachineIncomingGoodsHead) : null;
    }





    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        if(CollectionUtils.isEmpty(sids)){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要删除数据"));
        }
        if (sids.size() > 1){
            throw new ErrorException(400, XdoI18nUtil.t("只能选择一条数据删除"));
        }
        String sid = sids.get(0);
        // 级联删除数据
        bizSmokeMachineIncomingGoodsHeadMapper.deleteByHeadId(sid);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizSmokeMachineIncomingGoodsHeadDto> selectAll(BizSmokeMachineIncomingGoodsHeadParam exportParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadDtoMapper.toPo(exportParam);
        bizSmokeMachineIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        List<BizSmokeMachineIncomingGoodsHeadDto> bizSmokeMachineIncomingGoodsHeadDtos = new ArrayList<>();
        List<BizSmokeMachineIncomingGoodsHead> bizSmokeMachineIncomingGoodsHeadLists = bizSmokeMachineIncomingGoodsHeadMapper.getList(bizSmokeMachineIncomingGoodsHead);
        if (CollectionUtils.isNotEmpty(bizSmokeMachineIncomingGoodsHeadLists)) {
           bizSmokeMachineIncomingGoodsHeadDtos = bizSmokeMachineIncomingGoodsHeadLists.stream().map(head -> {
                    BizSmokeMachineIncomingGoodsHeadDto dto =  bizSmokeMachineIncomingGoodsHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizSmokeMachineIncomingGoodsHeadDtos;
    }

    public ResultObject getCustomerList(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        params.setTradeCode(userInfo.getCompany());
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizSmokeMachineIncomingGoodsHeadMapper.getCustomerList(params);
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    public ResultObject getPortList(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        params.setTradeCode(userInfo.getCompany());
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizSmokeMachineIncomingGoodsHeadMapper.getPortList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到港口信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    public ResultObject getCommonSearchList(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject instance = ResultObject.createInstance(true);
        BizSmokeMachineIncomingGoodsHeadCommonDto dto = new BizSmokeMachineIncomingGoodsHeadCommonDto();
        // 获取当前表单用户的 客户
        List<Map<String, String>> customerList = bizSmokeMachineIncomingGoodsHeadMapper.getCurrentCustomerList(userInfo.getCompany());
        dto.setCustomerList(customerList);

        // 获取当前表用户的 供应商
        List<Map<String, String>> supplierList = bizSmokeMachineIncomingGoodsHeadMapper.getCurrentSupplierList(userInfo.getCompany());
        dto.setSupplierList(supplierList);

        // 获取当前用户的制单人
        List<Map<String, String>> createByList = bizSmokeMachineIncomingGoodsHeadMapper.getCurrentCreateByList(userInfo.getCompany());
        dto.setCreateByList(createByList);


        // 获取港口信息
        // 获取当前企业的供应商信息
        List<Map<String,String>>  portList  = bizSmokeMachineIncomingGoodsHeadMapper.getPortList(userInfo.getCompany());
        dto.setPortList(portList);


        instance.setMessage("获取成功！");
        instance.setData(dto);
        return instance;
    }

    public ResultObject getIncomingGoodsHeadById(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功");
        String id = params.getId();
        if (StringUtils.isNotBlank(id)){
            BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(id);
            // 重新设置更新时间
            if (StringUtils.isNotBlank(bizSmokeMachineIncomingGoodsHead.getUpdateUserName())){
                bizSmokeMachineIncomingGoodsHead.setCreateBy(bizSmokeMachineIncomingGoodsHead.getUpdateUserName());
            }else {
                bizSmokeMachineIncomingGoodsHead.setCreateBy(bizSmokeMachineIncomingGoodsHead.getInsertUserName());
            }
            if (bizSmokeMachineIncomingGoodsHead.getUpdateTime() != null){
                bizSmokeMachineIncomingGoodsHead.setCreateTime(bizSmokeMachineIncomingGoodsHead.getUpdateTime());
            }else {
                bizSmokeMachineIncomingGoodsHead.setCreateTime(bizSmokeMachineIncomingGoodsHead.getCreateTime());
            }

            if (bizSmokeMachineIncomingGoodsHead != null){
                BizSmokeMachineIncomingGoodsHeadDto dto = bizSmokeMachineIncomingGoodsHeadDtoMapper.toDto(bizSmokeMachineIncomingGoodsHead);
                resultObject.setData(dto);
                return resultObject;
            }
            resultObject.setSuccess(false);
            resultObject.setMessage("未匹配到进货单数据！");
            return resultObject;
        }else {
            resultObject.setSuccess(false);
            resultObject.setMessage("进货单ID不能为空！");
            return resultObject;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmIncomingGoodsHead(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "确认成功");
        // 获取进货单表头表体数据
        String headId = params.getId();
        if (StringUtils.isBlank(headId)){
            throw new ErrorException(400,"未匹配到进货单数据！");
        }


        // 设置投保单表头更新状态
        BizSmokeMachineIncomingGoodsHead head = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(headId);
        head.setConfirmTime(new Date());
        head.setDataState("1");
        // 第三条线 烟机设备-外商合同-T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD 表头 CURR 表头
        // 第三条线 烟机设备-外伤合同-T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST 表体
        // 设置投保单信息

        // 确认插入投保信息 -> 现在改为保存
         BizSmokeMachineIncomingGoodsTb tb = new BizSmokeMachineIncomingGoodsTb();
         setConfirmTBData(head,tb,userInfo);


         // 更新表头数据
         bizSmokeMachineIncomingGoodsHeadMapper.updateByPrimaryKey(head);




        resultObject.setData(head);
        return resultObject;
    }

    public void setConfirmTBData(BizSmokeMachineIncomingGoodsHead head, BizSmokeMachineIncomingGoodsTb tb, UserInfoToken userInfo) {
        // 删除原先投保信息
        // bizSmokeMachineIncomingGoodsTbMapper.deleteByHeadId(head.getId());
        // 判断是否存在投保单数据
        BizSmokeMachineIncomingGoodsTb tbData = bizSmokeMachineIncomingGoodsTbMapper.getTBByHeadId(head.getId());
        if (tbData == null){
            tb.setId(UUID.randomUUID().toString());
            tb.setCreateTime(new Date());
            tb.setCreateBy(userInfo.getUserNo());
            tb.setInsertUserName(userInfo.getUserName());
            tb.setHeadId(head.getId());
            tb.setParentId(head.getId());
            // 1	编号	字符型（60）	文本	是	取进货单号
            tb.setCodeNo(head.getPurchaseNo());
            // 2	保险公司	字符型（200）	下拉框	是	客户档案，下拉选择
            tb.setInsuranceCompany(null);
            // 3	被保险人	字符型（200）	下拉框	否	进货单带入，不可修改
            tb.setInsuredPerson(head.getCustomer());
            // 4	发票抬头	字符型（200）	下拉框	否	企业档案，下拉选择
            tb.setInvoiceTitle(null);
            // 5	运输工具名称	字符型（200）	文本	否	录入
            tb.setTransportName(null);
            // 6	开航日期	日期型	日期控件	否	录入
            tb.setDepartureDate(null);
            // 7	运输路线自	字符型（200）	文本	否	录入
            tb.setRouteFrom(null);
            // 8	经	字符型（200）	文本	否	录入
            tb.setRouteVia(null);
            // 9	至	字符型（200）	文本	否	录入
            tb.setRouteTo(null);
            // 10	投保险别	字符型（200）	文本框	否	录入
            tb.setInsuranceType(null);
            // 11	币种	字符型（10）	下拉框	是	外商合同带入
            tb.setCurrency(head.getExtend1());
            // 12	投保加成%	数值型（19，4）	文本	否	录入
            tb.setInsurancePremiumRate(head.getInsuranceMarkup());
            // 14	保险费率	数值型（19，4）	文本	是	根据外商合同关联划款参数带出，允许修改
            // 如果用户填写了保险费率，那么就使用用户填写的保险费率
            if (head.getInsuranceRate() != null){
                tb.setInsuranceRate(head.getInsuranceRate());
            }
            // 下面三个栏位的值通过方法设置
            bizSmokeCommonService.setBaseTBMoney(tb,false,userInfo);
            // 13	保险金额	数值型（19，4）	文本	是	取进货单表体金额合计*（1+投保加成%）

            // 15	保费	数值型（19，2）	文本	是	系统计算=保险金额*汇率*保险费率，允许修改


            // 16	投保日期	日期型	日期控件	否	录入
            tb.setInsuranceDate(null);
            // 17	备注	字符型（500）	文本	否	保单相关的备注信息
            tb.setRemark(null);
            // 设置投保单数据状态
            tb.setDataState("0");
            log.error("最终入库投保信息：{}",tb);
            // 插入投保单数据
            int insert = bizSmokeMachineIncomingGoodsTbMapper.insert(tb);
            if (insert<= 0){
                throw new ErrorException(400,"投保单生成失败！");
            }
        }else {
            log.error("投保单已经存在，不进行操作！");
            log.error("{}",head);
        }

    }

    public ResultObject<List<ForeignContractHeadDto>> getExtractContractInfo(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject<List<ForeignContractHeadDto>> resultObject = ResultObject.createInstance(true, "获取成功！");
        String contractNo = params.getContractNo();
        List<ForeignContractHead> tempList = bizSmokeMachineIncomingGoodsHeadMapper.getExtractAuxmatContractList(contractNo,userInfo.getCompany());
        tempList.removeIf(item -> {
            // 过滤掉抄数量的合同
            String contractNoTemp = item.getContractNo();
            String tradeCode = userInfo.getCompany();
            // 获取当前合同提取的商品数量总和
            BigDecimal count = bizSmokeMachineIncomingGoodsHeadMapper.getIncomingCountByContractNo(contractNoTemp,tradeCode);
            if (count == null){
                count = new BigDecimal(0);
            }
            // 如果total数量大于count数量，则不进行移除
            if (item.getTotalQty().compareTo(count) > 0){
                return false;
            }
            return true;
        });


        if(CollectionUtils.isEmpty(tempList)){
            resultObject.setMessage("未查询到相关合同信息");
            return resultObject;
        }else {
            List<ForeignContractHeadDto> collect = tempList.stream().map(it -> {
                ForeignContractHeadDto dto = foreignContractHeadDtoMapper.toDto(it);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setData(collect);
        }
        return resultObject;
    }



    @Transactional(rollbackFor = Exception.class)
    public ResultObject extractContract(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        try {
            // 获取合同的ID
            String id = params.getId();
            String contractNo = params.getContractNo();
            if (StringUtils.isBlank(id)){
                throw new ErrorException(400, "合同ID不能为空");
            }

            // 获取外商合同表头信息
            ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(id);
            // 获取外商合同表体信息
            List<ForeignContractList> foreignContractList = foreignContractListMapper.getListByHeadId(id, userInfo.getCompany());


            log.error("表头信息：{}",foreignContractHead);
            foreignContractList.forEach(it -> {
                log.error("表体信息：{}",it);
            });

            // 获取当前合同的最大序号
            Integer serialNo = bizSmokeMachineIncomingGoodsHeadMapper.getMaxSerialNo(contractNo,userInfo.getCompany());
            if (serialNo == null){
                serialNo = 1;
            }

            // 设置提取信息
            BizSmokeGoodsExtractHeadList headList = new BizSmokeGoodsExtractHeadList();
            headList.setHead(new BizSmokeMachineIncomingGoodsHead());
            headList.setHeadList(new ArrayList<BizSmokeMachineIncomingGoodsList>());
            // 提取表头信息
            String sid = UUID.randomUUID().toString().replaceAll("-", "");
            // 获取币制信息
            String curr = foreignContractHead.getCurr();
            // 设置表头信息
            setContractHeadToIncomingGoodsHead(sid,foreignContractHead,headList.getHead(),userInfo,serialNo);
            // 提取外商表体信息
            foreignContractList.forEach(it -> {
                BizSmokeMachineIncomingGoodsList goodsList = new BizSmokeMachineIncomingGoodsList();
                // 需要汇总，当前非作废状态下合同，商品名称，对应的数量总和 ，和 进货管理当前合同提取的商品数量总和 ，如果前者大于后者，则可以提取
                // 根据合同号+商品名称，获取外商合同表体总数量
                BigDecimal count = bizSmokeMachineIncomingGoodsHeadMapper.getContractListCount(contractNo,it.getGName(),userInfo.getCompany());
                // 获取当前合同非作废状态下，商品名称，对应的数量总和
                BigDecimal count2 = bizSmokeMachineIncomingGoodsHeadMapper.getSmokeListCount(contractNo,it.getGName(),userInfo.getCompany());
                // 如果前者大于后者，则可以提取
                if(count.compareTo(count2) > 0){
                    // 提取表体信息
                    setContractListToIncomingGoodsList(it,goodsList,sid,curr,userInfo);
                    // 提取表体信息
                    headList.getHeadList().add(goodsList);
                }
            });

            // 插入表头表体信息
            bizSmokeMachineIncomingGoodsHeadMapper.insert(headList.getHead());
            // 重置表头返回信息
            headList.getHead().setCreateBy(userInfo.getUserName());
            // 插入表体信息
            headList.getHeadList().forEach(it -> {
                bizSmokeMachineIncomingGoodsListMapper.insert(it);
            });
            // 返回信息
            resultObject.setData(headList);
            return resultObject;
        } catch (ErrorException e) {
            log.error("提取外商合同错误：{}",e.getMessage());
            throw new RuntimeException("提取外商合同错误:" + e.getMessage());
        }
    }



    /**
     * 设置 第3条线 进货单表头信息
     * @param sid 进货单表头sid
     * @param foreignContractHead 外商合同表头
     * @param head 进货单表头
     * @param userInfo 用户信息
     * @param serialNo 序号
     *
     */
    public void setContractHeadToIncomingGoodsHead(String sid, ForeignContractHead foreignContractHead, BizSmokeMachineIncomingGoodsHead head, UserInfoToken userInfo, Integer serialNo) {
        head.setId(sid);
        head.setCreateTime(new Date());
        head.setCreateBy(userInfo.getUserNo());
        head.setInsertUserName(userInfo.getUserName());
        head.setExtend1(foreignContractHead.getCurr());
        head.setExtend3(foreignContractHead.getId());
        head.setDataState("0");
        head.setApprovalStatus("1");
        head.setTradeCode(userInfo.getCompany());
        // 1 业务类型	默认3-国营贸易进口烟机设备，置灰，不允许修改
        head.setBusinessType("3");
        // 2 合同号	弹窗选择，允许修改
        head.setContractNo(foreignContractHead.getContractNo());
        // 3 进货单号	合同号+2位流水号，唯一性校验，系统生成
        Integer currentSerialNo = serialNo + 1;
        head.setSerialNo(currentSerialNo);
        String formatSerialNo = String.format("%02d", currentSerialNo);
        head.setPurchaseNo(foreignContractHead.getContractNo() + formatSerialNo);
        // 4 客户	从选择的外商合同中“买方”带入。
        head.setCustomer(foreignContractHead.getBuyer());
        // 5 供应商	从选择的外商合同中“卖方”带入。
        head.setSupplier(foreignContractHead.getSeller());
        // 6 发票号 用户录入
        head.setInvoiceNo(null);
        // 7 启运港	从选择的外商合同中带入。(装运港)
        head.setPortOfDeparture(foreignContractHead.getShippingPort());
        // 8 目的地/港	从选择的外商合同中带入。
        head.setDestination(foreignContractHead.getDestPort());
        // 9 付款方式	从选择的外商合同中带入
        head.setPaymentMethod(foreignContractHead.getPaymentMethod());
        // 10 价格条款	从选择的外商合同中带入
        head.setPriceTerm(foreignContractHead.getPriceTerm());
        // 11 价格条款对应港口	从选择的外商合同中带入
        head.setPriceTermPort(foreignContractHead.getPriceTermPort());
        // 12 船名航次	用户录入
        head.setVesselVoyage(null);
        // 13 开航日期	用户录入
        head.setSailingDate(null);
        // 14 预计到达日期	用户录入
        head.setExpectedArrivalDate(null);
        // 15 做销日期	用户录入
        head.setSalesDateFrom(null);
        // 16 合同金额	系统计算=表体数量*表体单价，置灰，不允许修改。
        head.setContractAmount(bizSmokeCommonService.getTotalByHeadId(foreignContractHead.getId()));
        // 17 保险费率%	从外商合同对应的划款参数带入
        BigDecimal insuranceRateByType = bizSmokeCommonService.getInsuranceRateByType("3", userInfo.getCompany());
        head.setInsuranceRate(insuranceRateByType);
        // 18 投保加成%
        head.setInsuranceMarkup(null);
        // 19 制单人	自动识别最后操作人（编辑成功人员），显示登陆用户姓名，不允许编辑
        // 20 制单日期	系统自动生成最后操作时间：yyyy-mm-dd hh:mm:ss
        // 21 单据状态
        //    0.编制：未点击列表“确认”功能按钮
        //    1.确认：点击列表“确认”功能按钮，且成功提交
        //    2.作废：点击列表“作废”功能按钮，且成功提交
        // 22 确认时间	点击列表“确认”功能按钮，且成功提交的时间：yyyy-mm-dd hh:mm:ss
        head.setConfirmTime(null);
        // 23 审批状态	0不涉及审批：单据类型未启用审批流程的
        //	  1.未审批：启用审批流程而未点击列表“发送审批”功能按钮
        //	  2.审批中：点击列表“发送审批”功能按钮，且成功提交
        //	  3.审批通过：数据审批通过的（最后一节审批或全部审批通过时）
        //	  4.审批退回：数据审批某一节点有退回时
    }


    /**
     * 设置 第3条线 进货单表体信息
     * @param foreignContractList 进货单表体信息
     * @param goodsList 进货单表体信息
     * @param headId 表头ID
     * @param curr 表头币制
     * @param userInfo 用户信息
     */
    public void setContractListToIncomingGoodsList(ForeignContractList foreignContractList, BizSmokeMachineIncomingGoodsList goodsList, String headId, String curr, UserInfoToken userInfo) {
        goodsList.setId(UUID.randomUUID().toString().replaceAll("-", ""));
        goodsList.setHeadId(headId);
        goodsList.setCurr(curr);
        goodsList.setCreateTime(new Date());
        goodsList.setCreateBy(userInfo.getUserNo());
        goodsList.setInsertUserName(userInfo.getUserName());
        goodsList.setTradeCode(userInfo.getCompany());
        goodsList.setContractListId(foreignContractList.getId());
        // 1 商品名称	<新增明细>弹框带出，置灰，不允许修改
        goodsList.setGoodsName(foreignContractList.getGName());
        // 2 产品型号	<新增明细>弹框选择，根据商品名称带出，置灰，不允许修改
        goodsList.setProductModel(foreignContractList.getGModel());
        // 3 数量	录入
        goodsList.setQuantity(foreignContractList.getQty());
        // 4 单位	<新增明细>弹框选择，根据商品名称带出，置灰，不允许修改
        goodsList.setUnit(foreignContractList.getUnit());
        // 5 单价	录入
        goodsList.setUnitPrice(foreignContractList.getUnitPrice());
        // 6 金额	系统计算=数量*单价，置灰，不允许修改
        goodsList.setAmount(foreignContractList.getMoneyAmount());
        // 7 交货日期	录入
        goodsList.setDeliveryDate(foreignContractList.getDeliveryDate());
        // 8 总价折美元	金额*美元汇率
        bizSmokeCommonService.computedUsdMoney(goodsList,userInfo);
        // 9 备注
        goodsList.setNote(null);
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject cancel(BizSmokeMachineIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(params.getId());
        // 作废数据  进货信息表头 、 销售信息表头 、 证件信息表头
        ResultObject resultObject = ResultObject.createInstance(true, "作废成功！");
        if (StringUtils.isBlank(params.getId())){
            throw new ErrorException(400, "进货单ID不能为空");
        }
        // 判断是否为作废状态，如果是作废状态无需操作
        if (StringUtils.isNotBlank(bizSmokeMachineIncomingGoodsHead.getDataState()) && bizSmokeMachineIncomingGoodsHead.getDataState().equals("2")) {
            throw new ErrorException(400, "该票进货单已经作废，无需再次作废");
        }
        // 判断下游仓库管理是否存在提取数据记录
        // int count = bizISellHeadMapper.checkIsExtract(bizIncomingGoodsHead.getPurchaseNo(),userInfo.getCompany());
        // if (count > 0){
        //     throw new ErrorException(400, "仓库管理中存在该票进货单号，不允许作废");
        // }
        String id = params.getId();
        int i = bizSmokeMachineIncomingGoodsHeadMapper.cancelData(id);
        return resultObject;
    }


    /**
     * 第3条线 -> 烟机设备 -> 进货单发送您内审
     * @param param 进货单参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject sendAudit(BizSmokeMachineIncomingGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审核成功"));
        try {
            BizSmokeMachineIncomingGoodsHead head = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(param.getId());
            Assert.notNull(head, "进货单不存在，请刷新！");
            if (!"1".equals(head.getApprovalStatus())) {
                throw new ErrorException(400, "只有内审状态为 1 未审批的数据允许操作发送审批");
            }
            if (!"1".equals(head.getDataState())){
                throw new ErrorException(400, "只有单据状态为 1 确认的数据允许操作发送审批");
            }
            if (!CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue().equals(head.getApprovalStatus())
                    && !CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue().equals(head.getApprovalStatus())){
                throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
            }
            // 调用发送审核 审批流
            // 解析返回信息
            NextNodeInfoBatchVo batchVo = commonService.startFlowBatch(param.getBusinessType(), param.getBillType(), param.getIds(), userInfo);
            // 记录flowInstanceId
            head.setExtend2(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));

            // 表头更新状态
            head.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
            head.setUpdateBy(userInfo.getUserNo());
            head.setUpdateTime(new Date());
            head.setUpdateUserName(userInfo.getUserName());
            bizSmokeMachineIncomingGoodsHeadMapper.updateByPrimaryKey(head);

            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(head.getId());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
            return result;
        } catch (InterruptedException e) {
            log.error("发送内审失败：{}",e.getMessage());
            throw new RuntimeException(StringUtils.isBlank(e.getMessage())?"发送内审失败":e.getMessage());
        }
    }



    /**
     * 第3条线 -> 烟机设备 -> 内审通过
     * @param param 进货单参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject audit(BizSmokeMachineIncomingGoodsHeadParam param, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核成功"));
        try {
            List<String> sids = param.getIds();
            if (CollectionUtils.isEmpty(sids)) {
                throw new ErrorException(400, "请选择要审核的数据！");
            }
            //校验审核状态
            if (bizSmokeMachineIncomingGoodsHeadMapper.checkApprovalStatus(sids, CommonEnum.OrderApprStatusEnum.APPROVING.getValue()) > 0){
                throw new ErrorException(400, "存在不是<审批中>状态的数据，不允许操作！");
            }
            List<WorkFlowParam> flows = bizSmokeMachineIncomingGoodsHeadMapper.selectBySids(sids);
            Map<String, String> flowInstanceMap = flows.stream().collect(Collectors.toMap(WorkFlowParam::getFlowInstanceId, WorkFlowParam::getSid));
            // 调用 审批通过-审批流
            // 解析 返回结果
            List<NextNodeInfoVo> nextNodeInfoVos = commonService.passBatch(flows, param.getApprMessage(), userInfo);

            for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
                BizSmokeMachineIncomingGoodsHead head = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId()));
                if (nextNodeInfoVo.isFinish()) {
                    head.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                    head.setUpdateBy(userInfo.getUserNo());
                    head.setUpdateTime(new Date());
                    head.setUpdateUserName(userInfo.getUserName());
                    bizSmokeMachineIncomingGoodsHeadMapper.updateByPrimaryKey(head);
                    nextNodeInfoVo.setNodeName("审核通过");
                }
                //新增审核记录
                AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
                aeoAuditInfo.setBusinessSid(param.getIds().get(0));
                aeoAuditInfo.setApprNote(param.getApprMessage());
                aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName(), userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
            }
            return result;
        } catch (InterruptedException e) {
            log.error("内审失败：{}",e.getMessage());
            throw new RuntimeException(StringUtils.isBlank(e.getMessage())?"内审失败":e.getMessage());
        }
    }

    /**
     * 第3条线 -> 烟机设备 -> 内审退回
     * @param param 进货单参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject reject(BizSmokeMachineIncomingGoodsHeadParam param, UserInfoToken userInfo) {

        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("审核退回成功"));
        // 只有待审核数据才能操作
        if (bizSmokeMachineIncomingGoodsHeadMapper.checkApprovalStatus(param.getIds(), CommonEnum.OrderApprStatusEnum.APPROVING.getValue()) > 0){
            throw new ErrorException(400, "存在不是<审批中>状态的数据，不允许操作！");
        }


        try {
            List<WorkFlowParam> flows = bizSmokeMachineIncomingGoodsHeadMapper.selectBySids(param.getIds());
            // 审核退回 对接审批流
            // 解析 返回结果
            List<NextNodeInfoVo> nextNodeInfoVos = commonService.rejectBatch(flows, param.getApprMessage(), userInfo);

            for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
                //回退到发起人
                BizSmokeMachineIncomingGoodsHead head = bizSmokeMachineIncomingGoodsHeadMapper.selectByPrimaryKey(param.getIds().get(0));

                head.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
                head.setUpdateBy(userInfo.getUserNo());
                head.setUpdateTime(new Date());
                head.setUpdateUserName(userInfo.getUserName());
                bizSmokeMachineIncomingGoodsHeadMapper.updateByPrimaryKey(head);

                //新增审核记录
                AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
                aeoAuditInfo.setBusinessSid(param.getIds().get(0));
                aeoAuditInfo.setApprNote(param.getApprMessage());
                aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
            }
        } catch (Exception e) {
            log.error("内审退回失败：{}",e.getMessage());
            throw new RuntimeException(StringUtils.isBlank(e.getMessage())?"内审退回失败":e.getMessage(),e);
        }
        return result;
    }




    /**
     * 第3条线 -> 烟机设备 -> 查询内审列表
     * @param param 进货单参数
     * @param pageParam 分页参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject<List<BizIContractHeadDto>> getAeoListPaged(BizSmokeMachineIncomingGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalListByParam(param.getBusinessType(), CommonEnum.WORKFLOW_CONSTANT_ENUM.getValue(param.getApprovalStatus()));
        List<String> ids = (List<String>) result.getResult();

        // 如果ids为空，直接返回空分页结果
        if (ids == null || ids.isEmpty()) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }

        param.setIds(ids);

        param.setTradeCode(userInfo.getCompany());

        // 启用分页查询
        BizSmokeMachineIncomingGoodsHead head = bizSmokeMachineIncomingGoodsHeadDtoMapper.toPo(param);
        Page<BizSmokeMachineIncomingGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizSmokeMachineIncomingGoodsHeadMapper.getAeoList(head));
        List<BizSmokeMachineIncomingGoodsHeadDto> headDtos = page.getResult().stream()
                .map(bizSmokeMachineIncomingGoodsHeadDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(headDtos, (int) page.getTotal(), page.getPageNum());
    }

    public ResultObject getSumTotal(BizSmokeMachineIncomingGoodsHeadParam params,PageParam pageParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        // 启用分页查询
        BizSmokeMachineIncomingGoodsHead bizSmokeMachineIncomingGoodsHead = bizSmokeMachineIncomingGoodsHeadDtoMapper.toPo(params);
        bizSmokeMachineIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        BizSmokeHeadTotalDto dto =  bizSmokeMachineIncomingGoodsHeadMapper.getSumTotal(bizSmokeMachineIncomingGoodsHead);
        resultObject.setData(dto);
        return resultObject;
    }
}