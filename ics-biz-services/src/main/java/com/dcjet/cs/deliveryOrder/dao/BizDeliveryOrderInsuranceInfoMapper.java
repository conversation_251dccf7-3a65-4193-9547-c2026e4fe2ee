package com.dcjet.cs.deliveryOrder.dao;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderInsuranceInfo;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizDeliveryOrderInsuranceInfo
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizDeliveryOrderInsuranceInfoMapper extends Mapper<BizDeliveryOrderInsuranceInfo> {
    /**
     * 查询获取数据
     * @param bizDeliveryOrderInsuranceInfo
     * @return
     */
    List<BizDeliveryOrderInsuranceInfo> getList(BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);


    BizDeliveryOrderInsuranceInfo getDeliveryOrderInsuranceInfoByHeadSid(String headId);
    List<BizDeliveryOrderInsuranceInfo> getDeliveryOrderInsuranceInfoByHeadSids(List<String> sids);
    void deleteByHeadSids(List<String> sids);
}
