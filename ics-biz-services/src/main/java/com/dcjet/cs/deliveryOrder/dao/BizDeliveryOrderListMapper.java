package com.dcjet.cs.deliveryOrder.dao;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderList;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizDeliveryOrderList
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizDeliveryOrderListMapper extends Mapper<BizDeliveryOrderList> {
    /**
     * 查询获取数据
     * @param bizDeliveryOrderList
     * @return
     */
    List<BizDeliveryOrderList> getList(BizDeliveryOrderList bizDeliveryOrderList);
    List<BizDeliveryOrderList> getListByHeadId(String headId);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    void deleteByHeadSids(List<String> sids);
    SevenForeignContractList getForeignContractListQty(String sid);
}
