package com.dcjet.cs.deliveryOrder.service;
import com.aizuda.bpm.engine.assist.ObjectUtils;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.attach.dao.AttachedMapper;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListMapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderHead;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderList;
import com.dcjet.cs.deliveryOrder.dao.*;
import com.dcjet.cs.deliveryOrder.model.*;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountSummaryParam;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountTobacooParam;
import com.dcjet.cs.dto.customs.CustomsDeclarationData;
import com.dcjet.cs.dto.customs.CustomsDeclarationRequest;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.AttachmentItemDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.AttachmentRequestDto;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadDto;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList;
import com.dcjet.cs.nonAuxiliaryMaterials.service.BizNonIncomingGoodsHeadService;
import com.dcjet.cs.params.dao.PackageInfoMapper;
import com.dcjet.cs.params.model.PackageInfo;
import com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper;
import com.dcjet.cs.seven.dao.SevenForeignContractListMapper;
import com.dcjet.cs.seven.mapper.SevenForeignContractHeadDtoMapper;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.RMBConverterUtil;
import com.dcjet.cs.util.RequestUtil;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.deliveryOrder.mapper.BizDeliveryOrderHeadDtoMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizDeliveryOrderHeadService extends BaseService<BizDeliveryOrderHead> {
    private static final Logger log = LoggerFactory.getLogger(BizDeliveryOrderHeadService.class);
    @Resource
    private BizDeliveryOrderHeadMapper bizDeliveryOrderHeadMapper;
    @Resource
    private BizDeliveryOrderHeadDtoMapper bizDeliveryOrderHeadDtoMapper;
    @Override
    public Mapper<BizDeliveryOrderHead> getMapper() {
        return bizDeliveryOrderHeadMapper;
    }
    @Resource
    private AttachedMapper attachedMapper;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private CommonService commonService;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizDeliveryOrderHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizDeliveryOrderHeadDto>> getListPaged(BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadDtoMapper.toPo(bizDeliveryOrderHeadParam);
        bizDeliveryOrderHead.setTradeCode(userInfo.getCompany());
        Page<BizDeliveryOrderHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizDeliveryOrderHeadMapper.getList(bizDeliveryOrderHead));
        List<BizDeliveryOrderHeadDto> bizDeliveryOrderHeadDtos = page.getResult().stream().map(head -> {
            BizDeliveryOrderHeadDto dto = bizDeliveryOrderHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizDeliveryOrderHeadDto>> paged = ResultObject.createInstance(bizDeliveryOrderHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizDeliveryOrderHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderHeadDto insert(BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, UserInfoToken userInfo) {
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadDtoMapper.toPo(bizDeliveryOrderHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizDeliveryOrderHead.setSid(sid);
        bizDeliveryOrderHead.setCreateUserName(userInfo.getUserName());
        bizDeliveryOrderHead.setCreateBy(userInfo.getUserNo());
        bizDeliveryOrderHead.setCreateTime(new Date());
        bizDeliveryOrderHead.setBusinessType("7");
        bizDeliveryOrderHead.setSendDeclare("1");
        String serilNo = createSerilNo(bizDeliveryOrderHead.getContractNo());
        bizDeliveryOrderHead.setPurchaseOrderNo(serilNo);
        int check = bizDeliveryOrderHeadMapper.checkKey(bizDeliveryOrderHead);
        if(check>0){
            throw new ErrorException(400, "出货单号已经存在！");
        }
        // 新增数据
        int insertStatus = bizDeliveryOrderHeadMapper.insert(bizDeliveryOrderHead);
        return  insertStatus > 0 ? bizDeliveryOrderHeadDtoMapper.toDto(bizDeliveryOrderHead) : null;
    }

    private String createSerilNo(String contractNo) {
        String serilNo = bizDeliveryOrderHeadMapper.getSerilNo(contractNo);
        if(StringUtils.isNotBlank(serilNo)){
            return contractNo+String.format("%03d",Integer.valueOf(serilNo.substring(serilNo.length() - 2))+1);
        }
        return contractNo+"01";
    }
    public ResultObject getSupplierList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizDeliveryOrderHeadMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    public ResultObject getCreateUserList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizDeliveryOrderHeadMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 功能描述:修改
     *
     * @param bizDeliveryOrderHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderHeadDto update(BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, UserInfoToken userInfo) {
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(bizDeliveryOrderHeadParam.getSid());
        boolean b = StringUtils.equals(bizDeliveryOrderHead.getIsSave(), "1");
        bizDeliveryOrderHeadDtoMapper.updatePo(bizDeliveryOrderHeadParam, bizDeliveryOrderHead);
        bizDeliveryOrderHead.setUpdateBy(userInfo.getUserNo());
        bizDeliveryOrderHead.setUpdateUserName(userInfo.getUserName());
        bizDeliveryOrderHead.setUpdateTime(new Date());
        bizDeliveryOrderHead.setIsSave("1");
        // 更新数据
        int update = bizDeliveryOrderHeadMapper.updateByPrimaryKey(bizDeliveryOrderHead);
        if(update > 0 && !b){
            SevenForeignContractHead sevenForeignContractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(bizDeliveryOrderHead.getPurchaseNoMark());
            List<BizBpAnalyseOrderHead> listByContractId = bizBpAnalyseOrderHeadMapper.getListByContractId(bizDeliveryOrderHead.getPurchaseNoMark());
            BizDeliveryOrderShipmentsParam shipments = new BizDeliveryOrderShipmentsParam();
            shipments.setHeadId(bizDeliveryOrderHead.getSid());
            if(ObjectUtils.isNotEmpty(sevenForeignContractHead)){
                shipments.setPortOfShipment(sevenForeignContractHead.getShippingPort());
                shipments.setPortOfDestination(sevenForeignContractHead.getDestPort());
            }
            if(CollectionUtils.isNotEmpty(listByContractId)){
                shipments.setAnalysisId(listByContractId.get(0).getId());
                shipments.setShipper(listByContractId.get(0).getShipper());
                shipments.setConsignee(listByContractId.get(0).getConsignee());
                shipments.setNotifyParty(listByContractId.get(0).getNotifyParty());
                shipments.setDestinationDate(listByContractId.get(0).getShipmentDeadline());
                shipments.setWarehouseAddress(listByContractId.get(0).getWarehouseAddress());
                //联系人 电话  未拆分
                shipments.setContactPerson(listByContractId.get(0).getContactPhone());
                shipments.setContactPhone(listByContractId.get(0).getContactPhone());
            }
            bizDeliveryOrderShipmentsService.insert(shipments,userInfo);
            BizDeliveryOrderCertParam cert = new BizDeliveryOrderCertParam();
            cert.setHeadId(bizDeliveryOrderHead.getSid());
            bizDeliveryOrderCertService.insert(cert,userInfo);
            BizDeliveryOrderInsuranceInfoParam insuranceInfo = new BizDeliveryOrderInsuranceInfoParam();
            insuranceInfo.setHeadId(bizDeliveryOrderHead.getSid());
            insuranceInfo.setPurchaseOrderNo(bizDeliveryOrderHead.getPurchaseOrderNo());
            insuranceInfo.setInsuranceCompany(bizDeliveryOrderHead.getCustomer());
            insuranceInfo.setInvoiceTitle(bizDeliveryOrderHead.getCustomer());
            insuranceInfo.setInsurant(bizDeliveryOrderHead.getSupplier());
            insuranceInfo.setCurr(bizDeliveryOrderHead.getCurr());
            bizDeliveryOrderInsuranceInfoService.insert(insuranceInfo,userInfo);
        }

        return update > 0 ? bizDeliveryOrderHeadDtoMapper.toDto(bizDeliveryOrderHead) : null;
    }
    @Resource
    private BizBpAnalyseOrderHeadMapper bizBpAnalyseOrderHeadMapper;
    @Resource
    private BizBpAnalyseOrderListMapper bizBpAnalyseOrderListMapper;
    @Resource
    private SevenForeignContractHeadMapper sevenForeignContractHeadMapper;
    @Resource
    private BizDeliveryOrderShipmentsService bizDeliveryOrderShipmentsService;
    @Resource
    private BizDeliveryOrderCertService bizDeliveryOrderCertService;
    @Resource
    private BizDeliveryOrderInsuranceInfoService bizDeliveryOrderInsuranceInfoService;

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Resource
    private BizDeliveryOrderListMapper bizDeliveryOrderListMapper;
    @Resource
    private BizDeliveryOrderContainerListMapper bizDeliveryOrderContainerListMapper;
    @Resource
    private BizDeliveryOrderInsuranceInfoMapper bizDeliveryOrderInsuranceInfoMapper;
    @Resource
    private BizDeliveryOrderShipmentsMapper bizDeliveryOrderShipmentsMapper;
    @Resource
    private BizDeliveryOrderCertMapper bizDeliveryOrderCertMapper;
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizDeliveryOrderHeadMapper.deleteBySids(sids);
        bizDeliveryOrderContainerListMapper.deleteByHeadSids(sids);
        bizDeliveryOrderListMapper.deleteByHeadSids(sids);
        bizDeliveryOrderInsuranceInfoMapper.deleteByHeadSids(sids);
        bizDeliveryOrderShipmentsMapper.deleteByHeadSids(sids);
        bizDeliveryOrderCertMapper.deleteByHeadSids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizDeliveryOrderHeadDto> selectAll(BizDeliveryOrderHeadParam exportParam, UserInfoToken userInfo) {
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadDtoMapper.toPo(exportParam);
         bizDeliveryOrderHead.setTradeCode(userInfo.getCompany());
        List<BizDeliveryOrderHeadDto> bizDeliveryOrderHeadDtos = new ArrayList<>();
        List<BizDeliveryOrderHead> bizDeliveryOrderHeads = bizDeliveryOrderHeadMapper.getList(bizDeliveryOrderHead);
        if (CollectionUtils.isNotEmpty(bizDeliveryOrderHeads)) {
            bizDeliveryOrderHeadDtos = bizDeliveryOrderHeads.stream().map(head -> {
                BizDeliveryOrderHeadDto dto = bizDeliveryOrderHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizDeliveryOrderHeadDtos;
    }
    public ResultObject confirmStatus(BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(bizDeliveryOrderHeadParam.getSid());
        if (bizDeliveryOrderHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizDeliveryOrderHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizDeliveryOrderHead update = bizDeliveryOrderHeadDtoMapper.toPo(bizDeliveryOrderHeadParam);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsConfirm("1");
        bizDeliveryOrderHeadMapper.updateByPrimaryKeySelective(update);
        BizDeliveryOrderHeadDto dto = bizDeliveryOrderHeadDtoMapper.toDto(update);
        resultObject.setData(dto);
        return resultObject;
    }
    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));

        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(sid);
        if (bizDeliveryOrderHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizDeliveryOrderHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许退单");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(bizDeliveryOrderHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
        BizDeliveryOrderHead update = new BizDeliveryOrderHead();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        update.setIsConfirm("0");
        bizDeliveryOrderHeadMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(sid);
        if (bizDeliveryOrderHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizDeliveryOrderHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizDeliveryOrderHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizDeliveryOrderHead update = new BizDeliveryOrderHead();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        update.setIsConfirm("2");
        bizDeliveryOrderHeadMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(sid);
        if (bizDeliveryOrderHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizDeliveryOrderHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将出口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizDeliveryOrderHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizDeliveryOrderHead); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizDeliveryOrderHead bizDeliveryOrderHead) {
        BizDeliveryOrderHead update = new BizDeliveryOrderHead();
        update.setSid(bizDeliveryOrderHead.getSid());
        update.setApprStatus(bizDeliveryOrderHead.getApprStatus());
        bizDeliveryOrderHeadMapper.updateByPrimaryKeySelective(update);
    }
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private SevenForeignContractListMapper sevenForeignContractListMapper;
    public ResultObject insertByContract(BizDeliveryOrderHeadParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        SevenForeignContractHead sevenForeignContractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(param.getSid());
        if(ObjectUtils.isEmpty(sevenForeignContractHead)){
            resultObject.setSuccess(false);
            return resultObject;
        }
        BizMerchant bizMerchant = bizMerchantMapper.getByMerchantCode(sevenForeignContractHead.getBuyer(), userInfo.getCompany());
        List<SevenForeignContractList> contractList = bizDeliveryOrderHeadMapper.getForeignContractListQty(sevenForeignContractHead.getId());
//        List<BizBpAnalyseOrderHead> bpAnalyseOrderList = bizBpAnalyseOrderHeadMapper.getListByContractId(sevenForeignContractHead.getId());
//        BizBpAnalyseOrderHead bpAnalyseOrder = null;
//        List<BizBpAnalyseOrderList> AnalyseList = null;
//        if(CollectionUtils.isNotEmpty(bpAnalyseOrderList)){
//            bpAnalyseOrder = bpAnalyseOrderList.get(0);
//            AnalyseList = bizBpAnalyseOrderListMapper.getListByHeadId(bpAnalyseOrder.getId());
//        }
        BigDecimal totalGrossWt = new BigDecimal(0);
        BigDecimal totalNetWt = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);

        BizDeliveryOrderHead bizDeliveryOrderHead = new BizDeliveryOrderHead();
        String sid = UUID.randomUUID().toString();
        bizDeliveryOrderHead.setSid(sid);
        bizDeliveryOrderHead.setBusinessType("7");
        bizDeliveryOrderHead.setContractNo(sevenForeignContractHead.getContractNo());
        bizDeliveryOrderHead.setPurchaseOrderNo(createSerilNo(bizDeliveryOrderHead.getContractNo()));
        bizDeliveryOrderHead.setSupplier(sevenForeignContractHead.getSeller());
        bizDeliveryOrderHead.setCustomer(sevenForeignContractHead.getBuyer());
        bizDeliveryOrderHead.setCustomerAddress(bizMerchant.getMerchantAddress());
        bizDeliveryOrderHead.setBusinessEnterprise(sevenForeignContractHead.getBuyer());
        bizDeliveryOrderHead.setDeliveryEnterprise(sevenForeignContractHead.getBuyer());
        bizDeliveryOrderHead.setDeliveryEnterpriseAddress(bizMerchant.getMerchantAddress());
        bizDeliveryOrderHead.setPortOfDestination(sevenForeignContractHead.getDestPort());
        bizDeliveryOrderHead.setPaymentMethod(sevenForeignContractHead.getPaymentMethod());
        bizDeliveryOrderHead.setCurr(sevenForeignContractHead.getCurr());
        bizDeliveryOrderHead.setPriceTerm(sevenForeignContractHead.getPriceTerm());
        bizDeliveryOrderHead.setPriceTermPort(sevenForeignContractHead.getPriceTermPort());

        if(CollectionUtils.isNotEmpty(contractList)){
            for (SevenForeignContractList foreignContractList : contractList) {
                BizDeliveryOrderList bizDeliveryOrderList = new BizDeliveryOrderList();
                bizDeliveryOrderList.setHeadId(sid);
                bizDeliveryOrderList.setContractListId(foreignContractList.getId());
                bizDeliveryOrderList.setProductName(foreignContractList.getGName());
                bizDeliveryOrderList.setProductModel(foreignContractList.getGModel());
                bizDeliveryOrderList.setUnit(foreignContractList.getUnit());
                bizDeliveryOrderList.setQty(null != foreignContractList.getQtya() ? foreignContractList.getQtya() : new BigDecimal(0));
                bizDeliveryOrderList.setUnitPrice(foreignContractList.getUnitPrice());
                BigDecimal amount = foreignContractList.getUnitPrice().multiply(bizDeliveryOrderList.getQty()).setScale(2, RoundingMode.HALF_UP);
                bizDeliveryOrderList.setAmount(amount);
                String lsid = UUID.randomUUID().toString();
                bizDeliveryOrderList.setSid(lsid);
                bizDeliveryOrderList.setTradeCode(userInfo.getCompany());
                bizDeliveryOrderList.setCreateUserName(userInfo.getUserName());
                bizDeliveryOrderList.setCreateBy(userInfo.getUserNo());
                bizDeliveryOrderList.setCreateTime(new Date());
                bizDeliveryOrderListMapper.insert(bizDeliveryOrderList);
                totalAmount = totalAmount.add(amount);

                BizDeliveryOrderContainerList bizDeliveryOrderContainerList = new BizDeliveryOrderContainerList();
                bizDeliveryOrderContainerList.setProductName(foreignContractList.getGName());
                bizDeliveryOrderContainerList.setContainerNum(new BigDecimal(0));
                bizDeliveryOrderContainerList.setQty(new BigDecimal(0));
                bizDeliveryOrderContainerList.setGrossWt(new BigDecimal(0));
                bizDeliveryOrderContainerList.setNetWt(new BigDecimal(0));
                bizDeliveryOrderContainerList.setTareWt(new BigDecimal(0));
                bizDeliveryOrderContainerList.setLonger(new BigDecimal(0));
                bizDeliveryOrderContainerList.setWhither(new BigDecimal(0));
                bizDeliveryOrderContainerList.setHigher(new BigDecimal(0));
                bizDeliveryOrderContainerList.setContractListId(lsid);
                bizDeliveryOrderContainerList.setHeadId(sid);
                bizDeliveryOrderContainerList.setSid(UUID.randomUUID().toString());
                bizDeliveryOrderContainerList.setTradeCode(userInfo.getCompany());
                bizDeliveryOrderContainerList.setCreateUserName(userInfo.getUserName());
                bizDeliveryOrderContainerList.setCreateBy(userInfo.getUserNo());
                bizDeliveryOrderContainerList.setCreateTime(new Date());
                bizDeliveryOrderContainerListMapper.insert(bizDeliveryOrderContainerList);

//                if(!ObjectUtils.isEmpty(bpAnalyseOrder) && CollectionUtils.isNotEmpty(AnalyseList)){
//                    Optional<BizBpAnalyseOrderList> first = AnalyseList.stream().filter(x -> x.getProductName().equals(foreignContractList.getGName())
//                            && x.getProductModel().equals(foreignContractList.getGModel())).findFirst();
//                    if(first.isPresent()){
//                        bizDeliveryOrderList.setAnalyseListId(first.get().getId());
//                        BigDecimal listGrossWt = bizDeliveryOrderList.getQty().divide(first.get().getQuantity(), 6, RoundingMode.HALF_UP)
//                                .multiply(first.get().getGrossWeight()).setScale(4, RoundingMode.HALF_UP);
//                        bizDeliveryOrderList.setGrossWeight(listGrossWt);
//                        totalGrossWt = totalGrossWt.add(listGrossWt);
//                        BigDecimal listNetWt = bizDeliveryOrderList.getQty().divide(first.get().getQuantity(), 6, RoundingMode.HALF_UP)
//                                .multiply(first.get().getNetWt()).setScale(4, RoundingMode.HALF_UP);
//                        bizDeliveryOrderList.setNetWeight(listNetWt);
//                        totalNetWt = totalNetWt.add(listNetWt);
//                    }
//                }
            }
            bizDeliveryOrderHead.setTotalGrossWt(totalGrossWt);
            bizDeliveryOrderHead.setTotalNetWt(totalNetWt);
            bizDeliveryOrderHead.setTotalAmount(totalAmount);
            if(null != bizDeliveryOrderHead.getTotalGrossWt() && null != bizDeliveryOrderHead.getTotalNetWt()){
                bizDeliveryOrderHead.setTotalTare(bizDeliveryOrderHead.getTotalGrossWt().subtract(bizDeliveryOrderHead.getTotalNetWt()).setScale(4,RoundingMode.HALF_UP));
            }
        }


        bizDeliveryOrderHead.setCreateUserName(userInfo.getUserName());
        bizDeliveryOrderHead.setCreateBy(userInfo.getUserNo());
        bizDeliveryOrderHead.setCreateTime(new Date());
        bizDeliveryOrderHead.setBusinessType("7");
        bizDeliveryOrderHead.setSendDeclare("1");
        bizDeliveryOrderHead.setPurchaseMark("1");
        bizDeliveryOrderHead.setPurchaseNoMark(sevenForeignContractHead.getId());
        bizDeliveryOrderHead.setStatus("0");
        bizDeliveryOrderHead.setRedFlush("1");
        bizDeliveryOrderHead.setIsConfirm("0");
        bizDeliveryOrderHead.setSendFinance("0");
        bizDeliveryOrderHead.setApprStatus("0");
        bizDeliveryOrderHead.setTradeCode(userInfo.getCompany());

        int insertStatus = bizDeliveryOrderHeadMapper.insert(bizDeliveryOrderHead);
        bizDeliveryOrderHead.setCreaterBy(userInfo.getUserNo());
        bizDeliveryOrderHead.setCreaterTime(new Date());
        bizDeliveryOrderHead.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? bizDeliveryOrderHeadDtoMapper.toDto(bizDeliveryOrderHead) : null);
        return resultObject;
    }
    @Resource
    private ExportService exportService;
    @Resource
    private PackageInfoMapper packageInfoMapper;
    public ResponseEntity printLink(BizDeliveryOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(param.getSid());

        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);

        if(bizDeliveryOrderHead == null){
            throw new ErrorException(400,"出货单不存在！");
        }else {
            convertHeadToPrintLink(bizDeliveryOrderHead,bizMerchantMap,userInfo);
        }
        BigDecimal lwh = new BigDecimal(0);
        List<BizDeliveryOrderContainerList> list = bizDeliveryOrderContainerListMapper.getListByHeadId(bizDeliveryOrderHead.getSid());
        List<BizDeliveryOrderList> listList = bizDeliveryOrderListMapper.getListByHeadId(bizDeliveryOrderHead.getSid());
        PackageInfo packageInfo = new PackageInfo();
        packageInfo.setTradeCode(userInfo.getCompany());
        List<PackageInfo> storehouses = packageInfoMapper.getList(packageInfo);
        if(CollectionUtils.isNotEmpty(list)){
            BizDeliveryOrderCert cert = bizDeliveryOrderCertMapper.getDeliveryOrderCertByHeadSid(bizDeliveryOrderHead.getSid());
            for (BizDeliveryOrderContainerList container : list) {
                if(ObjectUtils.isNotEmpty(cert)){
                    if(StringUtils.isNotBlank(cert.getShippingMark())){
                        container.setShippingMark(cert.getShippingMark());
                    }
                }
                if(StringUtils.isNotBlank(container.getPackageStyle())){
                    Optional<PackageInfo> first = storehouses.stream().filter(x -> x.getParamCode().equals(container.getPackageStyle())).findFirst();
                    first.ifPresent(info -> container.setPackageStyleStr(info.getPackUnitCnName() + " \n" + NumberFormatterUtils.formatNumber(container.getQty())));
                }
                if(null != container.getGrossWt()){
                    container.setGrossWtStr(NumberFormatterUtils.formatNumber(container.getGrossWt()));
                }
                if(null != container.getNetWt()){
                    container.setNetWtStr(NumberFormatterUtils.formatNumber(container.getNetWt()));
                }
                if(null != container.getLonger() && null != container.getWhither() && null != container.getHigher()){
                    BigDecimal bigDecimal = container.getLonger().multiply(container.getWhither()).multiply(container.getHigher()).setScale(4, RoundingMode.HALF_UP);
                    container.setLwhStr(NumberFormatterUtils.formatNumber(bigDecimal));
                    lwh = lwh.add(bigDecimal);
                }
                Optional<BizDeliveryOrderList> first = listList.stream().filter(x -> x.getSid().equals(container.getContractListId())).findFirst();
                if(first.isPresent()){
                    if(null != first.get().getUnitPrice()){
                        container.setUnitPriceStr(NumberFormatterUtils.formatNumber(first.get().getUnitPrice()));
                    }
                    if(null != first.get().getAmount() && null != container.getQty() && null != first.get().getQty()){
                        BigDecimal bigDecimal = container.getQty().divide(first.get().getQty(),2,RoundingMode.HALF_UP).multiply(first.get().getAmount()).setScale(2,RoundingMode.HALF_UP);
                        container.setAmountStr(NumberFormatterUtils.formatNumber(bigDecimal));
                    }
                }

            }
            BigDecimal grossWtTotal = list.stream().map(BizDeliveryOrderContainerList::getGrossWt).reduce(BigDecimal.ZERO, BigDecimal::add);
            bizDeliveryOrderHead.setTotalGrossWtStr(NumberFormatterUtils.formatNumber(grossWtTotal));
            BigDecimal netWtTotal = list.stream().map(BizDeliveryOrderContainerList::getNetWt).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal qtyTotal = list.stream().map(BizDeliveryOrderContainerList::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            bizDeliveryOrderHead.setTotalNetWtStr(NumberFormatterUtils.formatNumber(netWtTotal));
            bizDeliveryOrderHead.setQtyTotalStr(NumberFormatterUtils.formatNumber(qtyTotal));
            bizDeliveryOrderHead.setTotalLwhStr(NumberFormatterUtils.formatNumber(lwh));
        }

        String tempName = "biz_delivery_order.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());

        String outName = xdoi18n.XdoI18nUtil.t("出货单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(bizDeliveryOrderHead),list,fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
    public ResponseEntity printIns(BizDeliveryOrderHeadParam param, UserInfoToken userInfo) throws Exception {
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(param.getSid());
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);
        Map<String, String> bizMerchantMapEn = createMerchantMapEn(bizMerchants);
        BigDecimal bigDecimal = new BigDecimal(0);
        BigDecimal bigDecimal2 = new BigDecimal(0);

        if(bizDeliveryOrderHead == null){
            throw new ErrorException(400,"出货单不存在！");
        }
        BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo = bizDeliveryOrderInsuranceInfoMapper.getDeliveryOrderInsuranceInfoByHeadSid(param.getSid());
        if(ObjectUtils.isEmpty(bizDeliveryOrderInsuranceInfo)){
            throw new ErrorException(400,"出货单投保信息不存在！");
        }
        BizDeliveryOrderShipments shipments = bizDeliveryOrderShipmentsMapper.getDeliveryOrderShipmentsByHeadSid(param.getSid());
        bizDeliveryOrderInsuranceInfo.setInsurantStr(bizMerchantMapEn.get(bizDeliveryOrderInsuranceInfo.getInsurant()) + "\n " +bizMerchantMapEn.get(bizDeliveryOrderInsuranceInfo.getInvoiceTitle()));
        bizDeliveryOrderInsuranceInfo.setContractNo(bizDeliveryOrderHead.getContractNo());
        bizDeliveryOrderInsuranceInfo.setStartShipmentDateStr(formatter.format(bizDeliveryOrderInsuranceInfo.getStartShipmentDate()));
        bizDeliveryOrderInsuranceInfo.setInvoiceTitleStr(bizMerchantMap.get(bizDeliveryOrderInsuranceInfo.getInvoiceTitle()));
        if(ObjectUtils.isNotEmpty(shipments)){
            List<BizBpAnalyseOrderList> listByHeadId = bizBpAnalyseOrderListMapper.getListByHeadId(shipments.getAnalysisId());
            bigDecimal = listByHeadId.stream().map(BizBpAnalyseOrderList::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            bigDecimal2 = listByHeadId.stream().map(BizBpAnalyseOrderList::getBoxCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        List<BizDeliveryOrderList> list = bizDeliveryOrderListMapper.getListByHeadId(bizDeliveryOrderHead.getSid());
        if(CollectionUtils.isNotEmpty(list)){
            if(bigDecimal.compareTo(new BigDecimal(0)) != 0){
                BigDecimal reduce = list.stream().map(BizDeliveryOrderList::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal qty = reduce.divide(bigDecimal, 4, RoundingMode.HALF_UP).multiply(bigDecimal2).setScale(2, RoundingMode.HALF_UP);
                bizDeliveryOrderInsuranceInfo.setQtyStr(NumberFormatterUtils.formatNumber(qty));
            }else {
                bizDeliveryOrderInsuranceInfo.setQtyStr("0.00");
            }
            bizDeliveryOrderInsuranceInfo.setProductNameStr(list.get(0).getProductName());
            bizDeliveryOrderInsuranceInfo.setAmountCurrStr(bizDeliveryOrderInsuranceInfo.getCurr() + NumberFormatterUtils.formatNumber(bizDeliveryOrderHead.getTotalAmount()));
        }

        String tempName = "biz_delivery_order_insurance.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());

        String outName = xdoi18n.XdoI18nUtil.t("投保单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(bizDeliveryOrderInsuranceInfo),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    private Map<String, String> createMerchantMapEn(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameEn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameEn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }
    private void convertHeadToPrintLink(BizDeliveryOrderHead head, Map<String, String> bizMerchantMap, UserInfoToken userInfo){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        BizDeliveryOrderShipments shipments = bizDeliveryOrderShipmentsMapper.getDeliveryOrderShipmentsByHeadSid(head.getSid());
        List<Map<String, String>> portList = bizDeliveryOrderHeadMapper.getPortList(userInfo.getCompany());
        List<Map<String, String>> countryList = bizDeliveryOrderHeadMapper.getCountryList(userInfo.getCompany());

        head.setCreaterUserName(StringUtils.isNotBlank(head.getUpdateUserName()) ? head.getUpdateUserName() : head.getCreateUserName());
        if(null != head.getTotalGrossWt()){
            head.setTotalGrossWtStr(NumberFormatterUtils.formatNumber(head.getTotalGrossWt()));
        }
        if(null != head.getTotalNetWt()){
            head.setTotalNetWtStr(NumberFormatterUtils.formatNumber(head.getTotalNetWt()));
        }
        if(null != head.getTotalAmount()){
            head.setTotalAmountStr(NumberFormatterUtils.formatNumber(head.getTotalAmount()));
        }
        if(StringUtils.isNotBlank(head.getPaymentMethod())){
            if("0".equals(head.getPaymentMethod())){
                head.setPaymentMethod("付款交单");
            }else if("1".equals(head.getPaymentMethod())){
                head.setPaymentMethod("即期信用证");
            }else if("2".equals(head.getPaymentMethod())){
                head.setPaymentMethod("电汇");
            }else if("3".equals(head.getPaymentMethod())){
                head.setPaymentMethod("预付款");
            }
        }
        if(StringUtils.isNotBlank(head.getTradeCountry())){
            for(Map<String, String> country : countryList){
                if(country.get("value").equals(head.getTradeCountry())){
                    head.setTradeCountry(country.get("label"));
                }
            }
        }
        if(ObjectUtils.isNotEmpty(shipments)){
            if(null != shipments.getDestinationDate()){
                String formattedDate = formatter.format(shipments.getDestinationDate());
                head.setDestinationDateStr(formattedDate);
            }
            if(StringUtils.isNotBlank(shipments.getPortOfShipment())){
                for(Map<String, String> destination : portList){
                    if(destination.get("value").equals(shipments.getPortOfShipment())){
                        head.setPortOfShipmentStr(destination.get("label"));
                    }
                }
            }
            if(StringUtils.isNotBlank(shipments.getPortOfDestination())){
                for(Map<String, String> destination : portList){
                    if(destination.get("value").equals(shipments.getPortOfDestination())){
                        head.setPortOfDestinationStr(destination.get("label"));
                    }
                }
            }
            if(StringUtils.isNotBlank(shipments.getShipper())){
                head.setShipper(shipments.getShipper());
            }
            if(StringUtils.isNotBlank(shipments.getConsignee())){
                head.setConsignee(shipments.getConsignee());
            }
            if(StringUtils.isNotBlank(shipments.getNotifyParty())){
                head.setNotifyParty(shipments.getNotifyParty());
            }
            if(StringUtils.isNotBlank(shipments.getWarehouseAddress())){
                head.setWarehouseAddressStr(shipments.getWarehouseAddress());
            }
            if(StringUtils.isNotBlank(shipments.getContactPerson())){
                head.setContactPersonStr(shipments.getContactPerson()+"/"+shipments.getContactPhone());
            }
        }
    }

    public ResultObject<BizDeliveryOrderHeadDto> getDeliveryOrderSid(BizDeliveryOrderHeadParam param, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(param.getSid());
        if (bizDeliveryOrderHead != null) {
            bizDeliveryOrderHead.setCreaterBy(StringUtils.isNotBlank(bizDeliveryOrderHead.getUpdateBy()) ? bizDeliveryOrderHead.getUpdateBy() : bizDeliveryOrderHead.getCreateBy());
            bizDeliveryOrderHead.setCreaterTime(null != bizDeliveryOrderHead.getUpdateTime() ? bizDeliveryOrderHead.getUpdateTime() : bizDeliveryOrderHead.getCreateTime());
            bizDeliveryOrderHead.setCreaterUserName(StringUtils.isNotBlank(bizDeliveryOrderHead.getUpdateUserName()) ? bizDeliveryOrderHead.getUpdateUserName() : bizDeliveryOrderHead.getCreateUserName());
            BizDeliveryOrderHeadDto dto = bizDeliveryOrderHeadDtoMapper.toDto(bizDeliveryOrderHead);
            resultObject.setData(dto);
        }
        return resultObject;
    }

    public ResultObject sendEntry(BizDeliveryOrderHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String id = params.getSid();

        try {
            // 1. 获取认证Token
            String accessToken = getAuthToken();
            if (accessToken == null) {
                throw new ErrorException(400, "获取认证Token失败，无法发送数据");
            }

            // 2. 获取通关配置信息
            GwstdHttpConfig httpConfig = commonService.getHttpConfigInfo("GW_ERP_E_DEC_LIST");
            if (httpConfig == null) {
                throw new ErrorException(400, "未配置通关接口信息，type: GW_ERP_E_DEC_LIST");
            }

            // 拼接完整的接口地址
            String apiUrl = httpConfig.getBaseUrl() + httpConfig.getServiceUrl();

            // 2. 查询表头数据
            BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadMapper.selectByPrimaryKey(id);
            if (bizDeliveryOrderHead == null) {
                throw new ErrorException(400, "未找到对应的出货信息表头数据");
            }

            // 3. 查询表体数据
            List<BizDeliveryOrderList> listByHeadId = bizDeliveryOrderListMapper.getListByHeadId(bizDeliveryOrderHead.getSid());

            if (CollectionUtils.isEmpty(listByHeadId)) {
                throw new ErrorException(400, "未找到对应的出货信息表体数据");
            }

            // 4. 构建发送数据
            DeliveryOrderDeclarationRequest requestData = buildSendData(bizDeliveryOrderHead, listByHeadId);

            // 5. 转换为JSON并发送
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonData = objectMapper.writeValueAsString(requestData);

            log.info("发送出货信息到通关接口，URL: {}, 数据: {}", apiUrl, jsonData);

            // 6. 发送HTTP请求，使用获取到的accessToken
            String response = RequestUtil.sendPost(apiUrl, accessToken, jsonData);

            log.info("通关接口返回结果: {}", response);

            // 7. 解析返回结果并处理
            ObjectMapper responseMapper = new ObjectMapper();
            Map<String, Object> responseMap = responseMapper.readValue(response, Map.class);

            Boolean success = (Boolean) responseMap.get("success");
            String message = (String) responseMap.get("message");
            Object data = responseMap.get("data");

            if (success != null && success && data == null) {
                // 成功且data为null，更新发送状态
                bizDeliveryOrderHead.setSendDeclare("0");
                bizDeliveryOrderHeadMapper.updateByPrimaryKey(bizDeliveryOrderHead);

                resultObject.setMessage("发送成功");
                resultObject.setData(response);
                log.info("数据发送成功，已更新发送状态");

            } else if (data != null) {
                // data不为null，说明有错误信息，不更新状态，返回错误信息
                resultObject.setSuccess(false);
                resultObject.setMessage(message != null ? message : "数据校验失败");

                // 处理错误数据，提取facGNo和errorMsg
                String formattedErrorMessage = formatErrorMessage(data);
                resultObject.setData(formattedErrorMessage);
                log.warn("数据校验失败: {}", message);

            } else {
                // 其他情况，根据success状态处理
                if (success != null && success) {
                    bizDeliveryOrderHead.setSendDeclare("0");
                    bizDeliveryOrderHeadMapper.updateByPrimaryKey(bizDeliveryOrderHead);
                    resultObject.setMessage(message != null ? message : "发送成功");
                    resultObject.setData(response);
                } else {
                    resultObject.setSuccess(false);
                    resultObject.setMessage(message != null ? message : "发送失败");
                    resultObject.setData(response);
                }
            }
            //发送附件至关务
            processAttachments(bizDeliveryOrderHead, accessToken, userInfo);

        } catch (Exception e) {
            log.error("发送出货信息到通关接口失败", e);
            resultObject.setSuccess(false);
            resultObject.setMessage("发送失败: " + e.getMessage());
        }

        return resultObject;
    }
    private DeliveryOrderDeclarationRequest buildSendData(BizDeliveryOrderHead head, List<BizDeliveryOrderList> list) {
        DeliveryOrderDeclarationRequest request = new DeliveryOrderDeclarationRequest();
        List<DeliveryOrderDeclarationData> dataList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = dateFormat.format(new Date());

        for (int i = 0; i < list.size(); i++) {
            BizDeliveryOrderList goods = list.get(i);
            DeliveryOrderDeclarationData data = new DeliveryOrderDeclarationData();

            // 根据字段对应关系构建数据
            data.setBillNo(head.getPurchaseOrderNo()); // 单据号 - 出货单号
            data.setBillSerialNo(i + 1); // 单据序号 - 表体行号
            data.setBondMark(""); // 保完税标志 - 空
            data.setGMark(""); // 物料类型 - 空
            data.setFacGNo(goods.getProductName()); // 企业料号 - 商品名称
            data.setQtyErp(goods.getQty()); // 数量 - 出货信息表体-数量
            data.setDecPrice(goods.getUnitPrice()); // 单价 - 出货信息表体-单价
            data.setDecTotal(goods.getAmount()); // 总价 - 出货信息表体-金额
            data.setUnitErp(goods.getUnit()); // ERP交易单位 - 出货信息表体-单位
            data.setCurr(head.getCurr()); // 币制 - 出货信息表头-币种
            data.setNetWt(goods.getQty()); // 净重 - 当前数量对应的净重
            data.setGrossWet(""); // 毛重 - 空
            data.setDestinationCountry(""); // 最终目的国 - 空
            data.setBomVersion("");
            data.setSONumber("");
            data.setSOLineNumber("");
            data.setSODate("");
            data.setInvNo(""); // 发票号 - 出货信息表头-发票号码
            data.setSOInvDate(""); // 发票日期 - 空
            data.setSupplierCode(head.getCustomer()); // 供应商代码 - 出货信息表头-供应商
            data.setIeMode(""); // 出口方式 - 空
            data.setFirstQty(""); // 法定数量 - 空
            data.setSecondQty(""); // 法定第二数量 - 空
            data.setNote(""); // 备注 - 空
            data.setLastUpdateTime(currentTime); // ERP创建时间 - 接口数据接收时间
            data.setTempOwner(currentTime); // 传输批次号 - 接口数据接收时间
            data.setDutyMode(""); // 征免方式 - 空
            data.setDistrictCode(""); // 境内目的地 - 空
            data.setDistrictPostCode(""); // 境内目的地行政区划 - 空
            data.setOriginCountry(""); // 原产国 - 空
            data.setEntryGNo(""); // 报关单归并序号 - 空
            data.setRemark1(goods.getNote()); // 备注1 - 出货信息表体-商品描述
            data.setRemark2(""); // 备注2 - 空
            data.setRemark3(""); // 备注3 - 空
            data.setInOutRelNo(""); // 入库关联单号 - 空
            data.setGmodel("");
            data.setUnit("");
            data.setPackNum("");

            dataList.add(data);
        }

        request.setData(dataList);
        return request;
    }
    private void processAttachments(BizDeliveryOrderHead bizDeliveryOrderHead, String accessToken, UserInfoToken userInfo) {
        GwstdHttpConfig httpConfig = commonService.getHttpConfigInfo("GW_ATTACH");
        if (httpConfig == null) {
            throw new ErrorException(400, "未配置附件接口信息，type: GW_ATTACH");
        }

        // 拼接完整的接口地址
        String apiUrl = httpConfig.getBaseUrl() + httpConfig.getServiceUrl();

        //组装body
        List<Attached> byHeadId = attachedMapper.getByHeadId(bizDeliveryOrderHead.getSid(), userInfo.getCompany());

        AttachmentRequestDto attachmentRequestDto = new AttachmentRequestDto();
        attachmentRequestDto.setPurchaseOrderNo(bizDeliveryOrderHead.getPurchaseOrderNo());
        List<AttachmentItemDto> attachmentItemList = new ArrayList<>();
        byHeadId.forEach(item -> {
            AttachmentItemDto attachmentItemDto = new AttachmentItemDto();
            attachmentItemDto.setAttachmentType(item.getAcmpType());
            //base64
            try {
                attachmentItemDto.setFileContent(Base64.getEncoder().encodeToString(fileHandler.downloadFile(item.getFileName())));
            } catch (Exception e) {
                attachmentItemDto.setFileContent(StringUtils.EMPTY);
            }
            attachmentItemDto.setFileName(item.getOriginFileName());
            attachmentItemDto.setNote(item.getNote());
            attachmentItemList.add(attachmentItemDto);
        });
        attachmentRequestDto.setAttachmentList(attachmentItemList);

        try {
            // 5. 转换为JSON并发送
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonData = objectMapper.writeValueAsString(attachmentRequestDto);

            log.info("发送附件到附件接口，URL: {}, 数据: {}", apiUrl, jsonData);

            // 6. 发送HTTP请求，使用获取到的accessToken
            String response = RequestUtil.sendPost(apiUrl, accessToken, jsonData);

            log.info("附件接口返回结果: {}", response);

        } catch (Exception e) {
            log.error("发送附件到附件接口失败", e);
        }

    }
    /**
     * 获取中央认证接口的Token
     * @return 返回accessToken，如果获取失败返回null
     */
    private String getAuthToken() {
        try {
            // 1. 获取认证接口配置信息
            GwstdHttpConfig authConfig = commonService.getHttpConfigInfo("GW_AUTH_TOKEN");
            if (authConfig == null) {
                log.error("未配置认证接口信息，type: GW_AUTH_TOKEN");
                return null;
            }

            // 2. 构建认证请求数据
            Map<String, String> authData = new HashMap<>();
            authData.put("userno", authConfig.getExtendFiled1()); // 用户名存储在扩展字段1
            authData.put("password", authConfig.getExtendFiled2()); // 密码存储在扩展字段2

            // 3. 转换为JSON
            ObjectMapper objectMapper = new ObjectMapper();
            String authJson = objectMapper.writeValueAsString(authData);

            // 4. 拼接认证接口地址
            String authUrl = authConfig.getBaseUrl() + authConfig.getServiceUrl();

            log.info("调用中央认证接口，URL: {}, 请求数据: {}", authUrl, authJson);

            // 5. 发送认证请求
            String response = RequestUtil.sendPost(authUrl, null, authJson);

            log.info("中央认证接口返回结果: {}", response);

            // 6. 解析返回结果
            Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);
            Boolean success = (Boolean) responseMap.get("success");

            if (success != null && success) {
                Map<String, Object> data = (Map<String, Object>) responseMap.get("data");
                if (data != null) {
                    String accessToken = (String) data.get("accessToken");
                    log.info("成功获取accessToken: {}", accessToken);
                    return accessToken;
                }
            } else {
                String message = (String) responseMap.get("message");
                log.error("认证失败: {}", message);
            }

        } catch (Exception e) {
            log.error("获取认证Token失败", e);
        }

        return null;
    }
    private String formatErrorMessage(Object data) {
        try {
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                Object errorDataListObj = dataMap.get("errorDataList");

                if (errorDataListObj instanceof List) {
                    List<Map<String, Object>> errorDataList = (List<Map<String, Object>>) errorDataListObj;
                    StringBuilder errorMessage = new StringBuilder();

                    for (int i = 0; i < errorDataList.size(); i++) {
                        Map<String, Object> errorItem = errorDataList.get(i);

                        // 获取errorData中的facGNo和errorMsg
                        Object errorDataObj = errorItem.get("errorData");
                        String errorMsg = (String) errorItem.get("errorMsg");

                        String facGNo = "";
                        if (errorDataObj instanceof Map) {
                            Map<String, Object> errorDataMap = (Map<String, Object>) errorDataObj;
                            facGNo = (String) errorDataMap.get("facGNo");
                        }

                        // 组合facGNo和errorMsg
                        if (StringUtils.isNotBlank(facGNo) && StringUtils.isNotBlank(errorMsg)) {
                            if (i > 0) {
                                errorMessage.append("; ");
                            }
                            errorMessage.append("商品[").append(facGNo).append("]: ").append(errorMsg);
                        } else if (StringUtils.isNotBlank(errorMsg)) {
                            if (i > 0) {
                                errorMessage.append("; ");
                            }
                            errorMessage.append(errorMsg);
                        }
                    }

                    return errorMessage.toString();
                }
            }
        } catch (Exception e) {
            log.error("格式化错误信息失败", e);
        }

        return "数据校验失败，请检查数据格式";
    }
    @Resource
    private SevenForeignContractHeadDtoMapper sevenForeignContractHeadDtoMapper;
    public ResultObject<List<SevenForeignContractHeadDto>> listInDeliveryOrderHead(BizDeliveryOrderHeadParam params, PageParam pageParam, UserInfoToken userInfo) {
        BizDeliveryOrderHead bizDeliveryOrderHead = bizDeliveryOrderHeadDtoMapper.toPo(params);
        bizDeliveryOrderHead.setTradeCode(userInfo.getCompany());
        Page<SevenForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizDeliveryOrderHeadMapper.getForeignContractHeadListQty(bizDeliveryOrderHead));
        List<SevenForeignContractHeadDto> headDtoList = page.getResult().stream()
                .map(fcd -> this.sevenForeignContractHeadDtoMapper.toDto(fcd))
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }
}
