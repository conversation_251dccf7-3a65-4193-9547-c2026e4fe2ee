<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderShipmentsMapper">
    <resultMap id="bizDeliveryOrderShipmentsResultMap" type="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="SHIPPER" property="shipper" jdbcType="VARCHAR" />
		<result column="CONSIGNEE" property="consignee" jdbcType="VARCHAR" />
		<result column="NOTIFY_PARTY" property="notifyParty" jdbcType="VARCHAR" />
		<result column="PORT_OF_SHIPMENT" property="portOfShipment" jdbcType="VARCHAR" />
		<result column="PORT_OF_DESTINATION" property="portOfDestination" jdbcType="VARCHAR" />
		<result column="DESTINATION_DATE" property="destinationDate" jdbcType="TIMESTAMP" />
		<result column="WAREHOUSE_ADDRESS" property="warehouseAddress" jdbcType="VARCHAR" />
		<result column="CONTACT_PERSON" property="contactPerson" jdbcType="VARCHAR" />
		<result column="CONTACT_PHONE" property="contactPhone" jdbcType="VARCHAR" />
		<result column="NOTE" property="note" jdbcType="VARCHAR" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="ANALYSIS_ID" property="analysisId" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,CREATE_BY
     ,CREATE_TIME
     ,CREATE_USER_NAME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,UPDATE_USER_NAME
     ,TRADE_CODE
     ,SYS_ORG_CODE
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
     ,BUSINESS_TYPE
     ,SHIPPER
     ,CONSIGNEE
     ,NOTIFY_PARTY
     ,PORT_OF_SHIPMENT
     ,PORT_OF_DESTINATION
     ,DESTINATION_DATE
     ,WAREHOUSE_ADDRESS
     ,CONTACT_PERSON
     ,CONTACT_PHONE
     ,NOTE
     ,HEAD_ID
     ,ANALYSIS_ID
    </sql>
    <sql id="condition">
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizDeliveryOrderShipmentsResultMap" parameterType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_DELIVERY_ORDER_SHIPMENTS t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getDeliveryOrderShipmentsByHeadSid"
            resultType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments">
        select <include refid="Base_Column_List"/> from T_BIZ_DELIVERY_ORDER_SHIPMENTS t where t.HEAD_ID = #{headId};
    </select>
    <select id="getDeliveryOrderShipmentsByHeadSids"
            resultType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments">
        select <include refid="Base_Column_List"/> from T_BIZ_DELIVERY_ORDER_SHIPMENTS t where t.HEAD_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_DELIVERY_ORDER_SHIPMENTS t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadSids">
        delete from T_BIZ_DELIVERY_ORDER_SHIPMENTS t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
