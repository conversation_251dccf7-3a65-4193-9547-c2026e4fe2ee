package com.dcjet.cs.deliveryOrder.mapper;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderContainerList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizDeliveryOrderContainerListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizDeliveryOrderContainerListDto toDto(BizDeliveryOrderContainerList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizDeliveryOrderContainerList toPo(BizDeliveryOrderContainerListParam param);
    /**
     * 数据库原始数据更新
     * @param bizDeliveryOrderContainerListParam
     * @param bizDeliveryOrderContainerList
     */
    void updatePo(BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, @MappingTarget BizDeliveryOrderContainerList bizDeliveryOrderContainerList);
    default void patchPo(BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, BizDeliveryOrderContainerList bizDeliveryOrderContainerList) {
        // TODO 自行实现局部更新
    }
}
