package com.dcjet.cs.deliveryOrder.mapper;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizDeliveryOrderHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizDeliveryOrderHeadDto toDto(BizDeliveryOrderHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizDeliveryOrderHead toPo(BizDeliveryOrderHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizDeliveryOrderHeadParam
     * @param bizDeliveryOrderHead
     */
    void updatePo(BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, @MappingTarget BizDeliveryOrderHead bizDeliveryOrderHead);
    default void patchPo(BizDeliveryOrderHeadParam bizDeliveryOrderHeadParam, BizDeliveryOrderHead bizDeliveryOrderHead) {
        // TODO 自行实现局部更新
    }
}
