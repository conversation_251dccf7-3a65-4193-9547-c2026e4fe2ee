package com.dcjet.cs.deliveryOrder.dao;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderContainerList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizDeliveryOrderContainerList
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizDeliveryOrderContainerListMapper extends Mapper<BizDeliveryOrderContainerList> {
    /**
     * 查询获取数据
     * @param bizDeliveryOrderContainerList
     * @return
     */
    List<BizDeliveryOrderContainerList> getList(BizDeliveryOrderContainerList bizDeliveryOrderContainerList);
    List<BizDeliveryOrderContainerList> getListByHeadId(String headId);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    void deleteByHeadSids(List<String> sids);
}
