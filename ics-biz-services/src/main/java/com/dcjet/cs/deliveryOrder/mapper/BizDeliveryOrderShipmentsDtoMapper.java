package com.dcjet.cs.deliveryOrder.mapper;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizDeliveryOrderShipmentsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizDeliveryOrderShipmentsDto toDto(BizDeliveryOrderShipments po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizDeliveryOrderShipments toPo(BizDeliveryOrderShipmentsParam param);
    /**
     * 数据库原始数据更新
     * @param bizDeliveryOrderShipmentsParam
     * @param bizDeliveryOrderShipments
     */
    void updatePo(BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, @MappingTarget BizDeliveryOrderShipments bizDeliveryOrderShipments);
    default void patchPo(BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, BizDeliveryOrderShipments bizDeliveryOrderShipments) {
        // TODO 自行实现局部更新
    }
}
