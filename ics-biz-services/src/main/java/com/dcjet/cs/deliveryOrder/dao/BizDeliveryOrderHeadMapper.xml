<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderHeadMapper">
    <resultMap id="bizDeliveryOrderHeadResultMap" type="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR" />
		<result column="PURCHASE_ORDER_NO" property="purchaseOrderNo" jdbcType="VARCHAR" />
		<result column="SUPPLIER" property="supplier" jdbcType="VARCHAR" />
		<result column="CUSTOMER" property="customer" jdbcType="VARCHAR" />
		<result column="CUSTOMER_ADDRESS" property="customerAddress" jdbcType="VARCHAR" />
		<result column="TRADE_COUNTRY" property="tradeCountry" jdbcType="VARCHAR" />
		<result column="BUSINESS_ENTERPRISE" property="businessEnterprise" jdbcType="VARCHAR" />
		<result column="PORT_OF_DESTINATION" property="portOfDestination" jdbcType="VARCHAR" />
		<result column="PAYMENT_METHOD" property="paymentMethod" jdbcType="VARCHAR" />
		<result column="CURR" property="curr" jdbcType="VARCHAR" />
		<result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="NUMERIC" />
		<result column="TRANSPORT_MODE" property="transportMode" jdbcType="VARCHAR" />
		<result column="PRICE_TERM" property="priceTerm" jdbcType="VARCHAR" />
		<result column="PRICE_TERM_PORT" property="priceTermPort" jdbcType="VARCHAR" />
		<result column="DELIVERY_ENTERPRISE" property="deliveryEnterprise" jdbcType="VARCHAR" />
		<result column="WRAP_TYPE" property="wrapType" jdbcType="VARCHAR" />
		<result column="PACK_NUM" property="packNum" jdbcType="NUMERIC" />
		<result column="DELIVERY_ENTERPRISE_ADDRESS" property="deliveryEnterpriseAddress" jdbcType="VARCHAR" />
		<result column="TOTAL_NET_WT" property="totalNetWt" jdbcType="NUMERIC" />
		<result column="TOTAL_GROSS_WT" property="totalGrossWt" jdbcType="NUMERIC" />
		<result column="TOTAL_TARE" property="totalTare" jdbcType="NUMERIC" />
		<result column="SEND_DECLARE" property="sendDeclare" jdbcType="VARCHAR" />
		<result column="BUSINESS_DATE" property="businessDate" jdbcType="TIMESTAMP" />
		<result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="IS_CONFIRM" property="isConfirm" jdbcType="VARCHAR" />
		<result column="IS_SAVE" property="isSave" jdbcType="VARCHAR" />
		<result column="NOTE" property="note" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR" />
		<result column="SEND_FINANCE" property="sendFinance" jdbcType="VARCHAR" />
		<result column="RED_FLUSH" property="redFlush" jdbcType="VARCHAR" />
		<result column="PURCHASE_MARK" property="purchaseMark" jdbcType="VARCHAR" />
		<result column="PURCHASE_NO_MARK" property="purchaseNoMark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     t.SID
     ,COALESCE(t.update_by,t.create_by) as createrBy
     ,COALESCE(t.update_user_name,t.create_user_name) as createrUserName
     ,COALESCE(t.update_time,t.create_time) as createrTime
     ,t.CREATE_BY
     ,t.CREATE_TIME
     ,t.CREATE_USER_NAME
     ,t.UPDATE_BY
     ,t.UPDATE_TIME
     ,t.UPDATE_USER_NAME
     ,t.TRADE_CODE
     ,t.SYS_ORG_CODE
     ,t.EXTEND1
     ,t.EXTEND2
     ,t.EXTEND3
     ,t.EXTEND4
     ,t.EXTEND5
     ,t.EXTEND6
     ,t.EXTEND7
     ,t.EXTEND8
     ,t.EXTEND9
     ,t.EXTEND10
     ,t.BUSINESS_TYPE
     ,t.CONTRACT_NO
     ,t.PURCHASE_ORDER_NO
     ,t.SUPPLIER
     ,t.CUSTOMER
     ,t.CUSTOMER_ADDRESS
     ,t.TRADE_COUNTRY
     ,t.BUSINESS_ENTERPRISE
     ,t.PORT_OF_DESTINATION
     ,t.PAYMENT_METHOD
     ,t.CURR
     ,t.TOTAL_AMOUNT
     ,t.TRANSPORT_MODE
     ,t.PRICE_TERM
     ,t.PRICE_TERM_PORT
     ,t.DELIVERY_ENTERPRISE
     ,t.WRAP_TYPE
     ,t.PACK_NUM
     ,t.DELIVERY_ENTERPRISE_ADDRESS
     ,t.TOTAL_NET_WT
     ,t.TOTAL_GROSS_WT
     ,t.TOTAL_TARE
     ,t.SEND_DECLARE
     ,t.BUSINESS_DATE
     ,t.CONFIRM_TIME
     ,t.IS_CONFIRM
     ,t.IS_SAVE
     ,t.NOTE
     ,t.STATUS
     ,t.APPR_STATUS
     ,t.SEND_FINANCE
     ,t.RED_FLUSH
     ,t.PURCHASE_MARK
     ,t.PURCHASE_NO_MARK
     ,tc.ENTRY_NO
     ,tc.DECLARATION_DATE
    </sql>
    <sql id="condition">
        <if test="tradeCode != null and tradeCode != ''">
            and t.trade_code = #{tradeCode}
        </if>
    <if test="contractNo != null and contractNo != ''">
	  and t.CONTRACT_NO like '%'|| #{contractNo} || '%'
	</if>
    <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
	  and t.PURCHASE_ORDER_NO like '%'|| #{purchaseOrderNo} || '%'
	</if>
    <if test="portOfDestination != null and portOfDestination != ''">
	  and t.PORT_OF_DESTINATION like '%'|| #{portOfDestination} || '%'
	</if>
    <if test="status != null and status != ''">
		and t.STATUS = #{status}
	</if>
        <if test="createBy != null and createBy != ''">
            and COALESCE(t.update_by,t.create_by) like '%'|| #{createBy} || '%'
        </if>
        <if test="customer != null and customer != ''">
            and t.customer like '%'|| #{customer} || '%'
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) < DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
        <if test="destinationDateFrom != null and destinationDateFrom != ''">
            <![CDATA[ and ts.DESTINATION_DATE >= to_date(#{destinationDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="destinationDateTo != null and destinationDateTo != ''">
            <![CDATA[ and ts.DESTINATION_DATE < DATEADD(DAY, 1, to_date(#{destinationDateTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizDeliveryOrderHeadResultMap" parameterType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_DELIVERY_ORDER_HEAD t
        left join T_BIZ_DELIVERY_ORDER_CERT tc on t.sid = tc.head_id
        left join T_BIZ_DELIVERY_ORDER_SHIPMENTS ts on t.sid = ts.head_id
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.update_time,t.create_time) desc
    </select>
    <select id="getSerilNo" resultType="java.lang.String">
        SELECT t.PURCHASE_ORDER_NO FROM T_BIZ_DELIVERY_ORDER_HEAD t where CONTRACT_NO = #{contractNo} order BY t.PURCHASE_ORDER_NO DESC LIMIT 1
    </select>
    <select id="getForeignContractHeadListQty" resultType="com.dcjet.cs.seven.model.SevenForeignContractHead">
        SELECT h.ID as tId,t.ID ,h.CONTRACT_NO ,t.G_NAME,
        (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) AS qtya
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD h
        LEFT JOIN T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST t ON h.ID = t.HEAD_ID
        LEFT JOIN T_BIZ_DELIVERY_ORDER_LIST o ON t.id = o.CONTRACT_LIST_ID
        where h.DATA_STATUS = '1' AND t.BODY_TYPE='process'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO like '%'|| #{contractNo} || '%'
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and t.trade_code = #{tradeCode}
        </if>
        GROUP BY h.ID,t.ID ,h.CONTRACT_NO ,t.G_NAME,t.QTY
        HAVING (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) > 0
    </select>
    <select id="getForeignContractListQty" resultType="com.dcjet.cs.seven.model.SevenForeignContractList">
        SELECT t.ID, t.G_NAME ,t.G_MODEL,t.UNIT,t.UNIT_PRICE,
               (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) AS qtya
        FROM T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST t
                  LEFT JOIN T_BIZ_DELIVERY_ORDER_LIST o ON t.id = o.CONTRACT_LIST_ID
        WHERE t.HEAD_ID = #{headId} AND t.BODY_TYPE='process'
        GROUP BY t.ID, t.G_NAME ,t.G_MODEL,t.QTY,t.UNIT,t.UNIT_PRICE
        HAVING (COALESCE(t.QTY, 0) - COALESCE(SUM(o.QTY), 0)) > 0;
    </select>
    <select id="checkKey" resultType="java.lang.Integer">
        select count(1) from T_BIZ_DELIVERY_ORDER_HEAD where PURCHASE_ORDER_NO = #{PURCHASE_ORDER_NO} and TRADE_CODE = #{tradeCode} and STATUS !='2'
        <if test="sid != null and sid != ''"> and sid !=#{sid} </if>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_DELIVERY_ORDER_HEAD t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            t.customer as "value",
            m.MERCHANT_NAME_CN as "label"
        from  T_BIZ_DELIVERY_ORDER_HEAD t LEFT JOIN T_BIZ_MERCHANT m ON t.customer = m.merchant_code
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="getCreateUserList" resultType="java.util.Map">
        select
            distinct
            COALESCE(update_by,create_by) as "value"
                   ,COALESCE(update_user_name,create_user_name) as "label"
        from  T_BIZ_DELIVERY_ORDER_HEAD t
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="getPortList" resultType="java.util.Map">
        select
            distinct
            params_code as "value",
            params_name as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'PORT'
          and TRADE_CODE = #{tradeCode}
    </select>
    <select id="getCountryList" resultType="java.util.Map">
        select
            distinct
            params_name as "value",
            custom_param_name as "label"
        from  T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'COUNTRY'
          and TRADE_CODE = #{tradeCode}
    </select>
</mapper>
