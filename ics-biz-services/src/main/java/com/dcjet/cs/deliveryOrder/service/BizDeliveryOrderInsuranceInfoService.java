package com.dcjet.cs.deliveryOrder.service;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderInsuranceInfo;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderInsuranceInfoMapper;
import com.dcjet.cs.deliveryOrder.mapper.BizDeliveryOrderInsuranceInfoDtoMapper;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderInsuranceInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizDeliveryOrderInsuranceInfoService extends BaseService<BizDeliveryOrderInsuranceInfo> {
    @Resource
    private BizDeliveryOrderInsuranceInfoMapper bizDeliveryOrderInsuranceInfoMapper;
    @Resource
    private BizDeliveryOrderInsuranceInfoDtoMapper bizDeliveryOrderInsuranceInfoDtoMapper;
    @Override
    public Mapper<BizDeliveryOrderInsuranceInfo> getMapper() {
        return bizDeliveryOrderInsuranceInfoMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizDeliveryOrderInsuranceInfoParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizDeliveryOrderInsuranceInfoDto>> getListPaged(BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo = bizDeliveryOrderInsuranceInfoDtoMapper.toPo(bizDeliveryOrderInsuranceInfoParam);
        bizDeliveryOrderInsuranceInfo.setTradeCode(userInfo.getCompany());
        Page<BizDeliveryOrderInsuranceInfo> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizDeliveryOrderInsuranceInfoMapper.getList(bizDeliveryOrderInsuranceInfo));
        List<BizDeliveryOrderInsuranceInfoDto> bizDeliveryOrderInsuranceInfoDtos = page.getResult().stream().map(head -> {
            BizDeliveryOrderInsuranceInfoDto dto = bizDeliveryOrderInsuranceInfoDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizDeliveryOrderInsuranceInfoDto>> paged = ResultObject.createInstance(bizDeliveryOrderInsuranceInfoDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizDeliveryOrderInsuranceInfoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderInsuranceInfoDto insert(BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, UserInfoToken userInfo) {
        BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo = bizDeliveryOrderInsuranceInfoDtoMapper.toPo(bizDeliveryOrderInsuranceInfoParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizDeliveryOrderInsuranceInfo.setSid(sid);
        bizDeliveryOrderInsuranceInfo.setCreateUserName(userInfo.getUserName());
        bizDeliveryOrderInsuranceInfo.setCreateBy(userInfo.getUserNo());
        bizDeliveryOrderInsuranceInfo.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizDeliveryOrderInsuranceInfoMapper.insert(bizDeliveryOrderInsuranceInfo);
        return  insertStatus > 0 ? bizDeliveryOrderInsuranceInfoDtoMapper.toDto(bizDeliveryOrderInsuranceInfo) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizDeliveryOrderInsuranceInfoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderInsuranceInfoDto update(BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, UserInfoToken userInfo) {
        BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo = bizDeliveryOrderInsuranceInfoMapper.selectByPrimaryKey(bizDeliveryOrderInsuranceInfoParam.getSid());
        bizDeliveryOrderInsuranceInfoDtoMapper.updatePo(bizDeliveryOrderInsuranceInfoParam, bizDeliveryOrderInsuranceInfo);
        bizDeliveryOrderInsuranceInfo.setUpdateBy(userInfo.getUserNo());
        bizDeliveryOrderInsuranceInfo.setUpdateUserName(userInfo.getUserName());
        bizDeliveryOrderInsuranceInfo.setUpdateTime(new Date());
        // 更新数据
        int update = bizDeliveryOrderInsuranceInfoMapper.updateByPrimaryKey(bizDeliveryOrderInsuranceInfo);
        return update > 0 ? bizDeliveryOrderInsuranceInfoDtoMapper.toDto(bizDeliveryOrderInsuranceInfo) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizDeliveryOrderInsuranceInfoMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizDeliveryOrderInsuranceInfoDto> selectAll(BizDeliveryOrderInsuranceInfoParam exportParam, UserInfoToken userInfo) {
        BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo = bizDeliveryOrderInsuranceInfoDtoMapper.toPo(exportParam);
         bizDeliveryOrderInsuranceInfo.setTradeCode(userInfo.getCompany());
        List<BizDeliveryOrderInsuranceInfoDto> bizDeliveryOrderInsuranceInfoDtos = new ArrayList<>();
        List<BizDeliveryOrderInsuranceInfo> bizDeliveryOrderInsuranceInfos = bizDeliveryOrderInsuranceInfoMapper.getList(bizDeliveryOrderInsuranceInfo);
        if (CollectionUtils.isNotEmpty(bizDeliveryOrderInsuranceInfos)) {
            bizDeliveryOrderInsuranceInfoDtos = bizDeliveryOrderInsuranceInfos.stream().map(head -> {
                BizDeliveryOrderInsuranceInfoDto dto = bizDeliveryOrderInsuranceInfoDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizDeliveryOrderInsuranceInfoDtos;
    }
    public ResultObject<BizDeliveryOrderInsuranceInfoDto> getDeliveryOrderInsuranceInfoByHeadSid(BizDeliveryOrderInsuranceInfoParam param, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderInsuranceInfoDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo = bizDeliveryOrderInsuranceInfoMapper.getDeliveryOrderInsuranceInfoByHeadSid(param.getHeadId());
        if (bizDeliveryOrderInsuranceInfo != null) {
            BizDeliveryOrderInsuranceInfoDto dto = bizDeliveryOrderInsuranceInfoDtoMapper.toDto(bizDeliveryOrderInsuranceInfo);
            resultObject.setData(dto);
        }
        return resultObject;
    }
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    public ResultObject<EnterpriseRate> getExchangeRate(String curr,UserInfoToken userInfo) {
        ResultObject<EnterpriseRate> resultObject = ResultObject.createInstance(true,"获取成功！");
        EnterpriseRate enterpriseRate = new EnterpriseRate();
        enterpriseRate.setCurr(curr);
        enterpriseRate.setTradeCode(userInfo.getCompany());
        List<EnterpriseRate> enterpriseRates = enterpriseRateMapper.selectMessage(enterpriseRate);
        if(CollectionUtils.isNotEmpty(enterpriseRates)){
            resultObject.setData(enterpriseRates.get(0));
        }
        return resultObject;
    }
}
