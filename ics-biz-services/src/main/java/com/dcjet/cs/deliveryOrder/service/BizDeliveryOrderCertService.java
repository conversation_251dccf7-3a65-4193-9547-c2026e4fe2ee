package com.dcjet.cs.deliveryOrder.service;
import com.dcjet.cs.dto.warehouse.BizStoreEHeadDto;
import com.dcjet.cs.dto.warehouse.BizStoreEHeadParam;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderCertMapper;
import com.dcjet.cs.deliveryOrder.mapper.BizDeliveryOrderCertDtoMapper;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizDeliveryOrderCertService extends BaseService<BizDeliveryOrderCert> {
    @Resource
    private BizDeliveryOrderCertMapper bizDeliveryOrderCertMapper;
    @Resource
    private BizDeliveryOrderCertDtoMapper bizDeliveryOrderCertDtoMapper;
    @Override
    public Mapper<BizDeliveryOrderCert> getMapper() {
        return bizDeliveryOrderCertMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizDeliveryOrderCertParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizDeliveryOrderCertDto>> getListPaged(BizDeliveryOrderCertParam bizDeliveryOrderCertParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizDeliveryOrderCert bizDeliveryOrderCert = bizDeliveryOrderCertDtoMapper.toPo(bizDeliveryOrderCertParam);
        bizDeliveryOrderCert.setTradeCode(userInfo.getCompany());
        Page<BizDeliveryOrderCert> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizDeliveryOrderCertMapper.getList(bizDeliveryOrderCert));
        List<BizDeliveryOrderCertDto> bizDeliveryOrderCertDtos = page.getResult().stream().map(head -> {
            BizDeliveryOrderCertDto dto = bizDeliveryOrderCertDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizDeliveryOrderCertDto>> paged = ResultObject.createInstance(bizDeliveryOrderCertDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizDeliveryOrderCertParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderCertDto insert(BizDeliveryOrderCertParam bizDeliveryOrderCertParam, UserInfoToken userInfo) {
        BizDeliveryOrderCert bizDeliveryOrderCert = bizDeliveryOrderCertDtoMapper.toPo(bizDeliveryOrderCertParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizDeliveryOrderCert.setSid(sid);
        bizDeliveryOrderCert.setCreateBy(userInfo.getUserNo());
        bizDeliveryOrderCert.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizDeliveryOrderCertMapper.insert(bizDeliveryOrderCert);
        return  insertStatus > 0 ? bizDeliveryOrderCertDtoMapper.toDto(bizDeliveryOrderCert) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizDeliveryOrderCertParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderCertDto update(BizDeliveryOrderCertParam bizDeliveryOrderCertParam, UserInfoToken userInfo) {
        BizDeliveryOrderCert bizDeliveryOrderCert = bizDeliveryOrderCertMapper.selectByPrimaryKey(bizDeliveryOrderCertParam.getSid());
        bizDeliveryOrderCertDtoMapper.updatePo(bizDeliveryOrderCertParam, bizDeliveryOrderCert);
        bizDeliveryOrderCert.setUpdateBy(userInfo.getUserNo());
        bizDeliveryOrderCert.setUpdateUserName(userInfo.getUserName());
        bizDeliveryOrderCert.setUpdateTime(new Date());
        // 更新数据
        int update = bizDeliveryOrderCertMapper.updateByPrimaryKey(bizDeliveryOrderCert);
        return update > 0 ? bizDeliveryOrderCertDtoMapper.toDto(bizDeliveryOrderCert) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizDeliveryOrderCertMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizDeliveryOrderCertDto> selectAll(BizDeliveryOrderCertParam exportParam, UserInfoToken userInfo) {
        BizDeliveryOrderCert bizDeliveryOrderCert = bizDeliveryOrderCertDtoMapper.toPo(exportParam);
         bizDeliveryOrderCert.setTradeCode(userInfo.getCompany());
        List<BizDeliveryOrderCertDto> bizDeliveryOrderCertDtos = new ArrayList<>();
        List<BizDeliveryOrderCert> bizDeliveryOrderCerts = bizDeliveryOrderCertMapper.getList(bizDeliveryOrderCert);
        if (CollectionUtils.isNotEmpty(bizDeliveryOrderCerts)) {
            bizDeliveryOrderCertDtos = bizDeliveryOrderCerts.stream().map(head -> {
                BizDeliveryOrderCertDto dto = bizDeliveryOrderCertDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizDeliveryOrderCertDtos;
    }
    public ResultObject<BizDeliveryOrderCertDto> getDeliveryOrderCertByHeadSid(BizDeliveryOrderCertParam param, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderCertDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizDeliveryOrderCert bizDeliveryOrderCert = bizDeliveryOrderCertMapper.getDeliveryOrderCertByHeadSid(param.getHeadId());
        if (bizDeliveryOrderCert != null) {
            BizDeliveryOrderCertDto dto = bizDeliveryOrderCertDtoMapper.toDto(bizDeliveryOrderCert);
            resultObject.setData(dto);
        }
        return resultObject;
    }
}
