<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderCertMapper">
    <resultMap id="bizDeliveryOrderCertResultMap" type="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="TRANSPORT_PERMIT" property="transportPermit" jdbcType="VARCHAR" />
		<result column="SHIPMENT_CONFIRM_DATE" property="shipmentConfirmDate" jdbcType="TIMESTAMP" />
		<result column="ENTRY_NO" property="entryNo" jdbcType="VARCHAR" />
		<result column="DECLARATION_DATE" property="declarationDate" jdbcType="TIMESTAMP" />
		<result column="RELEASE_DATE" property="releaseDate" jdbcType="TIMESTAMP" />
		<result column="SHIPPING_MARK" property="shippingMark" jdbcType="VARCHAR" />
		<result column="NOTE" property="note" jdbcType="VARCHAR" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="ANALYSIS_ID" property="analysisId" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,CREATE_BY
     ,CREATE_TIME
     ,CREATE_USER_NAME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,UPDATE_USER_NAME
     ,TRADE_CODE
     ,SYS_ORG_CODE
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
     ,BUSINESS_TYPE
     ,TRANSPORT_PERMIT
     ,SHIPMENT_CONFIRM_DATE
     ,ENTRY_NO
     ,DECLARATION_DATE
     ,RELEASE_DATE
     ,SHIPPING_MARK
     ,NOTE
     ,HEAD_ID
     ,ANALYSIS_ID
    </sql>
    <sql id="condition">
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizDeliveryOrderCertResultMap" parameterType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_DELIVERY_ORDER_CERT t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getDeliveryOrderCertByHeadSid" resultType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert">
        select <include refid="Base_Column_List"/> from T_BIZ_DELIVERY_ORDER_CERT t where t.HEAD_ID = #{headId};
    </select>
    <select id="getDeliveryOrderCertByHeadSids" resultType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert">
        select <include refid="Base_Column_List"/> from T_BIZ_DELIVERY_ORDER_CERT t where t.HEAD_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_DELIVERY_ORDER_CERT t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadSids">
        delete from T_BIZ_DELIVERY_ORDER_CERT t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
