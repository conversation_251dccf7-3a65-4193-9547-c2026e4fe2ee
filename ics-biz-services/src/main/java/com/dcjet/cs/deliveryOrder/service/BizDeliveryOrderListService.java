package com.dcjet.cs.deliveryOrder.service;
import com.aizuda.bpm.engine.assist.ObjectUtils;
import com.dcjet.cs.dec.dao.BizBpAnalyseOrderListMapper;
import com.dcjet.cs.dec.model.BizBpAnalyseOrderList;
import com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderHeadMapper;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderListMapper;
import com.dcjet.cs.deliveryOrder.mapper.BizDeliveryOrderListDtoMapper;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizDeliveryOrderListService extends BaseService<BizDeliveryOrderList> {
    @Resource
    private BizDeliveryOrderListMapper bizDeliveryOrderListMapper;
    @Resource
    private BizDeliveryOrderListDtoMapper bizDeliveryOrderListDtoMapper;
    @Override
    public Mapper<BizDeliveryOrderList> getMapper() {
        return bizDeliveryOrderListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizDeliveryOrderListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizDeliveryOrderListDto>> getListPaged(BizDeliveryOrderListParam bizDeliveryOrderListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizDeliveryOrderList bizDeliveryOrderList = bizDeliveryOrderListDtoMapper.toPo(bizDeliveryOrderListParam);
        bizDeliveryOrderList.setTradeCode(userInfo.getCompany());
        Page<BizDeliveryOrderList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizDeliveryOrderListMapper.getList(bizDeliveryOrderList));
        List<BizDeliveryOrderListDto> bizDeliveryOrderListDtos = page.getResult().stream().map(head -> {
            BizDeliveryOrderListDto dto = bizDeliveryOrderListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizDeliveryOrderListDto>> paged = ResultObject.createInstance(bizDeliveryOrderListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizDeliveryOrderListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderListDto insert(BizDeliveryOrderListParam bizDeliveryOrderListParam, UserInfoToken userInfo) {
        BizDeliveryOrderList bizDeliveryOrderList = bizDeliveryOrderListDtoMapper.toPo(bizDeliveryOrderListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizDeliveryOrderList.setSid(sid);
        bizDeliveryOrderList.setTradeCode(userInfo.getCompany());
        bizDeliveryOrderList.setCreateUserName(userInfo.getUserName());
        bizDeliveryOrderList.setCreateBy(userInfo.getUserNo());
        bizDeliveryOrderList.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizDeliveryOrderListMapper.insert(bizDeliveryOrderList);
        return  insertStatus > 0 ? bizDeliveryOrderListDtoMapper.toDto(bizDeliveryOrderList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizDeliveryOrderListParam
     * @param userInfo
     * @return
     */
    @Resource
    private BizBpAnalyseOrderListMapper bizBpAnalyseOrderListMapper;
    @Resource
    private BizDeliveryOrderHeadMapper bizDeliveryOrderHeadMapper;
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderListDto update(BizDeliveryOrderListParam bizDeliveryOrderListParam, UserInfoToken userInfo) {
        BizDeliveryOrderList bizDeliveryOrderList = bizDeliveryOrderListMapper.selectByPrimaryKey(bizDeliveryOrderListParam.getSid());
//        BizDeliveryOrderHead headOld = bizDeliveryOrderHeadMapper.selectByPrimaryKey(bizDeliveryOrderList.getHeadId());

        if(null != bizDeliveryOrderListParam.getQty() && bizDeliveryOrderListParam.getQty().compareTo(bizDeliveryOrderList.getQty()) != 0){
            SevenForeignContractList foreignContractListQty = bizDeliveryOrderListMapper.getForeignContractListQty(bizDeliveryOrderList.getContractListId());
            if((ObjectUtils.isEmpty(foreignContractListQty) && bizDeliveryOrderListParam.getQty().compareTo(bizDeliveryOrderList.getQty()) > 0 )
                    || (!ObjectUtils.isEmpty(foreignContractListQty) && bizDeliveryOrderListParam.getQty().compareTo(foreignContractListQty.getQtya().add(bizDeliveryOrderList.getQty())) > 0)){
                throw new ErrorException(400,"出货单表体数量超过外商合同表体数量！");
            }
//            if(StringUtils.isNotBlank(bizDeliveryOrderList.getAnalyseListId()) && bizDeliveryOrderListParam.getQty().compareTo(bizDeliveryOrderList.getQty()) != 0){
//                BizBpAnalyseOrderList bizBpAnalyseOrderList = bizBpAnalyseOrderListMapper.selectByPrimaryKey(bizDeliveryOrderList.getAnalyseListId());
//                if(ObjectUtils.isNotEmpty(bizBpAnalyseOrderList)){
//                    BigDecimal listGrossWt = bizDeliveryOrderListParam.getQty().divide(bizBpAnalyseOrderList.getQuantity(), 6, RoundingMode.HALF_UP)
//                            .multiply(bizBpAnalyseOrderList.getGrossWeight()).setScale(4, RoundingMode.HALF_UP);
//                    if(listGrossWt.compareTo(bizDeliveryOrderList.getGrossWeight()) != 0){
//                        bizDeliveryOrderListParam.setGrossWeight(listGrossWt);
//                        head.setTotalGrossWt(headOld.getTotalGrossWt().subtract(bizDeliveryOrderList.getGrossWeight()).add(listGrossWt));
//                    }
//                    BigDecimal listNetWt = bizDeliveryOrderListParam.getQty().divide(bizBpAnalyseOrderList.getQuantity(), 6, RoundingMode.HALF_UP)
//                            .multiply(bizBpAnalyseOrderList.getNetWt()).setScale(4, RoundingMode.HALF_UP);
//                    if(listNetWt.compareTo(bizDeliveryOrderList.getNetWeight()) != 0){
//                        bizDeliveryOrderListParam.setNetWeight(listNetWt);
//                        head.setTotalNetWt(headOld.getTotalNetWt().subtract(bizDeliveryOrderList.getNetWeight()).add(listNetWt));
//                    }
//                }
//            }
        }
        bizDeliveryOrderListDtoMapper.updatePo(bizDeliveryOrderListParam, bizDeliveryOrderList);
        if(null != bizDeliveryOrderList.getQty() && null != bizDeliveryOrderList.getUnitPrice()){
            bizDeliveryOrderList.setAmount(bizDeliveryOrderList.getQty().multiply(bizDeliveryOrderList.getUnitPrice()).setScale(2, RoundingMode.HALF_UP));
        }
        bizDeliveryOrderList.setUpdateBy(userInfo.getUserNo());
        bizDeliveryOrderList.setUpdateUserName(userInfo.getUserName());
        bizDeliveryOrderList.setUpdateTime(new Date());
        // 更新数据
        int update = bizDeliveryOrderListMapper.updateByPrimaryKey(bizDeliveryOrderList);
        if(update > 0){
            List<BizDeliveryOrderList> listByHeadId = bizDeliveryOrderListMapper.getListByHeadId(bizDeliveryOrderList.getHeadId());
            BizDeliveryOrderHead head = new BizDeliveryOrderHead();
            head.setSid(bizDeliveryOrderList.getHeadId());
            head.setTotalAmount(listByHeadId.stream().map(BizDeliveryOrderList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            bizDeliveryOrderHeadMapper.updateByPrimaryKeySelective(head);
        }

        return update > 0 ? bizDeliveryOrderListDtoMapper.toDto(bizDeliveryOrderList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizDeliveryOrderListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizDeliveryOrderListDto> selectAll(BizDeliveryOrderListParam exportParam, UserInfoToken userInfo) {
        BizDeliveryOrderList bizDeliveryOrderList = bizDeliveryOrderListDtoMapper.toPo(exportParam);
         bizDeliveryOrderList.setTradeCode(userInfo.getCompany());
        List<BizDeliveryOrderListDto> bizDeliveryOrderListDtos = new ArrayList<>();
        List<BizDeliveryOrderList> bizDeliveryOrderLists = bizDeliveryOrderListMapper.getList(bizDeliveryOrderList);
        if (CollectionUtils.isNotEmpty(bizDeliveryOrderLists)) {
            bizDeliveryOrderListDtos = bizDeliveryOrderLists.stream().map(head -> {
                BizDeliveryOrderListDto dto = bizDeliveryOrderListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizDeliveryOrderListDtos;
    }
    public ResultObject<BizDeliveryOrderListDto> getDeliveryOrderListBySid(BizDeliveryOrderListParam param, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderListDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizDeliveryOrderList bizDeliveryOrderList = bizDeliveryOrderListMapper.selectByPrimaryKey(param.getSid());
        if (bizDeliveryOrderList != null) {
            BizDeliveryOrderListDto dto = bizDeliveryOrderListDtoMapper.toDto(bizDeliveryOrderList);
            resultObject.setData(dto);
        }
        return resultObject;
    }
}
