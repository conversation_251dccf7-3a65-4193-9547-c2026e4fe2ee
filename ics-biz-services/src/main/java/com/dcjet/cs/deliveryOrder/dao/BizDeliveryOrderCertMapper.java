package com.dcjet.cs.deliveryOrder.dao;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizDeliveryOrderCert
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizDeliveryOrderCertMapper extends Mapper<BizDeliveryOrderCert> {
    /**
     * 查询获取数据
     * @param bizDeliveryOrderCert
     * @return
     */
    List<BizDeliveryOrderCert> getList(BizDeliveryOrderCert bizDeliveryOrderCert);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    BizDeliveryOrderCert getDeliveryOrderCertByHeadSid(String headId);
    List<BizDeliveryOrderCert> getDeliveryOrderCertByHeadSids(List<String> sids);
    void deleteByHeadSids(List<String> sids);
}
