package com.dcjet.cs.deliveryOrder.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter
@Getter
@Table(name = "T_BIZ_DELIVERY_ORDER_HEAD")
public class BizDeliveryOrderHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "CREATE_USER_NAME")
	private  String createUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "EXTEND10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
     * 合同号
     */
	@Column(name = "CONTRACT_NO")
	private  String contractNo;
	/**
     * 出货单号
     */
	@Column(name = "PURCHASE_ORDER_NO")
	private  String purchaseOrderNo;
	/**
     * 供应商
     */
	@Column(name = "SUPPLIER")
	private  String supplier;
	/**
     * 客户
     */
	@Column(name = "CUSTOMER")
	private  String customer;
	/**
     * 客户地址
     */
	@Column(name = "CUSTOMER_ADDRESS")
	private  String customerAddress;
	/**
     * 贸易国别
     */
	@Column(name = "TRADE_COUNTRY")
	private  String tradeCountry;
	/**
     * 经营单位
     */
	@Column(name = "BUSINESS_ENTERPRISE")
	private  String businessEnterprise;
	/**
     * 目的地/港
     */
	@Column(name = "PORT_OF_DESTINATION")
	private  String portOfDestination;
	/**
     * 付款方式
     */
	@Column(name = "PAYMENT_METHOD")
	private  String paymentMethod;
	/**
     * 币种
     */
	@Column(name = "CURR")
	private  String curr;
	/**
     * 总金额
     */
	@Column(name = "TOTAL_AMOUNT")
	private  BigDecimal totalAmount;
	/**
     * 运输方式
     */
	@Column(name = "TRANSPORT_MODE")
	private  String transportMode;
	/**
     * 价格条款
     */
	@Column(name = "PRICE_TERM")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@Column(name = "PRICE_TERM_PORT")
	private  String priceTermPort;
	/**
     * 发货单位
     */
	@Column(name = "DELIVERY_ENTERPRISE")
	private  String deliveryEnterprise;
	/**
     * 包装种类
     */
	@Column(name = "WRAP_TYPE")
	private  String wrapType;
	/**
     * 包装数量
     */
	@Column(name = "PACK_NUM")
	private  BigDecimal packNum;
	/**
     * 发货单位所在地
     */
	@Column(name = "DELIVERY_ENTERPRISE_ADDRESS")
	private  String deliveryEnterpriseAddress;
	/**
     * 总毛重
     */
	@Column(name = "TOTAL_NET_WT")
	private  BigDecimal totalNetWt;
	/**
     * 总净重
     */
	@Column(name = "TOTAL_GROSS_WT")
	private  BigDecimal totalGrossWt;
	/**
     * 总皮重
     */
	@Column(name = "TOTAL_TARE")
	private  BigDecimal totalTare;
	/**
     * 发送报关
     */
	@Column(name = "SEND_DECLARE")
	private  String sendDeclare;
	/**
     * 业务日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "BUSINESS_DATE")
	private  Date businessDate;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CONFIRM_TIME")
	private  Date confirmTime;
	/**
     * 是否确认
     */
	@Column(name = "IS_CONFIRM")
	private  String isConfirm;
	/**
     * 是否保存
     */
	@Column(name = "IS_SAVE")
	private  String isSave;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 单据状态
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 审核状态
     */
	@Column(name = "APPR_STATUS")
	private  String apprStatus;
	/**
     * 发送财务系统
     */
	@Column(name = "SEND_FINANCE")
	private  String sendFinance;
	/**
     * 是否红冲
     */
	@Column(name = "RED_FLUSH")
	private  String redFlush;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "PURCHASE_MARK")
	private  String purchaseMark;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "PURCHASE_NO_MARK")
	private  String purchaseNoMark;

	@Transient
	private String insertTimeFrom;
	@Transient
	private String insertTimeTo;

	@Transient
	private String createrBy;

	@Transient
	private String createrUserName;

	@Transient
	private Date createrTime;
	@Transient
	private String destinationDateFrom;
	@Transient
	private String destinationDateTo;
	@Transient
	private String entryNo;
	@Transient
	private Date declarationDate;
	@Transient
	private String headId;
	@Transient
	private String notifyParty;
	@Transient
	private String shipper;
	@Transient
	private String consignee;
	@Transient
	private String freight;
	@Transient
	private String qtyTotalStr;
	@Transient
	private String totalLwhStr;
	@Transient
	private String totalGrossWtStr;
	@Transient
	private String totalNetWtStr;
	@Transient
	private String destinationDateStr;
	@Transient
	private String portOfShipmentStr;
	@Transient
	private String portOfDestinationStr;
	@Transient
	private String warehouseAddressStr;
	@Transient
	private String contactPersonStr;
	@Transient
	private String totalAmountStr;
}
