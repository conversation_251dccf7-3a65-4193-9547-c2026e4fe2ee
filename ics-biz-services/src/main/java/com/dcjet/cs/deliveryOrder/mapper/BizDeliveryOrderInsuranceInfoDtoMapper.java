package com.dcjet.cs.deliveryOrder.mapper;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderInsuranceInfo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizDeliveryOrderInsuranceInfoDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizDeliveryOrderInsuranceInfoDto toDto(BizDeliveryOrderInsuranceInfo po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizDeliveryOrderInsuranceInfo toPo(BizDeliveryOrderInsuranceInfoParam param);
    /**
     * 数据库原始数据更新
     * @param bizDeliveryOrderInsuranceInfoParam
     * @param bizDeliveryOrderInsuranceInfo
     */
    void updatePo(BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, @MappingTarget BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo);
    default void patchPo(BizDeliveryOrderInsuranceInfoParam bizDeliveryOrderInsuranceInfoParam, BizDeliveryOrderInsuranceInfo bizDeliveryOrderInsuranceInfo) {
        // TODO 自行实现局部更新
    }
}
