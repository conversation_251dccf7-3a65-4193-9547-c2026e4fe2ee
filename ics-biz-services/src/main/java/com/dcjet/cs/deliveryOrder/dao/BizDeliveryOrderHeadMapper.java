package com.dcjet.cs.deliveryOrder.dao;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizDeliveryOrderHead
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizDeliveryOrderHeadMapper extends Mapper<BizDeliveryOrderHead> {
    /**
     * 查询获取数据
     * @param bizDeliveryOrderHead
     * @return
     */
    List<BizDeliveryOrderHead> getList(BizDeliveryOrderHead bizDeliveryOrderHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getSerilNo(String contractNo);
    List<SevenForeignContractHead> getForeignContractHeadListQty(BizDeliveryOrderHead bizDeliveryOrderHead);
    List<SevenForeignContractList> getForeignContractListQty(String headId);

    int checkKey(BizDeliveryOrderHead bizDeliveryOrderHead);
    List<Map<String, String>> getOrderSupplierList(String company);

    List<Map<String, String>> getCreateUserList(String company);
    List<Map<String, String>> getPortList(@Param("tradeCode") String tradeCode);
    List<Map<String, String>> getCountryList(@Param("tradeCode") String tradeCode);
}
