package com.dcjet.cs.deliveryOrder.service;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderShipmentsMapper;
import com.dcjet.cs.deliveryOrder.mapper.BizDeliveryOrderShipmentsDtoMapper;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizDeliveryOrderShipmentsService extends BaseService<BizDeliveryOrderShipments> {
    @Resource
    private BizDeliveryOrderShipmentsMapper bizDeliveryOrderShipmentsMapper;
    @Resource
    private BizDeliveryOrderShipmentsDtoMapper bizDeliveryOrderShipmentsDtoMapper;
    @Override
    public Mapper<BizDeliveryOrderShipments> getMapper() {
        return bizDeliveryOrderShipmentsMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizDeliveryOrderShipmentsParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizDeliveryOrderShipmentsDto>> getListPaged(BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizDeliveryOrderShipments bizDeliveryOrderShipments = bizDeliveryOrderShipmentsDtoMapper.toPo(bizDeliveryOrderShipmentsParam);
        bizDeliveryOrderShipments.setTradeCode(userInfo.getCompany());
        Page<BizDeliveryOrderShipments> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizDeliveryOrderShipmentsMapper.getList(bizDeliveryOrderShipments));
        List<BizDeliveryOrderShipmentsDto> bizDeliveryOrderShipmentsDtos = page.getResult().stream().map(head -> {
            BizDeliveryOrderShipmentsDto dto = bizDeliveryOrderShipmentsDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizDeliveryOrderShipmentsDto>> paged = ResultObject.createInstance(bizDeliveryOrderShipmentsDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizDeliveryOrderShipmentsParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderShipmentsDto insert(BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, UserInfoToken userInfo) {
        BizDeliveryOrderShipments bizDeliveryOrderShipments = bizDeliveryOrderShipmentsDtoMapper.toPo(bizDeliveryOrderShipmentsParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizDeliveryOrderShipments.setSid(sid);
        bizDeliveryOrderShipments.setCreateUserName(userInfo.getUserName());
        bizDeliveryOrderShipments.setCreateBy(userInfo.getUserNo());
        bizDeliveryOrderShipments.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizDeliveryOrderShipmentsMapper.insert(bizDeliveryOrderShipments);
        return  insertStatus > 0 ? bizDeliveryOrderShipmentsDtoMapper.toDto(bizDeliveryOrderShipments) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizDeliveryOrderShipmentsParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderShipmentsDto update(BizDeliveryOrderShipmentsParam bizDeliveryOrderShipmentsParam, UserInfoToken userInfo) {
        BizDeliveryOrderShipments bizDeliveryOrderShipments = bizDeliveryOrderShipmentsMapper.selectByPrimaryKey(bizDeliveryOrderShipmentsParam.getSid());
        bizDeliveryOrderShipmentsDtoMapper.updatePo(bizDeliveryOrderShipmentsParam, bizDeliveryOrderShipments);
        bizDeliveryOrderShipments.setUpdateBy(userInfo.getUserNo());
        bizDeliveryOrderShipments.setUpdateUserName(userInfo.getUserName());
        bizDeliveryOrderShipments.setUpdateTime(new Date());
        // 更新数据
        int update = bizDeliveryOrderShipmentsMapper.updateByPrimaryKey(bizDeliveryOrderShipments);
        return update > 0 ? bizDeliveryOrderShipmentsDtoMapper.toDto(bizDeliveryOrderShipments) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizDeliveryOrderShipmentsMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizDeliveryOrderShipmentsDto> selectAll(BizDeliveryOrderShipmentsParam exportParam, UserInfoToken userInfo) {
        BizDeliveryOrderShipments bizDeliveryOrderShipments = bizDeliveryOrderShipmentsDtoMapper.toPo(exportParam);
         bizDeliveryOrderShipments.setTradeCode(userInfo.getCompany());
        List<BizDeliveryOrderShipmentsDto> bizDeliveryOrderShipmentsDtos = new ArrayList<>();
        List<BizDeliveryOrderShipments> bizDeliveryOrderShipmentss = bizDeliveryOrderShipmentsMapper.getList(bizDeliveryOrderShipments);
        if (CollectionUtils.isNotEmpty(bizDeliveryOrderShipmentss)) {
            bizDeliveryOrderShipmentsDtos = bizDeliveryOrderShipmentss.stream().map(head -> {
                BizDeliveryOrderShipmentsDto dto = bizDeliveryOrderShipmentsDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizDeliveryOrderShipmentsDtos;
    }
    public ResultObject<BizDeliveryOrderShipmentsDto> getDeliveryOrderShipmentsByHeadSid(BizDeliveryOrderShipmentsParam param, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderShipmentsDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizDeliveryOrderShipments bizDeliveryOrderShipments = bizDeliveryOrderShipmentsMapper.getDeliveryOrderShipmentsByHeadSid(param.getHeadId());
        if (bizDeliveryOrderShipments != null) {
            BizDeliveryOrderShipmentsDto dto = bizDeliveryOrderShipmentsDtoMapper.toDto(bizDeliveryOrderShipments);
            resultObject.setData(dto);
        }
        return resultObject;
    }
}
