package com.dcjet.cs.deliveryOrder.mapper;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderCert;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizDeliveryOrderCertDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizDeliveryOrderCertDto toDto(BizDeliveryOrderCert po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizDeliveryOrderCert toPo(BizDeliveryOrderCertParam param);
    /**
     * 数据库原始数据更新
     * @param bizDeliveryOrderCertParam
     * @param bizDeliveryOrderCert
     */
    void updatePo(BizDeliveryOrderCertParam bizDeliveryOrderCertParam, @MappingTarget BizDeliveryOrderCert bizDeliveryOrderCert);
    default void patchPo(BizDeliveryOrderCertParam bizDeliveryOrderCertParam, BizDeliveryOrderCert bizDeliveryOrderCert) {
        // TODO 自行实现局部更新
    }
}
