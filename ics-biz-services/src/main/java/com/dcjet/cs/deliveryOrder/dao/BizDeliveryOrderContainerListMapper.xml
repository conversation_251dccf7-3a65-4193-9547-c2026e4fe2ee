<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderContainerListMapper">
    <resultMap id="bizDeliveryOrderContainerListResultMap" type="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderContainerList">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="BOX_NO_START" property="boxNoStart" jdbcType="VARCHAR" />
		<result column="BOX_NO_END" property="boxNoEnd" jdbcType="VARCHAR" />
		<result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR" />
		<result column="PACKAGE_STYLE" property="packageStyle" jdbcType="VARCHAR" />
		<result column="GROSS_WT" property="grossWt" jdbcType="NUMERIC" />
		<result column="NET_WT" property="netWt" jdbcType="NUMERIC" />
		<result column="TARE_WT" property="tareWt" jdbcType="NUMERIC" />
		<result column="LONGER" property="longer" jdbcType="NUMERIC" />
		<result column="WHITHER" property="whither" jdbcType="NUMERIC" />
		<result column="HIGHER" property="higher" jdbcType="NUMERIC" />
		<result column="NOTE" property="note" jdbcType="VARCHAR" />
		<result column="CONTAINER_NUM" property="containerNum" jdbcType="NUMERIC" />
		<result column="QTY" property="qty" jdbcType="NUMERIC" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="CONTRACT_LIST_ID" property="contractListId" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,CREATE_BY
     ,CREATE_TIME
     ,CREATE_USER_NAME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,UPDATE_USER_NAME
     ,TRADE_CODE
     ,SYS_ORG_CODE
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
     ,BUSINESS_TYPE
     ,BOX_NO_START
     ,BOX_NO_END
     ,PRODUCT_NAME
     ,PACKAGE_STYLE
     ,GROSS_WT
     ,NET_WT
     ,TARE_WT
     ,LONGER
     ,WHITHER
     ,HIGHER
     ,NOTE
     ,CONTAINER_NUM
     ,HEAD_ID
     ,CONTRACT_LIST_ID
     ,QTY
    </sql>
    <sql id="condition">
        and HEAD_ID = #{headId}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizDeliveryOrderContainerListResultMap" parameterType="com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderContainerList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_DELIVERY_ORDER_CONTAINER_LIST t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.update_time,t.create_time) desc
    </select>
    <select id="getListByHeadId" resultMap="bizDeliveryOrderContainerListResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_DELIVERY_ORDER_CONTAINER_LIST t
        where HEAD_ID = #{headId}
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_DELIVERY_ORDER_CONTAINER_LIST t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadSids">
        delete from T_BIZ_DELIVERY_ORDER_CONTAINER_LIST t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
