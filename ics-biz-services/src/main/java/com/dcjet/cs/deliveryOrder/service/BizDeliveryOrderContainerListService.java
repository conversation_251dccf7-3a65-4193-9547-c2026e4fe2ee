package com.dcjet.cs.deliveryOrder.service;
import com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderHeadMapper;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderList;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.deliveryOrder.dao.BizDeliveryOrderContainerListMapper;
import com.dcjet.cs.deliveryOrder.mapper.BizDeliveryOrderContainerListDtoMapper;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderContainerList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizDeliveryOrderContainerListService extends BaseService<BizDeliveryOrderContainerList> {
    @Resource
    private BizDeliveryOrderContainerListMapper bizDeliveryOrderContainerListMapper;
    @Resource
    private BizDeliveryOrderContainerListDtoMapper bizDeliveryOrderContainerListDtoMapper;
    @Override
    public Mapper<BizDeliveryOrderContainerList> getMapper() {
        return bizDeliveryOrderContainerListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizDeliveryOrderContainerListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizDeliveryOrderContainerListDto>> getListPaged(BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizDeliveryOrderContainerList bizDeliveryOrderContainerList = bizDeliveryOrderContainerListDtoMapper.toPo(bizDeliveryOrderContainerListParam);
        bizDeliveryOrderContainerList.setTradeCode(userInfo.getCompany());
        Page<BizDeliveryOrderContainerList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizDeliveryOrderContainerListMapper.getList(bizDeliveryOrderContainerList));
        List<BizDeliveryOrderContainerListDto> bizDeliveryOrderContainerListDtos = page.getResult().stream().map(head -> {
            BizDeliveryOrderContainerListDto dto = bizDeliveryOrderContainerListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizDeliveryOrderContainerListDto>> paged = ResultObject.createInstance(bizDeliveryOrderContainerListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizDeliveryOrderContainerListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderContainerListDto insert(BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, UserInfoToken userInfo) {
        BizDeliveryOrderContainerList bizDeliveryOrderContainerList = bizDeliveryOrderContainerListDtoMapper.toPo(bizDeliveryOrderContainerListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizDeliveryOrderContainerList.setSid(sid);
        bizDeliveryOrderContainerList.setCreateUserName(userInfo.getUserName());
        bizDeliveryOrderContainerList.setCreateBy(userInfo.getUserNo());
        bizDeliveryOrderContainerList.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizDeliveryOrderContainerListMapper.insert(bizDeliveryOrderContainerList);
        return  insertStatus > 0 ? bizDeliveryOrderContainerListDtoMapper.toDto(bizDeliveryOrderContainerList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizDeliveryOrderContainerListParam
     * @param userInfo
     * @return
     */
    @Resource
    private BizDeliveryOrderHeadMapper bizDeliveryOrderHeadMapper;
    @Transactional(rollbackFor = Exception.class)
    public BizDeliveryOrderContainerListDto update(BizDeliveryOrderContainerListParam bizDeliveryOrderContainerListParam, UserInfoToken userInfo) {
        BizDeliveryOrderContainerList bizDeliveryOrderContainerList = bizDeliveryOrderContainerListMapper.selectByPrimaryKey(bizDeliveryOrderContainerListParam.getSid());
        bizDeliveryOrderContainerListDtoMapper.updatePo(bizDeliveryOrderContainerListParam, bizDeliveryOrderContainerList);
        if(null != bizDeliveryOrderContainerList.getGrossWt() && null != bizDeliveryOrderContainerList.getNetWt()){
            bizDeliveryOrderContainerList.setTareWt(bizDeliveryOrderContainerList.getGrossWt().subtract(bizDeliveryOrderContainerList.getNetWt()));
        }
        bizDeliveryOrderContainerList.setUpdateBy(userInfo.getUserNo());
        bizDeliveryOrderContainerList.setUpdateUserName(userInfo.getUserName());
        bizDeliveryOrderContainerList.setUpdateTime(new Date());
        // 更新数据
        int update = bizDeliveryOrderContainerListMapper.updateByPrimaryKey(bizDeliveryOrderContainerList);
        if(update > 0){
            List<BizDeliveryOrderContainerList> listByHeadId = bizDeliveryOrderContainerListMapper.getListByHeadId(bizDeliveryOrderContainerList.getHeadId());
            BizDeliveryOrderHead head = new BizDeliveryOrderHead();
            head.setSid(bizDeliveryOrderContainerList.getHeadId());
            head.setTotalGrossWt(listByHeadId.stream().map(BizDeliveryOrderContainerList::getGrossWt).reduce(BigDecimal.ZERO, BigDecimal::add));
            head.setTotalNetWt(listByHeadId.stream().map(BizDeliveryOrderContainerList::getNetWt).reduce(BigDecimal.ZERO, BigDecimal::add));
            head.setTotalTare(head.getTotalGrossWt().subtract(head.getTotalNetWt()));
            bizDeliveryOrderHeadMapper.updateByPrimaryKeySelective(head);
        }
        return update > 0 ? bizDeliveryOrderContainerListDtoMapper.toDto(bizDeliveryOrderContainerList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizDeliveryOrderContainerListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizDeliveryOrderContainerListDto> selectAll(BizDeliveryOrderContainerListParam exportParam, UserInfoToken userInfo) {
        BizDeliveryOrderContainerList bizDeliveryOrderContainerList = bizDeliveryOrderContainerListDtoMapper.toPo(exportParam);
         bizDeliveryOrderContainerList.setTradeCode(userInfo.getCompany());
        List<BizDeliveryOrderContainerListDto> bizDeliveryOrderContainerListDtos = new ArrayList<>();
        List<BizDeliveryOrderContainerList> bizDeliveryOrderContainerLists = bizDeliveryOrderContainerListMapper.getList(bizDeliveryOrderContainerList);
        if (CollectionUtils.isNotEmpty(bizDeliveryOrderContainerLists)) {
            bizDeliveryOrderContainerListDtos = bizDeliveryOrderContainerLists.stream().map(head -> {
                BizDeliveryOrderContainerListDto dto = bizDeliveryOrderContainerListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizDeliveryOrderContainerListDtos;
    }
    public ResultObject<BizDeliveryOrderContainerListDto> getDeliveryOrderCListBySid(BizDeliveryOrderContainerListParam param, UserInfoToken userInfo) {
        ResultObject<BizDeliveryOrderContainerListDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizDeliveryOrderContainerList bizDeliveryOrderContainerList = bizDeliveryOrderContainerListMapper.selectByPrimaryKey(param.getSid());
        if (bizDeliveryOrderContainerList != null) {
            BizDeliveryOrderContainerListDto dto = bizDeliveryOrderContainerListDtoMapper.toDto(bizDeliveryOrderContainerList);
            resultObject.setData(dto);
        }
        return resultObject;
    }
}
