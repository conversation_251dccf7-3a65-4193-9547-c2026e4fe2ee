package com.dcjet.cs.deliveryOrder.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter
@Getter
@Table(name = "T_BIZ_DELIVERY_ORDER_LIST")
public class BizDeliveryOrderList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "CREATE_USER_NAME")
	private  String createUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "EXTEND10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
     * 商品名称
     */
	@Column(name = "PRODUCT_NAME")
	private  String productName;
	/**
     * 产品型号
     */
	@Column(name = "PRODUCT_MODEL")
	private  String productModel;
	/**
     * 单位
     */
	@Column(name = "UNIT")
	private  String unit;
	/**
     * 数量
     */
	@Column(name = "QTY")
	private  BigDecimal qty;
	/**
     * 单价
     */
	@Column(name = "UNIT_PRICE")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Column(name = "AMOUNT")
	private  BigDecimal amount;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 箱数
     */
	@Column(name = "CONTAINER_NUM")
	private  BigDecimal containerNum;
	/**
     * 表头id
     */
	@Column(name = "HEAD_ID")
	private  String headId;
	/**
     * 外商合同表体id
     */
	@Column(name = "CONTRACT_LIST_ID")
	private  String contractListId;
	@Column(name = "ANALYSE_LIST_ID")
	private  String analyseListId;
	@Column(name = "GROSS_WEIGHT")
	private  BigDecimal grossWeight;
	@Column(name = "NET_WEIGHT")
	private  BigDecimal netWeight;
}
