package com.dcjet.cs.deliveryOrder.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter
@Getter
@Table(name = "T_BIZ_DELIVERY_ORDER_INSURANCE_INFO")
public class BizDeliveryOrderInsuranceInfo implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "CREATE_USER_NAME")
	private  String createUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "EXTEND10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
     * 编号
     */
	@Column(name = "PURCHASE_ORDER_NO")
	private  String purchaseOrderNo;
	/**
     * 保险公司
     */
	@Column(name = "INSURANCE_COMPANY")
	private  String insuranceCompany;
	/**
     * 被保险人
     */
	@Column(name = "INSURANT")
	private  String insurant;
	/**
     * 发票抬头
     */
	@Column(name = "INVOICE_TITLE")
	private  String invoiceTitle;
	/**
     * 运输工具名称
     */
	@Column(name = "TRAF_NAME")
	private  String trafName;
	/**
     * 开航日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "START_SHIPMENT_DATE")
	private  Date startShipmentDate;
	/**
     * 运输路线自
     */
	@Column(name = "TRAF_ROUTE_FROM")
	private  String trafRouteFrom;
	/**
     * 经
     */
	@Column(name = "TRAF_ROUTE_PASS")
	private  String trafRoutePass;
	/**
     * 至
     */
	@Column(name = "TRAF_ROUTE_TO")
	private  String trafRouteTo;
	/**
     * 投保险别
     */
	@Column(name = "INSURANCE_TYPE")
	private  String insuranceType;
	/**
     * 币种
     */
	@Column(name = "CURR")
	private  String curr;
	/**
     * 投保加成%
     */
	@Column(name = "INSURANCE_MARKUP")
	private  BigDecimal insuranceMarkup;
	/**
     * 保险金额
     */
	@Column(name = "INSURANCE_AMOUNT")
	private  BigDecimal insuranceAmount;
	/**
     * 保险费率
     */
	@Column(name = "INSURANCE_RATE")
	private  BigDecimal insuranceRate;
	/**
     * 保费
     */
	@Column(name = "INSURANCE_FEE")
	private  BigDecimal insuranceFee;
	/**
     * 投保日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSURANCE_DATE")
	private  Date insuranceDate;
	/**
     * 运费
     */
	@Column(name = "FREIGHT_FEE")
	private  BigDecimal freightFee;
	/**
     * 运费币种
     */
	@Column(name = "FREIGHT_CURR")
	private  String freightCurr;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 表头id
     */
	@Column(name = "HEAD_ID")
	private  String headId;
	/**
     * 分析单id
     */
	@Column(name = "ANALYSIS_ID")
	private  String analysisId;
	@Transient
	private  String amountCurrStr;
	@Transient
	private  String invoiceTitleStr;
	@Transient
	private  String startShipmentDateStr;
	@Transient
	private  String insurantStr;
	@Transient
	private  String qtyStr;
	@Transient
	private  String productNameStr;
	@Transient
	private  String contractNo;
}
