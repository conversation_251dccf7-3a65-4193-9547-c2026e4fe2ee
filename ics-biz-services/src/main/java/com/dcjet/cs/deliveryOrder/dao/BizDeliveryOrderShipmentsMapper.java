package com.dcjet.cs.deliveryOrder.dao;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderShipments;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizDeliveryOrderShipments
* <AUTHOR>
* @date: 2025-7-7
*/
public interface BizDeliveryOrderShipmentsMapper extends Mapper<BizDeliveryOrderShipments> {
    /**
     * 查询获取数据
     * @param bizDeliveryOrderShipments
     * @return
     */
    List<BizDeliveryOrderShipments> getList(BizDeliveryOrderShipments bizDeliveryOrderShipments);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    BizDeliveryOrderShipments getDeliveryOrderShipmentsByHeadSid(String headId);
    List<BizDeliveryOrderShipments> getDeliveryOrderShipmentsByHeadSids(List<String> sids);
    void deleteByHeadSids(List<String> sids);
}
