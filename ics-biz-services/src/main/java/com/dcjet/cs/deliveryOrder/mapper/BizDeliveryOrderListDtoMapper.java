package com.dcjet.cs.deliveryOrder.mapper;
import com.dcjet.cs.dto.deliveryOrder.*;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizDeliveryOrderListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizDeliveryOrderListDto toDto(BizDeliveryOrderList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizDeliveryOrderList toPo(BizDeliveryOrderListParam param);
    /**
     * 数据库原始数据更新
     * @param bizDeliveryOrderListParam
     * @param bizDeliveryOrderList
     */
    void updatePo(BizDeliveryOrderListParam bizDeliveryOrderListParam, @MappingTarget BizDeliveryOrderList bizDeliveryOrderList);
    default void patchPo(BizDeliveryOrderListParam bizDeliveryOrderListParam, BizDeliveryOrderList bizDeliveryOrderList) {
        // TODO 自行实现局部更新
    }
}
