package com.dcjet.cs.exportAuxMatAnnualPlan.mapper;

import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanDto;
import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanParam;
import com.dcjet.cs.exportAuxMatAnnualPlan.model.ExportAuxMatAnnualPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExportAuxMatAnnualPlanDtoMapper {
    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    ExportAuxMatAnnualPlanDto toDto(ExportAuxMatAnnualPlan po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    ExportAuxMatAnnualPlan toPo(ExportAuxMatAnnualPlanParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(ExportAuxMatAnnualPlanParam param, @MappingTarget ExportAuxMatAnnualPlan po);
}