package com.dcjet.cs.exportAuxMatAnnualPlan.service;

import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanDto;
import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanOptionsDto;
import com.dcjet.cs.dto.exportAuxMatAnnualPlan.ExportAuxMatAnnualPlanParam;
import com.dcjet.cs.exportAuxMatAnnualPlan.dao.ExportAuxMatAnnualPlanMapper;
import com.dcjet.cs.exportAuxMatAnnualPlan.mapper.ExportAuxMatAnnualPlanDtoMapper;
import com.dcjet.cs.exportAuxMatAnnualPlan.model.ExportAuxMatAnnualPlan;
import com.dcjet.cs.params.dao.ProductTypeMapper;
import com.dcjet.cs.params.model.ProductType;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExportAuxMatAnnualPlanService extends BaseService<ExportAuxMatAnnualPlan> {
    private final static String VALUE = "value", LABEL = "label";
    private final static String BUSINESS_TYPE_NO = CommonEnum.COMMON_BUSINESS_TYPE_ENUM.EXPORT_ACCESSORIES.getType();

    @Resource
    private ExportAuxMatAnnualPlanMapper exportAuxMatAnnualPlanMapper;
    @Resource
    private ExportAuxMatAnnualPlanDtoMapper exportAuxMatAnnualPlanDtoMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private ProductTypeMapper productTypeMapper;

    @Override
    public Mapper<ExportAuxMatAnnualPlan> getMapper() {
        return this.exportAuxMatAnnualPlanMapper;
    }

    /**
     * 选项
     *
     * @param userInfo 用户选项
     * @return 选项传输数据
     */
    public ExportAuxMatAnnualPlanOptionsDto options(UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        ExportAuxMatAnnualPlanOptionsDto optionsDto = new ExportAuxMatAnnualPlanOptionsDto();
        // 客户
        List<BizMerchant> merchants = this.bizMerchantMapper.provideForOptions(BUSINESS_TYPE_NO, tradeCode);
        if (CollectionUtils.isNotEmpty(merchants)) {
            List<Map<String, String>> customerOptions = merchants.stream()
                    .filter(merchant -> CommonEnum.MERCHANT_TYPE.CUSTOMER.getValue()
                            .equals(merchant.getMerchantType()))
                    .map(merchant -> {
                        Map<String, String> map = new HashMap<>(2);
                        map.put(VALUE, merchant.getMerchantCode());
                        map.put(LABEL, merchant.getMerchantNameCn());
                        return map;
                    }).collect(Collectors.toList());
            optionsDto.setCustomerOptions(customerOptions);
        }
        // 商品类别
        List<ProductType> productTypes = this.productTypeMapper.getList(new ProductType().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(productTypes)) {
            List<Map<String, String>> productTypeOptions = productTypes.stream()
                    .map(productType -> {
                        Map<String, String> map = new HashMap<>(2);
                        map.put(LABEL, productType.getCategoryName());
                        map.put(VALUE, productType.getParamCode());
                        return map;
                    }).collect(Collectors.toList());
            optionsDto.setProductTypeOptions(productTypeOptions);
        }
        return optionsDto;
    }

    /**
     * 获取分页列表
     *
     * @param annualPlanParam 年度计划参数
     * @param pageParam       分页参数
     * @param userInfo        用户信息
     * @return 出口辅料年度计划分页列表
     */
    public ResultObject<List<ExportAuxMatAnnualPlanDto>> getListPaged(ExportAuxMatAnnualPlanParam annualPlanParam,
                                                                      PageParam pageParam, UserInfoToken<?> userInfo) {
        ExportAuxMatAnnualPlan annualPlan = this.exportAuxMatAnnualPlanDtoMapper.toPo(annualPlanParam);
        annualPlan.setTradeCode(userInfo.getCompany());
        Page<ExportAuxMatAnnualPlan> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit()
                        , pageParam.getSortOrderContent())
                .doSelectPage(() -> this.exportAuxMatAnnualPlanMapper.getList(annualPlan));
        List<ExportAuxMatAnnualPlanDto> dtoList = page.getResult().stream()
                .map(ap -> this.exportAuxMatAnnualPlanDtoMapper.toDto(ap))
                .collect(Collectors.toList());
        return ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增出口辅料年度计划
     *
     * @param annualPlanParam 年度计划参数
     * @param userInfo        用户信息
     * @return 出口辅料年度计划传输数据
     */
    @Transactional(rollbackFor = Exception.class)
    public ExportAuxMatAnnualPlanDto insert(ExportAuxMatAnnualPlanParam annualPlanParam, UserInfoToken<?> userInfo) {
        int samePkCount = this.exportAuxMatAnnualPlanMapper.countByBusinessPk(annualPlanParam.getExportCustomer()
                , annualPlanParam.getExportCategory(), userInfo.getCompany());
        if (samePkCount > 0) {
            throw new ErrorException(500, "相同的出口客户与出口类别已存在，请重新录入");
        }
        ExportAuxMatAnnualPlan annualPlan = this.exportAuxMatAnnualPlanDtoMapper.toPo(annualPlanParam);
        annualPlan.setTradeCode(userInfo.getCompany());
        annualPlan.setId(UUID.randomUUID().toString());
        annualPlan.setCreateBy(userInfo.getUserNo());
        annualPlan.setCreateTime(new Date());
        annualPlan.setCreateByName(userInfo.getUserName());
        return this.exportAuxMatAnnualPlanMapper.insert(annualPlan) > 0
                ? this.exportAuxMatAnnualPlanDtoMapper.toDto(annualPlan) : null;
    }

    /**
     * 修改出口辅料年度计划
     *
     * @param annualPlanParam 年度计划参数
     * @param userInfo        用户信息
     * @return 出口辅料年度计划传输数据
     */
    @Transactional(rollbackFor = Exception.class)
    public ExportAuxMatAnnualPlanDto update(ExportAuxMatAnnualPlanParam annualPlanParam, UserInfoToken<?> userInfo) {
        ExportAuxMatAnnualPlan annualPlan = this.exportAuxMatAnnualPlanMapper.selectByPrimaryKey(annualPlanParam.getId());
        String exportCustomer = annualPlan.getExportCustomer(), exportCategory = annualPlan.getExportCategory();
        if (!Objects.equals(exportCustomer, annualPlanParam.getExportCustomer())
                || !Objects.equals(exportCategory, annualPlanParam.getExportCategory())) {
            int samePkCount = this.exportAuxMatAnnualPlanMapper.countByBusinessPk(annualPlanParam.getExportCustomer()
                    , annualPlanParam.getExportCategory(), userInfo.getCompany());
            if (samePkCount > 0) {
                throw new ErrorException(500, "相同的出口客户与出口类别已存在，请重新录入");
            }
            annualPlan.setExportCustomer(annualPlanParam.getExportCustomer());
            annualPlan.setExportCategory(annualPlanParam.getExportCategory());
        }
        annualPlan.setPlanExportAmount(annualPlanParam.getPlanExportAmount());
        annualPlan.setNote(annualPlanParam.getNote());
        annualPlan.setUpdateTime(new Date());
        annualPlan.setUpdateBy(userInfo.getUserNo());
        annualPlan.setUpdateByName(userInfo.getUserName());
        return this.exportAuxMatAnnualPlanMapper.updateByPrimaryKey(annualPlan) > 0
                ? this.exportAuxMatAnnualPlanDtoMapper.toDto(annualPlan) : null;
    }

    /**
     * 根据主键列表删除
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<String> ids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        log.info("user {} of company {} deleted records with {}", userInfo.getUserNo(), userInfo.getCompany(), ids);
        this.exportAuxMatAnnualPlanMapper.deleteByIds(ids);
    }

    /**
     * 查询所有出口辅料年度计划
     *
     * @param annualPlanParam 年度计划参数
     * @param userInfo        用户信息
     * @return 出口辅料年度计划传输列表
     */
    public List<ExportAuxMatAnnualPlanDto> getAllList(ExportAuxMatAnnualPlanParam annualPlanParam, UserInfoToken<?> userInfo) {
        ExportAuxMatAnnualPlan annualPlan = this.exportAuxMatAnnualPlanDtoMapper.toPo(annualPlanParam);
        annualPlan.setTradeCode(userInfo.getCompany());
        List<ExportAuxMatAnnualPlan> annualPlanList = this.exportAuxMatAnnualPlanMapper.getList(annualPlan);
        if (CollectionUtils.isEmpty(annualPlanList)) {
            return Collections.emptyList();
        }
        return annualPlanList.stream()
                .map(ap -> this.exportAuxMatAnnualPlanDtoMapper.toDto(ap))
                .collect(Collectors.toList());
    }

    /**
     * 查询excel列表
     *
     * @param annualPlanParam 年度计划参数
     * @param userInfo        用户信息
     * @return 出口辅料年度计划excel列表
     */
    public List<ExportAuxMatAnnualPlanDto> gerExcelList(ExportAuxMatAnnualPlanParam annualPlanParam, UserInfoToken<?> userInfo) {
        List<ExportAuxMatAnnualPlanDto> dtoList = this.getAllList(annualPlanParam, userInfo);
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        List<String> categories = dtoList.stream()
                .map(ExportAuxMatAnnualPlanDto::getExportCategory)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<String> merchantCodes = dtoList.stream()
                .map(ExportAuxMatAnnualPlanDto::getExportCustomer)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<BizMerchant> merchants = CollectionUtils.isEmpty(merchantCodes) ? Collections.emptyList()
                : this.bizMerchantMapper.getByMerchantCodes(merchantCodes, userInfo.getCompany());
        List<ProductType> productTypes = CollectionUtils.isEmpty(categories) ? Collections.emptyList()
                : this.productTypeMapper.getByParamCodes(categories, userInfo.getCompany());
        for (ExportAuxMatAnnualPlanDto dto : dtoList) {
            if (StringUtils.isNotBlank(dto.getExportCustomer())) {
                merchants.stream()
                        .filter(merchant -> Objects.equals(dto.getExportCustomer(), merchant.getMerchantCode()))
                        .findAny()
                        .ifPresent(merchant -> {
                            if (StringUtils.isNotBlank(merchant.getMerchantNameCn())) {
                                dto.setExportCustomer(merchant.getMerchantNameCn());
                            }
                        });
            }
            if (StringUtils.isNotBlank(dto.getExportCategory())) {
                productTypes.stream()
                        .filter(productType -> Objects.equals(dto.getExportCategory(), productType.getParamCode()))
                        .findAny()
                        .ifPresent(productType -> {
                            if (StringUtils.isNotBlank(productType.getCategoryName())) {
                                dto.setExportCategory(productType.getCategoryName());
                            }
                        });
            }
        }
        return dtoList;
    }
}