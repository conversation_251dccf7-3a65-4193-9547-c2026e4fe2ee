package com.dcjet.cs.exportAuxMatAnnualPlan.dao;

import com.dcjet.cs.exportAuxMatAnnualPlan.model.ExportAuxMatAnnualPlan;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ExportAuxMatAnnualPlanMapper extends Mapper<ExportAuxMatAnnualPlan> {
    /**
     * 获取列表
     *
     * @param exportAuxMatAnnualPlan 出口辅料年度计划
     * @return 出口辅料年度计划列表
     */
    List<ExportAuxMatAnnualPlan> getList(ExportAuxMatAnnualPlan exportAuxMatAnnualPlan);

    /**
     * 根据主键列表删除
     *
     * @param ids 主键列表
     * @return deleted rows
     */
    int deleteByIds(@Param("ids") List<String> ids);

    /**
     * 根据业务主键计数
     *
     * @param exportCustomer 出口客户
     * @param exportCategory 出口类别
     * @param tradeCode      企业编码
     * @return 数量
     */
    int countByBusinessPk(@Param("exportCustomer") String exportCustomer,
                          @Param("exportCategory") String exportCategory,
                          @Param("tradeCode") String tradeCode);

    /**
     * 根据业务主键获取列表
     *
     * @param exportCustomer 出口客户
     * @param exportCategory 出口类别
     * @param tradeCode      企业编码
     * @return 出口辅料年度计划列表
     */
    List<ExportAuxMatAnnualPlan> getByBusinessPk(@Param("exportCustomer") String exportCustomer,
                                                 @Param("exportCategory") String exportCategory,
                                                 @Param("tradeCode") String tradeCode);
}