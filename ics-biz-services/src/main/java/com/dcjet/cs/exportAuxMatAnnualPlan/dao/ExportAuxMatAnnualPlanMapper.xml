<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.exportAuxMatAnnualPlan.dao.ExportAuxMatAnnualPlanMapper">
    <resultMap id="ExportAuxMatAnnualPlanResultMap"
               type="com.dcjet.cs.exportAuxMatAnnualPlan.model.ExportAuxMatAnnualPlan">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="EXPORT_CUSTOMER" property="exportCustomer" jdbcType="VARCHAR"/>
        <result column="EXPORT_CATEGORY" property="exportCategory" jdbcType="VARCHAR"/>
        <result column="PLAN_EXPORT_AMOUNT" property="planExportAmount" jdbcType="NUMERIC"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY_NAME" property="createByName" jdbcType="VARCHAR"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY_NAME" property="updateByName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        ID,
        EXPORT_CUSTOMER,
        EXPORT_CATEGORY,
        PLAN_EXPORT_AMOUNT,
        NOTE,
        TRADE_CODE,
        SYS_ORG_CODE,
        CREATE_BY,
        CREATE_TIME,
        CREATE_BY_NAME,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_BY_NAME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>


    <sql id="condition">
        <if test="true">
            and TRADE_CODE = #{tradeCode}
        </if>
        <if test="exportCustomer != null and exportCustomer != ''">
            and EXPORT_CUSTOMER = #{exportCustomer}
        </if>
        <if test="exportCategory != null and exportCategory != ''">
            and EXPORT_CATEGORY = #{exportCategory}
        </if>
    </sql>

    <select id="getList" resultMap="ExportAuxMatAnnualPlanResultMap"
            parameterType="com.dcjet.cs.exportAuxMatAnnualPlan.model.ExportAuxMatAnnualPlan">
        select
            <include refid="columns"/>
        from T_BIZ_E_AUX_MAT_ANNUAL_PLAN
        <where>
            <include refid="condition"/>
        </where>
        order by CREATE_TIME desc
    </select>

    <delete id="deleteByIds" parameterType="java.util.List">
        delete from T_BIZ_E_AUX_MAT_ANNUAL_PLAN where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByBusinessPk" resultType="int">
        select count(1)
        from T_BIZ_E_AUX_MAT_ANNUAL_PLAN
        where TRADE_CODE = #{tradeCode}
          and EXPORT_CUSTOMER = #{exportCustomer}
          and EXPORT_CATEGORY = #{exportCategory}
    </select>

    <select id="getByBusinessPk" resultMap="ExportAuxMatAnnualPlanResultMap">
        select
            <include refid="columns"/>
        from T_BIZ_E_AUX_MAT_ANNUAL_PLAN
        where TRADE_CODE = #{tradeCode}
          and EXPORT_CUSTOMER = #{exportCustomer}
          and EXPORT_CATEGORY = #{exportCategory}
    </select>
</mapper>