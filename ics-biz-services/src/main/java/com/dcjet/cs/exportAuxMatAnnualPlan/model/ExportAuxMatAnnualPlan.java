package com.dcjet.cs.exportAuxMatAnnualPlan.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_BIZ_E_AUX_MAT_ANNUAL_PLAN")
@Accessors(chain = true)
public class ExportAuxMatAnnualPlan implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    private String id;

    /**
     * 出口客户
     */
    @Column(name = "EXPORT_CUSTOMER")
    private String exportCustomer;

    /**
     * 出口品类
     */
    @Column(name = "EXPORT_CATEGORY")
    private String exportCategory;

    /**
     * 年计划出口总量（万支，吨）
     */
    @Column(name = "PLAN_EXPORT_AMOUNT")
    private BigDecimal planExportAmount;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 创建人部门编码
     */
    @Column(name = "SYS_ORG_CODE")
    private String sysOrgCode;

    /**
     * 创建人
     */
    @Column(name = "CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "UPDATE_BY")
    private String updateBy;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 创建人名称
     */
    @Column(name = "CREATE_BY_NAME")
    private String createByName;

    /**
     * 修改人名称
     */
    @Column(name = "UPDATE_BY_NAME")
    private String updateByName;

    /**
     * 扩展字段1
     */
    @Column(name = "EXTEND1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @Column(name = "EXTEND2")
    private String extend2;
    /**
     * 扩展字段3
     */
    @Column(name = "EXTEND3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @Column(name = "EXTEND4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @Column(name = "EXTEND5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @Column(name = "EXTEND6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @Column(name = "EXTEND7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @Column(name = "EXTEND8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @Column(name = "EXTEND9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @Column(name = "EXTEND10")
    private String extend10;
}