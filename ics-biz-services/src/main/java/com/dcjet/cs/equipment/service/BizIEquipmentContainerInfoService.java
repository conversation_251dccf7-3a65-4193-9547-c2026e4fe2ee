package com.dcjet.cs.equipment.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.equipment.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.equipment.dao.BizIEquipmentContainerInfoMapper;
import com.dcjet.cs.equipment.mapper.BizIEquipmentContainerInfoDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentContainerInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Service
public class BizIEquipmentContainerInfoService extends BaseService<BizIEquipmentContainerInfo> {
    @Resource
    private BizIEquipmentContainerInfoMapper bizIEquipmentContainerInfoMapper;
    @Resource
    private BizIEquipmentContainerInfoDtoMapper bizIEquipmentContainerInfoDtoMapper;
    @Override
    public Mapper<BizIEquipmentContainerInfo> getMapper() {
        return bizIEquipmentContainerInfoMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIEquipmentContainerInfoParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIEquipmentContainerInfoDto>> getListPaged(BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, PageParam pageParam) {
        // 启用分页查询
        BizIEquipmentContainerInfo bizIEquipmentContainerInfo = bizIEquipmentContainerInfoDtoMapper.toPo(bizIEquipmentContainerInfoParam);
        Page<BizIEquipmentContainerInfo> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentContainerInfoMapper.getList(bizIEquipmentContainerInfo));
        List<BizIEquipmentContainerInfoDto> bizIEquipmentContainerInfoDtos = page.getResult().stream().map(head -> {
            BizIEquipmentContainerInfoDto dto = bizIEquipmentContainerInfoDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIEquipmentContainerInfoDto>> paged = ResultObject.createInstance(bizIEquipmentContainerInfoDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIEquipmentContainerInfoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentContainerInfoDto insert(BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, UserInfoToken userInfo) {
        BizIEquipmentContainerInfo bizIEquipmentContainerInfo = bizIEquipmentContainerInfoDtoMapper.toPo(bizIEquipmentContainerInfoParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIEquipmentContainerInfo.setId(sid);
        bizIEquipmentContainerInfo.setCreateBy(userInfo.getUserNo());
        bizIEquipmentContainerInfo.setCreateTime(new Date());
        bizIEquipmentContainerInfo.setCreateUserName(userInfo.getUserName());
        bizIEquipmentContainerInfo.setTradeCode(userInfo.getCompany());
        bizIEquipmentContainerInfo.setHeadId(bizIEquipmentContainerInfoParam.getHeadId());
        // 新增数据
        int insertStatus = bizIEquipmentContainerInfoMapper.insert(bizIEquipmentContainerInfo);
        return  insertStatus > 0 ? bizIEquipmentContainerInfoDtoMapper.toDto(bizIEquipmentContainerInfo) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIEquipmentContainerInfoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentContainerInfoDto update(BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, UserInfoToken userInfo) {
        BizIEquipmentContainerInfo bizIEquipmentContainerInfo = bizIEquipmentContainerInfoMapper.selectByPrimaryKey(bizIEquipmentContainerInfoParam.getId());
        bizIEquipmentContainerInfoDtoMapper.updatePo(bizIEquipmentContainerInfoParam, bizIEquipmentContainerInfo);
        bizIEquipmentContainerInfo.setUpdateBy(userInfo.getUserNo());
        bizIEquipmentContainerInfo.setUpdateTime(new Date());
        // 更新数据
        int update = bizIEquipmentContainerInfoMapper.updateByPrimaryKey(bizIEquipmentContainerInfo);
        return update > 0 ? bizIEquipmentContainerInfoDtoMapper.toDto(bizIEquipmentContainerInfo) : null;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<BizIEquipmentContainerInfoDto> updateAll(List<BizIEquipmentContainerInfoParam> bizIEquipmentContainerInfoParamList, UserInfoToken userInfo) {
        List<BizIEquipmentContainerInfoDto> savedList = new ArrayList<>();
        for (BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam : bizIEquipmentContainerInfoParamList) {
            BizIEquipmentContainerInfo bizIEquipmentContainerInfo = bizIEquipmentContainerInfoMapper.selectByPrimaryKey(bizIEquipmentContainerInfoParam.getId());
            int result;
            if (bizIEquipmentContainerInfo==null){
                BizIEquipmentContainerInfo newContainer = bizIEquipmentContainerInfoDtoMapper.toPo(bizIEquipmentContainerInfoParam);
                /**
                 * 规范固定字段
                 */
                String sid = UUID.randomUUID().toString();
                newContainer.setId(sid);
                newContainer.setCreateBy(userInfo.getUserNo());
                newContainer.setCreateTime(new Date());
                newContainer.setCreateUserName(userInfo.getUserName());
                newContainer.setTradeCode(userInfo.getCompany());
                // 新增数据
                result=bizIEquipmentContainerInfoMapper.insert(newContainer);
                if (result > 0){
                    savedList.add(bizIEquipmentContainerInfoDtoMapper.toDto(newContainer));
                }

            }else {
                bizIEquipmentContainerInfoDtoMapper.updatePo(bizIEquipmentContainerInfoParam, bizIEquipmentContainerInfo);
                bizIEquipmentContainerInfo.setUpdateBy(userInfo.getUserNo());
                bizIEquipmentContainerInfo.setUpdateTime(new Date());
                bizIEquipmentContainerInfo.setUpdateUserName(userInfo.getUserName());
                // 更新数据
                result=bizIEquipmentContainerInfoMapper.updateByPrimaryKey(bizIEquipmentContainerInfo);
                if (result > 0){
                    savedList.add(bizIEquipmentContainerInfoDtoMapper.toDto(bizIEquipmentContainerInfo));
                }
            }

        }
        return savedList;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizIEquipmentContainerInfoMapper.deleteBySids(sids);
    }
    @Transient
    public void deleteByNotify(String headId, UserInfoToken userInfo) {
		bizIEquipmentContainerInfoMapper.deleteByNotify(headId);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIEquipmentContainerInfoDto> selectAll(BizIEquipmentContainerInfoParam exportParam, UserInfoToken userInfo) {
        BizIEquipmentContainerInfo bizIEquipmentContainerInfo = bizIEquipmentContainerInfoDtoMapper.toPo(exportParam);
        // bizIEquipmentContainerInfo.setTradeCode(userInfo.getCompany());
        List<BizIEquipmentContainerInfoDto> bizIEquipmentContainerInfoDtos = new ArrayList<>();
        List<BizIEquipmentContainerInfo> bizIEquipmentContainerInfos = bizIEquipmentContainerInfoMapper.getList(bizIEquipmentContainerInfo);
        if (CollectionUtils.isNotEmpty(bizIEquipmentContainerInfos)) {
            bizIEquipmentContainerInfoDtos = bizIEquipmentContainerInfos.stream().map(head -> {
                BizIEquipmentContainerInfoDto dto = bizIEquipmentContainerInfoDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIEquipmentContainerInfoDtos;
    }
}
