package com.dcjet.cs.equipment.dao;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.dto.iEBusiness.ForContractListDto;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* BizIEquipmentAgentAgreement
* <AUTHOR>
* @date: 2025-7-2
*/
public interface BizIEquipmentAgentAgreementMapper extends Mapper<BizIEquipmentAgentAgreement> {
    /**
     * 查询获取数据
     * @param bizIEquipmentAgentAgreement
     * @return
     */
    List<BizIEquipmentAgentAgreement> getList(BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkCanDelBySids(List<String> sids);

    List<WorkFlowParam> getFlowList(List<String> ids);

    List<BizIEquipmentAgentAgreement> getAeoList(BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement);

    List<ForContractListDto> getForContractList(@Param("tradeCode") String company, @Param("contractNo") String contractNo);

    void insertListByForContract(@Param("id") String id, @Param("headId") String sid, @Param("tradeCode") String userNo);

    BigDecimal getContractAmount(@Param("headId") String sid);

    int checkAgreementNo(@Param("agreementNo") String agreementNo, @Param("id") String id);

    String getSellerByParentId(@Param("parentId") String parentId);

    int checkContractNo(@Param("contractNo") String contractNo, @Param("agreementType") String agreementType, @Param("id") String id);
}
