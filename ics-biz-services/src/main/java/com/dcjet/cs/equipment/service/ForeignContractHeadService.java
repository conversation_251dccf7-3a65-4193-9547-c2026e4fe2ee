package com.dcjet.cs.equipment.service;

import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import com.dcjet.cs.attach.dao.AttachedMapper;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.dto.equipment.*;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.dao.ForeignContractListMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractHeadDtoMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractListDtoMapper;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.equipment.model.ForeignContractList;
import com.dcjet.cs.params.dao.CityMapper;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.dao.PriceTermsMapper;
import com.dcjet.cs.params.dao.ProductTypeMapper;
import com.dcjet.cs.params.model.City;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.params.model.PriceTerms;
import com.dcjet.cs.params.model.ProductType;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.vo.HttpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("equipmentForeignContractHeadService")
@Slf4j
public class ForeignContractHeadService extends BaseService<ForeignContractHead> implements ApprovalFlowService {
    private final static String VALUE = "value", LABEL = "label", EXT = "ext";
    private final static String PARAM_TYPE_CURR = "CURR", PARAM_TYPE_PORT = "PORT", PARAM_TYPE_UNIT = "UNIT";
    private final static String DEFAULT_BUSINESS_TYPE = CommonEnum.COMMON_BUSINESS_TYPE_ENUM
            .STATE_TRADE_IMPORT_EQUIPMENT.getType();
    private final static String DEFAULT_VERSION_NO = "1";
    private final static String DEFAULT_DATA_STATUS = CommonEnum.STATE_ENUM.DRAFT.getType();
    private final static String DEFAULT_APPR_STATUS = CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue();
    private final static String VERSION_ID_GAP = "_";

    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;
    @Resource
    private ForeignContractHeadDtoMapper foreignContractHeadDtoMapper;
    @Resource
    private ForeignContractListMapper foreignContractListMapper;
    @Resource
    private ForeignContractListDtoMapper foreignContractListDtoMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private CityMapper cityMapper;
    @Resource
    private PriceTermsMapper priceTermsMapper;
    @Resource
    private ProductTypeMapper productTypeMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;

    @Resource
    private AttachedMapper attachedMapper;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;
    @Resource
    private CommonService commonService;

    @Override
    public Mapper<ForeignContractHead> getMapper() {
        return this.foreignContractHeadMapper;
    }

    /**
     * 获取美元汇率
     *
     * @param curr     币制
     * @param userInfo 用户信息
     * @return 美元汇率
     */
    public BigDecimal getDollarRate(String curr, UserInfoToken<?> userInfo) {
        if (StringUtils.isEmpty(curr)) {
            return null;
        }
        if ("USD".equals(curr)) {
            return BigDecimal.ONE;
        }
        List<EnterpriseRate> rates = this.enterpriseRateMapper.getRateByCurrList(Collections.singletonList(curr), userInfo.getCompany());
        if (CollectionUtils.isEmpty(rates)) {
            return null;
        }
        EnterpriseRate rate = rates.get(0);
        return Objects.nonNull(rate) && Objects.nonNull(rate.getUsdRate()) ? rate.getUsdRate() : null;
    }

    /**
     * 获取分页列表
     *
     * @param foreignContractHeadParam 外商合同表体参数
     * @param pageParam                分页参数
     * @param userInfo                 用户信息
     * @return 分页列表
     */
    public ResultObject<List<ForeignContractHeadDto>> getListPaged(ForeignContractHeadParam foreignContractHeadParam,
                                                                   PageParam pageParam, UserInfoToken<?> userInfo) {
        ForeignContractHead foreignContractHead = this.foreignContractHeadDtoMapper.toPo(foreignContractHeadParam);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        int currentPage = pageParam.getPage(), currentLimit = pageParam.getLimit();
        Page<ForeignContractHead> page = PageHelper.startPage(currentPage, currentLimit, pageParam.getSortOrderContent())
                .doSelectPage(() -> this.foreignContractHeadMapper.getList(foreignContractHead));
        List<ForeignContractHeadDto> headDtoList = page.getResult().stream()
                .map(fch -> this.foreignContractHeadDtoMapper.toDto(fch))
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 获取所有选项
     *
     * @param userInfo 用户信息
     * @return 选项信息
     */
    public ForeignContractOptionsDto getAllOptions(UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        ForeignContractOptionsDto optionsDto = new ForeignContractOptionsDto();
        // 客户 & 供应商
        List<BizMerchant> merchants = this.bizMerchantMapper.provideForOptions(DEFAULT_BUSINESS_TYPE, tradeCode);
        if (CollectionUtils.isNotEmpty(merchants)) {
            optionsDto.setSupplierOptions(merchants.stream()
                    .filter(merchant -> CommonEnum.MERCHANT_TYPE.SUPPLIER
                            .getValue().equals(merchant.getMerchantType()))
                    .map(merchant -> {
                        Map<String, String> options = new HashMap<>();
                        options.put(VALUE, merchant.getMerchantCode());
                        options.put(LABEL, merchant.getMerchantNameCn());
                        return options;
                    }).collect(Collectors.toList()));
            optionsDto.setCustomerOptions(merchants.stream()
                    .filter(merchant -> CommonEnum.MERCHANT_TYPE.CUSTOMER
                            .getValue().equals(merchant.getMerchantType()))
                    .map(merchant -> {
                        Map<String, String> options = new HashMap<>();
                        options.put(LABEL, merchant.getMerchantNameCn());
                        options.put(VALUE, merchant.getMerchantCode());
                        return options;
                    }).collect(Collectors.toList()));
        }
        // 制单人
        List<ForeignContractHead> allMakers = this.foreignContractHeadMapper.getAllMakers(tradeCode);
        if (!CollectionUtils.isEmpty(allMakers)) {
            List<Map<String, String>> makerOptions = allMakers.stream().map(maker -> {
                Map<String, String> map = new HashMap<>();
                map.put(LABEL, maker.getDocumentMaker());
                map.put(VALUE, maker.getDocumentMakerNo());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setMakerOptions(makerOptions);
        }
        // 商品类别
        List<ProductType> productTypeList = this.productTypeMapper.getList(new ProductType().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(productTypeList)) {
            List<Map<String, String>> productTypeOptions = productTypeList.stream().map(productType -> {
                Map<String, String> map = new HashMap<>();
                map.put(LABEL, productType.getCategoryName());
                map.put(VALUE, productType.getCategoryCode());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setProductTypeOptions(productTypeOptions);
        }
        // 价格条款
        List<PriceTerms> priceTermsList = this.priceTermsMapper.getList(new PriceTerms().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(priceTermsList)) {
            List<Map<String, String>> priceTermsOptions = priceTermsList.stream().map(priceTerm -> {
                Map<String, String> map = new HashMap<>();
                map.put(LABEL, priceTerm.getPriceTerm());
                map.put(VALUE, priceTerm.getPriceTerm());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setPriceTermsOptions(priceTermsOptions);
        }
        // 城市
        List<City> cityList = this.cityMapper.getList(new City().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(cityList)) {
            List<Map<String, String>> cityOptions = cityList.stream().map(city -> {
                Map<String, String> map = new HashMap<>();
                map.put(LABEL, city.getCityCnName());
                map.put(VALUE, city.getCityCnName());
                map.put(EXT, city.getCityEnName());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setCityOptions(cityOptions);
        }
        String[] paramTypes = {PARAM_TYPE_CURR, PARAM_TYPE_PORT, PARAM_TYPE_UNIT};
        List<BaseInfoCustomerParams> paramList = this.baseInfoCustomerParamsMapper
                .getParamsSelectByTypes(Arrays.asList(paramTypes), DEFAULT_BUSINESS_TYPE, tradeCode);
        Function<String, List<Map<String, String>>> getParamOptionsByType = paramType -> paramList.stream()
                .filter(param -> Objects.equals(param.getParamsType(), paramType))
                .map(param -> {
                    Map<String, String> map = new HashMap<>();
                    map.put(LABEL, param.getParamsName());
                    map.put(EXT, param.getCustomParamCode());
                    map.put(VALUE, param.getParamsCode());
                    return map;
                }).collect(Collectors.toList());
        // 币种
        optionsDto.setCurrOptions(getParamOptionsByType.apply(PARAM_TYPE_CURR));
        // 港口
        optionsDto.setPortOptions(getParamOptionsByType.apply(PARAM_TYPE_PORT));
        // 单位
        optionsDto.setUnitOptions(getParamOptionsByType.apply(PARAM_TYPE_UNIT));
        return optionsDto;
    }

    /**
     * 获取所有列表
     *
     * @param foreignContractHeadParam 外商合同表头参数
     * @param userInfo                 用户信息
     * @return 外商合同表头列表
     */
    public List<ForeignContractHeadDto> getAllList(ForeignContractHeadParam foreignContractHeadParam, UserInfoToken<?> userInfo) {
        ForeignContractHead foreignContractHead = this.foreignContractHeadDtoMapper.toPo(foreignContractHeadParam);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        List<ForeignContractHead> foreignContractHeadList = this.foreignContractHeadMapper.getList(foreignContractHead);
        if (CollectionUtils.isEmpty(foreignContractHeadList)) {
            return Collections.emptyList();
        }
        List<ForeignContractHeadDto> dtoList = foreignContractHeadList.stream()
                .map(fch -> this.foreignContractHeadDtoMapper.toDto(fch))
                .collect(Collectors.toList());
        List<String> merchantCodes = Stream.of(foreignContractHeadList.stream().map(ForeignContractHead::getSeller),
                        foreignContractHeadList.stream().map(ForeignContractHead::getBuyer),
                        foreignContractHeadList.stream().map(ForeignContractHead::getDomesticClient),
                        foreignContractHeadList.stream().map(ForeignContractHead::getUsingManufacturer))
                .flatMap(s -> s)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<BizMerchant> merchants = CollectionUtils.isNotEmpty(merchantCodes)
                ? this.bizMerchantMapper.getByMerchantCodes(merchantCodes, userInfo.getCompany())
                : Collections.emptyList();
        dtoList.forEach(dto -> {
            if (StringUtils.isNotBlank(dto.getBusinessType())) {
                dto.setBusinessType(dto.getBusinessType() + StringUtils.SPACE +
                        CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(dto.getBusinessType()));
            }
            if (CollectionUtils.isNotEmpty(merchants)) {
                if (StringUtils.isNotBlank(dto.getSeller())) {
                    String sellerName = merchants.stream()
                            .filter(merchant -> Objects.equals(merchant.getMerchantCode(), dto.getSeller()))
                            .map(BizMerchant::getMerchantNameCn)
                            .filter(StringUtils::isNotBlank)
                            .distinct()
                            .findAny()
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotBlank(sellerName)) {
                        dto.setSeller(dto.getSeller() + StringUtils.SPACE + sellerName);
                    }
                }
                if (StringUtils.isNotBlank(dto.getBuyer())) {
                    String buyerName = merchants.stream()
                            .filter(merchant -> Objects.equals(merchant.getMerchantCode(), dto.getBuyer()))
                            .map(BizMerchant::getMerchantNameCn)
                            .filter(StringUtils::isNotBlank)
                            .distinct()
                            .findAny()
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotBlank(buyerName)) {
                        dto.setBuyer(dto.getBuyer() + StringUtils.SPACE + buyerName);
                    }
                }
                if (StringUtils.isNotBlank(dto.getUsingManufacturer())) {
                    String usingManufacturerName = merchants.stream()
                            .filter(merchant -> Objects.equals(merchant.getMerchantCode(), dto.getUsingManufacturer()))
                            .map(BizMerchant::getMerchantNameCn)
                            .filter(StringUtils::isNotBlank)
                            .distinct()
                            .findAny()
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotBlank(usingManufacturerName)) {
                        dto.setUsingManufacturer(dto.getUsingManufacturer() + StringUtils.SPACE + usingManufacturerName);
                    }
                }
                if (StringUtils.isNotBlank(dto.getDomesticClient())) {
                    String domesticClientName = merchants.stream()
                            .filter(merchant -> Objects.equals(merchant.getMerchantCode(), dto.getDomesticClient()))
                            .map(BizMerchant::getMerchantNameCn)
                            .filter(StringUtils::isNotBlank)
                            .distinct()
                            .findAny()
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotBlank(domesticClientName)) {
                        dto.setDomesticClient(dto.getDomesticClient() + StringUtils.SPACE + domesticClientName);
                    }
                }
            }
            if (StringUtils.isNotBlank(dto.getDataStatus())) {
                dto.setDataStatus(dto.getDataStatus() + StringUtils.SPACE +
                        CommonEnum.STATE_ENUM.getValue(dto.getDataStatus()));
            }
        });
        return dtoList;
    }

    /**
     * 新增外商合同
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 外商合同传输对象
     */
    @Transactional(rollbackFor = Exception.class)
    public ForeignContractHeadDto insert(ForeignContractAddParam addParam, UserInfoToken<?> userInfo) {
        // 校验
        String contractNo = addParam.getHead().getContractNo();
        int sameContractNoHeadCount = this.foreignContractHeadMapper.getCountByContractNo(contractNo, userInfo.getCompany());
        if (sameContractNoHeadCount > 0) {
            throw new ErrorException(500, "合同编号已存在，请重新录入");
        }
        // 新增表头
        ForeignContractHead contractHead = this.foreignContractHeadDtoMapper.toPo(addParam.getHead());
        contractHead.setId(UUID.randomUUID() + VERSION_ID_GAP + DEFAULT_VERSION_NO);
        contractHead.setTradeCode(userInfo.getCompany());
        contractHead.setCreateBy(userInfo.getUserNo());
        contractHead.setCreateByName(userInfo.getUserName());
        contractHead.setCreateTime(new Date());
        contractHead.setVersionNo(DEFAULT_VERSION_NO);
        contractHead.setDataStatus(DEFAULT_DATA_STATUS);
        contractHead.setApprStatus(DEFAULT_APPR_STATUS);
        contractHead.setContractNo(contractNo);
        contractHead.setBusinessType(DEFAULT_BUSINESS_TYPE);
        contractHead.setDocumentMakerNo(userInfo.getUserNo());
        contractHead.setDocumentMaker(userInfo.getUserName());
        contractHead.setDocumentMakeDate(contractHead.getCreateTime());
        this.foreignContractHeadMapper.insert(contractHead);
        // 新增表体
        BigDecimal dollarRate = this.getDollarRate(contractHead.getCurr(), userInfo);
        List<ForeignContractListParam> bodyParamList = addParam.getBodyList();
        if (CollectionUtils.isEmpty(bodyParamList)) {
            return this.foreignContractHeadDtoMapper.toDto(contractHead);
        }
        List<ForeignContractList> bodyList = new ArrayList<>(bodyParamList.size());
        for (ForeignContractListParam bodyParam : bodyParamList) {
            ForeignContractList body = this.foreignContractListDtoMapper.toPo(bodyParam);
            BigDecimal unitPrice = body.getUnitPrice(), qty = body.getQty();
            if (Objects.nonNull(unitPrice) && Objects.nonNull(qty)) {
                body.setMoneyAmount(unitPrice.multiply(qty).setScale(4, RoundingMode.HALF_UP));
                BigDecimal moneyAmount = body.getMoneyAmount();
                if (Objects.nonNull(dollarRate)) {
                    body.setConvertedTotalDollars(moneyAmount.multiply(dollarRate).setScale(4, RoundingMode.HALF_UP));
                }
            }
            body.setId(UUID.randomUUID() + VERSION_ID_GAP + DEFAULT_VERSION_NO);
            body.setHeadId(contractHead.getId());
            body.setTradeCode(userInfo.getCompany());
            body.setCreateBy(userInfo.getUserNo());
            body.setCreateByName(userInfo.getUserName());
            body.setCreateTime(new Date());
            body.setDataStatus(DEFAULT_DATA_STATUS);
            bodyList.add(body);
        }
        BulkSqlOpt.batchInsertAndThrowException(bodyList, ForeignContractListMapper.class);
        return this.foreignContractHeadDtoMapper.toDto(contractHead);
    }

    /**
     * 更改外商合同
     *
     * @param headParam 表头参数
     * @param userInfo  用户信息
     * @return 外商合同传输对象
     */
    @Transactional(rollbackFor = Exception.class)
    public ForeignContractHeadDto update(ForeignContractHeadParam headParam, UserInfoToken<?> userInfo) {
        ForeignContractHead contractHead = this.foreignContractHeadMapper.selectByPrimaryKey(headParam.getId());
        if (!CommonEnum.STATE_ENUM.DRAFT.getType().equals(contractHead.getDataStatus())) {
            throw new ErrorException(500, "仅编制状态数据允许编辑");
        }
        contractHead.setNote(headParam.getNote()); // 备注
        contractHead.setUpdateTime(new Date()); // 修改时间
        contractHead.setUpdateBy(userInfo.getUserNo()); // 修改人
        contractHead.setUpdateByName(userInfo.getUserName()); // 修改人名称
        contractHead.setDocumentMakeDate(contractHead.getUpdateTime()); // 制单时间
        contractHead.setDocumentMaker(userInfo.getUserName()); // 制单人名称
        contractHead.setDocumentMakerNo(userInfo.getUserNo()); // 制单人编号
        contractHead.setBusinessPlace(headParam.getBusinessPlace()); // 业务地点
        contractHead.setBuyer(headParam.getBuyer()); // 买家
        contractHead.setSeller(headParam.getSeller()); // 卖家
        contractHead.setUsingManufacturer(headParam.getUsingManufacturer()); // 使用厂家
        contractHead.setDomesticClient(headParam.getDomesticClient()); // 国内委托方
        contractHead.setSignDate(headParam.getSignDate()); // 签约日期
        contractHead.setSignPlaceCn(headParam.getSignPlaceCn()); // 签约地点（中文）
        contractHead.setSignPlaceEn(headParam.getSignPlaceEn()); // 签约地点（英文）
        contractHead.setContractEffectiveDate(headParam.getContractEffectiveDate()); // 合同生效期
        contractHead.setContractValidityDate(headParam.getContractValidityDate()); // 合同有效期
        contractHead.setTransportMode(headParam.getTransportMode()); // 运输方式
        contractHead.setShippingPort(headParam.getShippingPort()); // 装运港
        contractHead.setDestPort(headParam.getDestPort()); // 目的港
        contractHead.setCustomsDeclarationPort(headParam.getCustomsDeclarationPort()); // 报关口岸
        contractHead.setPaymentMethod(headParam.getPaymentMethod()); // 付款方式
        contractHead.setPriceTerm(headParam.getPriceTerm()); // 价格条款
        contractHead.setPriceTermPort(headParam.getPriceTermPort()); // 价格条款对应港口
        contractHead.setSuggestAuthorSignatory(headParam.getSuggestAuthorSignatory()); // 建议授权签约人
        contractHead.setShortOverflowNumber(headParam.getShortOverflowNumber()); // 短溢数
        if (!Objects.equals(contractHead.getCurr(), headParam.getCurr())) {
            contractHead.setCurr(headParam.getCurr()); // 币种
            // 需要刷新表体折合美元
            BigDecimal dollarRate = this.getDollarRate(contractHead.getCurr(), userInfo);
            String updateBy = userInfo.getUserNo(), updateByName = userInfo.getUserName();
            this.foreignContractListMapper.flushMoney(headParam.getId(), dollarRate, updateBy, updateByName);
        }
        return this.foreignContractHeadMapper.updateByPrimaryKey(contractHead) > 0
                ? this.foreignContractHeadDtoMapper.toDto(contractHead) : null;
    }

    /**
     * 删除外商合同
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<ForeignContractHead> foreignContractHeadList = this.foreignContractHeadMapper.getByIds(ids);
        boolean hasAnyNotDraft = foreignContractHeadList.stream()
                .anyMatch(head -> !CommonEnum.STATE_ENUM.DRAFT.getType().equals(head.getDataStatus()));
        if (hasAnyNotDraft) {
            throw new ErrorException(500, "仅编制状态数据允许删除");
        }
        this.foreignContractHeadMapper.deleteByIds(ids);
        this.foreignContractListMapper.deleteByHeadIds(ids, userInfo.getCompany());
    }

    /**
     * 确认外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return dto
     */
    @Transactional(rollbackFor = Exception.class)
    public ForeignContractHeadDto confirm(String id, UserInfoToken<?> userInfo) {
        ForeignContractHead foreignContractHead = this.foreignContractHeadMapper.selectByPrimaryKey(id);
        if (CommonEnum.STATE_ENUM.CONFIRMED.getType().equals(foreignContractHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已确认，无需重复操作");
        }
        if (CommonEnum.STATE_ENUM.INVALID.getType().equals(foreignContractHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已作废，不允许确认");
        }
        this.foreignContractHeadMapper.confirmById(id, userInfo.getUserNo(), userInfo.getUserName());
        this.foreignContractListMapper.confirmByHeadId(id, userInfo.getUserNo(), userInfo.getUserName());
        ForeignContractHead newForeignContractHead = this.foreignContractHeadMapper.selectByPrimaryKey(id);
        return this.foreignContractHeadDtoMapper.toDto(newForeignContractHead);
    }

    /**
     * 作废外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void invalidate(String id, UserInfoToken<?> userInfo) {
        ForeignContractHead foreignContractHead = this.foreignContractHeadMapper.selectByPrimaryKey(id);
        if (CommonEnum.STATE_ENUM.INVALID.getType().equals(foreignContractHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已作废，无需重复操作");
        }
        this.foreignContractHeadMapper.invalidateById(id, userInfo.getUserNo(), userInfo.getUserName());
        this.foreignContractListMapper.invalidateByHeadId(id, userInfo.getUserNo(), userInfo.getUserName());
    }

    /**
     * 外商合同版本校验
     *
     * @param contractNo 合同号
     * @param userInfo   用户信息
     * @return 是否存在有效数据
     */
    public String checkVersionCopy(String contractNo, UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        List<ForeignContractHead> headList = this.foreignContractHeadMapper.getByContractNo(contractNo, tradeCode);
        if (CollectionUtils.isEmpty(headList)) {
            return CommonVariable.NO_CODE;
        }
        boolean hasValidData = headList.stream()
                .anyMatch(head -> !CommonEnum.STATE_ENUM.INVALID.getType().equals(head.getDataStatus()));
        return hasValidData ? CommonVariable.YES_CODE : CommonVariable.NO_CODE;
    }

    /**
     * 外商合同版本复制
     *
     * @param foreignContractHeadParam 外商合同表头参数
     * @param userInfo                 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void versionCopy(ForeignContractHeadParam foreignContractHeadParam, UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany(), headId = foreignContractHeadParam.getId();
        ForeignContractHead contractHead = this.foreignContractHeadMapper.selectByPrimaryKey(headId);
        if (contractHead == null) {
            throw new ErrorException(500, "外商合同数据不存在，请刷新");
        }
        if (!tradeCode.equals(contractHead.getTradeCode())) {
            throw new ErrorException(500, "企业编码不匹配，无法操作该数据");
        }
        String currentContractNo = contractHead.getContractNo();
        // 查询最大版本号、下一个版本号
        ForeignContractHead maxVersionContractHead = this.foreignContractHeadMapper
                .getMaxVersionContract(currentContractNo, tradeCode);
        int nextVersionNo = Integer.parseInt(maxVersionContractHead.getVersionNo()) + 1;
        // 作废当前所有剩余外商合同
        String updateBy = userInfo.getUserNo(), updateByName = userInfo.getUserName();
        this.foreignContractHeadMapper.invalidateByContractNo(currentContractNo, updateBy, updateByName, tradeCode);
        this.foreignContractListMapper.invalidateByContractNo(currentContractNo, updateBy, updateByName, tradeCode);
        // 复制表头
        ForeignContractHead newContractHead = new ForeignContractHead();
        BeanUtils.copyProperties(contractHead, newContractHead);
        newContractHead.setVersionNo(String.valueOf(nextVersionNo));
        newContractHead.setConfirmTime(null);
        newContractHead.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
        newContractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue());
        newContractHead.setCreateBy(userInfo.getUserNo());
        newContractHead.setCreateByName(userInfo.getUserName());
        newContractHead.setCreateTime(new Date());
        newContractHead.setDocumentMakerNo(userInfo.getUserNo());
        newContractHead.setDocumentMaker(userInfo.getUserName());
        newContractHead.setDocumentMakeDate(newContractHead.getCreateTime());
        newContractHead.setUpdateBy(null);
        newContractHead.setUpdateByName(null);
        newContractHead.setUpdateTime(null);
        newContractHead.setId(UUID.randomUUID() + VERSION_ID_GAP + nextVersionNo);
        newContractHead.setPrevVersionId(contractHead.getId());
        this.foreignContractHeadMapper.insert(newContractHead);
        // 复制表体
        List<ForeignContractList> contractBodyList = this.foreignContractListMapper
                .getListByHeadId(contractHead.getId(), tradeCode)
                .stream()
                .sorted(Comparator.comparing(ForeignContractList::getCreateTime).reversed())
                .collect(Collectors.toList());
        List<ForeignContractList> newContractBodyList = new ArrayList<>(contractBodyList.size());
        for (ForeignContractList contractBody : contractBodyList) {
            ForeignContractList newContractBody = new ForeignContractList();
            BeanUtils.copyProperties(contractBody, newContractBody);
            newContractBody.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
            newContractBody.setConfirmTime(null);
            newContractBody.setCreateBy(userInfo.getUserNo());
            newContractBody.setCreateTime(new Date());
            newContractBody.setCreateByName(userInfo.getUserName());
            newContractBody.setUpdateByName(null);
            newContractBody.setUpdateTime(null);
            newContractBody.setUpdateBy(null);
            newContractBody.setId(UUID.randomUUID() + VERSION_ID_GAP + nextVersionNo);
            newContractBody.setPrevVersionId(contractBody.getId());
            newContractBody.setHeadId(newContractHead.getId());
            newContractBodyList.add(newContractBody);
        }
        // 复制归档附件
        List<Attached> attachedList = this.attachedMapper.getByHeadId(contractHead.getId(), tradeCode);
        if (CollectionUtils.isNotEmpty(attachedList)) {
            List<Attached> tempAttachList = new ArrayList<>();
            for (Attached attached : attachedList) {
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setTradeCode(userInfo.getCompany());
                attached.setBusinessSid(newContractHead.getId());
                attached.setSid(UUID.randomUUID().toString());
                attached.setNote("复制第三条线外商合同【" + contractHead.getContractNo() + "】归档文件！");
                String oldFileName = attached.getFileName();
                try {
                    byte[] bytes = oldFileName.startsWith("TIANYI") ? fileHandler.downloadFile(oldFileName)
                            : otherFileHandler.downloadFile(oldFileName);
                    attached.setFileName(fileHandler.uploadFile(bytes, oldFileName));
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            RoundingMode.HALF_UP));
                    this.attachedMapper.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}", tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(500, "复制归档文件异常！");
                }
            }
        }
        // 表体入库(放最后，不属同一事务)
        if (CollectionUtils.isNotEmpty(newContractBodyList)) {
            BulkSqlOpt.batchInsertAndThrowException(newContractBodyList, ForeignContractListMapper.class);
        }
    }

    public ResultObject<List<ForeignContractHeadDto>> getContractForPlan(ForeignContractHeadParam foreignContractHeadParam,
                                                                         PageParam pageParam, UserInfoToken<?> userInfo) {
        ForeignContractHead foreignContractHead = this.foreignContractHeadDtoMapper.toPo(foreignContractHeadParam);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        int currentPage = pageParam.getPage(), currentLimit = pageParam.getLimit();
        Page<ForeignContractHead> page = PageHelper.startPage(currentPage, currentLimit, pageParam.getSortOrderContent())
                .doSelectPage(() -> this.foreignContractHeadMapper.getContractForPlan(foreignContractHead));
        List<ForeignContractHeadDto> headDtoList = page.getResult().stream()
                .map(fcd -> this.foreignContractHeadDtoMapper.toDto(fcd))
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }

    @Override
    public void startFlowBatch(NextNodeInfoBatchVo batchVo, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        // 更新表头状态
        String id = approvalFlowParam.getIds().get(0);
        ForeignContractHead contractHead = this.foreignContractHeadMapper.selectByPrimaryKey(id);
        String[] allowStartApprStatus = {CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue(),
                CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue()};
        if (!Arrays.asList(allowStartApprStatus).contains(contractHead.getApprStatus())) {
            throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
        }
        contractHead.setUpdateBy(userInfo.getUserNo());
        contractHead.setUpdateByName(userInfo.getUserName());
        contractHead.setUpdateTime(new Date());
        contractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        // 记录流程实例id
        contractHead.setExtend1(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));
        this.foreignContractHeadMapper.updateByPrimaryKey(contractHead);
        // 新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(contractHead.getId());
        this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核"
                , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
    }

    @Override
    public void audit(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam,
                      Map<String, String> flowInstanceMap, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            String businessId = flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId());
            ForeignContractHead contractHead = this.foreignContractHeadMapper.selectByPrimaryKey(businessId);
            if (nextNodeInfoVo.isFinish()) {
                contractHead.setUpdateBy(userInfo.getUserNo());
                contractHead.setUpdateByName(userInfo.getUserName());
                contractHead.setUpdateTime(new Date());
                contractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                this.foreignContractHeadMapper.updateByPrimaryKey(contractHead);
                nextNodeInfoVo.setNodeName("审核通过");
            }
            // 新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(businessId);
            this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName()
                    , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public void reject(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            // 回退到发起人（仅单条退回）
            String businessId = approvalFlowParam.getIds().get(0);
            ForeignContractHead contractHead = this.foreignContractHeadMapper.selectByPrimaryKey(businessId);
            contractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
            contractHead.setUpdateBy(userInfo.getUserNo());
            contractHead.setUpdateByName(userInfo.getUserName());
            contractHead.setUpdateTime(new Date());
            this.foreignContractHeadMapper.updateByPrimaryKey(contractHead);

            // 新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(businessId);
            aeoAuditInfo.setApprNote(approvalFlowParam.getApprMessage());
            this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回"
                    , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public List<WorkFlowParam> getFlowList(List<String> ids) {
        return this.foreignContractHeadMapper.getFlowList(ids);
    }

    public ResultObject<List<ForeignContractHeadDto>> getAeoListPaged(ForeignContractHeadParam headParam,
                                                                      PageParam pageParam, UserInfoToken<?> userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = this.commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalList(headParam.getBusinessType());
        List<String> ids = (List<String>) result.getResult();
        // 如果ids为空，直接返回空分页结果
        if (CollectionUtils.isEmpty(ids)) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }
        headParam.setIds(ids);
        // 启用分页查询
        ForeignContractHead contractHead = this.foreignContractHeadDtoMapper.toPo(headParam);
        contractHead.setTradeCode(userInfo.getCompany());
        Page<ForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.foreignContractHeadMapper.getAeoList(contractHead));
        List<ForeignContractHeadDto> dtoList = page.getResult().stream()
                .map(this.foreignContractHeadDtoMapper::toDto)
                .collect(Collectors.toList());
        return ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
    }
}