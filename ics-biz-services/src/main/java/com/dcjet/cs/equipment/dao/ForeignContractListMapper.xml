<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.equipment.dao.ForeignContractListMapper">
    <resultMap id="ForeignContractListResultMap" type="com.dcjet.cs.equipment.model.ForeignContractList">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="PREV_VERSION_ID" property="prevVersionId" jdbcType="VARCHAR"/>
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR"/>
        <result column="G_NAME" property="gName" jdbcType="VARCHAR"/>
        <result column="G_MODEL" property="gModel" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="UNIT_PRICE" property="unitPrice" jdbcType="NUMERIC"/>
        <result column="MONEY_AMOUNT" property="moneyAmount" jdbcType="NUMERIC"/>
        <result column="DELIVERY_DATE" property="deliveryDate" jdbcType="TIMESTAMP"/>
        <result column="CONVERTED_TOTAL_DOLLARS" property="convertedTotalDollars" jdbcType="NUMERIC"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="DATA_STATUS" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY_NAME" property="createByName" jdbcType="VARCHAR"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY_NAME" property="updateByName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        ID,
        PREV_VERSION_ID,
        HEAD_ID,
        G_NAME,
        G_MODEL,
        QTY,
        UNIT,
        UNIT_PRICE,
        MONEY_AMOUNT,
        DELIVERY_DATE,
        CONVERTED_TOTAL_DOLLARS,
        NOTE,
        DATA_STATUS,
        CONFIRM_TIME,
        TRADE_CODE,
        SYS_ORG_CODE,
        CREATE_BY,
        CREATE_TIME,
        CREATE_BY_NAME,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_BY_NAME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>

    <sql id="condition">
        <if test="true">
            and TRADE_CODE = #{tradeCode}
        </if>
        <if test="true">
            and HEAD_ID = #{headId}
        </if>
    </sql>

    <select id="getList" resultMap="ForeignContractListResultMap"
            parameterType="com.dcjet.cs.equipment.model.ForeignContractList">
        select
        <include refid="columns"/>
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        <where>
            <include refid="condition"/>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="getListByHeadId" resultMap="ForeignContractListResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        where HEAD_ID = #{headId} and TRADE_CODE = #{tradeCode}
        order by CREATE_TIME desc
    </select>

    <select id="getListByHeadIds" resultMap="ForeignContractListResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        where TRADE_CODE = #{tradeCode} and HEAD_ID in
        <foreach collection="headIds" item="headId" open="(" separator="," close=")">
            #{headId}
        </foreach>
        order by CREATE_TIME desc
    </select>

    <select id="getSummaryInfo" resultType="com.dcjet.cs.dto.equipment.ForeignContractListSummaryDto">
        select
        COALESCE(SUM(QTY), 0) AS TOTAL_QTY,
        COALESCE(SUM(MONEY_AMOUNT), 0) AS TOTAL_MONEY_AMOUNT,
        COALESCE(SUM(CONVERTED_TOTAL_DOLLARS), 0) AS TOTAL_CONVERTED_DOLLARS
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <delete id="deleteByHeadIds">
        delete from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        where TRADE_CODE = #{tradeCode} and HEAD_ID in
        <foreach collection="headIds" item="headId" open="(" separator="," close=")">
            #{headId}
        </foreach>
    </delete>

    <update id="confirmByHeadId">
        update T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        set DATA_STATUS    = '1',
            CONFIRM_TIME   = current_timestamp,
            UPDATE_BY      = #{userNo},
            UPDATE_BY_NAME = #{userName},
            UPDATE_TIME    = current_timestamp
        where HEAD_ID = #{headId}
    </update>

    <update id="invalidateByHeadId">
        update T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_BY      = #{userNo},
            UPDATE_BY_NAME = #{userName},
            UPDATE_TIME    = current_timestamp
        where HEAD_ID = #{headId}
    </update>

    <delete id="deleteByIds">
        delete from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="invalidateByContractNo">
        update T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName},
            UPDATE_TIME    = current_timestamp
        where DATA_STATUS &lt;> '2'
          and TRADE_CODE = #{tradeCode}
          and HEAD_ID in
              (select ID
               from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD
               where TRADE_CODE = #{tradeCode}
                 and CONTRACT_NO = #{contractNo})
    </update>

    <update id="flushMoney">
        update T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST
        set UPDATE_BY               = #{updateBy},
            UPDATE_BY_NAME          = #{updateByName},
            UPDATE_TIME             = current_timestamp,
            MONEY_AMOUNT            = QTY * UNIT_PRICE,
            CONVERTED_TOTAL_DOLLARS = QTY * UNIT_PRICE * #{dollarRate}
        where HEAD_ID = #{headId}
    </update>
</mapper>