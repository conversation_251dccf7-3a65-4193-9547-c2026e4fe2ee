package com.dcjet.cs.equipment.mapper;

import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ForeignContractHeadDtoMapper {

    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    ForeignContractHeadDto toDto(ForeignContractHead po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    ForeignContractHead toPo(ForeignContractHeadParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(ForeignContractHeadParam param, @MappingTarget ForeignContractHead po);
}