package com.dcjet.cs.equipment.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter
@Getter
@Table(name = "T_BIZ_I_EQUIPMENT_PLAN_HEAD")
public class BizIEquipmentPlanHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "ID")
	private  String id;
	/**
     * 计划书单号
     */
	@Column(name = "PLAN_NO")
	private  String planNo;
	/**
     * 业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）
     */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
     * 合同号（弹窗选择，允许修改）
     */
	@Column(name = "CONTRACT_NO")
	private  String contractNo;
	/**
     * 业务地点（从外商合同带入，不允许修改）
     */
	@Column(name = "BUSINESS_LOCATION")
	private  String businessLocation;
	/**
     * 买家（从外商合同带入，不允许修改）
     */
	@Column(name = "BUYER")
	private  String buyer;
	/**
     * 卖家（从外商合同带入，不允许修改）
     */
	@Column(name = "SELLER")
	private  String seller;
	/**
     * 使用厂家（从外商合同带入）
     */
	@Column(name = "MANUFACTURER")
	private  String manufacturer;
	/**
     * 国内委托方（从外商合同带入）
     */
	@Column(name = "DOMESTIC_CLIENT")
	private  String domesticClient;
	/**
     * 预计收款时间（用户录入，当收款状态为未收款时触发预警）
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EST_RECEIVE_DATE")
	private  Date estReceiveDate;
	/**
     * 收款状态（0未收款，1已收款。默认未收款）
     */
	@Column(name = "RECEIVE_STATUS")
	private  String receiveStatus;
	/**
     * 预计付款时间（用户录入，当付款状态为未付款时触发预警）
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EST_PAYMENT_DATE")
	private  Date estPaymentDate;
	/**
     * 付款状态（0未付款，1已付款。默认未付款）
     */
	@Column(name = "PAYMENT_STATUS")
	private  String paymentStatus;
	/**
     * 预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EST_ARBITRATION_DATE")
	private  Date estArbitrationDate;
	/**
     * 预裁定状态（0未裁定，1已裁定。默认未裁定）
     */
	@Column(name = "ARBITRATION_STATUS")
	private  String arbitrationStatus;
	/**
     * 预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EST_LICENSE_DATE")
	private  Date estLicenseDate;
	/**
     * 许可证状态（0未办理，1已办理。默认未办理）
     */
	@Column(name = "LICENSE_STATUS")
	private  String licenseStatus;
	/**
     * 预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EST_TRANSPORT_CERT_DATE")
	private  Date estTransportCertDate;
	/**
     * 准运证状态（0未办理，1已办理。默认未办理）
     */
	@Column(name = "TRANSPORT_CERT_STATUS")
	private  String transportCertStatus;
	/**
     * 预计保险申办时间（用户录入，当保险状态为未办理时触发预警）
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EST_INSURANCE_DATE")
	private  Date estInsuranceDate;
	/**
     * 保险状态（0未办理，1已办理。默认未办理）
     */
	@Column(name = "INSURANCE_STATUS")
	private  String insuranceStatus;
	/**
     * 报关状态
     */
	@Column(name = "ENTRY_STATUS")
	private  String entryStatus;
	/**
     * 预计装箱信息（用户录入，预估箱型及箱数）
     */
	@Column(name = "EST_PACKING_INFO")
	private  String estPackingInfo;
	/**
     * 许可证号
     */
	@Column(name = "LICENSE_NO")
	private  String licenseNo;
	/**
     * 许可证申请日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "LICENSE_APPLY_DATE")
	private  Date licenseApplyDate;
	/**
     * 许可证有效期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "LICENSE_VALIDITY_DATE")
	private  Date licenseValidityDate;
	/**
     * 许可证备注
     */
	@Column(name = "LICENSE_REMARK")
	private  String licenseRemark;
	/**
     * 备注（用户录入）
     */
	@Column(name = "REMARK")
	private  String remark;
	/**
     * 数据状态
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 审批状态
     */
	@Column(name = "APPR_STATUS")
	private  String apprStatus;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CONFIRM_TIME")
	private  Date confirmTime;
	/**
     * 版本号
     */
	@Column(name = "VERSION_NO")
	private  String versionNo;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 父级ID
     */
	@Column(name = "PARENT_ID")
	private  String parentId;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 创建时间-开始
     */
	@Transient
	private String createTimeFrom;
	/**
     * 创建时间-结束
     */
	@Transient
    private String createTimeTo;
	/**
     * 最后修改人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 创建人姓名
     */
	@Column(name = "CREATE_USER_NAME")
	private  String createUserName;
	/**
     * 最后修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 扩展字段1
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "EXTEND10")
	private  String extend10;

	@Transient
	private String insertTime;
}
