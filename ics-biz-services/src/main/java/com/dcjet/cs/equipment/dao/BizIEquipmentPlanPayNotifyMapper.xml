<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.equipment.dao.BizIEquipmentPlanPayNotifyMapper">
    <resultMap id="bizIEquipmentPlanPayNotifyResultMap" type="com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="serial_no" property="serialNo" jdbcType="VARCHAR" />
		<result column="payment_type" property="paymentType" jdbcType="VARCHAR" />
		<result column="contract_amount" property="contractAmount" jdbcType="NUMERIC" />
		<result column="exchange_rate" property="exchangeRate" jdbcType="NUMERIC" />
		<result column="import_export_agent_rate" property="importExportAgentRate" jdbcType="NUMERIC" />
		<result column="import_export_agent_fee" property="importExportAgentFee" jdbcType="NUMERIC" />
		<result column="head_office_agent_rate" property="headOfficeAgentRate" jdbcType="NUMERIC" />
		<result column="head_office_agent_fee" property="headOfficeAgentFee" jdbcType="NUMERIC" />
		<result column="charge_container_count" property="chargeContainerCount" jdbcType="VARCHAR" />
		<result column="customs_clearance_fee" property="customsClearanceFee" jdbcType="NUMERIC" />
		<result column="container_inspection_fee" property="containerInspectionFee" jdbcType="NUMERIC" />
		<result column="freight_forwarding_fee" property="freightForwardingFee" jdbcType="NUMERIC" />
		<result column="insurance_rate" property="insuranceRate" jdbcType="NUMERIC" />
		<result column="insurance_fee" property="insuranceFee" jdbcType="NUMERIC" />
		<result column="remittance_amount_rmb" property="remittanceAmountRmb" jdbcType="NUMERIC" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,head_id
     ,serial_no
     ,payment_type
     ,contract_amount
     ,exchange_rate
     ,import_export_agent_rate
     ,import_export_agent_fee
     ,head_office_agent_rate
     ,head_office_agent_fee
     ,charge_container_count
     ,customs_clearance_fee
     ,container_inspection_fee
     ,freight_forwarding_fee
     ,insurance_rate
     ,insurance_fee
     ,remittance_amount_rmb
     ,remark
     ,create_by
     ,create_time
     ,update_by
     ,update_time
     ,sys_org_code
     ,trade_code
     ,status
     ,version_no
     ,parent_id
     ,create_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
    </sql>
	<sql id="condition">
	<if test="headId != null and headId != ''">
		  head_id = #{headId}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIEquipmentPlanPayNotifyResultMap" parameterType="com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify">
        select <include refid="Base_Column_List" />
        from T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY t
        <where>
            <!-- 用户Grid查询 and 条件-->
			<include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY t
		where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
              #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY t
		where
		t.head_id
		in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

    <select id="getMaxSerialNoByHeadId" resultType="java.lang.Integer" parameterType="java.lang.String">
        select NVL(MAX(CAST(serial_no AS INTEGER)), 0)
        from T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY
        where head_id = #{headId}
        and serial_no is not null
        and serial_no != ''
    </select>
    <select id="getTransMes" resultType="com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify">
        select HQ_AGENT_FEE_RATE as headOfficeAgentRate,IE_AGENT_FEE_RATE as importExportAgentRate,
               CUSTOMS_FEE_AMT as customsClearanceFee,CNTR_INSP_FEE_AMT as containerInspectionFee,INSURANCE_RATE
        from T_BIZ_TRANSCODE
        where BIZ_TYPE=#{businessType} and trade_code=#{tradeCode}
        order by CREATE_TIME desc
        Limit 1
    </select>
    <select id="getDetailTotalAmount" resultType="java.math.BigDecimal">
        select sum(AMOUNT)
        from T_BIZ_I_EQUIPMENT_PLAN_LIST
        where head_id = #{headId}
    </select>
    <select id="getExchangeRate" resultType="java.math.BigDecimal">
        SELECT coalesce(latest_rate.latest_rate_value, 0) *
               (1 +  case when floating.FLOAT_RATE is null then 0 else
                   floating.FLOAT_RATE / 100 end) AS calculated_value
        FROM T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD detail
                  LEFT JOIN T_BIZ_I_EQUIPMENT_PLAN_HEAD plan ON plan.PARENT_ID = detail.id
                  LEFT JOIN (SELECT rate_inner.CURR,
                                    rate_inner.TRADE_CODE,
                                    rate_inner.RATE AS latest_rate_value,
                                    rate_inner.MONTH
                             FROM (SELECT *,
                                          ROW_NUMBER() OVER(PARTITION BY CURR, TRADE_CODE ORDER BY MONTH DESC ) AS rn
                                   FROM T_BIZ_ENTERPRISE_RATE) rate_inner
                             WHERE rate_inner.rn = 1) latest_rate
                            ON latest_rate.CURR = detail.CURR AND
                               latest_rate.TRADE_CODE = detail.TRADE_CODE
                  LEFT JOIN T_BIZ_RATE_TABLE floating ON floating.curr = detail.curr
            AND detail.trade_code = floating.trade_code
            AND floating.BUSINESS_TYPE like '%' || plan.BUSINESS_TYPE || '%'
        WHERE plan.id = #{Id}
            LIMIT 1
    </select>

    <select id="getContainerMes" resultType="java.math.BigDecimal">
        select COALESCE(LAND_FREIGHT_AMT,0) + COALESCE(PORT_CHARGES_AMT,0) +COALESCE( INTL_FREIGHT_AMT,0) as intlFreightAmt
        from T_BIZ_TRANSCODE
        where BIZ_TYPE=#{businessType} and CONTAINER_TYPE= #{containerType} and trade_code=#{tradeCode}
        order by CREATE_TIME desc
            Limit 1
    </select>
    <select id="getContainerList" resultType="java.lang.String">
        select distinct CONTAINER_TYPE from T_BIZ_TRANSCODE
        where BIZ_TYPE=#{businessType} and trade_code=#{tradeCode} and CONTAINER_TYPE is not null;
    </select>
    <select id="getMesForPrint" resultType="com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify">
        select head.CONTRACT_NO,head.BUYER,head.SELLER,notify.PAYMENT_TYPE,
               notify.REMITTANCE_AMOUNT_RMB,
               (select PRODUCT_NAME  from T_BIZ_I_EQUIPMENT_PLAN_LIST where HEAD_ID = head.ID order by CREATE_TIME asc limit 1) as productName
        from T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY notify
                 left join T_BIZ_I_EQUIPMENT_PLAN_HEAD head on notify.head_id =head.ID
        where notify.id=#{Id}
    </select>
</mapper>
