package com.dcjet.cs.equipment.service;
import com.dcjet.cs.dto.dec.BizIListTotal;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListExportParam;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListDto;
import com.dcjet.cs.equipment.dao.BizIEquipmentAgentAgreementListMapper;
import com.dcjet.cs.equipment.mapper.BizIEquipmentAgentAgreementListDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreementList;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Service
public class BizIEquipmentAgentAgreementListService extends BaseService<BizIEquipmentAgentAgreementList> {
    @Resource
    private BizIEquipmentAgentAgreementListMapper bizIEquipmentAgentAgreementListMapper;
    @Resource
    private BizIEquipmentAgentAgreementListDtoMapper bizIEquipmentAgentAgreementListDtoMapper;
    @Override
    public Mapper<BizIEquipmentAgentAgreementList> getMapper() {
        return bizIEquipmentAgentAgreementListMapper;
    }
    /**
     * 功能描述: grid分页查询
     *
     * @param bizIEquipmentAgentAgreementListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIEquipmentAgentAgreementListDto>> selectAllPaged(BizIEquipmentAgentAgreementListParam bizIEquipmentAgentAgreementListParam, PageParam pageParam) {
        // 启用分页查询
        BizIEquipmentAgentAgreementList bizIEquipmentAgentAgreementList = bizIEquipmentAgentAgreementListDtoMapper.toPo(bizIEquipmentAgentAgreementListParam);
        Page<BizIEquipmentAgentAgreementList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentAgentAgreementListMapper.getList(bizIEquipmentAgentAgreementList));
        List<BizIEquipmentAgentAgreementListDto> bizIEquipmentAgentAgreementListDtos = page.getResult().stream().map(head -> {
            BizIEquipmentAgentAgreementListDto dto = bizIEquipmentAgentAgreementListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<BizIEquipmentAgentAgreementListDto>> paged = ResultObject.createInstance(bizIEquipmentAgentAgreementListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<BizIEquipmentAgentAgreementListDto> selectAll(BizIEquipmentAgentAgreementListParam exportParam) {
        BizIEquipmentAgentAgreementList bizIEquipmentAgentAgreementList = bizIEquipmentAgentAgreementListDtoMapper.toPo(exportParam);
        List<BizIEquipmentAgentAgreementListDto> bizIEquipmentAgentAgreementListDtos = new ArrayList<>();
        List<BizIEquipmentAgentAgreementList> bizIEquipmentAgentAgreementLists = bizIEquipmentAgentAgreementListMapper.getList(bizIEquipmentAgentAgreementList);
        if (CollectionUtils.isNotEmpty(bizIEquipmentAgentAgreementLists)) {
            bizIEquipmentAgentAgreementListDtos = bizIEquipmentAgentAgreementLists.stream().map(item -> {
                BizIEquipmentAgentAgreementListDto dto = bizIEquipmentAgentAgreementListDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIEquipmentAgentAgreementListDtos;
    }
    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public BizIEquipmentAgentAgreementListDto insert(BizIEquipmentAgentAgreementListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        BizIEquipmentAgentAgreementList bizIEquipmentAgentAgreementList = bizIEquipmentAgentAgreementListDtoMapper.toPo(model);
        bizIEquipmentAgentAgreementList.setId(sid);
        bizIEquipmentAgentAgreementList.setCreateBy(userInfo.getUserNo());
        bizIEquipmentAgentAgreementList.setCreateTime(new Date());
        int insertStatus = bizIEquipmentAgentAgreementListMapper.insert(bizIEquipmentAgentAgreementList);
       return insertStatus > 0 ? bizIEquipmentAgentAgreementListDtoMapper.toDto(bizIEquipmentAgentAgreementList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIEquipmentAgentAgreementListParam
     * @param userInfo
     * @return
     */
    public BizIEquipmentAgentAgreementListDto update(BizIEquipmentAgentAgreementListParam bizIEquipmentAgentAgreementListParam, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreementList bizIEquipmentAgentAgreementList = bizIEquipmentAgentAgreementListMapper.selectByPrimaryKey(bizIEquipmentAgentAgreementListParam.getSid());
        bizIEquipmentAgentAgreementListDtoMapper.updatePo(bizIEquipmentAgentAgreementListParam, bizIEquipmentAgentAgreementList);
        bizIEquipmentAgentAgreementList.setUpdateBy(userInfo.getUserNo());
        bizIEquipmentAgentAgreementList.setUpdateTime(new Date());
        if (bizIEquipmentAgentAgreementList.getUnitPrice() != null && bizIEquipmentAgentAgreementList.getQuantity() != null) {
            bizIEquipmentAgentAgreementList.setTotalAmount(bizIEquipmentAgentAgreementList.getUnitPrice().multiply(bizIEquipmentAgentAgreementList.getQuantity()));
        }
        // 更新数据
        int update = bizIEquipmentAgentAgreementListMapper.updateByPrimaryKey(bizIEquipmentAgentAgreementList);
         return update > 0 ? bizIEquipmentAgentAgreementListDtoMapper.toDto(bizIEquipmentAgentAgreementList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        bizIEquipmentAgentAgreementListMapper.deleteBySids(sids);
    }
	public int getListNumByHeadIds(List<String> sids) {
        return bizIEquipmentAgentAgreementListMapper.getListNumByHeadIds(sids);
    }

    public ResultObject<BizIContractListDto> getContractTotal(BizIEquipmentAgentAgreementListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");

        BizIEquipmentAgentAgreementList bizIEquipmentAgentAgreementList = bizIEquipmentAgentAgreementListDtoMapper.toPo(param);
        bizIEquipmentAgentAgreementList.setTradeCode(userInfo.getCompany());
        BizIListTotal bizIListTotal = bizIEquipmentAgentAgreementListMapper.getContractTotal(bizIEquipmentAgentAgreementList);
        resultObject.setData(bizIListTotal);
        return resultObject;
    }
}
