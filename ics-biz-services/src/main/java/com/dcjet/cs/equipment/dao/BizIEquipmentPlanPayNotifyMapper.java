package com.dcjet.cs.equipment.dao;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify;
import com.dcjet.cs.params.model.TransCode;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
/**
* generated by Generate dcits
* BizIEquipmentPlanPayNotify
* <AUTHOR>
* @date: 2025-7-8
*/
public interface BizIEquipmentPlanPayNotifyMapper extends Mapper<BizIEquipmentPlanPayNotify> {
    /**
     * 根据参数查询
     *
     * @param bizIEquipmentPlanPayNotify
     * @return
     */
    List<BizIEquipmentPlanPayNotify> getList(BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据表头headId批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);
    /**
     * 根据表头headId查询是否存在表体数据
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);

    /**
     * 根据表头headId获取最大序号
     * @param headId
     * @return
     */
    Integer getMaxSerialNoByHeadId(String headId);

    BizIEquipmentPlanPayNotify getTransMes(@Param("businessType") String businessType, @Param("tradeCode")String tradeCode);
    BigDecimal getContainerMes(@Param("businessType") String businessType,@Param("containerType") String containerType, @Param("tradeCode")String tradeCode);
    List<String> getContainerList(@Param("businessType") String businessType, @Param("tradeCode")String tradeCode);

    BigDecimal getDetailTotalAmount(String headId);
    BigDecimal getExchangeRate(String Id);

    BizIEquipmentPlanPayNotify getMesForPrint(String Id);
}
