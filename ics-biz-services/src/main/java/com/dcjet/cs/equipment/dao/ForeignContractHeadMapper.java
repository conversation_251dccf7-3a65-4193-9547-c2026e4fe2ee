package com.dcjet.cs.equipment.dao;

import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ForeignContractHeadMapper extends Mapper<ForeignContractHead> {
    /**
     * 获取列表
     *
     * @param foreignContractHead 外商合同表头
     * @return 外商合同表头列表
     */
    List<ForeignContractHead> getList(ForeignContractHead foreignContractHead);

    List<ForeignContractHead> getContractForPlan(ForeignContractHead foreignContractHead);

    /**
     * 根据主键列表获取数据
     *
     * @param ids 主键列表
     * @return 外商合同表头列表
     */
    List<ForeignContractHead> getByIds(@Param("ids") List<String> ids);

    /**
     * 获取所有制单人
     *
     * @param tradeCode 企业编码
     * @return 所有制单人
     */
    List<ForeignContractHead> getAllMakers(@Param("tradeCode") String tradeCode);

    /**
     * 根据主键列表删除
     *
     * @param ids 主键列表
     */
    void deleteByIds(@Param("ids") List<String> ids);

    /**
     * 根据主键确定
     *
     * @param id       主键
     * @param userNo   用户名
     * @param userName 用户名称
     */
    void confirmById(@Param("id") String id, @Param("userNo") String userNo, @Param("userName") String userName);

    /**
     * 根据主键作废
     *
     * @param id       主键
     * @param userNo   用户名
     * @param userName 用户名称
     */
    void invalidateById(@Param("id") String id, @Param("userNo") String userNo, @Param("userName") String userName);

    /**
     * 根据合同号计数
     *
     * @param contractNo 合同号
     * @param tradeCode  企业编码
     * @return 外商合同表头数量
     */
    int getCountByContractNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据合同号获取列表
     *
     * @param contractNo 合同号
     * @param tradeCode  企业编码
     * @return 外商合同表头列表
     */
    List<ForeignContractHead> getByContractNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 查询最大版本合同
     *
     * @param contractNo 合同编号
     * @param tradeCode  企业编码
     * @return 外商合同表头
     */
    ForeignContractHead getMaxVersionContract(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据合同号作废外商合同表头
     *
     * @param contractNo   合同编号
     * @param updateBy     更新人
     * @param updateByName 更新人名称
     * @param tradeCode    企业编码
     */
    void invalidateByContractNo(@Param("contractNo") String contractNo, @Param("updateBy") String updateBy,
                                @Param("updateByName") String updateByName, @Param("tradeCode") String tradeCode);

    ForeignContractHead selectByContractNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 获取工作流列表
     *
     * @param ids 主键列表
     * @return 工作流参数列表
     */
    List<WorkFlowParam> getFlowList(@Param("ids") List<String> ids);

    /**
     * 获取aeo列表
     *
     * @param contractHead 合同表头
     * @return 外商合同表头列表
     */
    List<ForeignContractHead> getAeoList(ForeignContractHead contractHead);
}
