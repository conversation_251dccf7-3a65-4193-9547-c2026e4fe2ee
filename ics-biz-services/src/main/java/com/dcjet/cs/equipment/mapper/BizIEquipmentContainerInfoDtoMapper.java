package com.dcjet.cs.equipment.mapper;
import com.dcjet.cs.dto.equipment.*;
import com.dcjet.cs.equipment.model.BizIEquipmentContainerInfo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIEquipmentContainerInfoDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIEquipmentContainerInfoDto toDto(BizIEquipmentContainerInfo po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIEquipmentContainerInfo toPo(BizIEquipmentContainerInfoParam param);
    /**
     * 数据库原始数据更新
     * @param bizIEquipmentContainerInfoParam
     * @param bizIEquipmentContainerInfo
     */
    void updatePo(BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, @MappingTarget BizIEquipmentContainerInfo bizIEquipmentContainerInfo);
    default void patchPo(BizIEquipmentContainerInfoParam bizIEquipmentContainerInfoParam, BizIEquipmentContainerInfo bizIEquipmentContainerInfo) {
        // TODO 自行实现局部更新
    }
}
