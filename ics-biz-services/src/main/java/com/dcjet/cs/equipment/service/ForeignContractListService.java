package com.dcjet.cs.equipment.service;

import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.dto.equipment.*;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.dao.ForeignContractListMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractListDtoMapper;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.equipment.model.ForeignContractList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ForeignContractListService extends BaseService<ForeignContractList> {
    @Resource
    private ForeignContractListMapper foreignContractListMapper;
    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    @Resource
    private ForeignContractListDtoMapper foreignContractListDtoMapper;
    @Lazy
    @Resource
    private ForeignContractHeadService foreignContractHeadService;

    @Override
    public Mapper<ForeignContractList> getMapper() {
        return this.foreignContractListMapper;
    }

    /**
     * 获取新增表体物料信息
     *
     * @param userInfo 用户信息
     * @return 物料信息列表
     */
    public List<ForeignContractAddBodyMaterialDto> getMaterialInfoForAddBody(UserInfoToken<?> userInfo) {
        List<BizMaterialInformation> materialInfoList = this.bizMaterialInformationMapper.getMatForContract(
                CommonEnum.COMMON_BUSINESS_TYPE_ENUM.STATE_TRADE_IMPORT_EQUIPMENT.getType(), userInfo.getCompany());
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return Collections.emptyList();
        }
        return materialInfoList.stream().map(mat ->
                new ForeignContractAddBodyMaterialDto()
                        .setId(mat.getSid())
                        .setFullEnName(mat.getFullEnName())
                        .setSupplier(mat.getSupplierCode())
                        .setMerchandiseCategories(mat.getMerchandiseCategories())
                        .setGName(mat.getGname())
        ).collect(Collectors.toList());
    }

    /**
     * 新增表体
     *
     * @param addBodyParam 表体新增参数
     * @param userInfo     用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBody(ForeignContractAddBodyParam addBodyParam, UserInfoToken<?> userInfo) {
        String headId = addBodyParam.getHeadId();
        if (!StringUtils.isNotBlank(headId)) {
            throw new ErrorException(500, "表头id不能为空");
        }
        List<String> materialIds = addBodyParam.getMaterialIds();
        if (CollectionUtils.isEmpty(materialIds)) {
            throw new ErrorException(500, "请选择物料");
        }
        ForeignContractHead contractHead = this.foreignContractHeadMapper.selectByPrimaryKey(headId);
        if (Objects.isNull(contractHead)) {
            throw new ErrorException(500, "对应表头数据不存在");
        }
        if (!(Objects.equals(CommonEnum.STATE_ENUM.DRAFT.getType(), contractHead.getDataStatus()))) {
            throw new ErrorException(500, "表头仅编制状态支持新增表体");
        }
        List<BizMaterialInformation> materialList = this.bizMaterialInformationMapper.getByIds(materialIds);
        if (CollectionUtils.isEmpty(materialList)) {
            return;
        }
        List<ForeignContractList> bodyList = new ArrayList<>(materialList.size());
        for (BizMaterialInformation material : materialList) {
            ForeignContractList body = new ForeignContractList();
            body.setId(UUID.randomUUID().toString()); // 主键
            body.setTradeCode(userInfo.getCompany()); // 企业编码
            body.setCreateBy(userInfo.getUserNo()); //  创建人
            body.setCreateByName(userInfo.getUserName()); // 创建人名称
            body.setCreateTime(new Date()); // 创建时间
            body.setHeadId(headId); // 表头主键
            body.setGName(material.getGname()); // 商品名称
            body.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType()); // 数据状态
            bodyList.add(body);
        }
        BulkSqlOpt.batchInsertAndThrowException(bodyList, ForeignContractListMapper.class);
    }

    /**
     * 获取分页列表
     *
     * @param foreignContractListParam 表体参数
     * @param pageParam                分页参数
     * @param userInfo                 用户信息
     * @return 表体分页列表信息
     */
    public ResultObject<List<ForeignContractListDto>> getListPaged(ForeignContractListParam foreignContractListParam,
                                                                   PageParam pageParam, UserInfoToken<?> userInfo) {
        ForeignContractList foreignContractList = this.foreignContractListDtoMapper.toPo(foreignContractListParam);
        foreignContractList.setTradeCode(userInfo.getCompany());
        Page<ForeignContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.foreignContractListMapper.getList(foreignContractList));
        List<ForeignContractListDto> listDtoList = page.getResult().stream()
                .map(fcl -> this.foreignContractListDtoMapper.toDto(fcl))
                .collect(Collectors.toList());
        return ResultObject.createInstance(listDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 表体汇总
     *
     * @param foreignContractListParam 表体参数
     * @param userInfo                 用户信息
     * @return 表体汇总信息
     */
    public ForeignContractListSummaryDto getListSummary(ForeignContractListParam foreignContractListParam,
                                                        UserInfoToken<?> userInfo) {
        ForeignContractList contractBody = this.foreignContractListDtoMapper.toPo(foreignContractListParam);
        contractBody.setTradeCode(userInfo.getCompany());
        return this.foreignContractListMapper.getSummaryInfo(contractBody);
    }

    /**
     * 更新
     *
     * @param foreignContractListParam 表体参数
     * @param userInfo                 用户信息
     * @return 表体信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ForeignContractListDto updateBody(ForeignContractListParam foreignContractListParam, UserInfoToken<?> userInfo) {
        String id = foreignContractListParam.getId();
        ForeignContractList foreignContractList = this.foreignContractListMapper.selectByPrimaryKey(id);
        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(foreignContractList.getHeadId());
        BigDecimal dollarRate = this.foreignContractHeadService.getDollarRate(foreignContractHead.getCurr(), userInfo);
        foreignContractList.setUpdateBy(userInfo.getUserNo());
        foreignContractList.setUpdateByName(userInfo.getUserName());
        foreignContractList.setUpdateTime(new Date());
        foreignContractList.setGModel(foreignContractListParam.getGModel());
        foreignContractList.setUnit(foreignContractListParam.getUnit());
        foreignContractList.setUnitPrice(foreignContractListParam.getUnitPrice());
        foreignContractList.setQty(foreignContractListParam.getQty());
        BigDecimal unitPrice = foreignContractList.getUnitPrice(), qty = foreignContractList.getQty();
        if (Objects.nonNull(unitPrice) && Objects.nonNull(qty)) {
            foreignContractList.setMoneyAmount(unitPrice.multiply(qty).setScale(4, RoundingMode.HALF_UP));
            if (dollarRate != null) {
                BigDecimal moneyAmount = foreignContractList.getMoneyAmount();
                foreignContractList.setConvertedTotalDollars(moneyAmount.multiply(dollarRate)
                        .setScale(4, RoundingMode.HALF_UP));
            } else {
                foreignContractList.setConvertedTotalDollars(null);
            }
        }
        if (Objects.isNull(unitPrice) || Objects.isNull(qty)) {
            foreignContractList.setMoneyAmount(null);
            foreignContractList.setConvertedTotalDollars(null);
        }
        foreignContractList.setNote(foreignContractListParam.getNote());
        foreignContractList.setDeliveryDate(foreignContractListParam.getDeliveryDate());
        return this.foreignContractListMapper.updateByPrimaryKey(foreignContractList) > 0 ?
                this.foreignContractListDtoMapper.toDto(foreignContractList) : null;
    }

    /**
     * 删除
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> ids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        this.foreignContractListMapper.deleteByIds(ids);
    }
}