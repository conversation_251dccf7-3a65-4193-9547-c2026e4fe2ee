<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.equipment.dao.BizIEquipmentPlanHeadMapper">
    <resultMap id="bizIEquipmentPlanHeadResultMap" type="com.dcjet.cs.equipment.model.BizIEquipmentPlanHead">
		<id column="ID" property="id" jdbcType="VARCHAR" />
		<result column="PLAN_NO" property="planNo" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR" />
		<result column="BUSINESS_LOCATION" property="businessLocation" jdbcType="VARCHAR" />
		<result column="BUYER" property="buyer" jdbcType="VARCHAR" />
		<result column="SELLER" property="seller" jdbcType="VARCHAR" />
		<result column="MANUFACTURER" property="manufacturer" jdbcType="VARCHAR" />
		<result column="DOMESTIC_CLIENT" property="domesticClient" jdbcType="VARCHAR" />
		<result column="EST_RECEIVE_DATE" property="estReceiveDate" jdbcType="TIMESTAMP" />
		<result column="RECEIVE_STATUS" property="receiveStatus" jdbcType="VARCHAR" />
		<result column="EST_PAYMENT_DATE" property="estPaymentDate" jdbcType="TIMESTAMP" />
		<result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="VARCHAR" />
		<result column="EST_ARBITRATION_DATE" property="estArbitrationDate" jdbcType="TIMESTAMP" />
		<result column="ARBITRATION_STATUS" property="arbitrationStatus" jdbcType="VARCHAR" />
		<result column="EST_LICENSE_DATE" property="estLicenseDate" jdbcType="TIMESTAMP" />
		<result column="LICENSE_STATUS" property="licenseStatus" jdbcType="VARCHAR" />
		<result column="EST_TRANSPORT_CERT_DATE" property="estTransportCertDate" jdbcType="TIMESTAMP" />
		<result column="TRANSPORT_CERT_STATUS" property="transportCertStatus" jdbcType="VARCHAR" />
		<result column="EST_INSURANCE_DATE" property="estInsuranceDate" jdbcType="TIMESTAMP" />
		<result column="INSURANCE_STATUS" property="insuranceStatus" jdbcType="VARCHAR" />
		<result column="ENTRY_STATUS" property="entryStatus" jdbcType="VARCHAR" />
		<result column="EST_PACKING_INFO" property="estPackingInfo" jdbcType="VARCHAR" />
		<result column="LICENSE_NO" property="licenseNo" jdbcType="VARCHAR" />
		<result column="LICENSE_APPLY_DATE" property="licenseApplyDate" jdbcType="TIMESTAMP" />
		<result column="LICENSE_VALIDITY_DATE" property="licenseValidityDate" jdbcType="TIMESTAMP" />
		<result column="LICENSE_REMARK" property="licenseRemark" jdbcType="VARCHAR" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR" />
		<result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="PARENT_ID" property="parentId" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="insertTime" property="insertTime" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List" >
     ID
     ,PLAN_NO
     ,BUSINESS_TYPE
     ,CONTRACT_NO
     ,BUSINESS_LOCATION
     ,BUYER
     ,SELLER
     ,MANUFACTURER
     ,DOMESTIC_CLIENT
     ,EST_RECEIVE_DATE
     ,RECEIVE_STATUS
     ,EST_PAYMENT_DATE
     ,PAYMENT_STATUS
     ,EST_ARBITRATION_DATE
     ,ARBITRATION_STATUS
     ,EST_LICENSE_DATE
     ,LICENSE_STATUS
     ,EST_TRANSPORT_CERT_DATE
     ,TRANSPORT_CERT_STATUS
     ,EST_INSURANCE_DATE
     ,INSURANCE_STATUS
     ,ENTRY_STATUS
     ,EST_PACKING_INFO
     ,LICENSE_NO
     ,LICENSE_APPLY_DATE
     ,LICENSE_VALIDITY_DATE
     ,LICENSE_REMARK
     ,REMARK
     ,STATUS
     ,APPR_STATUS
     ,CONFIRM_TIME
     ,VERSION_NO
     ,TRADE_CODE
     ,SYS_ORG_CODE
     ,PARENT_ID
     ,CREATE_BY
     ,CREATE_TIME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,CREATE_USER_NAME
     ,UPDATE_USER_NAME
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
     ,COALESCE(t.UPDATE_USER_NAME,t.CREATE_USER_NAME) as createUserName
     ,COALESCE(t.UPDATE_TIME,t.create_time) as insertTime
    </sql>
    <sql id="condition">
    <if test="planNo != null and planNo != ''">
	  and PLAN_NO like '%'|| #{planNo} || '%'
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and CONTRACT_NO like '%'|| #{contractNo} || '%'
	</if>
    <if test="buyer != null and buyer != ''">
		and BUYER = #{buyer}
	</if>
    <if test="seller != null and seller != ''">
		and SELLER = #{seller}
	</if>
    <if test="status == null or status == ''">
            and STATUS !='2'
    </if>
    <if test="status != null and status != ''">
            and status = #{status}
    </if>
    <if test="createBy != null and createBy != ''">
	  and CREATE_BY like '%'|| #{createBy} || '%'
	</if>

    <if test=" createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= to_date(#{createTimeFrom}, 'yyyy-MM-dd hh24:mi:ss') ]]>
    </if>
    <if test=" createTimeTo != null and createTimeTo != ''">
        <![CDATA[ and coalesce(t.update_time,t.create_time)  <= DATEADD(DAY, 1, to_date(#{createTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
    </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIEquipmentPlanHeadResultMap" parameterType="com.dcjet.cs.equipment.model.BizIEquipmentPlanHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_I_EQUIPMENT_PLAN_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_EQUIPMENT_PLAN_HEAD t where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        ;
        delete from T_BIZ_I_EQUIPMENT_PLAN_LIST t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="checkCanDelBySids" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_EQUIPMENT_PLAN_HEAD t where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and t.status != '0'
    </select>

    <select id="getReceiveWaringPlan" resultMap="bizIEquipmentPlanHeadResultMap" >
        select
            PLAN_NO,CONTRACT_NO
        from T_BIZ_I_EQUIPMENT_PLAN_HEAD
        where EST_RECEIVE_DATE is not null and RECEIVE_STATUS ='0' and TRADE_CODE=#{tradeCode}
        group by PLAN_NO,CONTRACT_NO;
    </select>
    <select id="getPaymentWaringPlan" resultMap="bizIEquipmentPlanHeadResultMap" >
        select
            PLAN_NO,CONTRACT_NO
        from T_BIZ_I_EQUIPMENT_PLAN_HEAD
        where EST_PAYMENT_DATE is not null and PAYMENT_STATUS ='0' and TRADE_CODE=#{tradeCode}
        group by PLAN_NO,CONTRACT_NO;
    </select>
    <select id="getArbitrationWaringPlan" resultMap="bizIEquipmentPlanHeadResultMap" >
        select
            PLAN_NO,CONTRACT_NO
        from T_BIZ_I_EQUIPMENT_PLAN_HEAD
        where EST_ARBITRATION_DATE is not null and ARBITRATION_STATUS ='0' and TRADE_CODE=#{tradeCode}
        group by PLAN_NO,CONTRACT_NO;
    </select>
    <select id="getLicenseWaringPlan" resultMap="bizIEquipmentPlanHeadResultMap" >
        select
            PLAN_NO,CONTRACT_NO
        from T_BIZ_I_EQUIPMENT_PLAN_HEAD
        where EST_LICENSE_DATE is not null and LICENSE_STATUS ='0' and TRADE_CODE=#{tradeCode}
        group by PLAN_NO,CONTRACT_NO;
    </select>
    <select id="getTransportWaringPlan" resultMap="bizIEquipmentPlanHeadResultMap" >
        select
            PLAN_NO,CONTRACT_NO
        from T_BIZ_I_EQUIPMENT_PLAN_HEAD
        where EST_TRANSPORT_CERT_DATE is not null and TRANSPORT_CERT_STATUS ='0' and TRADE_CODE=#{tradeCode}
        group by PLAN_NO,CONTRACT_NO;
    </select>
    <select id="getInsuranceWaringPlan" resultMap="bizIEquipmentPlanHeadResultMap" >
        select
            PLAN_NO,CONTRACT_NO
        from T_BIZ_I_EQUIPMENT_PLAN_HEAD
        where EST_INSURANCE_DATE is not null and INSURANCE_STATUS ='0' and TRADE_CODE=#{tradeCode}
        group by PLAN_NO,CONTRACT_NO;

    </select>
</mapper>
