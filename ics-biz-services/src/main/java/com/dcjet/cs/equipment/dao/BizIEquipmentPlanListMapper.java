package com.dcjet.cs.equipment.dao;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;
/**
* generated by Generate dcits
* BizIEquipmentPlanList
* <AUTHOR>
* @date: 2025-7-8
*/
public interface BizIEquipmentPlanListMapper extends Mapper<BizIEquipmentPlanList> {
    /**
     * 根据参数查询
     *
     * @param bizIEquipmentPlanList
     * @return
     */
    List<BizIEquipmentPlanList> getList(BizIEquipmentPlanList bizIEquipmentPlanList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据表头headId批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);
    /**
     * 根据表头headId查询是否存在表体数据
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);

    /**
     * 根据外商合同号列表生成计划表体数据
     * @param forContractIdList 外商合同号列表
     * @param headId 计划表头ID
     * @param userNo 用户编号
     * @param userName 用户名称
     */
    void insertByContractList(@Param("forContractIdList") List<String> forContractIdList, @Param("headId") String headId, @Param("userNo") String userNo, @Param("userName") String userName);

    /**
     * 复制表体数据
     * @param oldHeadId 原表头ID
     * @param headId 新表头ID
     * @param userNo 用户编号
     * @param userName 用户名称
     */
    void copyListByHeadId(@Param("oldHeadId") String oldHeadId, @Param("headId") String headId, @Param("userNo") String userNo, @Param("userName") String userName);
}
