package com.dcjet.cs.equipment.service;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListExportParam;
import com.dcjet.cs.equipment.dao.BizIEquipmentPlanListMapper;
import com.dcjet.cs.equipment.mapper.BizIEquipmentPlanListDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanList;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Service
public class BizIEquipmentPlanListService extends BaseService<BizIEquipmentPlanList> {
    @Resource
    private BizIEquipmentPlanListMapper bizIEquipmentPlanListMapper;
    @Resource
    private BizIEquipmentPlanListDtoMapper bizIEquipmentPlanListDtoMapper;
    @Override
    public Mapper<BizIEquipmentPlanList> getMapper() {
        return bizIEquipmentPlanListMapper;
    }
    /**
     * 功能描述: grid分页查询
     *
     * @param bizIEquipmentPlanListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIEquipmentPlanListDto>> selectAllPaged(BizIEquipmentPlanListParam bizIEquipmentPlanListParam, PageParam pageParam) {
        // 启用分页查询
        BizIEquipmentPlanList bizIEquipmentPlanList = bizIEquipmentPlanListDtoMapper.toPo(bizIEquipmentPlanListParam);
        Page<BizIEquipmentPlanList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentPlanListMapper.getList(bizIEquipmentPlanList));
        List<BizIEquipmentPlanListDto> bizIEquipmentPlanListDtos = page.getResult().stream().map(head -> {
            BizIEquipmentPlanListDto dto = bizIEquipmentPlanListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<BizIEquipmentPlanListDto>> paged = ResultObject.createInstance(bizIEquipmentPlanListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<BizIEquipmentPlanListDto> selectAll(BizIEquipmentPlanListParam exportParam) {
        BizIEquipmentPlanList bizIEquipmentPlanList = bizIEquipmentPlanListDtoMapper.toPo(exportParam);
        List<BizIEquipmentPlanListDto> bizIEquipmentPlanListDtos = new ArrayList<>();
        List<BizIEquipmentPlanList> bizIEquipmentPlanLists = bizIEquipmentPlanListMapper.getList(bizIEquipmentPlanList);
        if (CollectionUtils.isNotEmpty(bizIEquipmentPlanLists)) {
            bizIEquipmentPlanListDtos = bizIEquipmentPlanLists.stream().map(item -> {
                BizIEquipmentPlanListDto dto = bizIEquipmentPlanListDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIEquipmentPlanListDtos;
    }
    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public BizIEquipmentPlanListDto insert(BizIEquipmentPlanListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        BizIEquipmentPlanList bizIEquipmentPlanList = bizIEquipmentPlanListDtoMapper.toPo(model);
        bizIEquipmentPlanList.setId(sid);
        bizIEquipmentPlanList.setCreateBy(userInfo.getUserNo());
        bizIEquipmentPlanList.setCreateTime(new Date());
        int insertStatus = bizIEquipmentPlanListMapper.insert(bizIEquipmentPlanList);
       return insertStatus > 0 ? bizIEquipmentPlanListDtoMapper.toDto(bizIEquipmentPlanList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIEquipmentPlanListParam
     * @param userInfo
     * @return
     */
    public BizIEquipmentPlanListDto update(BizIEquipmentPlanListParam bizIEquipmentPlanListParam, UserInfoToken userInfo) {
        BizIEquipmentPlanList bizIEquipmentPlanList = bizIEquipmentPlanListMapper.selectByPrimaryKey(bizIEquipmentPlanListParam.getId());
        bizIEquipmentPlanListDtoMapper.updatePo(bizIEquipmentPlanListParam, bizIEquipmentPlanList);
        bizIEquipmentPlanList.setUpdateBy(userInfo.getUserNo());
        bizIEquipmentPlanList.setUpdateTime(new Date());
        // 更新数据
        int update = bizIEquipmentPlanListMapper.updateByPrimaryKey(bizIEquipmentPlanList);
         return update > 0 ? bizIEquipmentPlanListDtoMapper.toDto(bizIEquipmentPlanList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        bizIEquipmentPlanListMapper.deleteBySids(sids);
    }
	public int getListNumByHeadIds(List<String> sids) {
        return bizIEquipmentPlanListMapper.getListNumByHeadIds(sids);
    }
}
