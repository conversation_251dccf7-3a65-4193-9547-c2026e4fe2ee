package com.dcjet.cs.equipment.dao;
import com.dcjet.cs.equipment.model.BizIEquipmentContainerInfo;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIEquipmentContainerInfo
* <AUTHOR>
* @date: 2025-7-8
*/
public interface BizIEquipmentContainerInfoMapper extends Mapper<BizIEquipmentContainerInfo> {
    /**
     * 查询获取数据
     * @param bizIEquipmentContainerInfo
     * @return
     */
    List<BizIEquipmentContainerInfo> getList(BizIEquipmentContainerInfo bizIEquipmentContainerInfo);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int deleteByNotify(String sids);
}
