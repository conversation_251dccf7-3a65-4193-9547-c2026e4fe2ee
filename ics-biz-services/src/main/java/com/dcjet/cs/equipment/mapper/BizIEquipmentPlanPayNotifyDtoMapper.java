package com.dcjet.cs.equipment.mapper;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyParam;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIEquipmentPlanPayNotifyDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    BizIEquipmentPlanPayNotifyDto toDto(BizIEquipmentPlanPayNotify po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIEquipmentPlanPayNotify toPo(BizIEquipmentPlanPayNotifyParam param);
    void updatePo(BizIEquipmentPlanPayNotifyParam bizIEquipmentPlanPayNotifyParam, @MappingTarget BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify);
}
