package com.dcjet.cs.equipment.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter
@Getter
public class BizIEquipmentWarningPlans implements Serializable {
    private static final long serialVersionUID = 1L;
	private String hasWarningPlans;
	List<BizIEquipmentPlanHead> receiveWaringPlan;
	List<BizIEquipmentPlanHead> paymentWaringPlan;
	List<BizIEquipmentPlanHead> arbitrationWaringPlan;
	List<BizIEquipmentPlanHead> licenseWaringPlan;
	List<BizIEquipmentPlanHead> transportWaringPlan;
	List<BizIEquipmentPlanHead> insuranceWaringPlan;
}
