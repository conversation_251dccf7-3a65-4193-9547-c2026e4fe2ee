package com.dcjet.cs.equipment.mapper;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanListParam;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIEquipmentPlanListDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    BizIEquipmentPlanListDto toDto(BizIEquipmentPlanList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIEquipmentPlanList toPo(BizIEquipmentPlanListParam param);
    void updatePo(BizIEquipmentPlanListParam bizIEquipmentPlanListParam, @MappingTarget BizIEquipmentPlanList bizIEquipmentPlanList);
}
