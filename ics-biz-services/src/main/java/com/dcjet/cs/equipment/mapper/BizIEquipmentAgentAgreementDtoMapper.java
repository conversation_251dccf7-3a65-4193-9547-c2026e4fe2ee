package com.dcjet.cs.equipment.mapper;
import com.dcjet.cs.dto.equipment.*;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIEquipmentAgentAgreementDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIEquipmentAgentAgreementDto toDto(BizIEquipmentAgentAgreement po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIEquipmentAgentAgreement toPo(BizIEquipmentAgentAgreementParam param);
    /**
     * 数据库原始数据更新
     * @param bizIEquipmentAgentAgreementParam
     * @param bizIEquipmentAgentAgreement
     */
    void updatePo(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, @MappingTarget BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement);
    default void patchPo(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement) {
        // TODO 自行实现局部更新
    }
}
