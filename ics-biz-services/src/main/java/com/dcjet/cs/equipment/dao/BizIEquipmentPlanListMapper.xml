<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.equipment.dao.BizIEquipmentPlanListMapper">
    <resultMap id="bizIEquipmentPlanListResultMap" type="com.dcjet.cs.equipment.model.BizIEquipmentPlanList">
		<id column="ID" property="id" jdbcType="VARCHAR" />
		<result column="HEAD_ID" property="headId" jdbcType="VARCHAR" />
		<result column="PARENT_ID" property="parentId" jdbcType="VARCHAR" />
		<result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR" />
		<result column="PRODUCT_MODEL" property="productModel" jdbcType="VARCHAR" />
		<result column="UNIT" property="unit" jdbcType="VARCHAR" />
		<result column="QUANTITY" property="quantity" jdbcType="NUMERIC" />
		<result column="UNIT_PRICE" property="unitPrice" jdbcType="NUMERIC" />
		<result column="AMOUNT" property="amount" jdbcType="NUMERIC" />
		<result column="EST_DELIVERY_DATE" property="estDeliveryDate" jdbcType="TIMESTAMP" />
		<result column="REMARK" property="remark" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     ID
     ,HEAD_ID
     ,PARENT_ID
     ,PRODUCT_NAME
     ,PRODUCT_MODEL
     ,UNIT
     ,QUANTITY
     ,UNIT_PRICE
     ,AMOUNT
     ,EST_DELIVERY_DATE
     ,REMARK
     ,TRADE_CODE
     ,SYS_ORG_CODE
     ,CREATE_BY
     ,CREATE_TIME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,CREATE_USER_NAME
     ,UPDATE_USER_NAME
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
    </sql>
	<sql id="condition">
	<if test="headId != null and headId != ''">
		  HEAD_ID = #{headId}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIEquipmentPlanListResultMap" parameterType="com.dcjet.cs.equipment.model.BizIEquipmentPlanList">
        select <include refid="Base_Column_List" />
        from T_BIZ_I_EQUIPMENT_PLAN_LIST t
        <where>
            <!-- 用户Grid查询 and 条件-->
			<include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_EQUIPMENT_PLAN_LIST t
		where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
              #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from T_BIZ_I_EQUIPMENT_PLAN_LIST t
		where
		t.HEAD_ID
		in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getListNumByHeadIds" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(*) from T_BIZ_I_EQUIPMENT_PLAN_LIST t where t.HEAD_ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>

    <!-- 根据外商合同号列表生成计划表体数据 -->
    <insert id="insertByContractList">
        insert into T_BIZ_I_EQUIPMENT_PLAN_LIST (
            ID,
            HEAD_ID,
            PARENT_ID,
            PRODUCT_NAME,
            PRODUCT_MODEL,
            UNIT,
            QUANTITY,
            UNIT_PRICE,
            AMOUNT,
            EST_DELIVERY_DATE,
            REMARK,
            TRADE_CODE,
            CREATE_BY,
            CREATE_TIME,
            CREATE_USER_NAME
        )
        select
            sys_guid(),
            #{headId},
            fch.ID,
            fcl.G_NAME,
            fcl.G_MODEL,
            fcl.UNIT,
            fcl.QTY,
            fcl.UNIT_PRICE,
            fcl.MONEY_AMOUNT,
            fcl.DELIVERY_DATE,
            fcl.NOTE,
            fch.TRADE_CODE,
            #{userNo},
            now(),
            #{userName}
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST fcl
        left join T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD fch on fch.ID = fcl.HEAD_ID
        where fch.ID in
        <foreach collection="forContractIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <!-- 复制表体数据 -->
    <insert id="copyListByHeadId">
        insert into T_BIZ_I_EQUIPMENT_PLAN_LIST (
            ID,
            HEAD_ID,
            PARENT_ID,
            PRODUCT_NAME,
            PRODUCT_MODEL,
            UNIT,
            QUANTITY,
            UNIT_PRICE,
            AMOUNT,
            EST_DELIVERY_DATE,
            REMARK,
            TRADE_CODE,
            CREATE_BY,
            CREATE_TIME,
            CREATE_USER_NAME
        )
        select
            sys_guid(),
            #{headId},
            PARENT_ID,
            PRODUCT_NAME,
            PRODUCT_MODEL,
            UNIT,
            QUANTITY,
            UNIT_PRICE,
            AMOUNT,
            EST_DELIVERY_DATE,
            REMARK,
            TRADE_CODE,
            #{userNo},
            now(),
            #{userName}
        from T_BIZ_I_EQUIPMENT_PLAN_LIST
        where HEAD_ID = #{oldHeadId}
    </insert>
</mapper>
