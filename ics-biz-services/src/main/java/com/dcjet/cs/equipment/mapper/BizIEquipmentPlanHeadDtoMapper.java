package com.dcjet.cs.equipment.mapper;
import com.dcjet.cs.dto.equipment.*;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIEquipmentPlanHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIEquipmentPlanHeadDto toDto(BizIEquipmentPlanHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIEquipmentPlanHead toPo(BizIEquipmentPlanHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizIEquipmentPlanHeadParam
     * @param bizIEquipmentPlanHead
     */
    void updatePo(BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, @MappingTarget BizIEquipmentPlanHead bizIEquipmentPlanHead);

    /**
     * 复制对象属性
     * @param source 源对象
     * @param target 目标对象
     */
    void copyProperties(BizIEquipmentPlanHead source, @MappingTarget BizIEquipmentPlanHead target);

    default void patchPo(BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, BizIEquipmentPlanHead bizIEquipmentPlanHead) {
        // TODO 自行实现局部更新
    }
}
