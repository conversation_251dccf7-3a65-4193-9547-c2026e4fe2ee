package com.dcjet.cs.equipment.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentPlanPayNotifyExportParam;
import com.dcjet.cs.equipment.dao.BizIEquipmentPlanPayNotifyMapper;
import com.dcjet.cs.equipment.mapper.BizIEquipmentPlanPayNotifyDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanPayNotify;
import com.dcjet.cs.params.model.TransCode;
import com.dcjet.cs.util.CommonEnum;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Service
public class BizIEquipmentPlanPayNotifyService extends BaseService<BizIEquipmentPlanPayNotify> {
    @Resource
    private BizIEquipmentPlanPayNotifyMapper bizIEquipmentPlanPayNotifyMapper;
    @Resource
    private BizIEquipmentPlanPayNotifyDtoMapper bizIEquipmentPlanPayNotifyDtoMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;

    @Resource
    private ExportService exportService;
    @Override
    public Mapper<BizIEquipmentPlanPayNotify> getMapper() {
        return bizIEquipmentPlanPayNotifyMapper;
    }
    /**
     * 功能描述: grid分页查询
     *
     * @param bizIEquipmentPlanPayNotifyParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIEquipmentPlanPayNotifyDto>> selectAllPaged(BizIEquipmentPlanPayNotifyParam bizIEquipmentPlanPayNotifyParam, PageParam pageParam) {
        // 启用分页查询
        BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify = bizIEquipmentPlanPayNotifyDtoMapper.toPo(bizIEquipmentPlanPayNotifyParam);
        Page<BizIEquipmentPlanPayNotify> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentPlanPayNotifyMapper.getList(bizIEquipmentPlanPayNotify));
        List<BizIEquipmentPlanPayNotifyDto> bizIEquipmentPlanPayNotifyDtos = page.getResult().stream().map(head -> {
            BizIEquipmentPlanPayNotifyDto dto = bizIEquipmentPlanPayNotifyDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<BizIEquipmentPlanPayNotifyDto>> paged = ResultObject.createInstance(bizIEquipmentPlanPayNotifyDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<BizIEquipmentPlanPayNotifyDto> selectAll(BizIEquipmentPlanPayNotifyParam exportParam) {
        BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify = bizIEquipmentPlanPayNotifyDtoMapper.toPo(exportParam);
        List<BizIEquipmentPlanPayNotifyDto> bizIEquipmentPlanPayNotifyDtos = new ArrayList<>();
        List<BizIEquipmentPlanPayNotify> bizIEquipmentPlanPayNotifys = bizIEquipmentPlanPayNotifyMapper.getList(bizIEquipmentPlanPayNotify);
        if (CollectionUtils.isNotEmpty(bizIEquipmentPlanPayNotifys)) {
            bizIEquipmentPlanPayNotifyDtos = bizIEquipmentPlanPayNotifys.stream().map(item -> {
                BizIEquipmentPlanPayNotifyDto dto = bizIEquipmentPlanPayNotifyDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIEquipmentPlanPayNotifyDtos;
    }
    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public BizIEquipmentPlanPayNotifyDto insert(BizIEquipmentPlanPayNotifyParam model, UserInfoToken userInfo) {

        BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify = bizIEquipmentPlanPayNotifyDtoMapper.toPo(model);
        bizIEquipmentPlanPayNotify.setCreateBy(userInfo.getUserNo());
        bizIEquipmentPlanPayNotify.setCreateTime(new Date());
        bizIEquipmentPlanPayNotify.setTradeCode(userInfo.getCompany());
        bizIEquipmentPlanPayNotify.setHeadId(model.getHeadId());

        // 自动生成序号：获取当前headId下的最大序号并加1
        if (model.getHeadId() != null && !model.getHeadId().isEmpty()) {
            Integer maxSerialNo = bizIEquipmentPlanPayNotifyMapper.getMaxSerialNoByHeadId(model.getHeadId());
            int nextSerialNo = (maxSerialNo != null ? maxSerialNo : 0) + 1;
            bizIEquipmentPlanPayNotify.setSerialNo(String.valueOf(nextSerialNo));
        }

        int insertStatus = bizIEquipmentPlanPayNotifyMapper.insert(bizIEquipmentPlanPayNotify);
       return insertStatus > 0 ? bizIEquipmentPlanPayNotifyDtoMapper.toDto(bizIEquipmentPlanPayNotify) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIEquipmentPlanPayNotifyParam
     * @param userInfo
     * @return
     */
    public BizIEquipmentPlanPayNotifyDto update(BizIEquipmentPlanPayNotifyParam bizIEquipmentPlanPayNotifyParam, UserInfoToken userInfo) {
        BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify = bizIEquipmentPlanPayNotifyMapper.selectByPrimaryKey(bizIEquipmentPlanPayNotifyParam.getId());
        bizIEquipmentPlanPayNotifyDtoMapper.updatePo(bizIEquipmentPlanPayNotifyParam, bizIEquipmentPlanPayNotify);
        bizIEquipmentPlanPayNotify.setUpdateBy(userInfo.getUserNo());
        bizIEquipmentPlanPayNotify.setUpdateTime(new Date());
        // 更新数据
        int update = bizIEquipmentPlanPayNotifyMapper.updateByPrimaryKey(bizIEquipmentPlanPayNotify);
         return update > 0 ? bizIEquipmentPlanPayNotifyDtoMapper.toDto(bizIEquipmentPlanPayNotify) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        bizIEquipmentPlanPayNotifyMapper.deleteBySids(sids);
    }

	public int getListNumByHeadIds(List<String> sids) {
        return bizIEquipmentPlanPayNotifyMapper.getListNumByHeadIds(sids);
    }

    public ResultObject getTransMes(String businessType,String headId,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String sid = UUID.randomUUID().toString();
        BigDecimal detailTotalAmount = bizIEquipmentPlanPayNotifyMapper.getDetailTotalAmount(headId);
        BigDecimal exchangeRate = bizIEquipmentPlanPayNotifyMapper.getExchangeRate(headId);
        BigDecimal baseAmount=detailTotalAmount.multiply(exchangeRate);
        BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify = bizIEquipmentPlanPayNotifyMapper.getTransMes(businessType,userInfo.getCompany());
        if (bizIEquipmentPlanPayNotify != null){
           bizIEquipmentPlanPayNotify.setContractAmount(detailTotalAmount);
           bizIEquipmentPlanPayNotify.setExchangeRate(exchangeRate);
           bizIEquipmentPlanPayNotify.setId(sid);
/*           bizIEquipmentPlanPayNotify.setHeadOfficeAgentFee(baseAmount.multiply(bizIEquipmentPlanPayNotify.getHeadOfficeAgentRate()));
           bizIEquipmentPlanPayNotify.setImportExportAgentRate(baseAmount.multiply(bizIEquipmentPlanPayNotify.getImportExportAgentRate()));
           bizIEquipmentPlanPayNotify.setInsuranceFee(baseAmount.multiply(bizIEquipmentPlanPayNotify.getInsuranceRate()).multiply(new BigDecimal("1.1")));*/
           resultObject.setData(bizIEquipmentPlanPayNotify);
        }else {
            BizIEquipmentPlanPayNotify emptyNotify = new BizIEquipmentPlanPayNotify();
            emptyNotify.setContractAmount(detailTotalAmount);
            emptyNotify.setExchangeRate(exchangeRate);
            emptyNotify.setId(sid);
            resultObject.setData(emptyNotify);
        }
        return resultObject;
    }

    /**
     * 获取箱单列表
     * @param businessType
     * @param userInfo
     * @return
     */
    public ResultObject getContainerList(String businessType,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);

        List<String> containerList = bizIEquipmentPlanPayNotifyMapper.getContainerList(businessType,userInfo.getCompany());
        resultObject.setData(containerList);

        return resultObject;
    }

    /**
     *
     * @param businessType
     * @param containerType
     * @param userInfo
     * @return
     */
    public ResultObject getContainerMes(String businessType,String containerType,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);

        BigDecimal transCode = bizIEquipmentPlanPayNotifyMapper.getContainerMes(businessType,containerType,userInfo.getCompany());
        resultObject.setData(transCode);

        return resultObject;
    }


    /**
     * 划款通知保存打印
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity printPayNotify(BizIEquipmentPlanPayNotify bizIEquipmentPlanPayNotify, UserInfoToken userInfo) throws Exception {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);

        BizIEquipmentPlanPayNotify transferNotice=bizIEquipmentPlanPayNotifyMapper.getMesForPrint(bizIEquipmentPlanPayNotify.getId());

       /* SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        transferNotice.setInsertTimeFrom("日期 "+sdf.format(new Date()));
        // 创建日期格式化对象
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy年MM月dd日");*/

        // 获取当前日期并加10天
        LocalDate futureDate = LocalDate.now().plusDays(10);

        bizIEquipmentPlanPayNotify.setExtend1("关于划拨合同 " + (transferNotice.getContractNo()==null?" ":transferNotice.getContractNo()) +" 项下向");
        bizIEquipmentPlanPayNotify.setContractNo(" 通知书编号：" + (transferNotice.getContractNo()==null?" ":transferNotice.getContractNo()));
        bizIEquipmentPlanPayNotify.setBuyer("致："+getMerchantNameSafely(bizMerchantMap,transferNotice.getBuyer())+" :");
        bizIEquipmentPlanPayNotify.setSeller(" " + getMerchantNameSafely(bizMerchantMap,transferNotice.getSeller()));
        bizIEquipmentPlanPayNotify.setProductName("定购的"+(transferNotice.getProductName()==null?" ":transferNotice.getProductName())+" 配套人民币的通知。");
        bizIEquipmentPlanPayNotify.setPaymentType("时支付"+(transferNotice.getPaymentType()==null?" ":transferNotice.getPaymentType())+
                "，请见函即将配套人民币"+ NumberFormatterUtils.formatNumber(transferNotice.getRemittanceAmountRmb())+"元，");


        String templateName = "第三线卷烟设备划款通知.xlsx";
        String finalOutName = xdoi18n.XdoI18nUtil.t("划款通知.")+bizIEquipmentPlanPayNotify.getFileType();
        String tempFileName = UUID.randomUUID() + ".xlsx";

        String exportFileName = exportService.export(
                Arrays.asList(bizIEquipmentPlanPayNotify),
                tempFileName,
                templateName
        );
       /* HttpHeaders h = new HttpHeaders();
        finalOutName = URLEncoder.encode(finalOutName, CommonVariable.UTF8);
        finalOutName = finalOutName.replaceAll("\\+", "%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(finalOutName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
       */
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        // 根据文件类型选择导出方式
        if ("pdf".equalsIgnoreCase(bizIEquipmentPlanPayNotify.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, finalOutName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(finalOutName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    /**
     * 创建商户映射表，处理重复key的情况
     * @param bizMerchants 商户列表
     * @return 商户编码到中文名称的映射
     */
    public Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }


    /**
     * 安全地获取商户中文名称
     * @param bizMerchantMap 商户映射表
     * @param merchantCode 商户编码
     * @return 商户中文名称，如果不存在则返回空字符串
     */
    public String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }


}
