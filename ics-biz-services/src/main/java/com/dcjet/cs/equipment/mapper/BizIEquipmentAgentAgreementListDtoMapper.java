package com.dcjet.cs.equipment.mapper;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListParam;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreementList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIEquipmentAgentAgreementListDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    BizIEquipmentAgentAgreementListDto toDto(BizIEquipmentAgentAgreementList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIEquipmentAgentAgreementList toPo(BizIEquipmentAgentAgreementListParam param);
    void updatePo(BizIEquipmentAgentAgreementListParam bizIEquipmentAgentAgreementListParam, @MappingTarget BizIEquipmentAgentAgreementList bizIEquipmentAgentAgreementList);
}
