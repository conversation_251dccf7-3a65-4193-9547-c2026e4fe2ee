package com.dcjet.cs.equipment.model;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Getter;
import lombok.Setter;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter
@Getter
@Table(name = "T_BIZ_I_EQUIPMENT_PLAN_PAY_NOTIFY")
public class BizIEquipmentPlanPayNotify implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 表头id
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 序号
     */
	@Column(name = "serial_no")
	private  String serialNo;
	/**
     * 款项类型（多复选框）
     */
	@Column(name = "payment_type")
	private  String paymentType;
	/**
     * 合同金额
     */
	@Column(name = "contract_amount")
	private  BigDecimal contractAmount;
	/**
     * 汇率
     */
	@Column(name = "exchange_rate")
	private  BigDecimal exchangeRate;
	/**
     * 进出口公司代理费率%
     */
	@Column(name = "import_export_agent_rate")
	private  BigDecimal importExportAgentRate;
	/**
     * 进出口公司代理费
     */
	@Column(name = "import_export_agent_fee")
	private  BigDecimal importExportAgentFee;
	/**
     * 总公司代理费率%
     */
	@Column(name = "head_office_agent_rate")
	private  BigDecimal headOfficeAgentRate;
	/**
     * 总公司代理费
     */
	@Column(name = "head_office_agent_fee")
	private  BigDecimal headOfficeAgentFee;
	/**
     * 计费箱数
     */
	@Column(name = "charge_container_count")
	private  String chargeContainerCount;
	/**
     * 通关费
     */
	@Column(name = "customs_clearance_fee")
	private  BigDecimal customsClearanceFee;
	/**
     * 验柜服务费
     */
	@Column(name = "container_inspection_fee")
	private  BigDecimal containerInspectionFee;
	/**
     * 货代费用
     */
	@Column(name = "freight_forwarding_fee")
	private  BigDecimal freightForwardingFee;
	/**
     * 保险费率
     */
	@Column(name = "insurance_rate")
	private  BigDecimal insuranceRate;
	/**
     * 保险费
     */
	@Column(name = "insurance_fee")
	private  BigDecimal insuranceFee;
	/**
     * 划款金额（RMB）
     */
	@Column(name = "remittance_amount_rmb")
	private  BigDecimal remittanceAmountRmb;
	/**
     * 备注
     */
	@Column(name = "remark")
	private  String remark;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 数据状态
     */
	@Column(name = "status")
	private  String status;
	/**
     * 版本号
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 父ID
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 创建人姓名
     */
	@Column(name = "create_user_name")
	private  String createUserName;
	/**
     * 最后修改人姓名
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 扩展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "extend10")
	private  String extend10;

	@Transient
	private String fileType;
	@Transient
	private String seller;
	@Transient
	private String buyer;
	@Transient
	private String contractNo;
	@Transient
	private String productName;
}
