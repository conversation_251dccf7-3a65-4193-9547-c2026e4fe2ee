package com.dcjet.cs.equipment.service;
import com.dcjet.cs.equipment.mapper.BizIEquipmentPlanListDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanList;
import com.dcjet.cs.equipment.model.BizIEquipmentWarningPlans;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.equipment.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.equipment.dao.BizIEquipmentPlanHeadMapper;
import com.dcjet.cs.equipment.dao.BizIEquipmentPlanListMapper;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.dao.ForeignContractListMapper;
import com.dcjet.cs.equipment.mapper.BizIEquipmentPlanHeadDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentPlanHead;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Service
public class BizIEquipmentPlanHeadService extends BaseService<BizIEquipmentPlanHead> {
    @Resource
    private BizIEquipmentPlanHeadMapper bizIEquipmentPlanHeadMapper;
    @Resource
    private BizIEquipmentPlanHeadDtoMapper bizIEquipmentPlanHeadDtoMapper;
    @Resource
    private BizIEquipmentPlanListMapper bizIEquipmentPlanListMapper;
    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;
    @Resource
    private BizIEquipmentPlanListDtoMapper bizIEquipmentPlanListDtoMapper;
    @Override
    public Mapper<BizIEquipmentPlanHead> getMapper() {
        return bizIEquipmentPlanHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIEquipmentPlanHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIEquipmentPlanHeadDto>> getListPaged(BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizIEquipmentPlanHead bizIEquipmentPlanHead = bizIEquipmentPlanHeadDtoMapper.toPo(bizIEquipmentPlanHeadParam);
        Page<BizIEquipmentPlanHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentPlanHeadMapper.getList(bizIEquipmentPlanHead));
        List<BizIEquipmentPlanHeadDto> bizIEquipmentPlanHeadDtos = page.getResult().stream().map(head -> {
            BizIEquipmentPlanHeadDto dto = bizIEquipmentPlanHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIEquipmentPlanHeadDto>> paged = ResultObject.createInstance(bizIEquipmentPlanHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIEquipmentPlanHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentPlanHeadDto insert(BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, UserInfoToken userInfo) {
        BizIEquipmentPlanHead bizIEquipmentPlanHead = bizIEquipmentPlanHeadDtoMapper.toPo(bizIEquipmentPlanHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIEquipmentPlanHead.setId(sid);
        bizIEquipmentPlanHead.setCreateBy(userInfo.getUserNo());
        bizIEquipmentPlanHead.setCreateTime(new Date());
        bizIEquipmentPlanHead.setTradeCode(userInfo.getCompany());
        bizIEquipmentPlanHead.setCreateUserName(userInfo.getUserName());

        // 设置表头字段默认值
        bizIEquipmentPlanHead.setBusinessType(CommonEnum.businessTypeEnum.type_3.getCode()); // 3-国营贸易进口烟机设备
//        bizIEquipmentPlanHead.setBusinessLocation("0"); // 默认为0上海业务
        bizIEquipmentPlanHead.setVersionNo("1"); // 初始默认为版本1
        bizIEquipmentPlanHead.setStatus(CommonEnum.STATE_ENUM.DRAFT.getType()); // 0编制
        bizIEquipmentPlanHead.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue()); // 0不涉及审批
        bizIEquipmentPlanHead.setEntryStatus(CommonEnum.ENTRY_STATUS_ENUM.NOT_DECLARED.getType());
        bizIEquipmentPlanHead.setReceiveStatus(CommonEnum.RECEIVE_STATUS_ENUM.UNCOLLECTED_PAYMENT.getType());//收款状态
        bizIEquipmentPlanHead.setPaymentStatus(CommonEnum.PAYMENT_STATUS_ENUM.NOT_PAYED.getType());//付款状态
        bizIEquipmentPlanHead.setArbitrationStatus(CommonEnum.ARBITRATION_STATUS_ENUM.NOT_DETERMINED.getType());//预裁定状态
        bizIEquipmentPlanHead.setLicenseStatus(CommonEnum.LICENSE_STATUS_ENUM.NOT_PROCESSED.getType());//许可证状态
        bizIEquipmentPlanHead.setInsuranceStatus(CommonEnum.INSURANCE_STATUS_ENUM.NOT_PROCESSED.getType());//保险状态
        bizIEquipmentPlanHead.setTransportCertStatus(CommonEnum.TRANSPORT_CERT_STATUS_ENUM.NOT_PROCESSED.getType());//准运证状态

        List<String> forContractIdList = bizIEquipmentPlanHeadParam.getForContractIdList();

        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(forContractIdList.get(0));
        if (foreignContractHead!= null){
            bizIEquipmentPlanHead.setContractNo(foreignContractHead.getContractNo());
            bizIEquipmentPlanHead.setBusinessLocation(foreignContractHead.getBusinessPlace());
            bizIEquipmentPlanHead.setBuyer(foreignContractHead.getBuyer());
            bizIEquipmentPlanHead.setSeller(foreignContractHead.getSeller());
            bizIEquipmentPlanHead.setManufacturer(foreignContractHead.getUsingManufacturer());
            bizIEquipmentPlanHead.setDomesticClient(foreignContractHead.getDomesticClient());
            bizIEquipmentPlanHead.setParentId(foreignContractHead.getId());
        }

        //生成对应表体数据
        if (CollectionUtils.isNotEmpty(forContractIdList)) {
            bizIEquipmentPlanListMapper.insertByContractList(forContractIdList, sid, userInfo.getUserNo(), userInfo.getUserName());
        }

        // 新增数据
        int insertStatus = bizIEquipmentPlanHeadMapper.insert(bizIEquipmentPlanHead);
        return  insertStatus > 0 ? bizIEquipmentPlanHeadDtoMapper.toDto(bizIEquipmentPlanHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIEquipmentPlanHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentPlanHeadDto update(BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, UserInfoToken userInfo) {
        List<BizIEquipmentPlanListParam> listData = bizIEquipmentPlanHeadParam.getListData();

        BizIEquipmentPlanHead bizIEquipmentPlanHead = bizIEquipmentPlanHeadMapper.selectByPrimaryKey(bizIEquipmentPlanHeadParam.getId());
        BizIEquipmentPlanHeadDto headDto = null;
        bizIEquipmentPlanHeadDtoMapper.updatePo(bizIEquipmentPlanHeadParam, bizIEquipmentPlanHead);
        bizIEquipmentPlanHead.setUpdateBy(userInfo.getUserNo());
        bizIEquipmentPlanHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizIEquipmentPlanHeadMapper.updateByPrimaryKey(bizIEquipmentPlanHead);
        if (update > 0) {
            headDto = bizIEquipmentPlanHeadDtoMapper.toDto(bizIEquipmentPlanHead);
        }
        List<BizIEquipmentPlanListDto> updatedList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(listData)) {
            for (BizIEquipmentPlanListParam listParam : listData) {
                BizIEquipmentPlanList bizIEquipmentPlanList = bizIEquipmentPlanListMapper.selectByPrimaryKey(listParam.getId());
                bizIEquipmentPlanListDtoMapper.updatePo(listParam, bizIEquipmentPlanList);
                bizIEquipmentPlanList.setUpdateBy(userInfo.getUserNo());
                bizIEquipmentPlanList.setUpdateUserName(userInfo.getUserName());
                bizIEquipmentPlanList.setUpdateTime(new Date());
                // 更新数据
                int updateList = bizIEquipmentPlanListMapper.updateByPrimaryKey(bizIEquipmentPlanList);
                if (updateList > 0) {
                    updatedList.add(bizIEquipmentPlanListDtoMapper.toDto(bizIEquipmentPlanList));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updatedList)) {
            headDto.setListData(updatedList);
        }

        return headDto;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        //校验数据状态
        if (bizIEquipmentPlanHeadMapper.checkCanDelBySids(sids) > 0){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }
		bizIEquipmentPlanHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIEquipmentPlanHeadDto> selectAll(BizIEquipmentPlanHeadParam exportParam, UserInfoToken userInfo) {
        BizIEquipmentPlanHead bizIEquipmentPlanHead = bizIEquipmentPlanHeadDtoMapper.toPo(exportParam);
        // bizIEquipmentPlanHead.setTradeCode(userInfo.getCompany());
        List<BizIEquipmentPlanHeadDto> bizIEquipmentPlanHeadDtos = new ArrayList<>();
        List<BizIEquipmentPlanHead> bizIEquipmentPlanHeads = bizIEquipmentPlanHeadMapper.getList(bizIEquipmentPlanHead);
        if (CollectionUtils.isNotEmpty(bizIEquipmentPlanHeads)) {
            bizIEquipmentPlanHeadDtos = bizIEquipmentPlanHeads.stream().map(head -> {
                BizIEquipmentPlanHeadDto dto = bizIEquipmentPlanHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIEquipmentPlanHeadDtos;
    }

    /**
     * 确认数据状态接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("确认成功"));

        BizIEquipmentPlanHead planHead = bizIEquipmentPlanHeadMapper.selectByPrimaryKey(sid);
        if (planHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        if (CommonEnum.STATE_ENUM.CONFIRMED.getType().equals(planHead.getStatus())) {
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(planHead.getStatus())){
            throw new ErrorException(400, "该数据已作废，不允许确认");
        }
        // 更新状态为确认
        planHead.setStatus(CommonEnum.STATE_ENUM.CONFIRMED.getType());
        planHead.setConfirmTime(new Date());
        bizIEquipmentPlanHeadMapper.updateByPrimaryKey(planHead);

        return resultObject;
    }

    /**
     * 发送审批接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("发送审批成功"));

        BizIEquipmentPlanHead planHead = bizIEquipmentPlanHeadMapper.selectByPrimaryKey(sid);
        if (planHead == null) {
            throw new ErrorException(400,"数据不存在，请刷新");
        }
        if (!CommonEnum.OrderAuditStatusEnum.NOT_INVOLVED.getValue().equals(planHead.getApprStatus())){
            throw new ErrorException(400, "只有未审核数据允许操作发送审批");
        }
        if (!CommonEnum.STATE_ENUM.CONFIRMED.getType().equals(planHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("只有数据状态为1确认的数据允许操作发送审批");
            return resultObject;
        }

        // 更新审批状态为审批中
        planHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        bizIEquipmentPlanHeadMapper.updateByPrimaryKey(planHead);

        return resultObject;
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyVersion(BizIEquipmentPlanHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("版本复制成功"));

        if (StringUtils.isBlank(params.getId())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请选择需要复制的数据");
            return resultObject;
        }

        BizIEquipmentPlanHead originalPlan = bizIEquipmentPlanHeadMapper.selectByPrimaryKey(params.getId());
        if (originalPlan == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("原始数据不存在");
            return resultObject;
        }

        // 创建新版本
        BizIEquipmentPlanHead newPlan = new BizIEquipmentPlanHead();
        // 复制原有数据
        bizIEquipmentPlanHeadDtoMapper.copyProperties(originalPlan, newPlan);

        // 设置新的ID和版本信息
        String newSid = UUID.randomUUID().toString();
        newPlan.setId(newSid);
        newPlan.setCreateBy(userInfo.getUserNo());
        newPlan.setCreateTime(new Date());
        newPlan.setCreateUserName(userInfo.getUserName());
        newPlan.setUpdateBy(null);
        newPlan.setUpdateTime(null);
        newPlan.setConfirmTime(null);

        // 重置状态
        newPlan.setStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
        newPlan.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue());

        // 版本号递增
        String currentVersion = originalPlan.getVersionNo();
        if (StringUtils.isNotBlank(currentVersion)) {
            try {
                int versionNum = Integer.parseInt(currentVersion) + 1;
                newPlan.setVersionNo(String.valueOf(versionNum));
            } catch (NumberFormatException e) {
                newPlan.setVersionNo("1");
            }
        } else {
            newPlan.setVersionNo("1");
        }

        // 插入新版本数据
        int insertResult = bizIEquipmentPlanHeadMapper.insert(newPlan);
        if (insertResult <= 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage("版本复制失败");
            return resultObject;
        }

        // 复制表体数据
        bizIEquipmentPlanListMapper.copyListByHeadId(params.getId(), newSid, userInfo.getUserNo(), userInfo.getUserName());

        resultObject.setData(bizIEquipmentPlanHeadDtoMapper.toDto(newPlan));
        return resultObject;
    }
    /**
     * 作废接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("作废成功"));

        BizIEquipmentPlanHead planHead = bizIEquipmentPlanHeadMapper.selectByPrimaryKey(sid);
        if (planHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(planHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }

        // 更新状态为作废
        planHead.setStatus(CommonEnum.STATE_ENUM.INVALID.getType());
        bizIEquipmentPlanHeadMapper.updateByPrimaryKey(planHead);

        return resultObject;
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject getWarningSettings(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "");

        BizIEquipmentPlanHead planHead = bizIEquipmentPlanHeadMapper.selectByPrimaryKey(sid);
        if (planHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        resultObject.setData(planHead);
        return resultObject;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BizIEquipmentPlanHeadDto> updateWarningSetting(BizIEquipmentPlanHeadParam bizIEquipmentPlanHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "预警设置成功");
        BizIEquipmentPlanHead bizIEquipmentPlanHead = bizIEquipmentPlanHeadMapper.selectByPrimaryKey(bizIEquipmentPlanHeadParam.getId());
        if (bizIEquipmentPlanHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        bizIEquipmentPlanHeadDtoMapper.updatePo(bizIEquipmentPlanHeadParam, bizIEquipmentPlanHead);
        // 更新数据
        int update = bizIEquipmentPlanHeadMapper.updateByPrimaryKeySelective(bizIEquipmentPlanHead);
        if (update > 0){
            resultObject.setData(bizIEquipmentPlanHeadDtoMapper.toDto(bizIEquipmentPlanHead));
        }else {
            resultObject.setSuccess(false);
            resultObject.setMessage("预警设置失败");
        }

        return resultObject;
    }


    public ResultObject<BizIEquipmentWarningPlans> getWaringPlanList(UserInfoToken userInfo){
        ResultObject resultObject = ResultObject.createInstance(true, "");
        BizIEquipmentWarningPlans warningPlans=new BizIEquipmentWarningPlans();
        warningPlans.setReceiveWaringPlan(bizIEquipmentPlanHeadMapper.getReceiveWaringPlan(userInfo.getCompany()));
        warningPlans.setPaymentWaringPlan(bizIEquipmentPlanHeadMapper.getPaymentWaringPlan(userInfo.getCompany()));
        warningPlans.setArbitrationWaringPlan(bizIEquipmentPlanHeadMapper.getArbitrationWaringPlan(userInfo.getCompany()));
        warningPlans.setLicenseWaringPlan(bizIEquipmentPlanHeadMapper.getLicenseWaringPlan(userInfo.getCompany()));
        warningPlans.setTransportWaringPlan(bizIEquipmentPlanHeadMapper.getTransportWaringPlan(userInfo.getCompany()));
        warningPlans.setInsuranceWaringPlan(bizIEquipmentPlanHeadMapper.getInsuranceWaringPlan(userInfo.getCompany()));

        if (CollectionUtils.isEmpty(warningPlans.getReceiveWaringPlan()) &&
            CollectionUtils.isEmpty(warningPlans.getPaymentWaringPlan()) &&
            CollectionUtils.isEmpty(warningPlans.getArbitrationWaringPlan()) &&
            CollectionUtils.isEmpty(warningPlans.getLicenseWaringPlan()) &&
            CollectionUtils.isEmpty(warningPlans.getTransportWaringPlan()) &&
            CollectionUtils.isEmpty(warningPlans.getInsuranceWaringPlan())) {
            warningPlans.setHasWarningPlans("0");
        }else {
            warningPlans.setHasWarningPlans("1");
        }

        resultObject.setData(warningPlans);
        return resultObject;
    }

}
