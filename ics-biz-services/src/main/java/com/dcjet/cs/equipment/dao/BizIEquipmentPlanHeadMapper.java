package com.dcjet.cs.equipment.dao;

import com.dcjet.cs.equipment.model.BizIEquipmentPlanHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIEquipmentPlanHead
* <AUTHOR>
* @date: 2025-7-8
*/
public interface BizIEquipmentPlanHeadMapper extends Mapper<BizIEquipmentPlanHead> {
    /**
     * 查询获取数据
     * @param bizIEquipmentPlanHead
     * @return
     */
    List<BizIEquipmentPlanHead> getList(BizIEquipmentPlanHead bizIEquipmentPlanHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    int checkCanDelBySids(List<String> sids);

    List<BizIEquipmentPlanHead> getReceiveWaringPlan(@Param("tradeCode") String tradeCode);
    List<BizIEquipmentPlanHead> getPaymentWaringPlan(@Param("tradeCode") String tradeCode);
    List<BizIEquipmentPlanHead> getArbitrationWaringPlan(@Param("tradeCode") String tradeCode);
    List<BizIEquipmentPlanHead> getLicenseWaringPlan(@Param("tradeCode") String tradeCode);
    List<BizIEquipmentPlanHead> getTransportWaringPlan(@Param("tradeCode") String tradeCode);
    List<BizIEquipmentPlanHead> getInsuranceWaringPlan(@Param("tradeCode") String tradeCode);
}
