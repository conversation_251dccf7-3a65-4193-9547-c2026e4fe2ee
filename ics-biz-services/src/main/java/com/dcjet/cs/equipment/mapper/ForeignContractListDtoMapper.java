package com.dcjet.cs.equipment.mapper;

import com.dcjet.cs.dto.equipment.ForeignContractListDto;
import com.dcjet.cs.dto.equipment.ForeignContractListParam;
import com.dcjet.cs.equipment.model.ForeignContractList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ForeignContractListDtoMapper {

    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    ForeignContractListDto toDto(ForeignContractList po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    ForeignContractList toPo(ForeignContractListParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(ForeignContractListParam param, @MappingTarget ForeignContractList po);
}