<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.equipment.dao.BizIEquipmentAgentAgreementMapper">
    <resultMap id="bizIEquipmentAgentAgreementResultMap" type="com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="data_state" property="dataState" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="business_place" property="businessPlace" jdbcType="VARCHAR" />
		<result column="agreement_type" property="agreementType" jdbcType="VARCHAR" />
		<result column="agreement_no" property="agreementNo" jdbcType="VARCHAR" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="supplier" property="supplier" jdbcType="VARCHAR" />
		<result column="sign_date" property="signDate" jdbcType="TIMESTAMP" />
		<result column="sign_place" property="signPlace" jdbcType="VARCHAR" />
		<result column="currency" property="currency" jdbcType="VARCHAR" />
		<result column="contract_amount" property="contractAmount" jdbcType="NUMERIC" />
		<result column="agency_rate" property="agencyRate" jdbcType="NUMERIC" />
		<result column="agency_fee" property="agencyFee" jdbcType="NUMERIC" />
		<result column="agreement_terms" property="agreementTerms" jdbcType="VARCHAR" />
		<result column="make_by" property="makeBy" jdbcType="VARCHAR" />
		<result column="make_date" property="makeDate" jdbcType="TIMESTAMP" />
		<result column="bill_status" property="billStatus" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="approval_status" property="approvalStatus" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,business_type
     ,trade_code
     ,sys_org_code
     ,data_state
     ,version_no
     ,parent_id
     ,create_by
     ,create_time
     ,update_by
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,contract_no
     ,business_place
     ,agreement_type
     ,agreement_no
     ,customer
     ,supplier
     ,sign_date
     ,sign_place
     ,currency
     ,contract_amount
     ,agency_rate
     ,agency_fee
     ,agreement_terms
     ,make_by
     ,make_date
     ,bill_status
     ,confirm_time
     ,approval_status
    </sql>
    <sql id="condition">
        <if test="approvalStatus != null and approvalStatus != ''">
            and approval_status = #{approvalStatus}
        </if>
    <if test="businessType != null and businessType != ''">
		and business_type = #{businessType}
	</if>
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
    <if test="agreementNo != null and agreementNo != ''">
	  and agreement_no like '%'|| #{agreementNo} || '%'
	</if>
    <if test="customer != null and customer != ''">
		and customer = #{customer}
	</if>
    <if test="supplier != null and supplier != ''">
		and supplier = #{supplier}
	</if>
        <if test="_databaseId != 'oracle' and signDateFrom != null and signDateFrom != ''">
            <![CDATA[ and sign_date >= to_date(#{signDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId != 'oracle' and signDateTo != null and signDateTo != ''">
            <![CDATA[ and sign_date <= to_date(#{signDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and signDateFrom != null and signDateFrom != ''">
            <![CDATA[ and sign_date >= to_date(#{signDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and signDateTo != null and signDateTo != ''">
            <![CDATA[ and sign_date <= to_date(#{signDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
    <if test="makeBy != null and makeBy != ''">
	  and make_by like '%'|| #{makeBy} || '%'
	</if>
        <if test="_databaseId != 'oracle' and makeDateFrom != null and makeDateFrom != ''">
            <![CDATA[ and make_date >= to_date(#{makeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId != 'oracle' and makeDateTo != null and makeDateTo != ''">
            <![CDATA[ and make_date <= to_date(#{makeDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and makeDateFrom != null and makeDateFrom != ''">
            <![CDATA[ and make_date >= to_timestamp(#{makeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and makeDateTo != null and makeDateTo != ''">
            <![CDATA[ and make_date <= to_timestamp(#{makeDateTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
    <if test="billStatus != null and billStatus != ''">
		and bill_status = #{billStatus}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIEquipmentAgentAgreementResultMap" parameterType="com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_equipment_agent_agreement t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.create_time desc
    </select>
    <select id="checkCanDelBySids" resultType="java.lang.Integer">
        select count(1)
        from t_biz_i_equipment_agent_agreement t where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and t.bill_status != '0'
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_equipment_agent_agreement t where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        ;
        delete from t_biz_i_equipment_agent_agreement_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getFlowList" resultType="com.dcjet.cs.common.model.WorkFlowParam">
        select
            id as sid,
            extend2 as flowInstanceId
        from t_biz_i_equipment_agent_agreement where id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </select>
    <select id="getAeoList" resultMap="bizIEquipmentAgentAgreementResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_i_equipment_agent_agreement t
        <where>
            <include refid="condition"></include>
            and t.id in
            <foreach collection="ids"  item="item" open="(" separator="," close=")"  >
                #{item}
            </foreach>
        </where>
        order by t.create_time desc
    </select>
    <select id="getForContractList" resultType="com.dcjet.cs.dto.iEBusiness.ForContractListDto">
        SELECT
            h.ID as id,
            h.CONTRACT_NO as contractNo,
            h.BUYER as buyer,
            h.SELLER as seller,
            h.SIGN_DATE as signDate,
            COALESCE(SUM(l.MONEY_AMOUNT), 0) as totalAmount
        FROM
            T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD h
        LEFT JOIN
            T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST l ON h.ID = l.HEAD_ID AND l.DATA_STATUS != '2'
        WHERE
            h.TRADE_CODE = #{tradeCode}
            AND h.DATA_STATUS = '1'
            <![CDATA[
            AND ((
            SELECT COUNT(1)
            FROM t_biz_i_equipment_agent_agreement t
            WHERE t.CONTRACT_NO = h.CONTRACT_NO
            AND t.bill_status != '2'
            ) < 2) ]]>
            <if test="contractNo != null and contractNo != ''">
                and h.CONTRACT_NO like '%'|| #{contractNo} || '%'
            </if>
        GROUP BY
            h.ID, h.CONTRACT_NO, h.BUYER, h.SELLER, h.SIGN_DATE
        ORDER BY
            h.ID DESC
    </select>
    <select id="getContractAmount" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(h.total_amount), 0) as totalAmount
        FROM
            t_biz_i_equipment_agent_agreement_list h
        WHERE
            h.head_id = #{headId}
    </select>
    <insert id="insertListByForContract">
        insert into t_biz_i_equipment_agent_agreement_list(
            id
            ,head_id
            ,parent_id
            ,product_name
            ,product_model
            ,unit
            ,quantity
            ,unit_price
            ,total_amount
            ,ship_date
            ,remark
            ,trade_code
            ,create_by
            ,create_time)
        select
            sys_guid(),
            #{headId},
            fcl.HEAD_ID,
            fcl.G_NAME,
            fcl.G_MODEL,
            fcl.UNIT,
            fcl.QTY,
            fcl.UNIT_PRICE,
            fcl.MONEY_AMOUNT,
            fcl.DELIVERY_DATE,
            fcl.NOTE,
            fch.TRADE_CODE,
            #{tradeCode},
            now()
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST fcl
        left join T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD fch on fch.ID = fcl.HEAD_ID
        where fcl.HEAD_ID = #{id}
    </insert>
    <select id="checkAgreementNo" resultType="java.lang.Integer">
        select count(1) from t_biz_i_equipment_agent_agreement t
        where t.agreement_no = #{agreementNo}
          and t.bill_status != '2'
        <if test="id != null and id != ''">
            and t.id != #{id}
        </if>
    </select>
    <select id="getSellerByParentId" resultType="java.lang.String">
        select
            seller
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD
        where id = #{parentId}
    </select>
    <select id="checkContractNo" resultType="java.lang.Integer">
        select count(1) from t_biz_i_equipment_agent_agreement t
        where t.contract_no = #{contractNo}
          and t.agreement_type = #{agreementType}
          and t.bill_status != '2'
        <if test="id != null and id != ''">
            and t.id != #{id}
        </if>
    </select>

</mapper>
