package com.dcjet.cs.equipment.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter
@Getter
@Table(name = "T_BIZ_I_EQUIPMENT_CONTAINER_INFO")
public class BizIEquipmentContainerInfo implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "ID")
	private  String id;
	/**
     * 表头汇款通知id
     */
	@Column(name = "HEAD_ID")
	private  String headId;
	/**
     * 箱型（如20GP,40HQ等）
     */
	@Column(name = "CONTAINER_TYPE")
	private  String containerType;
	/**
     * 单价合计
     */
	@Column(name = "UNIT_PRICE_TOTAL")
	private  BigDecimal unitPriceTotal;
	/**
     * 箱数
     */
	@Column(name = "CONTAINER_COUNT")
	private  BigDecimal containerCount;
	/**
     * 金额
     */
	@Column(name = "AMOUNT")
	private  BigDecimal amount;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人姓名
     */
	@Column(name = "CREATE_USER_NAME")
	private  String createUserName;
	/**
     * 最后修改人姓名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 扩展字段1
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "EXTEND10")
	private  String extend10;
}
