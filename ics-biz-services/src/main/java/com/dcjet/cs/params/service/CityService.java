package com.dcjet.cs.params.service;

import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.dto.bi.BizMerchantParam;
import com.dcjet.cs.dto.params.CityDto;
import com.dcjet.cs.dto.params.CityParam;
import com.dcjet.cs.params.dao.CityMapper;
import com.dcjet.cs.params.mapper.CityDtoMapper;
import com.dcjet.cs.params.model.City;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CityService extends BaseService<City> {
    @Resource
    private CityMapper cityMapper;
    @Resource
    private CityDtoMapper cityDtoMapper;

    @Override
    public Mapper<City> getMapper() {
        return this.cityMapper;
    }

    /**
     * 获取分页列表
     *
     * @param cityParam 城市参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 城市分页列表
     */
    public ResultObject<List<CityDto>> getListPaged(CityParam cityParam, PageParam pageParam, UserInfoToken<?> userInfo) {
        City city = this.cityDtoMapper.toPo(cityParam);
        city.setTradeCode(userInfo.getCompany());
        Page<City> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.cityMapper.getList(city));
        List<CityDto> cityDtoList = page.getResult().stream()
                .map(c -> this.cityDtoMapper.toDto(c))
                .collect(Collectors.toList());
        return ResultObject.createInstance(cityDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增城市
     *
     * @param cityParam 城市参数
     * @param userInfo  用户信息
     * @return 城市参数模型
     */
    @Transactional(rollbackFor = Exception.class)
    public CityDto insert(CityParam cityParam, UserInfoToken<?> userInfo) {
        int countByCityCnName = this.cityMapper.getCountByCityCnName(cityParam.getCityCnName(), userInfo.getCompany());
        if (countByCityCnName > 0) {
            throw new ErrorException(500, "城市中文名称已存在，请重新输入！");
        }
        City city = this.cityDtoMapper.toPo(cityParam);
        city.setSid(UUID.randomUUID().toString());
        city.setTradeCode(userInfo.getCompany());
        city.setParamCode(this.getNextParamCode(userInfo));
        city.setInsertUser(userInfo.getUserNo());
        city.setInsertUserName(userInfo.getUserName());
        city.setInsertTime(new Date());
        //国别中文
        city.setExtend1(cityParam.getExtend1());
        //国别英文
        city.setExtend2(cityParam.getExtend2());
        return this.cityMapper.insert(city) > 0 ? this.cityDtoMapper.toDto(city) : null;
    }

    /**
     * 修改城市
     *
     * @param cityParam 城市参数
     * @param userInfo  用户信息
     * @return 城市参数模型
     */
    @Transactional(rollbackFor = Exception.class)
    public CityDto update(CityParam cityParam, UserInfoToken<?> userInfo) {
        City city = this.cityMapper.selectByPrimaryKey(cityParam.getSid());
        if (StringUtils.isNotBlank(cityParam.getCityCnName()) &&
                !Objects.equals(cityParam.getCityCnName(), city.getCityCnName())) {
            int countByCityCnName = this.cityMapper.getCountByCityCnName(cityParam.getCityCnName(), userInfo.getCompany());
            if (countByCityCnName > 0) {
                throw new ErrorException(500, "城市中文名称已存在，请重新输入！");
            }
        }
        city.setCityCnName(cityParam.getCityCnName());
        city.setCityEnName(cityParam.getCityEnName());
        //国别中文
        city.setExtend1(cityParam.getExtend1());
        //国别英文
        city.setExtend2(cityParam.getExtend2());
        city.setNote(cityParam.getNote());
        city.setUpdateTime(new Date());
        city.setUpdateUser(userInfo.getUserNo());
        city.setUpdateUserName(userInfo.getUserName());
        return this.cityMapper.updateByPrimaryKey(city) > 0 ? this.cityDtoMapper.toDto(city) : null;
    }

    /**
     * 根据主键列表删除
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySids(List<String> sids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        log.info("user {} of company {} deleted records with {}", userInfo.getUserNo(), userInfo.getCompany(), sids);
        this.cityMapper.deleteBySids(sids);
    }

    /**
     * 查询所有城市
     *
     * @param cityParam 城市参数
     * @param userInfo  用户信息
     * @return 城市传输列表
     */
    public List<CityDto> getAllList(CityParam cityParam, UserInfoToken<?> userInfo) {
        City city = this.cityDtoMapper.toPo(cityParam);
        city.setTradeCode(userInfo.getCompany());
        List<City> cityList = this.cityMapper.getList(city);
        if (CollectionUtils.isEmpty(cityList)) {
            return Collections.emptyList();
        }
        List<CityDto> cityDtoList = cityList.stream()
                .map(c -> this.cityDtoMapper.toDto(c))
                .collect(Collectors.toList());
        cityDtoList.forEach(priceTermsDto -> {

        });
        return cityDtoList;
    }

    /**
     * 获取下一个参数代码
     *
     * @param userInfo 用户信息
     * @return 下一个参数代码
     */
    public String getNextParamCode(UserInfoToken<?> userInfo) {
        return this.cityMapper.getNextParamCode(userInfo.getCompany());
    }

    public City getShanghaiCode(CityParam cityParam, UserInfoToken userInfo){
        City param = cityDtoMapper.toPo(cityParam);
        param.setTradeCode(userInfo.getCompany());
        param.setCityCnName("上海");
        City city = cityMapper.getCityCodeByCn(param.getCityCnName(),param.getTradeCode());
        return city;
    }
}
