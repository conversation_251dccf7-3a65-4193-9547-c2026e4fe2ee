<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.params.dao.CityMapper">
    <resultMap id="cityResultMap" type="com.dcjet.cs.params.model.City">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="PARAM_CODE" property="paramCode" jdbcType="VARCHAR"/>
        <result column="CITY_CN_NAME" property="cityCnName" jdbcType="VARCHAR"/>
        <result column="CITY_EN_NAME" property="cityEnName" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        SID,
        PARAM_CODE,
        CITY_CN_NAME,
        CITY_EN_NAME,
        NOTE,
        TRADE_CODE,
        INSERT_USER,
        INSERT_TIME,
        UPDATE_USER,
        UPDATE_TIME,
        INSERT_USER_NAME,
        UPDATE_USER_NAME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>


    <sql id="condition">
        <if test="true">
            TRADE_CODE = #{tradeCode}
        </if>
        <if test="paramCode != null and paramCode != ''">
            and PARAM_CODE like concat('%', #{paramCode}, '%')
        </if>
        <if test="cityCnName != null and cityCnName != ''">
            and CITY_CN_NAME like concat('%', #{cityCnName}, '%')
        </if>
        <if test="cityEnName != null and cityEnName != ''">
            and CITY_EN_NAME like concat('%', #{cityEnName}, '%')
        </if>
    </sql>

    <select id="getList" resultMap="cityResultMap" parameterType="com.dcjet.cs.params.model.City">
        select
        <include refid="columns"/>
        from T_BIZ_CITY
        <where>
            <include refid="condition"/>
        </where>
        order by PARAM_CODE desc
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_CITY where SID in
        <foreach collection="sids" item="sid" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>

    <select id="getNextParamCode" resultType="java.lang.String">
        select LPAD(COALESCE(MAX(PARAM_CODE::NUMERIC), 0) + 1, 3, '0')
        from T_BIZ_CITY
        where trade_code = #{tradeCode}
    </select>

    <select id="getCountByCityCnName" resultType="java.lang.Integer">
        select COUNT(1)
        from T_BIZ_CITY
        where CITY_CN_NAME = #{cityCnName}
          and TRADE_CODE = #{tradeCode}
    </select>
    <select id="getCityCodeByCn" resultMap="cityResultMap">
        select <include refid="columns"/>
        from T_BIZ_CITY
        where CITY_CN_NAME = #{cityCnName}
          and TRADE_CODE = #{tradeCode}
    </select>
</mapper>
