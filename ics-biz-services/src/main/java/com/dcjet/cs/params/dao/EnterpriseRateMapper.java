package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.EnterpriseRate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* EnterpriseRate
* <AUTHOR>
* @date: 2025-3-11
*/
public interface EnterpriseRateMapper extends Mapper<EnterpriseRate> {
    /**
     * 查询获取数据
     * @param rateTable
     * @return
     */
    List<EnterpriseRate> getList(EnterpriseRate rateTable);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验 storehouseName 是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_enterprise_RATE WHERE month = #{month} and curr = #{curr} and trade_code = #{tradeCode}")
    int countByRateTableName(@Param("month")String month,@Param("curr")String curr,@Param("tradeCode") String tradeCode);

    // 更新时校验 storehouseName 是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_enterprise_RATE WHERE month = #{month} and curr = #{curr}  AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByRateTableNameAndNotSid(@Param("month")String month,@Param("curr")String curr, @Param("sid") String sid,@Param("tradeCode") String tradeCode);

    @Select("SELECT * FROM T_BIZ_enterprise_RATE WHERE  curr = #{curr} and trade_code = #{tradeCode} order by month::integer desc")
    List<EnterpriseRate> selectMessage(EnterpriseRate enterpriseRate);

    List<EnterpriseRate> getRateByCurrList(@Param("currList") List<String> currList, @Param("tradeCode") String tradeCode);

    /**
     * 根据月份和企业代码查询当月汇率数据
     * @param month 月份
     * @param tradeCode 企业代码
     * @return 当月汇率数据列表
     */
    @Select("SELECT * FROM T_BIZ_enterprise_RATE WHERE month = #{month} AND trade_code = #{tradeCode}")
    List<EnterpriseRate> findByMonthAndTradeCode(@Param("month") String month, @Param("tradeCode") String tradeCode);

    /**
     * 根据企业代码查询上月汇率数据（按月份倒序取第一条记录的月份）
     * @param tradeCode 企业代码
     * @param currentMonth 当前月份
     * @return 上月汇率数据列表
     */
    List<EnterpriseRate> findPreviousMonthData(@Param("tradeCode") String tradeCode, @Param("currentMonth") String currentMonth);
}
