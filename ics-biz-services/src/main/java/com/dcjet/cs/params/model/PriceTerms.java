package com.dcjet.cs.params.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_BIZ_PRICE_TERMS")
@Accessors(chain = true)
public class PriceTerms implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;

    /**
     * 参数代码
     */
    @Column(name = "PARAM_CODE")
    private String paramCode;

    /**
     * 价格条款
     */
    @Column(name = "PRICE_TERM")
    private String priceTerm;

    /**
     * 价格条款描述
     */
    @Column(name = "PRICE_TERM_DESC")
    private String priceTermDesc;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 新增用户
     */
    @Column(name = "INSERT_USER")
    private String insertUser;

    /**
     * 新增时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 修改用户
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 新增用户名称
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;

    /**
     * 修改用户名称
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 扩展字段1
     */
    @Column(name = "EXTEND1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @Column(name = "EXTEND2")
    private String extend2;
    /**
     * 扩展字段3
     */
    @Column(name = "EXTEND3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @Column(name = "EXTEND4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @Column(name = "EXTEND5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @Column(name = "EXTEND6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @Column(name = "EXTEND7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @Column(name = "EXTEND8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @Column(name = "EXTEND9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @Column(name = "EXTEND10")
    private String extend10;
}