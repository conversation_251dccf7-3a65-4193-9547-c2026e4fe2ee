package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.InsuranceType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* InsuranceType
* <AUTHOR>
* @date: 2025-3-11
*/
public interface InsuranceTypeMapper extends Mapper<InsuranceType> {
    /**
     * 查询获取数据
     * @param insuranceTypeCn
     * @return
     */
    List<InsuranceType> getList(InsuranceType insuranceTypeCn);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_INSURANCE_TYPE WHERE insurance_type_cn = #{insuranceTypeCn} and trade_code = #{tradeCode}")
    int countByInsuranceTypeName(@Param("insuranceTypeCn")String insuranceTypeCn,@Param("tradeCode") String tradeCode);

    // 更新时校验是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_INSURANCE_TYPE WHERE insurance_type_cn = #{insuranceTypeCn}  AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByInsuranceTypeNameAndNotSid(@Param("insuranceTypeCn")String curr, @Param("sid") String sid,@Param("tradeCode") String tradeCode);
}
