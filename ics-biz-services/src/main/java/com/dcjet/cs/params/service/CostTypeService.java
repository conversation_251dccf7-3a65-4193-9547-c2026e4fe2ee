package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.CostTypeDto;
import com.dcjet.cs.dto.params.CostTypeParam;
import com.dcjet.cs.params.dao.CostTypeMapper;
import com.dcjet.cs.params.mapper.CostTypeDtoMapper;
import com.dcjet.cs.params.model.CostType;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class CostTypeService extends BaseService<CostType> {
    @Resource
    private CostTypeMapper costTypeMapper;
    @Resource
    private CostTypeDtoMapper costTypeDtoMapper;
    @Override
    public Mapper<CostType> getMapper() {
        return costTypeMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param costTypeParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<CostTypeDto>> getListPaged(CostTypeParam costTypeParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        CostType costType = costTypeDtoMapper.toPo(costTypeParam);
        costType.setTradeCode(userInfo.getCompany());
        Page<CostType> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> costTypeMapper.getList(costType));
        List<CostTypeDto> costTypeDtos = page.getResult().stream().map(head -> {
            CostTypeDto dto = costTypeDtoMapper.toDto(head);
            if (StringUtils.isNotEmpty(dto.getCommonFlag())){
                dto.setBusinessTypeList(Arrays.asList(dto.getCommonFlag().split(",")));
            }
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<CostTypeDto>> paged = ResultObject.createInstance(costTypeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param costTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CostTypeDto insert(CostTypeParam costTypeParam, UserInfoToken userInfo) {
        // 校验 storehouseName 是否重复
        int count1 = costTypeMapper.countByCostTypeName(costTypeParam.getCostName(),userInfo.getCompany());
        if (count1 > 0) {
            throw new RuntimeException("费用名称已存在，请重新输入！");
        }
        if (StringUtils.isNotBlank(costTypeParam.getAccountSubject())){
            int count2 = costTypeMapper.countByAccountSubject(costTypeParam.getAccountSubject(),userInfo.getCompany());
            if (count2 > 0) {
                throw new RuntimeException("会计科目已存在，请重新输入！");
            }
        }

        CostType costType = costTypeDtoMapper.toPo(costTypeParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        costType.setSid(sid);
        costType.setInsertUser(userInfo.getUserNo());
        costType.setInsertTime(new Date());
        costType.setTradeCode(userInfo.getCompany());
        if (CollectionUtils.isNotEmpty(costTypeParam.getBusinessTypeList())){
            costType.setCommonFlag(String.join(",", costTypeParam.getBusinessTypeList()));
        }else {
            costType.setCommonFlag(null);
        }
        // 新增数据
        int insertStatus = costTypeMapper.insert(costType);
        return  insertStatus > 0 ? costTypeDtoMapper.toDto(costType) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param costTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CostTypeDto update(CostTypeParam costTypeParam, UserInfoToken userInfo) {
        CostType costType = costTypeMapper.selectByPrimaryKey(costTypeParam.getSid());

        // 校验 storehouseName 是否重复（排除自身）
        int count1 = costTypeMapper.countByCostTypeNameAndNotSid(
                costTypeParam.getCostName(), costTypeParam.getSid(),userInfo.getCompany());
        if (count1 > 0) {
            throw new RuntimeException("费用名称已存在，请重新输入！");
        }
        // 校验 storehouseName 是否重复（排除自身）
        if (StringUtils.isNotBlank(costTypeParam.getAccountSubject())) {
            int count2 = costTypeMapper.countByAccountSubjectAndNotSid(
                    costTypeParam.getAccountSubject(), costTypeParam.getSid(), userInfo.getCompany());
            if (count2 > 0) {
                throw new RuntimeException("会计科目已存在，请重新输入！");
            }
        }

        costTypeDtoMapper.updatePo(costTypeParam, costType);
        costType.setUpdateUser(userInfo.getUserNo());
        costType.setUpdateTime(new Date());
        if (CollectionUtils.isNotEmpty(costTypeParam.getBusinessTypeList())){
            costType.setCommonFlag(String.join(",", costTypeParam.getBusinessTypeList()));
        }else {
            costType.setCommonFlag(null);
        }
        // 更新数据
        int update = costTypeMapper.updateByPrimaryKey(costType);
        return update > 0 ? costTypeDtoMapper.toDto(costType) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		costTypeMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<CostTypeDto> selectAll(CostTypeParam exportParam, UserInfoToken userInfo) {
        CostType costType = costTypeDtoMapper.toPo(exportParam);
        costType.setTradeCode(userInfo.getCompany());
        List<CostTypeDto> costTypeDtos = new ArrayList<>();
        List<CostType> costTypes = costTypeMapper.getList(costType);
        if (CollectionUtils.isNotEmpty(costTypes)) {
            costTypeDtos = costTypes.stream().map(head -> {
                CostTypeDto dto = costTypeDtoMapper.toDto(head);
                if (StringUtils.isNotEmpty(dto.getCommonFlag())){
                    dto.setBusinessTypeList(Arrays.asList(dto.getCommonFlag().split(",")));
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return costTypeDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = costTypeMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
