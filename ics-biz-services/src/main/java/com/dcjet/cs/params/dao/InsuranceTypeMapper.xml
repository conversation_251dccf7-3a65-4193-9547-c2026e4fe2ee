<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.params.dao.InsuranceTypeMapper">
    <resultMap id="bizMerchantResultMap" type="com.dcjet.cs.params.model.InsuranceType">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>

		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />

		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="PARAM_CODE" property="paramCode" jdbcType="VARCHAR" />
		<result column="insurance_type_cn" property="insuranceTypeCn" jdbcType="VARCHAR" />
		<result column="insurance_type_en" property="insuranceTypeEn" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,trade_code

     ,insert_user
     ,insert_time
     ,update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,PARAM_CODE
     ,insurance_type_cn
     ,insurance_type_en
     ,note
    </sql>
    <sql id="condition">
        <if test="paramCode != null and paramCode != ''"> and PARAM_CODE like '%'|| #{paramCode} || '%' </if>
        <if test="insuranceTypeCn != null and insuranceTypeCn != ''"> and insurance_type_cn like '%'|| #{insuranceTypeCn} || '%' </if>
         and trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizMerchantResultMap" parameterType="com.dcjet.cs.params.model.InsuranceType">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_INSURANCE_TYPE t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(update_time, insert_time) DESC
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_INSURANCE_TYPE t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="getNextCode" resultType="java.lang.String">
        select MAX(param_code::NUMERIC) as paramCode
        from T_BIZ_INSURANCE_TYPE
        where trade_code = #{tradeCode}
    </select>
</mapper>
