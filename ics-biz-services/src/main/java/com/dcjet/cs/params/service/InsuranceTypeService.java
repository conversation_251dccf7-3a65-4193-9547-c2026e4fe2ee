package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.InsuranceTypeDto;
import com.dcjet.cs.dto.params.InsuranceTypeParam;
import com.dcjet.cs.params.dao.InsuranceTypeMapper;
import com.dcjet.cs.params.mapper.InsuranceTypeDtoMapper;
import com.dcjet.cs.params.model.InsuranceType;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class InsuranceTypeService extends BaseService<InsuranceType> {
    @Resource
    private InsuranceTypeMapper insuranceTypeMapper;
    @Resource
    private InsuranceTypeDtoMapper insuranceTypeDtoMapper;
    @Override
    public Mapper<InsuranceType> getMapper() {
        return insuranceTypeMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param insuranceTypeParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<InsuranceTypeDto>> getListPaged(InsuranceTypeParam insuranceTypeParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        InsuranceType insuranceType = insuranceTypeDtoMapper.toPo(insuranceTypeParam);
        insuranceType.setTradeCode(userInfo.getCompany());
        Page<InsuranceType> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> insuranceTypeMapper.getList(insuranceType));
        List<InsuranceTypeDto> insuranceTypeDtos = page.getResult().stream().map(head -> {
            InsuranceTypeDto dto = insuranceTypeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<InsuranceTypeDto>> paged = ResultObject.createInstance(insuranceTypeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param insuranceTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public InsuranceTypeDto insert(InsuranceTypeParam insuranceTypeParam, UserInfoToken userInfo) {
        // 校验 insuranceTypeCn 是否重复
        int count = insuranceTypeMapper.countByInsuranceTypeName(insuranceTypeParam.getInsuranceTypeCn(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("保险类别中文名称已存在，请重新输入！");
        }
        InsuranceType insuranceType = insuranceTypeDtoMapper.toPo(insuranceTypeParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        insuranceType.setSid(sid);
        insuranceType.setInsertUser(userInfo.getUserNo());
        insuranceType.setInsertTime(new Date());
        insuranceType.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = insuranceTypeMapper.insert(insuranceType);
        return  insertStatus > 0 ? insuranceTypeDtoMapper.toDto(insuranceType) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param insuranceTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public InsuranceTypeDto update(InsuranceTypeParam insuranceTypeParam, UserInfoToken userInfo) {
        InsuranceType insuranceType = insuranceTypeMapper.selectByPrimaryKey(insuranceTypeParam.getSid());

        // 校验 insuranceTypeCn 是否重复（排除自身）
        int count = insuranceTypeMapper.countByInsuranceTypeNameAndNotSid(
                insuranceTypeParam.getInsuranceTypeCn(), insuranceTypeParam.getSid(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("保险类别中文名称已存在，请重新输入！");
        }

        insuranceTypeDtoMapper.updatePo(insuranceTypeParam, insuranceType);
        insuranceType.setUpdateUser(userInfo.getUserNo());
        insuranceType.setUpdateTime(new Date());
        insuranceType.setTradeCode(userInfo.getCompany());

        // 更新数据
        int update = insuranceTypeMapper.updateByPrimaryKey(insuranceType);
        return update > 0 ? insuranceTypeDtoMapper.toDto(insuranceType) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		insuranceTypeMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<InsuranceTypeDto> selectAll(InsuranceTypeParam exportParam, UserInfoToken userInfo) {
        InsuranceType insuranceType = insuranceTypeDtoMapper.toPo(exportParam);
        insuranceType.setTradeCode(userInfo.getCompany());
        List<InsuranceTypeDto> insuranceTypeDtos = new ArrayList<>();
        List<InsuranceType> insuranceTypes = insuranceTypeMapper.getList(insuranceType);
        if (CollectionUtils.isNotEmpty(insuranceTypes)) {
            insuranceTypeDtos = insuranceTypes.stream().map(head -> {
                InsuranceTypeDto dto = insuranceTypeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return insuranceTypeDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = insuranceTypeMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
