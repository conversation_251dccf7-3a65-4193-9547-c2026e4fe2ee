package com.dcjet.cs.params.dao;

import com.dcjet.cs.params.model.PriceTerms;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface PriceTermsMapper extends Mapper<PriceTerms> {

    /**
     * 获取列表
     *
     * @param priceTerms 价格条款
     * @return 价格条款列表
     */
    List<PriceTerms> getList(PriceTerms priceTerms);

    /**
     * 根据主键列表删除
     *
     * @param sids 主键列表
     * @return deleted rows
     */
    int deleteBySids(@Param("sids") List<String> sids);

    /**
     * 获取下一个参数代码
     *
     * @param tradeCode 企业编码
     * @return 下一个参数代码
     */
    String getNextParamCode(@Param("tradeCode") String tradeCode);

    /**
     * 根据价格条款统计数量
     *
     * @param priceTerm 价格条款
     * @param tradeCode 企业编码
     */
    int getCountByPriceTerm(@Param("priceTerm") String priceTerm, @Param("tradeCode") String tradeCode);

    String getNameByPriceTerm(String priceTerm);
}