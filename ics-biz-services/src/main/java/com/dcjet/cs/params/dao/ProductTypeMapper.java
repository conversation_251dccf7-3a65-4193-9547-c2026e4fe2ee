package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.ProductType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* ProductType
* <AUTHOR>
* @date: 2025-3-11
*/
public interface ProductTypeMapper extends Mapper<ProductType> {
    /**
     * 查询获取数据
     * @param productType
     * @return
     */
    List<ProductType> getList(ProductType productType);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验 storehouseName 是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_PRODUCT_TYPE WHERE category_code = #{categoryCode} and trade_code = #{tradeCode} ")
    int countByProductTypeName(@Param("categoryCode")String categoryCode,@Param("tradeCode") String tradeCode);
    @Select("SELECT COUNT(*) FROM T_BIZ_PRODUCT_TYPE WHERE  category_name = #{categoryName} and trade_code = #{tradeCode} ")
    int countByCategoryName(@Param("categoryName")String categoryName,@Param("tradeCode") String tradeCode);

    // 更新时校验 storehouseName 是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_PRODUCT_TYPE WHERE category_code = #{categoryCode}  AND sid != #{sid} and trade_code = #{tradeCode} ")
    int countByProductTypeNameAndNotSid(@Param("categoryCode")String categoryCode, @Param("sid") String sid,@Param("tradeCode") String tradeCode);
    @Select("SELECT COUNT(*) FROM T_BIZ_PRODUCT_TYPE WHERE  category_name = #{categoryName} AND sid != #{sid} and trade_code = #{tradeCode} ")
    int countByCategoryNameAndNotSid(@Param("categoryName")String categoryName, @Param("sid") String sid,@Param("tradeCode") String tradeCode);

    List<ProductType> getByParamCodes(@Param("paramCodes") List<String> paramCodes, @Param("tradeCode") String tradeCode);
}
