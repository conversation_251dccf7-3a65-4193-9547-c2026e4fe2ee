package com.dcjet.cs.params.dao;

import com.dcjet.cs.params.model.City;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface CityMapper extends Mapper<City> {

    /**
     * 获取列表
     *
     * @param city 城市
     * @return 城市列表
     */
    List<City> getList(City city);

    /**
     * 根据主键列表删除
     *
     * @param sids 主键列表
     * @return deleted rows
     */
    int deleteBySids(@Param("sids") List<String> sids);

    /**
     * 获取下一个参数代码
     *
     * @param tradeCode 企业编码
     * @return 下一个参数代码
     */
    String getNextParamCode(@Param("tradeCode") String tradeCode);

    /**
     * 根据城市中文名称统计数量
     *
     * @param cityCnName 城市中文名称
     * @param tradeCode  企业编码
     */
    int getCountByCityCnName(@Param("cityCnName") String cityCnName, @Param("tradeCode") String tradeCode);

    City getCityCodeByCn(@Param("cityCnName") String cityCnName, @Param("tradeCode") String tradeCode);
}
