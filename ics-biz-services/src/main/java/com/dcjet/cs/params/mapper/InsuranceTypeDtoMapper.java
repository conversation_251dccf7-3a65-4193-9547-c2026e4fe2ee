package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.InsuranceTypeDto;
import com.dcjet.cs.dto.params.InsuranceTypeParam;
import com.dcjet.cs.params.model.InsuranceType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface InsuranceTypeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    InsuranceTypeDto toDto(InsuranceType po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    InsuranceType toPo(InsuranceTypeParam param);
    /**
     * 数据库原始数据更新
     * @param insuranceTypeParam
     * @param insuranceType
     */
    void updatePo(InsuranceTypeParam insuranceTypeParam, @MappingTarget InsuranceType insuranceType);
    default void patchPo(InsuranceTypeParam insuranceTypeParam, InsuranceType insuranceType) {
        // TODO 自行实现局部更新
    }
}
