<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.quo.dao.BizQuotationMapper">
    <resultMap id="bizQuotationResultMap" type="com.dcjet.cs.quo.model.BizQuotation">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="merchandise_categories" property="merchandiseCategories" jdbcType="VARCHAR" />
		<result column="product_model" property="productModel" jdbcType="VARCHAR" />
		<result column="specifications" property="specifications" jdbcType="VARCHAR" />
		<result column="grammage" property="grammage" jdbcType="NUMERIC" />
		<result column="material_no" property="materialNo" jdbcType="VARCHAR" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="unit_i" property="unitI" jdbcType="VARCHAR" />
		<result column="merchant_code" property="merchantCode" jdbcType="VARCHAR" />
		<result column="price_term" property="priceTerm" jdbcType="VARCHAR" />
		<result column="destination_port" property="destinationPort" jdbcType="VARCHAR" />
		<result column="import_unit_price" property="importUnitPrice" jdbcType="NUMERIC" />
		<result column="unit_tray_price" property="unitTrayPrice" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="exchange_rate" property="exchangeRate" jdbcType="NUMERIC" />
		<result column="tariff_rate" property="tariffRate" jdbcType="NUMERIC" />
		<result column="tariff_price" property="tariffPrice" jdbcType="NUMERIC" />
		<result column="vat" property="vat" jdbcType="NUMERIC" />
		<result column="head_agency_fee_rate" property="headAgencyFeeRate" jdbcType="NUMERIC" />
		<result column="head_agency_fee_price" property="headAgencyFeePrice" jdbcType="NUMERIC" />
		<result column="freight_forwarding_fee_price" property="freightForwardingFeePrice" jdbcType="NUMERIC" />
		<result column="freight_forwarding_fee" property="freightForwardingFee" jdbcType="NUMERIC" />
		<result column="insurance_fee" property="insuranceFee" jdbcType="NUMERIC" />
		<result column="purchase_cost" property="purchaseCost" jdbcType="NUMERIC" />
		<result column="storage_transport_tax" property="storageTransportTax" jdbcType="NUMERIC" />
		<result column="gross_margin" property="grossMargin" jdbcType="NUMERIC" />
		<result column="price_excluding_tax" property="priceExcludingTax" jdbcType="NUMERIC" />
		<result column="price_rmb" property="priceRmb" jdbcType="NUMERIC" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,COALESCE(update_by,create_by) as createrBy
     ,COALESCE(update_user_name,create_user_name) as createrUserName
     ,COALESCE(update_time,create_time) as createrTime
     ,create_by
     ,create_time
     ,create_user_name
     ,update_by
     ,update_time
     ,update_user_name
     ,trade_code
     ,sys_org_code
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,business_type
     ,g_name
     ,merchandise_categories
     ,product_model
     ,specifications
     ,grammage
     ,material_no
     ,unit
     ,unit_i
     ,merchant_code
     ,price_term
     ,destination_port
     ,import_unit_price
     ,unit_tray_price
     ,curr
     ,exchange_rate
     ,tariff_rate
     ,tariff_price
     ,vat
     ,head_agency_fee_rate
     ,head_agency_fee_price
     ,freight_forwarding_fee_price
     ,freight_forwarding_fee
     ,insurance_fee
     ,purchase_cost
     ,storage_transport_tax
     ,gross_margin
     ,price_excluding_tax
     ,price_rmb
     ,note
     ,status
    </sql>
    <sql id="condition">
        <if test="gName != null and gName != ''"> and g_name like '%'|| #{gName} || '%' </if>
        <if test="status != null and status != ''"> and status = #{status} </if>
        <if test="businessType != null and businessType != ''"> and business_type = #{businessType} </if>
        <if test="merchantCode != null and merchantCode != ''"> and merchant_code = #{merchantCode} </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(update_time,create_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(update_time,create_time) < DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
        and trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizQuotationResultMap" parameterType="com.dcjet.cs.quo.model.BizQuotation">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_quotation t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.update_time,t.create_time) desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_quotation t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="checkKey" resultType="java.lang.Integer">
        select count(1) from t_biz_quotation where g_name = #{gName} and specifications = #{specifications} and TRADE_CODE = #{tradeCode}
        <if test="sid != null and sid != ''"> and sid !=#{sid} </if>
    </select>

    <select id="getMatForBuyContract" resultType="com.dcjet.cs.quo.model.BizQuotation">
        select quo.sid, quo.g_name, quo.merchant_code, mer.MERCHANT_NAME_CN as merchantNameCn,quo.product_model,
        quo.specifications,quo.grammage,quo.unit,quo.unit_i,quo.import_unit_price
        from t_biz_quotation quo
        left join T_BIZ_MERCHANT mer on mer.MERCHANT_CODE = quo.merchant_code and quo.trade_code=mer.trade_code
        where quo.business_type = '2'
        and quo.trade_code = #{tradeCode}
        and quo.status = '0'
        <if test="gName != null and gName != ''">
            and quo.g_name like '%'|| #{gName} || '%'
        </if>
    </select>

    <select id="getByGNames" resultMap="bizQuotationResultMap">
        select
        <include refid="Base_Column_List" />
        from
        T_BIZ_QUOTATION t
        where TRADE_CODE = #{tradeCode} and G_NAME in
        <foreach collection="gNames"  item="gName" open="(" separator="," close=")"  >
            #{gName}
        </foreach>
    </select>
    <select id="getSupplierList" resultType="java.util.Map">
        select
            distinct
            MERCHANT_CODE as "value",
            MERCHANT_NAME_CN as "label"
        from  T_BIZ_MERCHANT
        where TRADE_CODE = #{tradeCode} and COMMON_FLAG like '%'|| #{businessType} || '%' and MERCHANT_TYPE = #{stype};
    </select>
</mapper>
