package com.dcjet.cs.quo.dao;
import com.dcjet.cs.dto.quo.BizQuotationDto;
import com.dcjet.cs.quo.model.BizQuotation;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizQuotation
* <AUTHOR>
* @date: 2025-5-20
*/
public interface BizQuotationMapper extends Mapper<BizQuotation> {
    /**
     * 查询获取数据
     * @param bizQuotation
     * @return
     */
    List<BizQuotation> getList(BizQuotation bizQuotation);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkKey(BizQuotation bizQuotation);

    List<BizQuotation> getMatForBuyContract(BizQuotationDto mat);

    List<BizQuotation> getByGNames(@Param("gNames") List<String> gNames, @Param("tradeCode") String tradeCode);

    List<Map<String, String>> getSupplierList( @Param("businessType")String businessType, @Param("stype") String stype, @Param("tradeCode") String tradeCode);
}
