<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.aeo.dao.BizFieldMarkingMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.aeo.model.BizFieldMarking">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="SID" jdbcType="VARCHAR" property="sid"/>
        <result column="FORM_TYPE" jdbcType="VARCHAR" property="formType"/>
        <result column="FIELD_MARKINGS" jdbcType="CLOB" property="fieldMarkings"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, SID, FORM_TYPE, FIELD_MARKINGS, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, REMARK
    </sql>

    <select id="getList" parameterType="com.dcjet.cs.aeo.model.BizFieldMarking" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BIZ_FIELD_MARKING
        <where>
            <if test="sid != null and sid != ''">
                AND SID = #{sid}
            </if>
            <if test="formType != null and formType != ''">
                AND FORM_TYPE = #{formType}
            </if>

        </where>
        ORDER BY CREATE_TIME DESC
    </select>

    <select id="getBySidAndFormType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BIZ_FIELD_MARKING
        WHERE SID = #{sid} AND FORM_TYPE = #{formType}
    </select>

    <delete id="deleteBatch">
        DELETE FROM T_BIZ_FIELD_MARKING
        WHERE ID IN
        <foreach collection="sids" item="sid" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>