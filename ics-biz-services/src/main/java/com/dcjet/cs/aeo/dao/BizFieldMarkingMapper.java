package com.dcjet.cs.aeo.dao;

import com.dcjet.cs.aeo.model.BizFieldMarking;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 表单字段标记信息Mapper
 *
 * <AUTHOR>
 * @date 2024
 */
public interface BizFieldMarkingMapper extends Mapper<BizFieldMarking> {
    
    /**
     * 查询获取数据
     *
     * @param bizFieldMarking
     * @return
     */
    List<BizFieldMarking> getList(BizFieldMarking bizFieldMarking);

    /**
     * 根据sid和formType查询
     *
     * @param sid
     * @param formType
     * @return
     */
    BizFieldMarking getBySidAndFormType(@Param("sid") String sid, @Param("formType") String formType);

    /**
     * 批量删除
     *
     * @param sids
     * @return
     */
    int deleteBatch(@Param("sids") List<String> sids);
}