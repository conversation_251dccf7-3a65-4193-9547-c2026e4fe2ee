package com.dcjet.cs.aeo.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 表单字段标记信息表
 *
 * <AUTHOR>
 * @date 2024
 */
@Setter
@Getter
@Table(name = "T_BIZ_FIELD_MARKING")
public class BizFieldMarking implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID")
    private String id;

    /**
     * 表单记录ID，关联具体的业务记录
     */
    @Column(name = "SID")
    private String sid;

    /**
     * 表单类型，用于区分不同的表单页面
     */
    @Column(name = "FORM_TYPE")
    private String formType;

    /**
     * 字段标记数据，JSON格式存储fieldMarkings.value的完整内容
     */
    @Column(name = "FIELD_MARKINGS")
    private String fieldMarkings;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 创建用户ID或用户名
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 更新用户ID或用户名
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 备注信息，可用于存储额外的标记说明
     */
    @Column(name = "REMARK")
    private String remark;
}