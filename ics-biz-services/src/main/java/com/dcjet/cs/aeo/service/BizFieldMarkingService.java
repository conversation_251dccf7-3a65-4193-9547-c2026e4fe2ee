package com.dcjet.cs.aeo.service;

import com.dcjet.cs.aeo.dao.BizFieldMarkingMapper;
import com.dcjet.cs.aeo.mapper.BizFieldMarkingDtoMapper;
import com.dcjet.cs.aeo.model.BizFieldMarking;
import com.dcjet.cs.dto.aeo.BizFieldMarkingDto;
import com.dcjet.cs.dto.aeo.BizFieldMarkingParam;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 表单字段标记信息Service
 *
 * <AUTHOR>
 * @date 2024
 */
@Service
@Slf4j
public class BizFieldMarkingService extends BaseService<BizFieldMarking> {
    
    @Resource
    private BizFieldMarkingMapper bizFieldMarkingMapper;

    @Resource
    private BizFieldMarkingDtoMapper bizFieldMarkingDtoMapper;

    @Override
    public Mapper<BizFieldMarking> getMapper() {
        return bizFieldMarkingMapper;
    }

    /**
     * 根据条件查询字段标记信息列表
     *
     * @param param
     * @param userInfo
     * @return
     */
    public List<BizFieldMarkingDto> selectList(BizFieldMarkingParam param, UserInfoToken userInfo) {
        BizFieldMarking bizFieldMarking = bizFieldMarkingDtoMapper.toPo(param);
        
        List<BizFieldMarkingDto> resultList = new ArrayList<>();
        List<BizFieldMarking> list = bizFieldMarkingMapper.getList(bizFieldMarking);
        
        if (CollectionUtils.isNotEmpty(list)) {
            resultList = list.stream().map(item -> {
                return bizFieldMarkingDtoMapper.toDto(item);
            }).collect(Collectors.toList());
        }
        
        return resultList;
    }

    /**
     * 根据sid和formType查询字段标记信息
     *
     * @param sid
     * @param formType
     * @param userInfo
     * @return
     */
    public BizFieldMarkingDto getBySidAndFormType(String sid, String formType, UserInfoToken userInfo) {
        BizFieldMarking bizFieldMarking = bizFieldMarkingMapper.getBySidAndFormType(sid, formType);
        if (bizFieldMarking != null) {
            return bizFieldMarkingDtoMapper.toDto(bizFieldMarking);
        }
        return null;
    }

    /**
     * 新增字段标记信息
     *
     * @param param
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizFieldMarkingDto insert(BizFieldMarkingParam param, UserInfoToken userInfo) {
        BizFieldMarking bizFieldMarking = bizFieldMarkingDtoMapper.toPo(param);
        
        // 设置主键
        String id = UUID.randomUUID().toString();
        bizFieldMarking.setId(id);
        
        // 设置默认表单类型
        if (StringUtils.isBlank(bizFieldMarking.getFormType())) {
            bizFieldMarking.setFormType("default");
        }
        
        // 设置创建信息
        Date now = new Date();
        bizFieldMarking.setCreateTime(now);
        bizFieldMarking.setUpdateTime(now);
        bizFieldMarking.setCreateUser(userInfo.getUserNo());
        bizFieldMarking.setUpdateUser(userInfo.getUserNo());
        
        bizFieldMarkingMapper.insertSelective(bizFieldMarking);
        
        return bizFieldMarkingDtoMapper.toDto(bizFieldMarking);
    }

    /**
     * 更新字段标记信息
     *
     * @param param
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizFieldMarkingDto update(BizFieldMarkingParam param, UserInfoToken userInfo) {
        if (StringUtils.isBlank(param.getId())) {
            throw new RuntimeException("主键ID不能为空");
        }
        
        BizFieldMarking existingRecord = bizFieldMarkingMapper.selectByPrimaryKey(param.getId());
        if (existingRecord == null) {
            throw new RuntimeException("记录不存在");
        }
        
        // 可以在这里添加权限检查逻辑
        
        // 更新字段
        bizFieldMarkingDtoMapper.updatePo(param, existingRecord);
        
        // 设置更新信息
        Date now = new Date();
        existingRecord.setUpdateTime(now);
        existingRecord.setUpdateUser(userInfo.getUserNo());
        
        bizFieldMarkingMapper.updateByPrimaryKeySelective(existingRecord);
        
        return bizFieldMarkingDtoMapper.toDto(existingRecord);
    }

    /**
     * 保存或更新字段标记信息（根据sid和formType判断）
     *
     * @param param
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizFieldMarkingDto saveOrUpdate(BizFieldMarkingParam param, UserInfoToken userInfo) {
        if (StringUtils.isBlank(param.getSid())) {
            throw new RuntimeException("表单记录ID不能为空");
        }
        
        String formType = StringUtils.isBlank(param.getFormType()) ? "default" : param.getFormType();
        BizFieldMarking existingRecord = bizFieldMarkingMapper.getBySidAndFormType(param.getSid(), formType);
        
        if (existingRecord != null) {
            // 更新
            param.setId(existingRecord.getId());
            return update(param, userInfo);
        } else {
            // 新增
            return insert(param, userInfo);
        }
    }

    /**
     * 删除字段标记信息
     *
     * @param id
     * @param userInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id, UserInfoToken userInfo) {
        if (StringUtils.isBlank(id)) {
            throw new RuntimeException("主键ID不能为空");
        }
        
        BizFieldMarking existingRecord = bizFieldMarkingMapper.selectByPrimaryKey(id);
        if (existingRecord == null) {
            throw new RuntimeException("记录不存在");
        }
        
        // 可以在这里添加权限检查逻辑
        
        bizFieldMarkingMapper.deleteByPrimaryKey(id);
    }
}