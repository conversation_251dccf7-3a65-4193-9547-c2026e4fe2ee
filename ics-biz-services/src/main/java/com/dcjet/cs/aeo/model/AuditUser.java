package com.dcjet.cs.aeo.model;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Transient;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter @Getter
@ApiModel(value = "内审差错统计返回信息")
public class AuditUser implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 企业编码
	 */
	private String tradeCode;
	/**
	 * 创建人
	 */
	private  String insertUser;
	/**
	 * 创建人
	 */
	private  String insertUserName;
	/**
	 * 审批类型
	 */
	private  String apprType;
	/**
	 * 总制单票数
	 */
	private  String allCount;
	/**
	 * 无差错票数
	 */
	private  String noErrorCount;
	/**
	 * 内审退回票数
	 */
	private  String rollBackCount;
	/**
	 * 总内审通过票数
	 */
	private  String passCount;

	/**
	 * 制单准确率
	 */
	private String percent;
	/**
	 * 创建时间-开始
	 */
	@Transient
	private String insertTimeFrom;
	/**
	 * 创建时间-结束
	 */
	@Transient
	private String insertTimeTo;

}
