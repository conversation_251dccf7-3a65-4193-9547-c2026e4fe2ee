package com.dcjet.cs.aeo.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter
@Getter
@Table(name = "T_AEO_AUDIT_INFO")
public class AeoAuditInfo extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审批类型
     */
    @Column(name = "APPR_TYPE")
    private String apprType;

    /**
     * 审批状态
     */
    @Column(name = "STATUS")
    private String status;

    /**
     * 内审员
     */
    @Column(name = "APPR_USER")
    private String apprUser;

    /**
     * 审批时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "APPR_DATE")
    private Date apprDate;

    /**
     * 审批时间-开始
     */
    @Transient
    private String apprDateFrom;

    /**
     * 审批时间-结束
     */
    @Transient
    private String apprDateTo;

    /**
     * 审核意见
     */
    @Column(name = "APPR_NOTE")
    private String apprNote;

    /**
     * 审批单据SID
     */
    @Column(name = "BILL_SID")
    private String businessSid;

    /**
     * 创建时间-开始
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 创建时间-结束
     */
    @Transient
    private String insertTimeTo;

    /**
     * 创建人
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;

    /**
     * 更新时间-开始
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束
     */
    @Transient
    private String updateTimeTo;

    /**
     * 企业代码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 审核方式
     */
    @Column(name = "AUDIT_TYPE")
    private String auditType;

    @Transient
    private String apprTypeName;

    @Transient
    private String statusName;

}
