package com.dcjet.cs.aeo.dao;

import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.model.AuditUser;
import com.dcjet.cs.dto.aeo.AeoAuditInfoDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * AeoAuditInfo
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
public interface AeoAuditInfoMapper extends Mapper<AeoAuditInfo> {
    /**
     * 查询获取数据
     *
     * @param aeoAuditInfo
     * @return
     */
    List<AeoAuditInfo> getList(AeoAuditInfo aeoAuditInfo);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);


}
