package com.dcjet.cs.aeo.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcjet.cs.aeo.dao.AeoAuditInfoMapper;
import com.dcjet.cs.aeo.mapper.AeoAuditInfoDtoMapper;
import com.dcjet.cs.aeo.model.AeoAuditInfo;

import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;


import com.dcjet.cs.dto.aeo.*;


import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.internal.util.StringHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Service
@Slf4j
public class AeoAuditInfoService extends BaseService<AeoAuditInfo> {
    @Resource
    private AeoAuditInfoMapper aeoAuditInfoMapper;

    @Resource
    private AeoAuditInfoDtoMapper aeoAuditInfoDtoMapper;

    @Override
    public Mapper<AeoAuditInfo> getMapper() {
        return aeoAuditInfoMapper;
    }

    @Resource
    private ExportService exportService;


    @Value("${dc.pcode.url}")
    private String pcodeUrl;

    @Resource
    private Validator validator;
    @Resource
    private CommonService commonService;
    /**
     * @param aeoAuditInfoParam
     * @param userInfo
     * @return
     */
    public List<AeoAuditInfoDto> selectListBySid(AeoAuditInfoParam aeoAuditInfoParam, UserInfoToken userInfo) {
        AeoAuditInfo aeoAuditInfo = aeoAuditInfoDtoMapper.toPo(aeoAuditInfoParam);
        aeoAuditInfo.setTradeCode(userInfo.getCompany());
        List<AeoAuditInfoDto> aeoAuditInfoDtos = new ArrayList<>();
        List<AeoAuditInfo> aeoAuditInfos = aeoAuditInfoMapper.getList(aeoAuditInfo);
        if (CollectionUtils.isNotEmpty(aeoAuditInfos)) {
            aeoAuditInfoDtos = aeoAuditInfos.stream().map(head -> {
                return aeoAuditInfoDtoMapper.toDto(head);
            }).collect(Collectors.toList());
        }

        return aeoAuditInfoDtos;
    }


    public void auditInsert(AeoAuditInfo aeoAuditInfo, Date date, String status, String userNo, String userName, String tradeCode) {
        log.info("内审日志--发送内审插入内审记录！");
        String sid = UUID.randomUUID().toString();
        aeoAuditInfo.setSid(sid);
        aeoAuditInfo.setApprUser(userNo);
        aeoAuditInfo.setApprDate(date);
        aeoAuditInfo.setStatus(status);
        aeoAuditInfo.setInsertUser(userNo);
        aeoAuditInfo.setInsertUserName(userName);
        aeoAuditInfo.setInsertTime(date);
        aeoAuditInfo.setTradeCode(tradeCode);
        aeoAuditInfoMapper.insert(aeoAuditInfo);
    }

}
