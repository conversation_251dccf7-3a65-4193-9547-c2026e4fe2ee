package com.dcjet.cs.aeo.mapper;

import com.dcjet.cs.aeo.model.BizFieldMarking;
import com.dcjet.cs.dto.aeo.BizFieldMarkingDto;
import com.dcjet.cs.dto.aeo.BizFieldMarkingParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * 表单字段标记信息映射器
 *
 * <AUTHOR>
 * @date 2024
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizFieldMarkingDtoMapper {
    
    /**
     * 转换数据库对象到DTO
     *
     * @param po
     * @return
     */
    BizFieldMarkingDto toDto(BizFieldMarking po);

    /**
     * 转换DTO到数据库对象
     *
     * @param param
     * @return
     */
    BizFieldMarking toPo(BizFieldMarkingParam param);

    /**
     * 更新数据库对象
     *
     * @param param
     * @param target
     */
    void updatePo(BizFieldMarkingParam param, @MappingTarget BizFieldMarking target);
}