<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.attach.dao.AttachedMapper">
    <resultMap id="attachedResultMap" type="com.dcjet.cs.attach.model.Attached">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="HEAD_ID" property="businessSid" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="ACMP_TYPE" property="acmpType" jdbcType="VARCHAR"/>
        <result column="ACMP_NO" property="acmpNo" jdbcType="VARCHAR"/>
        <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="ORIGIN_FILE_NAME" property="originFileName" jdbcType="VARCHAR"/>
        <result column="FDFS_ID" property="fdfsId" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="VARCHAR"/>
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
     SID
     ,TRADE_CODE
     ,HEAD_ID
     ,BUSINESS_TYPE
     ,ACMP_TYPE
     ,ACMP_NO
     ,FILE_NAME
     ,NOTE
     ,ORIGIN_FILE_NAME
     ,FDFS_ID
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,DATA_SOURCE
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and SID = #{sid}
        </if>
        and TRADE_CODE = #{tradeCode}
        and HEAD_ID = #{businessSid}
        <if test="businessType != null and businessType != ''">
            and BUSINESS_TYPE = #{businessType}
        </if>
        <if test="acmpType != null and acmpType!=''">
            <choose>
                <when test="acmpType.split(',').length >1">
                    and t.ACMP_TYPE IN
                    <foreach collection="acmpType.split(',')" item="item" index="index" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and t.ACMP_TYPE = #{acmpType,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="acmpNo != null and acmpNo != ''">
            and ACMP_NO = #{acmpNo}
        </if>
        <if test="fileName != null and fileName != ''">
            and FILE_NAME = #{fileName}
        </if>
        <if test="note != null and note != ''">
            and NOTE = #{note}
        </if>
        <if test="originFileName != null and originFileName != ''">
            and ORIGIN_FILE_NAME like concat(concat('%',#{originFileName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="fdfsId != null and fdfsId != ''">
            and FDFS_ID = #{fdfsId}
        </if>
        <if test="dataSource != null and dataSource != ''">
            and DATA_SOURCE = #{dataSource}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="attachedResultMap" parameterType="com.dcjet.cs.attach.model.Attached">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_ATTACHED t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.insert_time, t.sid
    </select>
    <select id="getSidList" resultType="java.lang.String">
        SELECT
        SID
        FROM
        T_ATTACHED t
        <where>
            and t.TRADE_CODE = #{tradeCode}
            and t.HEAD_ID in
            <foreach collection="billSids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_ATTACHED t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <resultMap id="decPreAcmpInfoMap" type="com.xdo.domain.KeyValuePair">
        <result column="KEY" jdbcType="VARCHAR" property="key"/>
        <result column="VALUE" jdbcType="VARCHAR" property="value"/>
    </resultMap>

    <select id="getIPreAcmpInfo" resultMap="decPreAcmpInfoMap" parameterType="String">
        SELECT  'emsListNo' AS KEY ,
            concat(concat(T.MAWB, CASE WHEN ( T.MAWB IS NULL OR T.HAWB IS NULL ) THEN '' ELSE'_' END ), T.HAWB ) AS VALUE
             FROM T_DEC_ERP_I_HEAD_N T
        WHERE T.SID=#{headId,jdbcType=VARCHAR}
        UNION
        SELECT  'pactCode' AS KEY, T.CONTR_NO AS VALUE FROM T_DEC_ERP_I_HEAD_N T
        WHERE T.SID=#{headId,jdbcType=VARCHAR}
        UNION
        SELECT  'invoiceNo' AS KEY,  T.INVOICE_NO AS VALUE FROM T_DEC_ERP_I_HEAD_N T
        WHERE T.SID=#{headId,jdbcType=VARCHAR}
        UNION
        SELECT 'orderNo' AS KEY, T.ORDER_NO AS VALUE   FROM T_DEC_ERP_I_LIST_N T
        WHERE T.HEAD_ID=#{headId,jdbcType=VARCHAR}
        GROUP BY  T.ORDER_NO
        UNION
        SELECT 'invoiceNo' AS KEY, T.INVOICE_NO AS VALUE FROM T_DEC_ERP_I_LIST_N T
        WHERE T.HEAD_ID=#{headId,jdbcType=VARCHAR}
        GROUP BY  T.INVOICE_NO
        UNION ALL
        SELECT 'entryNo' AS KEY, T.ENTRY_NO AS VALUE FROM T_DEC_I_CUSTOMS_TRACK T
        WHERE T.ERP_HEAD_ID=#{headId,jdbcType=VARCHAR}
        GROUP BY T.ENTRY_NO
        UNION ALL
        SELECT 'origin' AS KEY, T.CERT_CODE AS VALUE FROM t_dec_erp_i_docu T
        WHERE T.HEAD_ID=#{headId,jdbcType=VARCHAR} and T.DOCU_CODE='Y'
        GROUP BY T.CERT_CODE
    </select>

    <select id="getEPreAcmpInfo" resultMap="decPreAcmpInfoMap" parameterType="String">
        SELECT  'emsListNo' AS KEY ,
           concat(concat(T.MAWB, CASE WHEN ( T.MAWB IS NULL OR T.HAWB IS NULL ) THEN '' ELSE'_' END ), T.HAWB ) AS VALUE
            FROM T_DEC_ERP_E_HEAD_N T
        WHERE T.SID=#{headId,jdbcType=VARCHAR}
        UNION
        SELECT  'pactCode' AS KEY, T.CONTR_NO AS VALUE FROM T_DEC_ERP_E_HEAD_N T
        WHERE T.SID=#{headId,jdbcType=VARCHAR}
        UNION
        SELECT  'invoiceNo' AS KEY,  T.INVOICE_NO AS VALUE FROM T_DEC_ERP_E_HEAD_N T
        WHERE T.SID=#{headId,jdbcType=VARCHAR}
        UNION
        SELECT 'orderNo' AS KEY, T.ORDER_NO AS  VALUE   FROM T_DEC_ERP_E_LIST_N T
        WHERE T.HEAD_ID=#{headId,jdbcType=VARCHAR}
        GROUP BY  T.ORDER_NO
        UNION
        SELECT 'invoiceNo' AS KEY, T.INVOICE_NO AS  VALUE FROM T_DEC_ERP_E_LIST_N T
        WHERE T.HEAD_ID=#{headId,jdbcType=VARCHAR}
        GROUP BY  T.INVOICE_NO
        UNION ALL
        SELECT 'entryNo' AS KEY, T.ENTRY_NO AS  VALUE FROM T_DEC_E_CUSTOMS_TRACK T
        WHERE T.ERP_HEAD_ID=#{headId,jdbcType=VARCHAR}
        GROUP BY T.ENTRY_NO
        UNION ALL
        SELECT 'origin' AS KEY, T.CERT_CODE AS VALUE FROM t_dec_erp_e_docu T
        WHERE T.HEAD_ID=#{headId,jdbcType=VARCHAR} and T.DOCU_CODE='Y'
        GROUP BY T.CERT_CODE

    </select>
    <select id="selectForFree" resultType="java.util.Map" parameterType="java.lang.String">
        select file_name as "FILE_NAME", origin_file_name as "ORIGIN_FILE_NAME" from t_attached
        where (origin_file_name like concat('%', '.jpg') or origin_file_name like concat('%', '.jpeg') or
        origin_file_name like concat('%', '.png'))
        and head_id = #{headId}
        order by insert_time
    </select>
    <select id="selectData" resultType="com.dcjet.cs.attach.model.Attached">
        select sid, file_name from t_attached where head_id = #{businessSid} and origin_file_name like concat(concat(#{fileName}, '%'), 'pdf')
    </select>

    <select id="getCustomClearAttached" resultType="java.util.Map">
        SELECT
            sid,
            origin_file_name originFileName,
            insert_time insertTime,
            insert_user insertUser,
            note note
        FROM
            t_attached
        WHERE
            head_id = #{headId} and business_type = 'customVehicle'
    </select>



    <select id="getAttachedDataByHeadId" resultType="com.dcjet.cs.attach.model.Attached">
        select sid,
               trade_code,
               head_id,
               business_type,
               acmp_type,
               acmp_no,
               file_name,
               note,
               origin_file_name,
               fdfs_id,
               insert_user,
               insert_time,
               update_user,
               update_time,
               insert_user_name,
               update_user_name,
               file_size,
               data_source
        from t_attached
        <where>
             head_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        </where>
    </select>

    <select id="getAttachedByHeadId" resultType="com.dcjet.cs.attach.model.Attached">
        select sid,
        trade_code,
        head_id,
        business_type,
        acmp_type,
        acmp_no,
        file_name,
        note,
        origin_file_name,
        fdfs_id,
        insert_user,
        insert_time,
        update_user,
        update_time,
        insert_user_name,
        update_user_name,
        file_size,
        data_source
        from t_attached
        <where>
            business_type = #{businessType}
            <if test="headId != null and headId != ''">
                and head_id IN (SELECT unnest(string_to_array(#{headId},',')))
            </if>
        </where>
    </select>

    <select id="getCustomsDocCount" resultType="java.lang.Integer">
        select count(1) from t_attached
        where head_id = #{erpHeadId} and acmp_type = 'customsDoc'
    </select>

    <select id="getByHeadId" resultType="com.dcjet.cs.attach.model.Attached">
        SELECT
            SID,
            TRADE_CODE,
            HEAD_ID,
            BUSINESS_TYPE,
            ACMP_TYPE,
            ACMP_NO,
            FILE_NAME,
            NOTE,
            ORIGIN_FILE_NAME,
            FDFS_ID,
            INSERT_USER,
            INSERT_TIME,
            UPDATE_USER,
            UPDATE_TIME,
            INSERT_USER_NAME,
            UPDATE_USER_NAME,
            FILE_SIZE,
            DATA_SOURCE
        FROM
            T_ATTACHED
        WHERE
            HEAD_ID = #{headId} and TRADE_CODE = #{tradeCode}
    </select>
</mapper>
