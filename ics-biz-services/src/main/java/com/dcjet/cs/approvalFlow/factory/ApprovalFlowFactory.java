package com.dcjet.cs.approvalFlow.factory;

import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ApprovalFlowFactory {

    private final Map<String, ApprovalFlowService> approvalFlowServiceMap;

    public ApprovalFlowFactory(Map<String, ApprovalFlowService> approvalFlowServiceMap) {
        this.approvalFlowServiceMap = approvalFlowServiceMap;
    }

    public ApprovalFlowService getService(String type) {
        ApprovalFlowService service =  approvalFlowServiceMap.get(type);
        if (service == null){
            throw new IllegalArgumentException("未配置对应审批流服务: " + type);
        }
        return service;
    }
}
