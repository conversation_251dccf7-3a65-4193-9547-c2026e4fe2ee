package com.dcjet.cs.payment.mapper;


import com.dcjet.cs.dto.params.RegistrationListDto;
import com.dcjet.cs.dto.params.RegistrationListParam;
import com.dcjet.cs.payment.model.RegistrationList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RegistrationListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    RegistrationListDto toDto(RegistrationList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    RegistrationList toPo(RegistrationListParam param);
    /**
     * 数据库原始数据更新
     * @param registrationListParam
     * @param registrationList
     */
    void updatePo(RegistrationListParam registrationListParam, @MappingTarget RegistrationList registrationList);
    default void patchPo(RegistrationListParam registrationListParam, RegistrationList registrationList) {
        // TODO 自行实现局部更新
    }
}
