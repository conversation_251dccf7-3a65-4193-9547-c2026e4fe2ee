package com.dcjet.cs.payment.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Setter
@Getter
@Table(name = "T_BIZ_REGISTRATION_HEAD")
public class RegistrationHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;

	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 贸易条款文本框
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;


	/**
	 * 单据号
	 */
	@Column(name = "DOCUMENT_NO")
	private String documentNo;

	/**
	 * 业务类型
	 */
	@Column(name = "BUSINESS_TYPE")
	private String businessType;

	/**
	 * 预付标志
	 */
	@Column(name = "ADVANCE_FLAG")
	private String advanceFlag;

	/**
	 * 部门
	 */
	@Column(name = "DEPARTMENT")
	private String department;

	/**
	 * 币种
	 */
	@Column(name = "CURRENCY")
	private String currency;

	/**
	 * 付款客户
	 */
	@Column(name = "PAYER_NAME")
	private String payerName;

	/**
	 * 委托单位
	 */
	@Column(name = "ENTRUST_COMPANY")
	private String entrustCompany;

	/**
	 * 银行手续费
	 */
	@Column(name = "BANK_FEE")
	private BigDecimal bankFee;

	/**
	 * 收款金额
	 */
	@Column(name = "PAYMENT_AMOUNT")
	private BigDecimal paymentAmount;

	/**
	 * 发送财务系统
	 */
	@Column(name = "FINANCE_FLAG")
	private String financeFlag;

	/**
	 * 业务日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "BUSINESS_DATE")
	private Date businessDate;
	/**
	 * 确认时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "CONFIRM_TIME")
	private Date confirmTime;

	/**
	 * 是否红冲
	 */
	@Column(name = "REVERSAL_FLAG")
	private String reversalFlag;

	/**
	 * 单据状态
	 */
	@Column(name = "DOCUMENT_STATUS")
	private String documentStatus;

	/**
	 * 备注
	 */
	@Column(name = "REMARK")
	private String remark;


	@Transient
	private String contractNo;
	@Transient
	private String curr;
	@Transient
	private BigDecimal decTotal;
	@Transient
	private BigDecimal qty;
	@Transient
	private String unit;
	@Transient
	private String orderNumber;


	/**
	 * 修改时间-开始
	 */
	@Transient
	private String updateTimeFrom;

	/**
	 * 修改时间-结束
	 */
	@Transient
	private String updateTimeTo;

}
