<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.payment.dao.NotifyHeadMapper">
    <resultMap id="bizMerchantResultMap" type="com.dcjet.cs.payment.model.NotifyHead">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>

		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />

		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="BIZ_TYPE" property="bizType" jdbcType="VARCHAR" />
		<result column="PAYEE" property="payee" jdbcType="VARCHAR" />
		<result column="entrust_company" property="entrustCompany" jdbcType="VARCHAR" />
		<result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR" />
		<result column="ORDER_NUMBER" property="orderNumber" jdbcType="VARCHAR" />
		<result column="PAY_AMT" property="payAmt" jdbcType="NUMERIC" />
		<result column="PAY_AMT_RMB" property="payAmtRmb" jdbcType="NUMERIC" />
		<result column="RATE" property="rate" jdbcType="NUMERIC" />
		<result column="CURR" property="curr" jdbcType="VARCHAR" />
		<result column="PREPAY_FLAG" property="prepayFlag" jdbcType="VARCHAR" />
		<result column="SEND_UFIDA" property="sendUfida" jdbcType="VARCHAR" />
		<result column="DOC_STATUS" property="docStatus" jdbcType="VARCHAR" />
		<result column="RECV_BANK" property="recvBank" jdbcType="VARCHAR" />
		<result column="RECV_ACCT" property="recvAcct" jdbcType="VARCHAR" />
		<result column="DOC_NO" property="docNo" jdbcType="VARCHAR" />
		<result column="red_flush" property="redFlush" jdbcType="VARCHAR" />
		<result column="DEPARTMENT" property="department" jdbcType="VARCHAR" />
		<result column="CFM_TIME" property="cfmTime" jdbcType="TIMESTAMP" />
		<result column="BIZ_DATE" property="bizDate" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,trade_code

     ,insert_user
     ,insert_time
     ,update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,note
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,BIZ_TYPE
     ,PAYEE
     ,entrust_company
     ,CONTRACT_NO
     ,ORDER_NUMBER
     ,PAY_AMT
     ,CURR
     ,PREPAY_FLAG
     ,SEND_UFIDA
     ,DOC_STATUS
     ,CFM_TIME
     ,BIZ_DATE
     ,RECV_BANK
     ,RECV_ACCT
     ,PAY_AMT_RMB
     ,RATE
     ,DOC_NO
     ,DEPARTMENT
     ,red_flush
    </sql>
    <sql id="condition">
        <if test="bizType != null and bizType != ''"> and BIZ_TYPE = #{bizType} </if>
        <if test="payee != null and payee != ''"> and payee = #{payee} </if>
        <if test="docStatus != null and docStatus != ''"> and DOC_STATUS = #{docStatus} </if>
        <if test="docStatus == null or docStatus == ''"> and DOC_STATUS != '2' </if>
        <if test="contractNo != null and contractNo != ''"> and CONTRACT_NO like '%'|| #{contractNo} || '%' </if>
        <if test="orderNumber != null and orderNumber != ''"> and ORDER_NUMBER like '%'|| #{orderNumber} || '%' </if>
        <if test="updateUserName != null and updateUserName != ''"> and update_user_name like '%'|| #{updateUserName} || '%' </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.update_time >= to_timestamp(#{updateTimeFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.update_time < DATEADD(day, 1, to_timestamp(#{updateTimeTo}, 'yyyy-MM-dd HH24:mi:ss'))]]>
        </if>
         and trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizMerchantResultMap" parameterType="com.dcjet.cs.payment.model.NotifyHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_PAYMENT_NOTIFY_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(update_time, insert_time) DESC
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_PAYMENT_NOTIFY_HEAD t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;


        delete from T_BIZ_PAYMENT_NOTIFY_LIST t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>;
    </delete>

    <select id="getDocNo" resultType="java.lang.String">
        select MAX(SUBSTR(doc_no, LENGTH(doc_no) - 2, 3)::NUMERIC) as docNo
        from T_BIZ_PAYMENT_NOTIFY_HEAD
        where trade_code = #{tradeCode} and doc_no like  '%'|| #{docNo} || '%'
    </select>

    <select id="getPayeeList" resultMap="bizMerchantResultMap">
        select DISTINCT payee as merchantCode,mer.MERCHANT_NAME_CN as merchantNameCn from
        T_BIZ_PAYMENT_NOTIFY_HEAD head
        left join T_BIZ_MERCHANT mer on mer.MERCHANT_CODE=head.payee and mer.TRADE_CODE=head.TRADE_CODE
        where head.payee is not null
    </select>
</mapper>
