<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.payment.dao.RegistrationListMapper">
    <resultMap id="registrationListResultMap" type="com.dcjet.cs.payment.model.RegistrationList">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="order_number" property="orderNumber" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="dec_total" property="decTotal" jdbcType="VARCHAR" />
		<result column="invoice_number" property="invoiceNumber" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
        sid,
        trade_code,
        insert_user,
        insert_time,
        insert_user_name,
        update_user,
        update_time,
        update_user_name,
        extend1,
        extend2,
        extend3,
        extend4,
        extend5,
        extend6,
        extend7,
        extend8,
        extend9,
        extend10,
        head_id,
        contract_no,
        order_number,
        g_name,
        invoice_number,
        dec_total
    </sql>
    <sql id="condition">
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    <if test="headId != null and headId != ''">
		and head_id = #{headId}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="registrationListResultMap" parameterType="com.dcjet.cs.payment.model.RegistrationList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_REGISTRATION_LIST t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_REGISTRATION_LIST t
        where t.contract_no in (select l.contract_no
        from T_BIZ_REGISTRATION_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
        or t.order_number in (select l.order_number
        from T_BIZ_REGISTRATION_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)

    </delete>
    <delete id="deleteContractQtyIsZero">
        delete from T_BIZ_REGISTRATION_LIST t where t.head_id = #{headId}
        and t.contract_quantity = 0
    </delete>
    <insert id="insertByPlanList">
        <choose>
        <when test="businessType == '2'.toString()">
        insert into T_BIZ_REGISTRATION_LIST(sid,
        trade_code,
        insert_user,
        insert_time,
        insert_user_name,
        update_user,
        update_time,
        update_user_name,
        head_id,
        contract_no,
        order_number,
        g_name,
        invoice_number,
        dec_total)
        select sys_guid(),
        #{tradeCode},
        #{userNo},
        now(),
        #{userName},
        #{userNo},
        now(),
        #{userName},
        #{headId},
        p.CONTRACT_NO,
        '',
        l.G_NAME,
        '',
        l.AMOUNT
        FROM t_biz_i_auxmat_buy_contract p
        LEFT JOIN t_biz_i_auxmat_buy_contract_list l ON l.HEAD_ID = p.ID
        WHERE p.STATUS = '1' and p.trade_code = #{tradeCode} and p.contract_no = #{contractNo};

            update T_BIZ_REGISTRATION_HEAD t
            set PAYMENT_AMOUNT = (select max(PAYMENT_AMOUNT) from t_biz_i_auxmat_buy_contract p
            WHERE p.STATUS = '1' and p.trade_code = #{tradeCode} and p.contract_no = #{contractNo} limit 1)
            where t.sid = #{headId};

        </when>
            <when test="businessType == '6'.toString()">
                insert into T_BIZ_REGISTRATION_LIST(sid,
                trade_code,
                insert_user,
                insert_time,
                insert_user_name,
                update_user,
                update_time,
                update_user_name,
                head_id,
                contract_no,
                order_number,
                g_name,
                invoice_number,
                dec_total)
                select sys_guid(),
                #{tradeCode},
                #{userNo},
                now(),
                #{userName},
                #{userNo},
                now(),
                #{userName},
                #{headId},
                p.CONTRACT_NO,
                '',
                l.GOODS_NAME as G_NAME,
                '',
                l.AMOUNT
                FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD p
                LEFT JOIN T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l ON l.HEAD_ID = p.ID
                WHERE p.STATUS = '1' and p.trade_code = #{tradeCode} and p.contract_no = #{contractNo};

                update T_BIZ_REGISTRATION_HEAD t
                set PAYMENT_AMOUNT = (select max(PAYMENT_AMOUNT) from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD p
                WHERE p.STATUS = '1' and p.trade_code = #{tradeCode} and p.contract_no = #{contractNo} limit 1)
                where t.sid = #{headId};

            </when>
            <when test="businessType == '3'.toString()">
                insert into T_BIZ_REGISTRATION_LIST(sid,
                trade_code,
                insert_user,
                insert_time,
                insert_user_name,
                update_user,
                update_time,
                update_user_name,
                head_id,
                contract_no,
                order_number,
                g_name,
                invoice_number,
                dec_total)
                select sys_guid(),
                #{tradeCode},
                #{userNo},
                now(),
                #{userName},
                #{userNo},
                now(),
                #{userName},
                #{headId},
                p.CONTRACT_NO,
                '',
                l.G_NAME as G_NAME,
                '',
                l.MONEY_AMOUNT as AMOUNT
                FROM T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD p
                LEFT JOIN T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST l ON l.HEAD_ID = p.ID
                WHERE p.DATA_STATUS = '1' and p.trade_code = #{tradeCode} and p.contract_no = #{contractNo};

                update T_BIZ_REGISTRATION_HEAD t
                set PAYMENT_AMOUNT = (select sum(dec_total) from T_BIZ_REGISTRATION_LIST p where
                p.trade_code = #{tradeCode} and p.head_id = #{headId})
                where t.sid = #{headId};

            </when>
        </choose>



    </insert>
    <update id="updateListContractQty">
        <![CDATA[
        UPDATE T_BIZ_REGISTRATION_LIST cl
        SET
            formed_quantity = tmp.total_formed,
            executable_quantity = cl.PLAN_QUANTITY - tmp.total_formed,
            contract_quantity = cl.PLAN_QUANTITY - tmp.total_formed,
            total_value =
                CASE
                -- 检查计算结果是否在 DECIMAL(19,2) 的范围内
                WHEN ROUND((cl.PLAN_QUANTITY - tmp.total_formed) * cl.UNIT_PRICE, 2) <= 99999999999999999.99
                THEN ROUND((cl.PLAN_QUANTITY - tmp.total_formed) * cl.UNIT_PRICE, 2)
                ELSE NULL  -- 超出范围则置为 NULL
                END
            FROM (
                SELECT
                    h2.PLAN_NO,
                    l2.GOODS_BRAND,
                    SUM(l2.contract_quantity) AS total_formed
                FROM T_BIZ_REGISTRATION_LIST l2
                JOIN T_BIZ_I_CONTRACT_HEAD h2
                    ON h2.SID = l2.HEAD_ID
                WHERE h2.DATA_STATUS != '2' and h2.PLAN_NO = #{planNo}
                GROUP BY h2.PLAN_NO, l2.GOODS_BRAND
            ) tmp
        JOIN T_BIZ_I_CONTRACT_HEAD h
            ON h.SID = cl.HEAD_ID
            WHERE tmp.PLAN_NO = h.PLAN_NO
              AND tmp.GOODS_BRAND = cl.GOODS_BRAND
              AND h.PLAN_NO = #{planNo} and h.sid = #{sid};
        ]]>
    </update>
    <update id="updateListFormedQuantity">
        UPDATE T_BIZ_REGISTRATION_LIST cl
        SET
            formed_quantity = tmp.total_formed,
            executable_quantity = cl.PLAN_QUANTITY - tmp.total_formed
            FROM (
                SELECT
                    h2.PLAN_NO,
                    l2.GOODS_BRAND,
                    SUM(l2.contract_quantity) AS total_formed,
                    SUM(l2.PLAN_QUANTITY) AS total_plan
                FROM T_BIZ_REGISTRATION_LIST l2
                JOIN T_BIZ_I_CONTRACT_HEAD h2
                    ON h2.SID = l2.HEAD_ID
                WHERE h2.DATA_STATUS != '2'
                GROUP BY h2.PLAN_NO, l2.GOODS_BRAND
            ) tmp
        JOIN T_BIZ_I_CONTRACT_HEAD h
        ON h.SID = cl.HEAD_ID
        WHERE tmp.PLAN_NO = h.PLAN_NO
          AND tmp.GOODS_BRAND = cl.GOODS_BRAND
          AND h.PLAN_NO = #{planNo};
    </update>
    <select id="checkContractInOrderList" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_ORDER_LIST l
            left join T_BIZ_I_ORDER_HEAD h on h.sid = l.HEAD_ID
        where
            CONTRACT_LIST_ID = #{sid}
          and h.DATA_STATUS != '2';
    </select>
    <select id="copyListByHeadId">
        insert into T_BIZ_REGISTRATION_LIST(
                                           sid
                                         ,business_type
                                         ,data_status
                                         ,version_no
                                         ,trade_code
                                         ,parent_id
                                         ,head_id
                                         ,goods_brand
                                         ,unit
                                         ,plan_quantity
                                         ,formed_quantity
                                         ,executable_quantity
                                         ,contract_quantity
                                         ,curr
                                         ,unit_price
                                         ,total_value
                                         ,goods_category
                                         ,insert_user
                                         ,insert_time
        )
        select
             sys_guid()
             ,business_type
             ,data_status
             ,version_no
             ,trade_code
             ,parent_id
             ,#{headId}
             ,goods_brand
             ,unit
             ,plan_quantity
             ,formed_quantity
             ,executable_quantity
             ,contract_quantity
             ,curr
             ,unit_price
             ,total_value
             ,goods_category
             ,#{userNo}
             ,now()
        from T_BIZ_REGISTRATION_LIST where head_id = #{oldHeadId}
    </select>
    <select id="getContractListByHeadId" resultType="com.dcjet.cs.payment.model.RegistrationList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_REGISTRATION_LIST t
        <where>
            head_id = #{sid}
        </where>
    </select>
    <select id="getContractTotal" resultType="com.dcjet.cs.dto.dec.BizIListTotal">
        SELECT
            sum(t.dec_total) as decTotal
        FROM
        T_BIZ_REGISTRATION_LIST t
        <where>
            <include refid="condition"></include>
        </where>
    </select>

    <update id="updateCorrelationID">
        update T_BIZ_REGISTRATION_LIST set parent_id = #{newListId} where parent_id = #{oldSid};
    </update>

    <select id="selectByHeadId" resultMap="registrationListResultMap">
        select * from T_BIZ_REGISTRATION_LIST where head_id = #{sid}
    </select>
</mapper>
