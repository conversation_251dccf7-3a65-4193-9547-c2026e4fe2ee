package com.dcjet.cs.payment.service;

import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizIOrderListMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseHeadMapper;

import com.dcjet.cs.dto.params.RegistrationHeadDto;
import com.dcjet.cs.dto.params.RegistrationHeadParam;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.importedCigarettes.dao.BizIContractListMapper;

import com.dcjet.cs.importedCigarettes.model.BizIContractList;
import com.dcjet.cs.importedCigarettes.service.BizIContractListService;
import com.dcjet.cs.payment.dao.RegistrationListMapper;
import com.dcjet.cs.payment.dao.RegistrationHeadMapper;
import com.dcjet.cs.payment.dao.RegistrationListMapper;
import com.dcjet.cs.payment.mapper.RegistrationHeadDtoMapper;
import com.dcjet.cs.payment.model.RegistrationHead;
import com.dcjet.cs.payment.model.RegistrationList;
import com.dcjet.cs.payment.model.RegistrationHead;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Slf4j
@Service
public class RegistrationHeadService extends BaseService<RegistrationHead> {
    @Resource
    private BizIContractListService bizIContractListService;
    @Resource
    private RegistrationHeadMapper registrationHeadMapper;
    @Resource
    private RegistrationListMapper registrationListMapper;
    @Resource
    private BizIContractListMapper bizIContractListMapper;
    @Resource
    private RegistrationHeadDtoMapper registrationHeadDtoMapper;
    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;
    @Resource
    private BizIOrderListMapper bizIOrderListMapper;
    @Resource
    private BizIPurchaseHeadMapper bizIPurchaseHeadMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private AttachedService attachedService;
    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;
    @Resource
    private BizINonStateAuxmatAggrContractHeadMapper bizINonStateAuxmatAggrContractHeadMapper;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;

    @Override
    public Mapper<RegistrationHead> getMapper() {
        return registrationHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param registrationHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<RegistrationHeadDto>> getListPaged(RegistrationHeadParam registrationHeadParam, PageParam pageParam) {
        // 启用分页查询
        RegistrationHead registrationHead = registrationHeadDtoMapper.toPo(registrationHeadParam);
        Page<RegistrationHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> registrationHeadMapper.getList(registrationHead));
        List<RegistrationHeadDto> registrationHeadDtos = page.getResult().stream().map(head -> {
            RegistrationHeadDto dto = registrationHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<RegistrationHeadDto>> paged = ResultObject.createInstance(registrationHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public ResultObject<List<RegistrationHeadDto>> getPlanListPaged(RegistrationHeadParam registrationHeadParam, PageParam pageParam) {
        // 启用分页查询
        RegistrationHead registrationHead = registrationHeadDtoMapper.toPo(registrationHeadParam);
        Page<RegistrationHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> registrationHeadMapper.getPlanListPaged(registrationHead));
        List<RegistrationHeadDto> registrationHeadDtos = page.getResult().stream().map(head -> {
            RegistrationHeadDto dto = registrationHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<RegistrationHeadDto>> paged = ResultObject.createInstance(registrationHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RegistrationHeadDto insert(RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        RegistrationHead registrationHead = registrationHeadDtoMapper.toPo(registrationHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        String docNo = "";
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> list = bizMerchantMapper.getList(bizMerchant);
        if (StringUtils.isNotBlank(registrationHeadParam.getBusinessType())){
            if ("2".equals(registrationHeadParam.getBusinessType())){
                docNo = "FLSKDJ";
                registrationHead.setPayerName("江苏中烟工业有限责任公司");
                registrationHead.setEntrustCompany("江苏中烟工业有限责任公司");
            }else if ("6".equals(registrationHeadParam.getBusinessType())){
                docNo = "JKSKDJ";
                BizINonStateAuxmatAggrContractHead bizHead = bizINonStateAuxmatAggrContractHeadMapper.selectByContractNo(registrationHead.getContractNo(),userInfo.getCompany());
                if (bizHead!=null&&CollectionUtils.isNotEmpty(list)){
                    //从list中匹配对应的供应商代码转换成中文名
                    RegistrationHead finalRegistrationHead = registrationHead;
                    list.stream().filter(x -> x.getMerchantCode().equals(bizHead.getDomesticPrincipal())).findFirst().ifPresent(merchant -> {
                        finalRegistrationHead.setPayerName(merchant.getMerchantNameCn());
                        finalRegistrationHead.setEntrustCompany(merchant.getMerchantNameCn());
                    });
                    registrationHead.setPayerName(finalRegistrationHead.getPayerName());
                    registrationHead.setEntrustCompany(finalRegistrationHead.getEntrustCompany());
                }
            }else if ("3".equals(registrationHeadParam.getBusinessType())){
                docNo = "FLSKDJ";
                ForeignContractHead bizHead = foreignContractHeadMapper.selectByContractNo(registrationHead.getContractNo(),userInfo.getCompany());
                if (bizHead!=null&&CollectionUtils.isNotEmpty(list)){
                    //从list中匹配对应的供应商代码转换成中文名
                    RegistrationHead finalRegistrationHead = registrationHead;
                    list.stream().filter(x -> x.getMerchantCode().equals(bizHead.getDomesticClient())).findFirst().ifPresent(merchant -> {
                        finalRegistrationHead.setPayerName(merchant.getMerchantNameCn());
                        finalRegistrationHead.setEntrustCompany(merchant.getMerchantNameCn());
                    });
                    registrationHead.setPayerName(finalRegistrationHead.getPayerName());
                    registrationHead.setEntrustCompany(finalRegistrationHead.getEntrustCompany());
                }
            }
        }
        registrationHead.setSid(sid);
        registrationHead.setInsertUser(userInfo.getUserNo());
        registrationHead.setInsertTime(new Date());
        registrationHead.setUpdateUser(userInfo.getUserNo());
        registrationHead.setUpdateUserName(userInfo.getUserName());
        registrationHead.setUpdateTime(new Date());
        registrationHead.setTradeCode(userInfo.getCompany());
        String documentNo = getDocNo(docNo,userInfo);
        registrationHead.setDocumentNo(documentNo);
        registrationHead.setBusinessType(registrationHeadParam.getBusinessType());
        registrationHead.setAdvanceFlag("0");
        registrationHead.setDepartment("业务一部");
        registrationHead.setCurrency("CNY");
        registrationHead.setFinanceFlag("0");
        registrationHead.setReversalFlag("1");
        registrationHead.setDocumentStatus("0");
        registrationHead.setBusinessDate(new Date());
        // 新增数据
        int insertStatus = registrationHeadMapper.insert(registrationHead);

        //表体从进口计划表体带值
        registrationListMapper.insertByPlanList(registrationHead.getContractNo(), registrationHead.getBusinessType(), registrationHead.getTradeCode(), sid, userInfo.getUserNo(),userInfo.getUserName());




        registrationHead = registrationHeadMapper.selectByPrimaryKey(sid);
        return  insertStatus > 0 ? registrationHeadDtoMapper.toDto(registrationHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param registrationHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RegistrationHeadDto update(RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        RegistrationHead registrationHead = registrationHeadMapper.selectByPrimaryKey(registrationHeadParam.getSid());
        registrationHeadDtoMapper.updatePo(registrationHeadParam, registrationHead);
        registrationHead.setUpdateUser(userInfo.getUserNo());
        registrationHead.setUpdateUserName(userInfo.getUserName());
        registrationHead.setUpdateTime(new Date());

        // 更新数据
        int update = registrationHeadMapper.updateByPrimaryKey(registrationHead);

        return update > 0 ? registrationHeadDtoMapper.toDto(registrationHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        if (registrationHeadMapper.checkCanDelBySids(sids) > 0){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }
		registrationHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<RegistrationHeadDto> selectAll(RegistrationHeadParam exportParam, UserInfoToken userInfo) {
        RegistrationHead registrationHead = registrationHeadDtoMapper.toPo(exportParam);
        registrationHead.setTradeCode(userInfo.getCompany());
        List<RegistrationHeadDto> registrationHeadDtos = new ArrayList<>();
        List<RegistrationHead> registrationHeads = registrationHeadMapper.getList(registrationHead);
        if (CollectionUtils.isNotEmpty(registrationHeads)) {
            registrationHeadDtos = registrationHeads.stream().map(head -> {
                RegistrationHeadDto dto = registrationHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return registrationHeadDtos;
    }

    public ResultObject confirmDataStatus(RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("确认成功"));
        RegistrationHead registrationHead = registrationHeadMapper.selectByPrimaryKey(registrationHeadParam.getSid());
        if (registrationHead == null) {
            throw new ErrorException(400, "收款登记数据不存在，请刷新");
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(registrationHead.getDocumentStatus())){
            throw new ErrorException(400, "该条数据已确认，不可重复确认！");
        }

        registrationHead.setDocumentStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        registrationHead.setUpdateUser(userInfo.getUserNo());
        registrationHead.setUpdateTime(new Date());
        registrationHead.setUpdateUserName(userInfo.getUserName());
        registrationHead.setConfirmTime(new Date());
        registrationHeadMapper.updateByPrimaryKey(registrationHead);
        result.setData(registrationHead.getConfirmTime());
        return result;
    }



    public ResultObject cancelDataStatus(RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("作废成功"));
        RegistrationHead registrationHead = registrationHeadMapper.selectByPrimaryKey(registrationHeadParam.getSid());
        if (registrationHead == null) {
            throw new ErrorException(400, "收款登记数据不存在，请刷新");
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(registrationHead.getDocumentStatus())){
            throw new ErrorException(400, "该条数据已作废，不可重复作废！");
        }

        registrationHead.setDocumentStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        registrationHead.setUpdateUser(userInfo.getUserNo());
        registrationHead.setUpdateTime(new Date());
        registrationHead.setUpdateUserName(userInfo.getUserName());

        registrationHeadMapper.updateByPrimaryKey(registrationHead);

        return result;
    }

    public ResultObject backDataStatus(RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("作废成功"));
        RegistrationHead registrationHead = registrationHeadMapper.selectByPrimaryKey(registrationHeadParam.getSid());
        if (registrationHead == null) {
            throw new ErrorException(400, "收款登记数据不存在，请刷新");
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(registrationHead.getDocumentStatus())){
            throw new ErrorException(400, "该条数据已退单，不可重复退单！");
        }

        registrationHead.setDocumentStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        registrationHead.setUpdateUser(userInfo.getUserNo());
        registrationHead.setUpdateTime(new Date());
        registrationHead.setUpdateUserName(userInfo.getUserName());

        registrationHeadMapper.updateByPrimaryKey(registrationHead);

        return result;
    }
    public ResultObject redRush(RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("红冲成功"));
        RegistrationHead registrationHead = registrationHeadMapper.selectByPrimaryKey(registrationHeadParam.getSid());
        if (registrationHead == null) {
            throw new ErrorException(400, "收款登记数据不存在，请刷新");
        }
        if (CommonEnum.IS_NOT_ENUM.YES.getType().equals(registrationHead.getReversalFlag())){
            throw new ErrorException(400, "该条数据已红冲，不可重复红冲！");
        }

        registrationHead.setReversalFlag(CommonEnum.IS_NOT_ENUM.YES.getType());
        registrationHead.setUpdateUser(userInfo.getUserNo());
        registrationHead.setUpdateTime(new Date());
        registrationHead.setUpdateUserName(userInfo.getUserName());

        registrationHeadMapper.updateByPrimaryKey(registrationHead);

        return result;
    }

    public ResultObject checkStatus(RegistrationHeadParam registrationHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("查询成功"));
        Integer count = registrationHeadMapper.checkStatusByContractNo(registrationHeadParam.getSid());
        result.setData(count);
        return result;
    }

    public String getDocNo(String docNo,UserInfoToken userInfo) {
        // 1. 获取当前年月（格式：yyyyMM）
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String currentYm = sdf.format(new Date());  // 示例输出：202310（2023年10月）

        // 2. 从数据库查询当前最大流水号（最后三位）
        String code = registrationHeadMapper.getDocNo(docNo + currentYm,userInfo.getCompany());

        // 3. 处理流水号逻辑
        if (StringUtils.isBlank(code)) {
            code = "001";  // 如果无记录，从001开始
        } else {
            int nextNumber = Integer.parseInt(code) + 1;
            if (nextNumber > 999) {
                nextNumber = 1;  // 超过999则重置为1（如：999 → 001）
            }
            code = String.format("%03d", nextNumber);  // 格式化为3位，不足补零
        }

        // 4. 拼接完整 doc_no
        return docNo + currentYm + code;
    }

    public ResultObject copy(RegistrationHeadParam RegistrationHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("复制成功"));
        RegistrationHead registrationHead = registrationHeadMapper.selectByPrimaryKey(RegistrationHeadParam.getSid());
        if (registrationHead == null) {
            throw new ErrorException(400, "收款登记表头数据不存在，请刷新");
        }

        //表头
        RegistrationHead newRegistrationHead = new RegistrationHead();
        BeanUtils.copyProperties(registrationHead, newRegistrationHead);  // 复制所有属性
        String sid = UUID.randomUUID().toString();
        String docNo = "";
        if (StringUtils.isNotBlank(newRegistrationHead.getBusinessType())){
            if ("2".equals(newRegistrationHead.getBusinessType())){
                docNo = "FLSKDJ";
            }else if ("6".equals(newRegistrationHead.getBusinessType())){
                docNo = "JKSKDJ";
            }
        }
        newRegistrationHead.setDocumentStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        newRegistrationHead.setDocumentNo(getDocNo(docNo,userInfo));
        newRegistrationHead.setSid(sid);
        newRegistrationHead.setInsertTime(new Date());
        newRegistrationHead.setBusinessDate(new Date());
        newRegistrationHead.setUpdateUser(userInfo.getUserNo());
        newRegistrationHead.setUpdateTime(new Date());
        newRegistrationHead.setUpdateUserName(userInfo.getUserName());
        newRegistrationHead.setInsertUser(userInfo.getUserNo());
        newRegistrationHead.setTradeCode(userInfo.getCompany());
        newRegistrationHead.setConfirmTime(null);
        newRegistrationHead.setReversalFlag("1");
        registrationHeadMapper.insert(newRegistrationHead);


        //表体
        List<RegistrationList>  registrationLists =  registrationListMapper.selectByHeadId(RegistrationHeadParam.getSid());
        List<RegistrationList> insertRegistrationList = new ArrayList<>();
        for (RegistrationList registrationList : registrationLists) {
            RegistrationList newRegistrationList = new RegistrationList();
            BeanUtils.copyProperties(registrationList, newRegistrationList);  // 复制所有属性
            String sid2 = UUID.randomUUID().toString();
            newRegistrationList.setSid(sid2);
            newRegistrationList.setHeadId(sid);
            newRegistrationList.setInsertTime(new Date());
            newRegistrationList.setInsertUser(userInfo.getUserNo());
            insertRegistrationList.add(newRegistrationList);
        }
        if (CollectionUtils.isNotEmpty(insertRegistrationList)) {
            BulkSqlOpt.batchInsertForErp(insertRegistrationList, RegistrationListMapper.class);
        }



        return result;
    }
}
