package com.dcjet.cs.payment.mapper;


import com.dcjet.cs.dto.params.RegistrationHeadDto;
import com.dcjet.cs.dto.params.RegistrationHeadParam;
import com.dcjet.cs.payment.model.RegistrationHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RegistrationHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    RegistrationHeadDto toDto(RegistrationHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    RegistrationHead toPo(RegistrationHeadParam param);
    /**
     * 数据库原始数据更新
     * @param registrationHeadParam
     * @param registrationHead
     */
    void updatePo(RegistrationHeadParam registrationHeadParam, @MappingTarget RegistrationHead registrationHead);
    default void patchPo(RegistrationHeadParam registrationHeadParam, RegistrationHead registrationHead) {
        // TODO 自行实现局部更新
    }
}
