package com.dcjet.cs.payment.dao;

import com.dcjet.cs.dto.dec.BizIListTotal;

import com.dcjet.cs.payment.model.RegistrationList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* RegistrationList
* <AUTHOR>
* @date: 2025-3-11
*/
public interface RegistrationListMapper extends Mapper<RegistrationList> {
    /**
     * 查询获取数据
     * @param registrationList
     * @return
     */
    List<RegistrationList> getList(RegistrationList registrationList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    void insertByPlanList(@Param("contractNo") String contractNo,@Param("businessType") String businessType,@Param("tradeCode") String tradeCode, @Param("headId") String headId, @Param("userNo") String userNo,@Param("userName") String userName);

    void updateListContractQty(@Param("sid") String sid, @Param("planNo") String planNo);

    void updateListFormedQuantity(@Param("planNo") String planNo);

    int checkContractInOrderList(@Param("sid") String sid);

    void copyListByHeadId(@Param("oldHeadId") String oldHeadId, @Param("headId") String headId, @Param("userNo") String userNo );

    List<RegistrationList> getContractListByHeadId(@Param("sid") String sid);

    void updateCorrelationID(@Param("newListId") String newListId, @Param("oldSid") String oldSid);

    BizIListTotal getContractTotal(RegistrationList registrationList);

    void deleteContractQtyIsZero(@Param("headId") String sid);

    List<RegistrationList> selectByHeadId(String sid);
}
