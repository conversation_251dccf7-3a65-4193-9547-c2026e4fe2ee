package com.dcjet.cs.payment.dao;


import com.dcjet.cs.payment.model.RegistrationHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
/**
* generated by Generate 神码
* RegistrationHead
* <AUTHOR>
* @date: 2025-3-7
*/
public interface RegistrationHeadMapper extends Mapper<RegistrationHead> {
    /**
     * 查询获取数据
     * @param registrationHead
     * @return
     */
    List<RegistrationHead> getList(RegistrationHead registrationHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkContractNoExits(RegistrationHead registrationHead);

    List<RegistrationHead>  getPlanListPaged(RegistrationHead registrationHead);

    void updateContractAmountAndQty(@Param("sid") String sid);

    RegistrationHead getAllListAmountAndQty(@Param("sid") String sid);

    int checkContractIsUsed(@Param("contractNo") String contractNo);

    Integer checkStatusByContractNo(@Param("sid") String sid);

    void updateCancelByContract(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    int checkCanDelBySids(List<String> sids);

    int checkContractIsUsedBySids(List<String> sids);

    RegistrationHead getMaxVersionNoByContract(RegistrationHead registrationHead);

    List<RegistrationHead> getSellerList(RegistrationHead registrationHead);

    String getBuyerCodeByName(@Param("name") String name, @Param("tradeCode") String tradeCode);

    String getDocNo(@Param("docNo") String docNo, @Param("tradeCode") String tradeCode);
}
