package com.dcjet.cs.payment.service;

import com.dcjet.cs.dto.dec.BizIListTotal;

import com.dcjet.cs.dto.params.RegistrationListDto;
import com.dcjet.cs.dto.params.RegistrationListParam;
import com.dcjet.cs.payment.dao.RegistrationHeadMapper;
import com.dcjet.cs.payment.dao.RegistrationListMapper;
import com.dcjet.cs.payment.mapper.RegistrationListDtoMapper;
import com.dcjet.cs.payment.model.RegistrationHead;
import com.dcjet.cs.payment.model.RegistrationList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Slf4j
@Service
public class RegistrationListService extends BaseService<RegistrationList> {
    @Resource
    private RegistrationListMapper registrationListMapper;
    @Resource
    private RegistrationHeadMapper registrationHeadMapper;
    @Resource
    private RegistrationListDtoMapper registrationListDtoMapper;
    public static final BigDecimal DECIMAL19_2_MAX = new BigDecimal("99999999999999999.99");

    @Override
    public Mapper<RegistrationList> getMapper() {
        return registrationListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param registrationListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<RegistrationListDto>> getListPaged(RegistrationListParam registrationListParam, PageParam pageParam) {
        // 启用分页查询
        RegistrationList registrationList = registrationListDtoMapper.toPo(registrationListParam);
        Page<RegistrationList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> registrationListMapper.getList(registrationList));
        List<RegistrationListDto> registrationListDtos = page.getResult().stream().map(head -> {
            RegistrationListDto dto = registrationListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<RegistrationListDto>> paged = ResultObject.createInstance(registrationListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param registrationListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RegistrationListDto insert(RegistrationListParam registrationListParam, UserInfoToken userInfo) {
        RegistrationList registrationList = registrationListDtoMapper.toPo(registrationListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        registrationList.setSid(sid);
        registrationList.setInsertUser(userInfo.getUserNo());
        registrationList.setInsertTime(new Date());
        // 新增数据
        int insertStatus = registrationListMapper.insert(registrationList);
        return  insertStatus > 0 ? registrationListDtoMapper.toDto(registrationList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param registrationListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RegistrationListDto update(RegistrationListParam registrationListParam, UserInfoToken userInfo) {
        RegistrationList registrationList = registrationListMapper.selectByPrimaryKey(registrationListParam.getSid());
        registrationListDtoMapper.updatePo(registrationListParam, registrationList);
        //校验对应进口计划 版本号是否一致
        if (registrationListMapper.checkContractInOrderList(registrationList.getSid()) > 0){
            throw new ErrorException(400, "所选数据已产生后续单据，请将后续单据作废后再进行编辑修改");
        }
        registrationList.setUpdateUser(userInfo.getUserNo());
        registrationList.setUpdateTime(new Date());


        // 更新数据
        int update = registrationListMapper.updateByPrimaryKey(registrationList);
        RegistrationHead registrationHead = registrationHeadMapper.selectByPrimaryKey(registrationList.getHeadId());
//        // 更新已形成合同数量
//        registrationListMapper.updateListFormedQuantity(registrationHead.getPlanNo());
        // 更新已形成合同数量，添加死锁异常捕获

        return update > 0 ? registrationListDtoMapper.toDto(registrationList) : null;
    }

    public void updateListFormedQuantity(String planNo){
        try {
            registrationListMapper.updateListFormedQuantity(planNo);
        } catch (Exception e) {
            // 捕获死锁异常
            if (e.toString().contains("dm.jdbc.driver.DMException: 死锁") ||
                    (e.getCause() != null && e.getCause().toString().contains("dm.jdbc.driver.DMException: 死锁"))) {
                // 记录日志
                log.error("更新已形成合同数量时发生死锁。合同计划号: " + planNo, e);
            } else {
                // 其他异常继续抛出
                throw e;
            }
        }
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {

		registrationListMapper.deleteBySids(sids);

    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<RegistrationListDto> selectAll(RegistrationListParam exportParam, UserInfoToken userInfo) {
        RegistrationList registrationList = registrationListDtoMapper.toPo(exportParam);
        // registrationList.setTradeCode(userInfo.getCompany());
        List<RegistrationListDto> registrationListDtos = new ArrayList<>();
        List<RegistrationList> registrationLists = registrationListMapper.getList(registrationList);
        if (CollectionUtils.isNotEmpty(registrationLists)) {
            registrationListDtos = registrationLists.stream().map(head -> {
                RegistrationListDto dto = registrationListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return registrationListDtos;
    }

    public ResultObject<RegistrationListDto> getContractTotal(RegistrationListParam registrationListParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");

        RegistrationList registrationList = registrationListDtoMapper.toPo(registrationListParam);
        registrationList.setTradeCode(userInfo.getCompany());
        BizIListTotal bizIListTotal = registrationListMapper.getContractTotal(registrationList);
        resultObject.setData(bizIListTotal);
        return resultObject;
    }
}
