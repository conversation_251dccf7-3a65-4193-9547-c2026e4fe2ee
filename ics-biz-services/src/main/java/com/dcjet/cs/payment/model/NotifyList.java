package com.dcjet.cs.payment.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter
@Getter
@Table(name = "T_BIZ_PAYMENT_NOTIFY_LIST")
public class NotifyList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;

	@Transient
	private  String updateTimeFrom;
	@Transient
	private  String updateTimeTo;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
	 * 合同编号
	 */
	@Column(name = "CONTRACT_NO")
	private String contractNo;

	/**
	 * 进货单号
	 */
	@Column(name = "ORDER_NUMBER")
	private String orderNumber;

	/**
	 * 订单号
	 */
	@Column(name = "ORDER_NO")
	private String orderNo;

	/**
	 * 商品名称
	 */
	@Column(name = "GOODS_NAME")
	private String goodsName;

	/**
	 * 发票号码
	 */
	@Column(name = "INVOICE_NUMBER")
	private String invoiceNumber;

	/**
	 * 数量
	 */
	@Column(name = "QTY")
	private BigDecimal qty;

	/**
	 * 单位
	 */
	@Column(name = "UNIT")
	private String unit;

	/**
	 * 支付金额
	 */
	@Column(name = "PAY_AMT")
	private BigDecimal payAmt;

	/**
	 * 支付金额（人民币）
	 */
	@Column(name = "PAY_AMT_RMB")
	private BigDecimal payAmtRmb;


	@Column(name = "HEAD_ID")
	private String headId;

	@Column(name = "turnover_sid")
	private String turnoverSid;


	@Transient
	private  BigDecimal includingTax;//含税单价
	@Transient
	private  BigDecimal includingTaxTotal;//含税单价
	@Transient
	private  BigDecimal agencyFees;//代理手续费
	@Transient
	private  BigDecimal advPymtTotal;//预付款合计
	@Transient
	private  String hthspmcje;//合同号+商品名称+金额
}
