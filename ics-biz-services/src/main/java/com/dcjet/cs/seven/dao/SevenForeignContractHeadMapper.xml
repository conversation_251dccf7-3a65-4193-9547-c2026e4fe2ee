<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper">
    <resultMap id="ForeignContractHeadResultMap" type="com.dcjet.cs.seven.model.SevenForeignContractHead">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="PREV_VERSION_ID" property="prevVersionId" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR"/>
        <result column="BUYER" property="buyer" jdbcType="VARCHAR"/>
        <result column="SELLER" property="seller" jdbcType="VARCHAR"/>
        <result column="SIGN_DATE" property="signDate" jdbcType="TIMESTAMP"/>
        <result column="SHIP_PERIOD_DATE" property="shipPeriodDate" jdbcType="TIMESTAMP"/>
        <result column="SIGN_PLACE_CN" property="signPlaceCn" jdbcType="VARCHAR"/>
        <result column="SIGN_PLACE_EN" property="signPlaceEn" jdbcType="VARCHAR"/>
        <result column="CONTRACT_EFFECTIVE_DATE" property="contractEffectiveDate" jdbcType="TIMESTAMP"/>
        <result column="CONTRACT_VALIDITY_DATE" property="contractValidityDate" jdbcType="TIMESTAMP"/>
        <result column="SHIPPING_PORT" property="shippingPort" jdbcType="VARCHAR"/>
        <result column="DEST_PORT" property="destPort" jdbcType="VARCHAR"/>
        <result column="PAYMENT_METHOD" property="paymentMethod" jdbcType="VARCHAR"/>
        <result column="CURR" property="curr" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM" property="priceTerm" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM_PORT" property="priceTermPort" jdbcType="VARCHAR"/>
        <result column="SUGGEST_AUTHOR_SIGNATORY" property="suggestAuthorSignatory" jdbcType="VARCHAR"/>
        <result column="SHORT_OVERFLOW_NUMBER" property="shortOverflowNumber" jdbcType="NUMERIC"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_MAKER" property="documentMaker" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_MAKER_NO" property="documentMakerNo" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_MAKE_DATE" property="documentMakeDate" jdbcType="TIMESTAMP"/>
        <result column="DATA_STATUS" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY_NAME" property="createByName" jdbcType="VARCHAR"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY_NAME" property="updateByName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        ID,
        PREV_VERSION_ID,
        BUSINESS_TYPE,
        CONTRACT_NO,
        BUYER,
        SELLER,
        SIGN_DATE,
        SHIP_PERIOD_DATE,
        SIGN_PLACE_CN,
        SIGN_PLACE_EN,
        CONTRACT_EFFECTIVE_DATE,
        CONTRACT_VALIDITY_DATE,
        SHIPPING_PORT,
        DEST_PORT,
        PAYMENT_METHOD,
        CURR,
        PRICE_TERM,
        PRICE_TERM_PORT,
        SUGGEST_AUTHOR_SIGNATORY,
        SHORT_OVERFLOW_NUMBER,
        NOTE,
        VERSION_NO,
        DOCUMENT_MAKER,
        DOCUMENT_MAKER_NO,
        DOCUMENT_MAKE_DATE,
        DATA_STATUS,
        CONFIRM_TIME,
        APPR_STATUS,
        TRADE_CODE,
        SYS_ORG_CODE,
        CREATE_BY,
        CREATE_TIME,
        CREATE_BY_NAME,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_BY_NAME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>

    <sql id="condition">
        <if test="true">
            TRADE_CODE = #{tradeCode}
        </if>
        <choose>
            <when test="dataStatus != null and dataStatus != ''">
                and DATA_STATUS = #{dataStatus}
            </when>
            <otherwise>
                and DATA_STATUS &lt;> '2'
            </otherwise>
        </choose>
        <if test="apprStatus != null and apprStatus != ''">
            and APPR_STATUS = #{apprStatus}
        </if>
        <if test="contractNo != null and contractNo != ''">
            and CONTRACT_NO like CONCAT('%', #{contractNo}, '%')
        </if>
        <if test="buyer != null and buyer != ''">
            and BUYER = #{buyer}
        </if>
        <if test="seller != null and seller != ''">
            and SELLER = #{seller}
        </if>
        <if test="signDateFrom != null and signDateFrom != ''">
            and SIGN_DATE >= TO_DATE(#{signDateFrom}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="signDateTo != null and signDateTo != ''">
            and SIGN_DATE &lt; DATEADD(DAY, 1, TO_DATE(#{signDateTo}, 'yyyy-MM-dd hh24:mi:ss'))
        </if>
        <if test="documentMakeDateFrom != null and documentMakeDateFrom != ''">
            and DOCUMENT_MAKE_DATE >= TO_DATE(#{documentMakeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="documentMakeDateTo != null and documentMakeDateTo != ''">
            and DOCUMENT_MAKE_DATE &lt; DATEADD(DAY, 1, TO_DATE(#{documentMakeDateTo}, 'yyyy-MM-dd hh24:mi:ss'))
        </if>
        <if test="documentMaker != null and documentMaker != ''">
            and DOCUMENT_MAKER = #{documentMaker}
        </if>
    </sql>

    <select id="getList" resultMap="ForeignContractHeadResultMap"
            parameterType="com.dcjet.cs.seven.model.SevenForeignContractHead">
        select
        <include refid="columns"/>
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        <where>
            <include refid="condition"/>
        </where>
        order by DOCUMENT_MAKE_DATE desc
    </select>

    <select id="getByIds" resultMap="ForeignContractHeadResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getAllMakers" resultMap="ForeignContractHeadResultMap">
        select distinct DOCUMENT_MAKER,
                        DOCUMENT_MAKER_NO
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        where TRADE_CODE = #{tradeCode}
    </select>

    <delete id="deleteByIds">
        delete from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="confirmById">
        update T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        set DATA_STATUS    = '1',
            CONFIRM_TIME   = current_timestamp,
            UPDATE_BY      = #{userNo},
            UPDATE_BY_NAME = #{userName},
            UPDATE_TIME    = current_timestamp
        where ID = #{id}
    </update>

    <update id="invalidateById">
        update T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_BY      = #{userNo},
            UPDATE_BY_NAME = #{userName},
            UPDATE_TIME    = current_timestamp
        where ID = #{id}
    </update>

    <select id="getCountByContractNo" resultType="int">
        select count(1)
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        where TRADE_CODE = #{tradeCode}
          and CONTRACT_NO = #{contractNo}
    </select>

    <select id="getByContractNo" resultMap="ForeignContractHeadResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        where TRADE_CODE = #{tradeCode} and CONTRACT_NO = #{contractNo}
    </select>

    <select id="getMaxVersionContract" resultMap="ForeignContractHeadResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        where CONTRACT_NO = #{contractNo}
        and TRADE_CODE = #{tradeCode}
        order by CAST(VERSION_NO AS INTEGER) desc limit 1
    </select>

    <update id="invalidateByContractNo">
        update T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName},
            UPDATE_TIME    = current_timestamp
        where CONTRACT_NO = #{contractNo}
        and TRADE_CODE = #{tradeCode}
        and DATA_STATUS &lt;> '2'
    </update>

    <select id="getFlowList" resultType="com.dcjet.cs.common.model.WorkFlowParam">
        select
        id as sid,
        extend1 as FLOW_INSTANCE_ID
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")"  >
            #{id}
        </foreach>
    </select>

    <select id="getAeoList" resultMap="ForeignContractHeadResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD
        <where>
            <include refid="condition"/>
            <if test="ids != null and ids.size() > 0">
                and ID in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by DOCUMENT_MAKE_DATE desc
    </select>
</mapper>