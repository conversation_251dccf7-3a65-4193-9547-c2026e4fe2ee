package com.dcjet.cs.seven.mapper;

import com.dcjet.cs.dto.seven.SevenForeignContractHeadDto;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadParam;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SevenForeignContractHeadDtoMapper {

    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    SevenForeignContractHeadDto toDto(SevenForeignContractHead po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    SevenForeignContractHead toPo(SevenForeignContractHeadParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(SevenForeignContractHeadParam param, @MappingTarget SevenForeignContractHead po);
}