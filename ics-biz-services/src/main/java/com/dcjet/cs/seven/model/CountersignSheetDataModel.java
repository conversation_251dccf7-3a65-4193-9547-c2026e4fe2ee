package com.dcjet.cs.seven.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class CountersignSheetDataModel {
    /**
     * 合同表头id
     */
    private String contractHeadId;

    /**
     * 表头
     */
    private Head head;

    /**
     * 表头
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @Builder
    public static class Head implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 合同号
         */
        private String contractNo;

        /**
         * 买方
         */
        private String buyer;

        /**
         * 合同金额（大写）
         */
        private String moneyAmountUpperString;

        /**
         * 合同金额（小写）
         */
        private String moneyAmountLowerString;

        /**
         * 合同情况简要介绍
         */
        private String contractOverview;
    }
}