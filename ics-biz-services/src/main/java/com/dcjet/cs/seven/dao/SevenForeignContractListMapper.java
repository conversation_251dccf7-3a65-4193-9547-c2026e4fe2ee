package com.dcjet.cs.seven.dao;

import com.dcjet.cs.dto.seven.SevenForeignContractAddBodyMaterialDto;
import com.dcjet.cs.dto.seven.SevenForeignContractListDto;
import com.dcjet.cs.dto.seven.SevenForeignContractListSummaryDto;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SevenForeignContractListMapper extends Mapper<SevenForeignContractList> {

    /**
     * 获取列表
     *
     * @param foreignContractList 外商合同表体
     * @return 外商合同表体列表
     */
    List<SevenForeignContractList> getList(SevenForeignContractList foreignContractList);

    /**
     * 根据表头主键获取列表
     *
     * @param headId 表头主键
     * @return 外商合同表体列表
     */
    List<SevenForeignContractList> getListByHeadId(@Param("headId") String headId, @Param("tradeCode") String tradeCode);

    /**
     * 根据表头主键列表获取列表
     *
     * @param headIds 表头主键列表
     * @return 外商合同表体列表
     */
    List<SevenForeignContractList> getListByHeadIds(@Param("headIds") List<String> headIds, @Param("tradeCode") String tradeCode);

    /**
     * 获取汇总信息
     *
     * @param contractBody 外商合同表体
     * @return 汇总信息
     */
    SevenForeignContractListSummaryDto getSummaryInfo(SevenForeignContractList contractBody);

    /**
     * 根据表头主键列表删除
     *
     * @param headIds   表头主键列表
     * @param tradeCode 企业编码
     */
    void deleteByHeadIds(@Param("headIds") List<String> headIds, @Param("tradeCode") String tradeCode);

    /**
     * 根据表头主键确认
     *
     * @param headId   表头主键
     * @param userNo   用户名
     * @param userName 用户名称
     */
    void confirmByHeadId(@Param("headId") String headId, @Param("userNo") String userNo, @Param("userName") String userName);

    /**
     * 根据表头主键作废
     *
     * @param headId   表头主键
     * @param userNo   用户名
     * @param userName 用户名称
     */
    void invalidateByHeadId(@Param("headId") String headId, @Param("userNo") String userNo, @Param("userName") String userName);

    /**
     * 根据主键列表删除
     *
     * @param ids 主键列表
     */
    void deleteByIds(@Param("ids") List<String> ids);

    /**
     * 根据合同号作废外商合同表体
     *
     * @param contractNo   合同编号
     * @param updateBy     更新人
     * @param updateByName 更新人名称
     * @param tradeCode    企业编码
     */
    void invalidateByContractNo(@Param("contractNo") String contractNo, @Param("updateBy") String updateBy,
                                @Param("updateByName") String updateByName, @Param("tradeCode") String tradeCode);
}