package com.dcjet.cs.seven.service;

import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.dto.seven.*;
import com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper;
import com.dcjet.cs.seven.dao.SevenForeignContractListMapper;
import com.dcjet.cs.seven.mapper.SevenForeignContractListDtoMapper;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SevenForeignContractListService extends BaseService<SevenForeignContractList> {
    @Resource
    private SevenForeignContractListMapper sevenForeignContractListMapper;
    @Resource
    private SevenForeignContractHeadMapper sevenForeignContractHeadMapper;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    @Resource
    private SevenForeignContractListDtoMapper sevenForeignContractListDtoMapper;

    @Override
    public Mapper<SevenForeignContractList> getMapper() {
        return this.sevenForeignContractListMapper;
    }

    /**
     * 获取新增表体物料信息
     *
     * @param userInfo 用户信息
     * @return 物料信息列表
     */
    public List<SevenForeignContractAddBodyMaterialDto> getMaterialInfoForAddBody(UserInfoToken<?> userInfo) {
        List<BizMaterialInformation> materialInfoList = this.bizMaterialInformationMapper.getMatByBusinessType(
                CommonEnum.COMMON_BUSINESS_TYPE_ENUM.PROCESSING_IMPORT_SHEET.getType(), userInfo.getCompany());
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return Collections.emptyList();
        }
        return materialInfoList.stream().map(mat ->
                new SevenForeignContractAddBodyMaterialDto()
                        .setId(mat.getSid())
                        .setFullEnName(mat.getFullEnName())
                        .setSupplier(mat.getSupplierCode())
                        .setMerchandiseCategories(mat.getMerchandiseCategories())
                        .setGName(mat.getGname())
        ).collect(Collectors.toList());
    }

    /**
     * 新增表体
     *
     * @param addBodyParam 表体新增参数
     * @param userInfo     用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBody(SevenForeignContractAddBodyParam addBodyParam, UserInfoToken<?> userInfo) {
        String headId = addBodyParam.getHeadId();
        if (StringUtils.isBlank(headId)) {
            throw new ErrorException(500, "表头id不能为空");
        }
        List<String> materialIds = addBodyParam.getMaterialIds();
        if (CollectionUtils.isEmpty(materialIds)) {
            throw new ErrorException(500, "请选择物料");
        }
        SevenForeignContractHead contractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(headId);
        if (contractHead == null) {
            throw new ErrorException(500, "对应表头数据不存在");
        }
        if (!Objects.equals(CommonEnum.STATE_ENUM.DRAFT.getType(), contractHead.getDataStatus())) {
            throw new ErrorException(500, "表头仅编制状态支持新增表体");
        }
        List<BizMaterialInformation> materialList = this.bizMaterialInformationMapper.getByIds(materialIds);
        if (CollectionUtils.isEmpty(materialList)) {
            return;
        }
        List<SevenForeignContractList> bodyList = new ArrayList<>(materialList.size());
        for (BizMaterialInformation material : materialList) {
            SevenForeignContractList body = new SevenForeignContractList();
            body.setId(UUID.randomUUID().toString()); // 主键
            body.setHeadId(headId); // 表头主键
            body.setGName(material.getGname()); // 商品名称
            body.setBodyType(addBodyParam.getBodyType()); // 表体类型
            body.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType()); // 数据状态
            body.setTradeCode(userInfo.getCompany()); // 企业编码
            body.setCreateBy(userInfo.getUserNo()); //  创建人
            body.setCreateByName(userInfo.getUserName()); // 创建人名称
            body.setCreateTime(new Date()); // 创建时间
            bodyList.add(body);
        }
        BulkSqlOpt.batchInsertAndThrowException(bodyList, SevenForeignContractListMapper.class);
    }

    /**
     * 获取分页列表
     *
     * @param foreignContractListParam 表体参数
     * @param pageParam                分页参数
     * @param userInfo                 用户信息
     * @return 表体分页列表信息
     */
    public ResultObject<List<SevenForeignContractListDto>> getListPaged(SevenForeignContractListParam foreignContractListParam,
                                                                        PageParam pageParam, UserInfoToken<?> userInfo) {
        SevenForeignContractList foreignContractList = this.sevenForeignContractListDtoMapper.toPo(foreignContractListParam);
        foreignContractList.setTradeCode(userInfo.getCompany());
        Page<SevenForeignContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.sevenForeignContractListMapper.getList(foreignContractList));
        List<SevenForeignContractListDto> listDtoList = page.getResult().stream()
                .map(fcl -> this.sevenForeignContractListDtoMapper.toDto(fcl))
                .collect(Collectors.toList());
        return ResultObject.createInstance(listDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 表体汇总
     *
     * @param foreignContractListParam 表体参数
     * @param userInfo                 用户信息
     * @return 表体汇总信息
     */
    public SevenForeignContractListSummaryDto getListSummary(SevenForeignContractListParam foreignContractListParam,
                                                             UserInfoToken<?> userInfo) {
        SevenForeignContractList contractBody = this.sevenForeignContractListDtoMapper.toPo(foreignContractListParam);
        contractBody.setTradeCode(userInfo.getCompany());
        return this.sevenForeignContractListMapper.getSummaryInfo(contractBody);
    }

    /**
     * 更新
     *
     * @param foreignContractListParam 表体参数
     * @param userInfo                 用户信息
     * @return 表体信息
     */
    @Transactional(rollbackFor = Exception.class)
    public SevenForeignContractListDto updateBody(SevenForeignContractListParam foreignContractListParam, UserInfoToken<?> userInfo) {
        String id = foreignContractListParam.getId();
        SevenForeignContractList foreignContractList = this.sevenForeignContractListMapper.selectByPrimaryKey(id);
        foreignContractList.setUpdateBy(userInfo.getUserNo());
        foreignContractList.setUpdateByName(userInfo.getUserName());
        foreignContractList.setUpdateTime(new Date());
        foreignContractList.setGModel(foreignContractListParam.getGModel());
        foreignContractList.setUnit(foreignContractListParam.getUnit());
        foreignContractList.setQty(foreignContractListParam.getQty());
        foreignContractList.setBoxNum(foreignContractListParam.getBoxNum());
        foreignContractList.setGrossWeight(foreignContractListParam.getGrossWeight());
        foreignContractList.setUnitPrice(foreignContractListParam.getUnitPrice());
        BigDecimal unitPrice = foreignContractList.getUnitPrice(), qty = foreignContractList.getQty();
        if (unitPrice != null && qty != null) {
            foreignContractList.setMoneyAmount(unitPrice.multiply(qty).setScale(4, RoundingMode.HALF_UP));
        }
        if (unitPrice == null || qty == null) {
            foreignContractList.setMoneyAmount(null);
        }
        foreignContractList.setNote(foreignContractListParam.getNote());
        return this.sevenForeignContractListMapper.updateByPrimaryKey(foreignContractList) > 0 ?
                this.sevenForeignContractListDtoMapper.toDto(foreignContractList) : null;
    }

    /**
     * 删除
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> ids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        this.sevenForeignContractListMapper.deleteByIds(ids);
    }
}