package com.dcjet.cs.seven.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST")
public class SevenForeignContractList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    private String id;

    /**
     * 上一个版本id
     */
    @Column(name = "PREV_VERSION_ID")
    private String prevVersionId;

    /**
     * 表头id
     */
    @Column(name = "HEAD_ID")
    private String headId;

    /**
     * 商品名称
     */
    @Column(name = "G_NAME")
    private String gName;

    /**
     * 产品型号
     */
    @Column(name = "G_MODEL")
    private String gModel;

    /**
     * 单位
     */
    @Column(name = "UNIT")
    private String unit;

    /**
     * 数量
     */
    @Column(name = "QTY")
    private BigDecimal qty;

    /**
     * 箱数
     */
    @Column(name = "BOX_NUM")
    private BigDecimal boxNum;

    /**
     * 毛重
     */
    @Column(name = "GROSS_WEIGHT")
    private BigDecimal grossWeight;

    /**
     * 单价
     */
    @Column(name = "UNIT_PRICE")
    private BigDecimal unitPrice;

    /**
     * 金额
     */
    @Column(name = "MONEY_AMOUNT")
    private BigDecimal moneyAmount;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 表体类型 process出料加工 slice进口薄片
     */
    @Column(name = "BODY_TYPE")
    private String bodyType;

    /**
     * 单据状态 0编制 1确认 2作废
     */
    @Column(name = "DATA_STATUS")
    private String dataStatus;

    /**
     * 确认时间
     */
    @Column(name = "CONFIRM_TIME")
    private Date confirmTime;

    /**
     * 创建人部门编码
     */
    @Column(name = "SYS_ORG_CODE")
    private String sysOrgCode;

    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 制单用户
     */
    @Column(name = "CREATE_BY")
    private String createBy;

    /**
     * 制单时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 修改用户
     */
    @Column(name = "UPDATE_BY")
    private String updateBy;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 制单用户名称
     */
    @Column(name = "CREATE_BY_NAME")
    private String createByName;

    /**
     * 修改用户名称
     */
    @Column(name = "UPDATE_BY_NAME")
    private String updateByName;

    /**
     * 扩展字段1
     */
    @Column(name = "EXTEND1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @Column(name = "EXTEND2")
    private String extend2;
    /**
     * 扩展字段3
     */
    @Column(name = "EXTEND3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @Column(name = "EXTEND4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @Column(name = "EXTEND5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @Column(name = "EXTEND6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @Column(name = "EXTEND7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @Column(name = "EXTEND8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @Column(name = "EXTEND9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @Column(name = "EXTEND10")
    private String extend10;

    @Transient
    private BigDecimal qtya;
}