package com.dcjet.cs.seven.service;

import com.aspose.cells.PdfSaveOptions;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import com.dcjet.cs.attach.dao.AttachedMapper;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.dto.seven.*;
import com.dcjet.cs.params.dao.CityMapper;
import com.dcjet.cs.params.dao.PriceTermsMapper;
import com.dcjet.cs.params.dao.ProductTypeMapper;
import com.dcjet.cs.params.model.City;
import com.dcjet.cs.params.model.PriceTerms;
import com.dcjet.cs.params.model.ProductType;
import com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper;
import com.dcjet.cs.seven.dao.SevenForeignContractListMapper;
import com.dcjet.cs.seven.mapper.SevenForeignContractHeadDtoMapper;
import com.dcjet.cs.seven.mapper.SevenForeignContractListDtoMapper;
import com.dcjet.cs.seven.model.CountersignSheetDataModel;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.FileUtil;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.vo.HttpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class SevenForeignContractHeadService extends BaseService<SevenForeignContractHead> implements ApprovalFlowService {
    private final static String VALUE = "value", LABEL = "label", EXT = "ext";
    private final static String PARAM_TYPE_CURR = "CURR", PARAM_TYPE_PORT = "PORT", PARAM_TYPE_UNIT = "UNIT";
    private final static String DEFAULT_BUSINESS_TYPE = CommonEnum.COMMON_BUSINESS_TYPE_ENUM
            .PROCESSING_IMPORT_SHEET.getType();
    private final static String DEFAULT_VERSION_NO = "1";
    private final static String DEFAULT_DATA_STATUS = CommonEnum.STATE_ENUM.DRAFT.getType();
    private final static String DEFAULT_APPR_STATUS = CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue();
    private final static String VERSION_ID_GAP = "_";
    private final static String PRINT_EXCEL_FILE = "会签单.xlsx", PRINT_EXCEL_FILE_NAME = "会签单";
    private final static String SYSTEM_PRINT_TEMPLATE_NAME = "7-外商合同会签单.xlsx";
    private final static String PDF_TYPE = "PDF", DIR = "DIR";

    @Resource
    private SevenForeignContractHeadMapper sevenForeignContractHeadMapper;
    @Resource
    private SevenForeignContractHeadDtoMapper sevenForeignContractHeadDtoMapper;
    @Resource
    private SevenForeignContractListMapper sevenForeignContractListMapper;
    @Resource
    private SevenForeignContractListDtoMapper sevenForeignContractListDtoMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private CityMapper cityMapper;
    @Resource
    private PriceTermsMapper priceTermsMapper;
    @Resource
    private ProductTypeMapper productTypeMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private ExportService exportService;
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;

    @Resource
    private AttachedMapper attachedMapper;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;

    @Value("${dc.export.temp:}")
    private String tempPath;

    @Override
    public Mapper<SevenForeignContractHead> getMapper() {
        return this.sevenForeignContractHeadMapper;
    }

    /**
     * 获取分页列表
     *
     * @param foreignContractHeadParam 外商合同表体参数
     * @param pageParam                分页参数
     * @param userInfo                 用户信息
     * @return 分页列表
     */
    public ResultObject<List<SevenForeignContractHeadDto>> getListPaged(SevenForeignContractHeadParam foreignContractHeadParam,
                                                                        PageParam pageParam, UserInfoToken<?> userInfo) {
        SevenForeignContractHead foreignContractHead = this.sevenForeignContractHeadDtoMapper.toPo(foreignContractHeadParam);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        int currentPage = pageParam.getPage(), currentLimit = pageParam.getLimit();
        Page<SevenForeignContractHead> page = PageHelper.startPage(currentPage, currentLimit, pageParam.getSortOrderContent())
                .doSelectPage(() -> this.sevenForeignContractHeadMapper.getList(foreignContractHead));
        List<SevenForeignContractHeadDto> headDtoList = page.getResult().stream()
                .map(this.sevenForeignContractHeadDtoMapper::toDto)
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 获取所有选项
     *
     * @param userInfo 用户信息
     * @return 选项信息
     */
    public SevenForeignContractOptionsDto getAllOptions(UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        SevenForeignContractOptionsDto optionsDto = new SevenForeignContractOptionsDto();
        // 客商
        List<BizMerchant> merchantList = this.bizMerchantMapper.getList(new BizMerchant().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(merchantList)) {
            List<Map<String, String>> merchantOptions = merchantList.stream().map(merchant -> {
                Map<String, String> map = new HashMap<>(2);
                map.put(VALUE, merchant.getMerchantCode());
                map.put(LABEL, merchant.getMerchantNameCn());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setMerchantOptions(merchantOptions);
        }
        // 制单人
        List<SevenForeignContractHead> allMakers = this.sevenForeignContractHeadMapper.getAllMakers(tradeCode);
        if (CollectionUtils.isNotEmpty(allMakers)) {
            List<Map<String, String>> makerOptions = allMakers.stream().map(maker -> {
                Map<String, String> map = new HashMap<>(2);
                map.put(VALUE, maker.getDocumentMakerNo());
                map.put(LABEL, maker.getDocumentMaker());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setMakerOptions(makerOptions);
        }
        // 商品类别
        List<ProductType> productTypeList = this.productTypeMapper.getList(new ProductType().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(productTypeList)) {
            List<Map<String, String>> productTypeOptions = productTypeList.stream().map(productType -> {
                Map<String, String> map = new HashMap<>(2);
                map.put(VALUE, productType.getCategoryCode());
                map.put(LABEL, productType.getCategoryName());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setProductTypeOptions(productTypeOptions);
        }
        // 价格条款
        List<PriceTerms> priceTermsList = this.priceTermsMapper.getList(new PriceTerms().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(priceTermsList)) {
            List<Map<String, String>> priceTermsOptions = priceTermsList.stream().map(priceTerm -> {
                Map<String, String> map = new HashMap<>(2);
                map.put(VALUE, priceTerm.getPriceTerm());
                map.put(LABEL, priceTerm.getPriceTerm());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setPriceTermsOptions(priceTermsOptions);
        }
        // 城市
        List<City> cityList = this.cityMapper.getList(new City().setTradeCode(tradeCode));
        if (CollectionUtils.isNotEmpty(cityList)) {
            List<Map<String, String>> cityOptions = cityList.stream().map(city -> {
                Map<String, String> map = new HashMap<>(3);
                map.put(VALUE, city.getCityCnName());
                map.put(LABEL, city.getCityCnName());
                map.put(EXT, city.getCityEnName());
                return map;
            }).collect(Collectors.toList());
            optionsDto.setCityOptions(cityOptions);
        }
        String[] paramTypes = {PARAM_TYPE_CURR, PARAM_TYPE_PORT, PARAM_TYPE_UNIT};
        List<BaseInfoCustomerParams> paramList = this.baseInfoCustomerParamsMapper
                .getParamsSelectByTypes(Arrays.asList(paramTypes), DEFAULT_BUSINESS_TYPE, tradeCode);
        Function<String, List<Map<String, String>>> getParamOptionsByType = paramType -> paramList.stream()
                .filter(param -> Objects.equals(param.getParamsType(), paramType))
                .map(param -> {
                    Map<String, String> map = new HashMap<>(3);
                    map.put(VALUE, param.getParamsCode());
                    map.put(LABEL, param.getParamsName());
                    map.put(EXT, param.getCustomParamCode());
                    return map;
                }).collect(Collectors.toList());
        // 币种
        optionsDto.setCurrOptions(getParamOptionsByType.apply(PARAM_TYPE_CURR));
        // 港口
        optionsDto.setPortOptions(getParamOptionsByType.apply(PARAM_TYPE_PORT));
        // 单位
        optionsDto.setUnitOptions(getParamOptionsByType.apply(PARAM_TYPE_UNIT));
        return optionsDto;
    }

    /**
     * 获取所有列表
     *
     * @param foreignContractHeadParam 外商合同表头参数
     * @param userInfo                 用户信息
     * @return 外商合同表头列表
     */
    public List<SevenForeignContractHeadDto> getAllList(SevenForeignContractHeadParam foreignContractHeadParam, UserInfoToken<?> userInfo) {
        SevenForeignContractHead foreignContractHead = this.sevenForeignContractHeadDtoMapper.toPo(foreignContractHeadParam);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        List<SevenForeignContractHead> foreignContractHeadList = this.sevenForeignContractHeadMapper.getList(foreignContractHead);
        if (CollectionUtils.isEmpty(foreignContractHeadList)) {
            return Collections.emptyList();
        }
        List<SevenForeignContractHeadDto> dtoList = foreignContractHeadList.stream()
                .map(fch -> this.sevenForeignContractHeadDtoMapper.toDto(fch))
                .collect(Collectors.toList());
        List<String> merchantCodes = Stream.concat(foreignContractHeadList.stream().map(SevenForeignContractHead::getBuyer),
                        foreignContractHeadList.stream().map(SevenForeignContractHead::getSeller))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<BizMerchant> merchants = CollectionUtils.isEmpty(merchantCodes) ? Collections.emptyList()
                : this.bizMerchantMapper.getByMerchantCodes(merchantCodes, userInfo.getCompany());
        dtoList.forEach(dto -> {
            if (StringUtils.isNotBlank(dto.getBusinessType())) {
                dto.setBusinessType(dto.getBusinessType() + StringUtils.SPACE +
                        CommonEnum.COMMON_BUSINESS_TYPE_ENUM.getValue(dto.getBusinessType()));
            }
            if (CollectionUtils.isNotEmpty(merchants)) {
                if (StringUtils.isNotBlank(dto.getBuyer())) {
                    String buyerName = merchants.stream()
                            .filter(merchant -> Objects.equals(merchant.getMerchantCode(), dto.getBuyer()))
                            .map(BizMerchant::getMerchantNameCn)
                            .filter(StringUtils::isNotBlank)
                            .findAny()
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotBlank(buyerName)) {
                        dto.setBuyer(dto.getBuyer() + StringUtils.SPACE + buyerName);
                    }
                }
                if (StringUtils.isNotBlank(dto.getSeller())) {
                    String sellerName = merchants.stream()
                            .filter(merchant -> Objects.equals(merchant.getMerchantCode(), dto.getSeller()))
                            .map(BizMerchant::getMerchantNameCn)
                            .filter(StringUtils::isNotBlank)
                            .findAny()
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotBlank(sellerName)) {
                        dto.setSeller(dto.getSeller() + StringUtils.SPACE + sellerName);
                    }
                }
            }
            if (StringUtils.isNotBlank(dto.getDataStatus())) {
                dto.setDataStatus(dto.getDataStatus() + StringUtils.SPACE +
                        CommonEnum.STATE_ENUM.getValue(dto.getDataStatus()));
            }
        });
        return dtoList;
    }

    /**
     * 新增外商合同
     *
     * @param addParam 新增参数
     * @param userInfo 用户信息
     * @return 外商合同传输对象
     */
    @Transactional(rollbackFor = Exception.class)
    public SevenForeignContractHeadDto insert(SevenForeignContractAddParam addParam, UserInfoToken<?> userInfo) {
        // 校验
        String contractNo = addParam.getHead().getContractNo();
        int sameContractNoHeadCount = this.sevenForeignContractHeadMapper.getCountByContractNo(contractNo, userInfo.getCompany());
        if (sameContractNoHeadCount > 0) {
            throw new ErrorException(500, "合同编号已存在，请重新录入");
        }
        // 新增表头
        SevenForeignContractHead contractHead = this.sevenForeignContractHeadDtoMapper.toPo(addParam.getHead());
        contractHead.setContractNo(contractNo);
        contractHead.setTradeCode(userInfo.getCompany());
        contractHead.setBusinessType(DEFAULT_BUSINESS_TYPE);
        contractHead.setVersionNo(DEFAULT_VERSION_NO);
        contractHead.setDataStatus(DEFAULT_DATA_STATUS);
        contractHead.setApprStatus(DEFAULT_APPR_STATUS);
        contractHead.setId(UUID.randomUUID() + VERSION_ID_GAP + DEFAULT_VERSION_NO);
        contractHead.setCreateBy(userInfo.getUserNo());
        contractHead.setCreateByName(userInfo.getUserName());
        contractHead.setCreateTime(new Date());
        contractHead.setDocumentMakerNo(userInfo.getUserNo());
        contractHead.setDocumentMaker(userInfo.getUserName());
        contractHead.setDocumentMakeDate(contractHead.getCreateTime());
        this.sevenForeignContractHeadMapper.insert(contractHead);
        // 新增表体
        List<SevenForeignContractListParam> processBodyList = addParam.getProcessBodyList();
        processBodyList.forEach(process ->
                process.setBodyType(CommonEnum.SEVEN_BODY_TYPE.PROCESS.getValue()));
        List<SevenForeignContractListParam> sliceBodyList = addParam.getSliceBodyList();
        sliceBodyList.forEach(slice ->
                slice.setBodyType(CommonEnum.SEVEN_BODY_TYPE.SLICE.getValue()));
        List<SevenForeignContractListParam> bodyParamList = ListUtils.sum(processBodyList, sliceBodyList);
        if (CollectionUtils.isEmpty(bodyParamList)) {
            return this.sevenForeignContractHeadDtoMapper.toDto(contractHead);
        }
        List<SevenForeignContractList> bodyList = new ArrayList<>(bodyParamList.size());
        for (SevenForeignContractListParam bodyParam : bodyParamList) {
            SevenForeignContractList body = this.sevenForeignContractListDtoMapper.toPo(bodyParam);
            BigDecimal unitPrice = body.getUnitPrice(), qty = body.getQty();
            if (unitPrice != null && qty != null) {
                body.setMoneyAmount(unitPrice.multiply(qty).setScale(4, RoundingMode.HALF_UP));
            }
            body.setCreateBy(userInfo.getUserNo());
            body.setCreateByName(userInfo.getUserName());
            body.setCreateTime(new Date());
            body.setId(UUID.randomUUID() + VERSION_ID_GAP + DEFAULT_VERSION_NO);
            body.setHeadId(contractHead.getId());
            body.setDataStatus(DEFAULT_DATA_STATUS);
            body.setTradeCode(userInfo.getCompany());
            bodyList.add(body);
        }
        BulkSqlOpt.batchInsertAndThrowException(bodyList, SevenForeignContractListMapper.class);
        return this.sevenForeignContractHeadDtoMapper.toDto(contractHead);
    }

    /**
     * 更改外商合同
     *
     * @param headParam 表头参数
     * @param userInfo  用户信息
     * @return 外商合同传输对象
     */
    @Transactional(rollbackFor = Exception.class)
    public SevenForeignContractHeadDto update(SevenForeignContractHeadParam headParam, UserInfoToken<?> userInfo) {
        SevenForeignContractHead contractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(headParam.getId());
        if (!CommonEnum.STATE_ENUM.DRAFT.getType().equals(contractHead.getDataStatus())) {
            throw new ErrorException(500, "仅编制状态数据允许编辑");
        }
        contractHead.setBuyer(headParam.getBuyer()); // 买家
        contractHead.setSeller(headParam.getSeller()); // 卖家
        contractHead.setSignDate(headParam.getSignDate()); // 签约日期
        contractHead.setShipPeriodDate(headParam.getShipPeriodDate()); // 装运期限
        contractHead.setContractEffectiveDate(headParam.getContractEffectiveDate()); // 合同生效期
        contractHead.setContractValidityDate(headParam.getContractValidityDate()); // 合同有效期
        contractHead.setSignPlaceCn(headParam.getSignPlaceCn()); // 签约地点（中文）
        contractHead.setSignPlaceEn(headParam.getSignPlaceEn()); // 签约地点（英文）
        contractHead.setShippingPort(headParam.getShippingPort()); // 装运港
        contractHead.setDestPort(headParam.getDestPort()); // 目的港
        contractHead.setPaymentMethod(headParam.getPaymentMethod()); // 收汇方式
        contractHead.setCurr(headParam.getCurr()); // 币种
        contractHead.setPriceTerm(headParam.getPriceTerm()); // 价格条款
        contractHead.setPriceTermPort(headParam.getPriceTermPort()); // 价格条款对应港口
        contractHead.setSuggestAuthorSignatory(headParam.getSuggestAuthorSignatory()); // 建议授权签约人
        contractHead.setShortOverflowNumber(headParam.getShortOverflowNumber()); // 短溢数
        contractHead.setNote(headParam.getNote()); // 备注
        contractHead.setUpdateBy(userInfo.getUserNo()); // 修改人
        contractHead.setUpdateByName(userInfo.getUserName()); // 修改人名称
        contractHead.setUpdateTime(new Date()); // 修改时间
        contractHead.setDocumentMakeDate(contractHead.getUpdateTime()); // 制单时间
        contractHead.setDocumentMaker(userInfo.getUserName()); // 制单人名称
        contractHead.setDocumentMakerNo(userInfo.getUserNo()); // 制单人编号
        return this.sevenForeignContractHeadMapper.updateByPrimaryKey(contractHead) > 0
                ? this.sevenForeignContractHeadDtoMapper.toDto(contractHead) : null;
    }

    /**
     * 删除外商合同
     *
     * @param ids      主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(@PathVariable List<String> ids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<SevenForeignContractHead> foreignContractHeadList = this.sevenForeignContractHeadMapper.getByIds(ids);
        boolean hasAnyNotDraft = foreignContractHeadList.stream()
                .anyMatch(head -> !CommonEnum.STATE_ENUM.DRAFT.getType().equals(head.getDataStatus()));
        if (hasAnyNotDraft) {
            throw new ErrorException(500, "仅编制状态数据允许删除");
        }
        this.sevenForeignContractHeadMapper.deleteByIds(ids);
        this.sevenForeignContractListMapper.deleteByHeadIds(ids, userInfo.getCompany());
    }

    /**
     * 确认外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     * @return dto
     */
    @Transactional(rollbackFor = Exception.class)
    public SevenForeignContractHeadDto confirm(String id, UserInfoToken<?> userInfo) {
        SevenForeignContractHead foreignContractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(id);
        if (CommonEnum.STATE_ENUM.CONFIRMED.getType().equals(foreignContractHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已确认，无需重复操作");
        }
        if (CommonEnum.STATE_ENUM.INVALID.getType().equals(foreignContractHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已作废，不允许确认");
        }
        this.sevenForeignContractHeadMapper.confirmById(id, userInfo.getUserNo(), userInfo.getUserName());
        this.sevenForeignContractListMapper.confirmByHeadId(id, userInfo.getUserNo(), userInfo.getUserName());
        SevenForeignContractHead newForeignContractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(id);
        return this.sevenForeignContractHeadDtoMapper.toDto(newForeignContractHead);
    }

    /**
     * 作废外商合同
     *
     * @param id       主键
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void invalidate(String id, UserInfoToken<?> userInfo) {
        SevenForeignContractHead foreignContractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(id);
        if (CommonEnum.STATE_ENUM.INVALID.getType().equals(foreignContractHead.getDataStatus())) {
            throw new ErrorException(500, "该数据已作废，无需重复操作");
        }
        this.sevenForeignContractHeadMapper.invalidateById(id, userInfo.getUserNo(), userInfo.getUserName());
        this.sevenForeignContractListMapper.invalidateByHeadId(id, userInfo.getUserNo(), userInfo.getUserName());
    }

    /**
     * 外商合同版本校验
     *
     * @param contractNo 合同号
     * @param userInfo   用户信息
     * @return 是否存在有效数据
     */
    public String checkVersionCopy(String contractNo, UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany();
        List<SevenForeignContractHead> headList = this.sevenForeignContractHeadMapper.getByContractNo(contractNo, tradeCode);
        if (CollectionUtils.isEmpty(headList)) {
            return CommonVariable.NO_CODE;
        }
        boolean hasValidData = headList.stream()
                .anyMatch(head -> !CommonEnum.STATE_ENUM.INVALID.getType().equals(head.getDataStatus()));
        return hasValidData ? CommonVariable.YES_CODE : CommonVariable.NO_CODE;
    }

    /**
     * 外商合同版本复制
     *
     * @param foreignContractHeadParam 外商合同表头参数
     * @param userInfo                 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void versionCopy(SevenForeignContractHeadParam foreignContractHeadParam, UserInfoToken<?> userInfo) {
        String tradeCode = userInfo.getCompany(), headId = foreignContractHeadParam.getId();
        SevenForeignContractHead contractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(headId);
        if (contractHead == null) {
            throw new ErrorException(500, "外商合同数据不存在，请刷新");
        }
        if (!Objects.equals(tradeCode, contractHead.getTradeCode())) {
            throw new ErrorException(500, "企业编码不匹配，无法操作该数据");
        }
        String currentContractNo = contractHead.getContractNo();
        // 查询最大版本号、下一个版本号
        SevenForeignContractHead maxVersionContractHead = this.sevenForeignContractHeadMapper
                .getMaxVersionContract(currentContractNo, tradeCode);
        int nextVersionNo = Integer.parseInt(maxVersionContractHead.getVersionNo()) + 1;
        // 作废当前所有剩余外商合同
        String updateBy = userInfo.getUserNo(), updateByName = userInfo.getUserName();
        this.sevenForeignContractHeadMapper.invalidateByContractNo(currentContractNo, updateBy, updateByName, tradeCode);
        this.sevenForeignContractListMapper.invalidateByContractNo(currentContractNo, updateBy, updateByName, tradeCode);
        // 复制表头
        SevenForeignContractHead newContractHead = new SevenForeignContractHead();
        BeanUtils.copyProperties(contractHead, newContractHead);
        newContractHead.setId(UUID.randomUUID() + VERSION_ID_GAP + nextVersionNo);
        newContractHead.setPrevVersionId(contractHead.getId());
        newContractHead.setVersionNo(String.valueOf(nextVersionNo));
        newContractHead.setCreateBy(userInfo.getUserNo());
        newContractHead.setCreateByName(userInfo.getUserName());
        newContractHead.setCreateTime(new Date());
        newContractHead.setUpdateBy(null);
        newContractHead.setUpdateByName(null);
        newContractHead.setUpdateTime(null);
        newContractHead.setDocumentMakerNo(userInfo.getUserNo());
        newContractHead.setDocumentMaker(userInfo.getUserName());
        newContractHead.setDocumentMakeDate(newContractHead.getCreateTime());
        newContractHead.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
        newContractHead.setConfirmTime(null);
        newContractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue());
        this.sevenForeignContractHeadMapper.insert(newContractHead);
        // 复制表体
        List<SevenForeignContractList> contractBodyList = this.sevenForeignContractListMapper
                .getListByHeadId(contractHead.getId(), tradeCode)
                .stream()
                .sorted(Comparator.comparing(SevenForeignContractList::getCreateTime).reversed())
                .collect(Collectors.toList());
        List<SevenForeignContractList> newContractBodyList = new ArrayList<>(contractBodyList.size());
        for (SevenForeignContractList contractBody : contractBodyList) {
            SevenForeignContractList newContractBody = new SevenForeignContractList();
            BeanUtils.copyProperties(contractBody, newContractBody);
            newContractBody.setId(UUID.randomUUID() + VERSION_ID_GAP + nextVersionNo);
            newContractBody.setHeadId(newContractHead.getId());
            newContractBody.setPrevVersionId(contractBody.getId());
            newContractBody.setCreateBy(userInfo.getUserNo());
            newContractBody.setCreateTime(new Date());
            newContractBody.setCreateByName(userInfo.getUserName());
            newContractBody.setUpdateByName(null);
            newContractBody.setUpdateTime(null);
            newContractBody.setUpdateBy(null);
            newContractBody.setDataStatus(CommonEnum.STATE_ENUM.DRAFT.getType());
            newContractBody.setConfirmTime(null);
            newContractBodyList.add(newContractBody);
        }
        // 复制归档附件
        List<Attached> attachedList = this.attachedMapper.getByHeadId(contractHead.getId(), tradeCode);
        if (CollectionUtils.isNotEmpty(attachedList)) {
            List<Attached> tempAttachList = new ArrayList<>();
            for (Attached attached : attachedList) {
                attached.setBusinessSid(newContractHead.getId());
                attached.setSid(UUID.randomUUID().toString());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setTradeCode(userInfo.getCompany());
                attached.setNote("复制第七条线外商合同【" + contractHead.getContractNo() + "】归档文件！");
                String oldFileName = attached.getFileName();
                try {
                    byte[] bytes = oldFileName.startsWith("TIANYI") ? fileHandler.downloadFile(oldFileName)
                            : otherFileHandler.downloadFile(oldFileName);
                    String newFileName = fileHandler.uploadFile(bytes, oldFileName);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            RoundingMode.HALF_UP));
                    this.attachedMapper.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}", tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(500, "复制归档文件异常！");
                }
            }
        }
        // 表体入库(放最后，不属同一事务)
        if (CollectionUtils.isNotEmpty(newContractBodyList)) {
            BulkSqlOpt.batchInsertAndThrowException(newContractBodyList, SevenForeignContractListMapper.class);
        }
    }

    /**
     * 打印会签单
     *
     * @param ids      主键列表
     * @param type     类型 xlsx or pdf
     * @param userInfo 用户信息
     * @return 会签单文件数据
     */
    public byte[] printCountersignSheet(List<String> ids, String type, UserInfoToken<?> userInfo) throws IOException {
        if (CollectionUtils.isEmpty(ids)) {
            return new byte[0];
        }
        String tradeCode = userInfo.getCompany();
        // 获取会签单数据
        List<CountersignSheetDataModel> countersignSheetDataList = this.getCountersignSheetData(ids, tradeCode);
        if (CollectionUtils.isEmpty(countersignSheetDataList)) {
            return new byte[0];
        }
        // 创建文件夹
        boolean single = countersignSheetDataList.size() == 1, pdf = PDF_TYPE.equals(type);
        String flagId = String.valueOf(System.currentTimeMillis());
        String fileDirName = flagId + RandomUtils.nextInt(1, 1000) + "-" + DIR;
        FileUtils.forceMkdir(new File(this.getSystemTempPath() + fileDirName));
        String pdfDirName = fileDirName + "-" + PDF_TYPE;
        if (pdf) {
            FileUtils.forceMkdir(new File(getSystemTempPath() + pdfDirName));
        }
        List<String> filePaths = new ArrayList<>(ids.size());
        for (CountersignSheetDataModel countersignSheetData : countersignSheetDataList) {
            CountersignSheetDataModel.Head head = countersignSheetData.getHead();
            String filename = single ? PRINT_EXCEL_FILE : PRINT_EXCEL_FILE_NAME + head.getContractNo() + ".xlsx";
            String fileRelativePath = fileDirName + File.separator + filename;
            String filePath = this.exportService.export(Collections.singletonList(head), Collections.emptyList(),
                    fileRelativePath, SYSTEM_PRINT_TEMPLATE_NAME);
            if (pdf) {
                PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
                pdfSaveOptions.setOnePagePerSheet(true);
                byte[] pdfBytes = ExportService.pdfSetTitle(ExportService.excelToPdf(IOUtils.
                                toByteArray(Files.newInputStream(Paths.get(filePath))), pdfSaveOptions)
                        , PRINT_EXCEL_FILE_NAME + ".pdf");
                String pdfFilename = (single ? PRINT_EXCEL_FILE_NAME : PRINT_EXCEL_FILE_NAME + head.getContractNo()) + ".pdf";
                String pdfPath = this.getSystemTempPath() + File.separator + pdfDirName + File.separator + pdfFilename;
                IOUtils.write(pdfBytes, Files.newOutputStream(Paths.get(pdfPath)));
                filePaths.add(pdfPath);
            } else {
                filePaths.add(filePath);
            }
        }
        if (CollectionUtils.isEmpty(filePaths)) {
            return new byte[0];
        }
        if (single) {
            return IOUtils.toByteArray(Files.newInputStream(Paths.get(filePaths.get(0))));
        }
        String zipSourceDir = this.getSystemTempPath() + (pdf ? pdfDirName : fileDirName);
        FileUtil.fileToZip(zipSourceDir, this.getSystemTempPath(), flagId);
        return IOUtils.toByteArray(Files.newInputStream(Paths.get(this.tempPath + flagId + ".zip")));
    }

    /**
     * 获取会签单数据
     *
     * @param contractHeadIds 合同表头主键列表
     * @param tradeCode       企业编码
     * @return 会签单数据列表
     */
    private List<CountersignSheetDataModel> getCountersignSheetData(List<String> contractHeadIds, String tradeCode) {
        if (CollectionUtils.isEmpty(contractHeadIds) || StringUtils.isBlank(tradeCode)) {
            return Collections.emptyList();
        }
        List<SevenForeignContractHead> contractHeadList = this.sevenForeignContractHeadMapper.getByIds(contractHeadIds);
        List<SevenForeignContractList> contractBodyList = this.sevenForeignContractListMapper.getListByHeadIds(contractHeadIds, tradeCode);
        List<BizMerchant> bizMerchantList = this.bizMerchantMapper.getByMerchantCodes(contractHeadList.stream()
                .map(SevenForeignContractHead::getBuyer).collect(Collectors.toList()), tradeCode);
        List<CountersignSheetDataModel> sheetDataModelList = new ArrayList<>(contractHeadList.size());
        for (SevenForeignContractHead currentHead : contractHeadList) {
            String buyerName = bizMerchantList.stream()
                    .filter(merchant -> Objects.equals(merchant.getMerchantCode(), currentHead.getBuyer()))
                    .map(BizMerchant::getMerchantNameCn)
                    .findAny()
                    .orElse(StringUtils.EMPTY);
            CountersignSheetDataModel.Head.HeadBuilder headBuilder = CountersignSheetDataModel.Head.builder()
                    .contractNo(currentHead.getContractNo())
                    .buyer(buyerName);
            List<SevenForeignContractList> currentBodyList = contractBodyList.stream()
                    .filter(body -> Objects.equals(body.getHeadId(), currentHead.getId()))
                    .collect(Collectors.toList());
            BigDecimal totalMoneyAmount = currentBodyList.stream()
                    .map(body -> Optional.ofNullable(body.getMoneyAmount()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            String cnTotalMoneyAmount = NumberFormatterUtils.convertToChineseAmount(totalMoneyAmount);
            String numberTotalMoneyAmount = NumberFormatterUtils.formatNumber(totalMoneyAmount);
            String currEn = Optional.ofNullable(currentHead.getCurr()).orElse(StringUtils.EMPTY);
            String currCn = StringUtils.isEmpty(currEn) ? StringUtils.EMPTY : this.commonService.getCurrCn(currEn, PCodeType.CURR_ALL);
            headBuilder.moneyAmountUpperString("（大写）：" + currCn + cnTotalMoneyAmount)
                    .moneyAmountLowerString("（小写）：" + currEn + numberTotalMoneyAmount);
            StringBuilder overviewAppender = new StringBuilder();
            overviewAppender.append("我司向")
                    .append(buyerName)
                    .append("出口");
            List<SevenForeignContractList> processBodyList = currentBodyList.stream()
                    .filter(body -> CommonEnum.SEVEN_BODY_TYPE.PROCESS.getValue().equals(body.getBodyType()))
                    .sorted(Comparator.comparing(SevenForeignContractList::getCreateTime).reversed())
                    .collect(Collectors.toList());
            boolean processListEmpty = CollectionUtils.isEmpty(processBodyList);
            String firstGName = processListEmpty ? StringUtils.EMPTY :
                    Optional.ofNullable(processBodyList.get(0).getGName()).orElse(StringUtils.EMPTY);
            BigDecimal processTotalQty = processListEmpty ? BigDecimal.ZERO
                    : processBodyList.stream()
                    .map(body -> Optional.ofNullable(body.getQty()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal squat = processTotalQty.divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP);
            overviewAppender.append(firstGName)
                    .append(NumberFormatterUtils.formatNumber(squat))
                    .append("吨。 合同总价为")
                    .append(currEn)
                    .append(numberTotalMoneyAmount)
                    .append(StringUtils.SPACE)
                    .append(Optional.ofNullable(currentHead.getPriceTerm()).orElse(StringUtils.EMPTY))
                    .append("。 产品的技术规格、付款方式以及交货期均经过合同双方确认。");
            CountersignSheetDataModel.Head head = headBuilder.contractOverview(overviewAppender.toString()).build();
            sheetDataModelList.add(new CountersignSheetDataModel().setContractHeadId(currentHead.getId()).setHead(head));
        }
        return sheetDataModelList;
    }

    /**
     * 获取系统临时路径
     *
     * @return 临时路径
     */
    private String getSystemTempPath() {
        String tempPath = this.tempPath;
        if (StringUtils.isBlank(tempPath)) {
            throw new ErrorException(500, "请配置系统临时路径");
        }
        return tempPath.endsWith(File.separator) ? tempPath : tempPath + File.separator;
    }

    @Override
    public void startFlowBatch(NextNodeInfoBatchVo batchVo, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        // 更新表头状态
        String id = approvalFlowParam.getIds().get(0);
        SevenForeignContractHead contractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(id);
        String[] allowStartApprStatus = {CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue(),
                CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue()};
        if (!Arrays.asList(allowStartApprStatus).contains(contractHead.getApprStatus())) {
            throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
        }
        contractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        contractHead.setUpdateBy(userInfo.getUserNo());
        contractHead.setUpdateByName(userInfo.getUserName());
        contractHead.setUpdateTime(new Date());
        // 记录流程实例id
        contractHead.setExtend1(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));
        this.sevenForeignContractHeadMapper.updateByPrimaryKey(contractHead);
        // 新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(contractHead.getId());
        this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核"
                , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
    }

    @Override
    public void audit(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, Map<String, String> flowInstanceMap, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            String businessId = flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId());
            SevenForeignContractHead contractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(businessId);
            if (nextNodeInfoVo.isFinish()) {
                contractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                contractHead.setUpdateBy(userInfo.getUserNo());
                contractHead.setUpdateByName(userInfo.getUserName());
                contractHead.setUpdateTime(new Date());
                this.sevenForeignContractHeadMapper.updateByPrimaryKey(contractHead);
                nextNodeInfoVo.setNodeName("审核通过");
            }
            // 新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(businessId);
            this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName()
                    , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public void reject(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            // 回退到发起人（仅单条退回）
            String businessId = approvalFlowParam.getIds().get(0);
            SevenForeignContractHead contractHead = this.sevenForeignContractHeadMapper.selectByPrimaryKey(businessId);
            contractHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
            contractHead.setUpdateBy(userInfo.getUserNo());
            contractHead.setUpdateByName(userInfo.getUserName());
            contractHead.setUpdateTime(new Date());
            this.sevenForeignContractHeadMapper.updateByPrimaryKey(contractHead);

            // 新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(businessId);
            aeoAuditInfo.setApprNote(approvalFlowParam.getApprMessage());
            this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回"
                    , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public List<WorkFlowParam> getFlowList(List<String> ids) {
        return this.sevenForeignContractHeadMapper.getFlowList(ids);
    }

    public ResultObject<List<SevenForeignContractHeadDto>> getAeoListPaged(SevenForeignContractHeadParam headParam,
                                                                           PageParam pageParam, UserInfoToken<?> userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = this.commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalListByParam(headParam.getBusinessType(), CommonEnum.WORKFLOW_CONSTANT_ENUM.getValue(headParam.getApprStatus()));
        List<String> ids = (List<String>) result.getResult();
        // 如果ids为空，直接返回空分页结果
        if (CollectionUtils.isEmpty(ids)) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }
        headParam.setIds(ids);
        // 启用分页查询
        SevenForeignContractHead contractHead = this.sevenForeignContractHeadDtoMapper.toPo(headParam);
        contractHead.setTradeCode(userInfo.getCompany());
        Page<SevenForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.sevenForeignContractHeadMapper.getAeoList(contractHead));
        List<SevenForeignContractHeadDto> dtoList = page.getResult().stream()
                .map(this.sevenForeignContractHeadDtoMapper::toDto)
                .collect(Collectors.toList());
        return ResultObject.createInstance(dtoList, (int) page.getTotal(), page.getPageNum());
    }
}