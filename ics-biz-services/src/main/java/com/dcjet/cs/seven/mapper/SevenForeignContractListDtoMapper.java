package com.dcjet.cs.seven.mapper;

import com.dcjet.cs.dto.seven.SevenForeignContractListDto;
import com.dcjet.cs.dto.seven.SevenForeignContractListParam;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SevenForeignContractListDtoMapper {
    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    SevenForeignContractListDto toDto(SevenForeignContractList po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    SevenForeignContractList toPo(SevenForeignContractListParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(SevenForeignContractListParam param, @MappingTarget SevenForeignContractList po);
}