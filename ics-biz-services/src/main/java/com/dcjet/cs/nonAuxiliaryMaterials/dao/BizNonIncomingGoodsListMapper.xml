<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsListMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="product_model" property="productModel" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="NUMERIC"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="unit_price" property="unitPrice" jdbcType="NUMERIC"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="delivery_date" property="deliveryDate" jdbcType="TIMESTAMP"/>
        <result column="total_usd" property="totalUsd" jdbcType="NUMERIC"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="in_quantity" property="inQuantity" jdbcType="NUMERIC"/>
        <result column="in_unit" property="inUnit" jdbcType="VARCHAR"/>
        <result column="curr" property="curr" jdbcType="VARCHAR"/>
        <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="contract_list_id" property="contractListId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
            t.business_type,
            t.data_state,
            t.version_no,
            t.trade_code,
            t.sys_org_code,
            t.parent_id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.insert_user_name,
            t.update_user_name,
            t.extend1,
            t.extend2,
            t.extend3,
            t.extend4,
            t.extend5,
            t.extend6,
            t.extend7,
            t.extend8,
            t.extend9,
            t.extend10,
            t.goods_name,
            t.product_model,
            t.quantity,
            t.unit,
            t.unit_price,
            t.amount,
            t.delivery_date,
            t.total_usd,
            t.remarks,
            t.head_id,
            t.in_quantity,
            t.in_unit,
            t.curr,
            t.invoice_no,
            t.contract_list_id
    </sql>

    <sql id="condition">
        t.head_id = #{headId}
    </sql>
    <update id="batchUpdateInvoiceNo">
        UPDATE t_biz_non_incoming_goods_list t
        SET
            invoice_no = #{invoiceNo},
            update_time = now(),
            update_by = #{userNo},
            update_user_name = #{userName}
        WHERE t.id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_non_incoming_goods_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getListSumByInvoice" resultType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList">
        <![CDATA[
            select
                INVOICE_NO,
                round(sum(coalesce(IN_QUANTITY,0)),6) as IN_QUANTITY,
                round(sum(coalesce(AMOUNT,0)),2) as AMOUNT
            from t_biz_non_incoming_goods_list
            where HEAD_ID = #{headId} AND coalesce(INVOICE_NO,'') <> ''
            GROUP BY
                INVOICE_NO
        ]]>

    </select>
    <select id="getListByHeadSids" resultType="com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_biz_non_incoming_goods_list t where t.head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getSumTotalByHeadId" resultType="com.dcjet.cs.dec.model.InComingListSumTotal">
        SELECT
        round(sum(coalesce(QUANTITY,0)),6) as quantity,
        round(sum(coalesce(IN_QUANTITY,0)),6) as inQuantity,
        round(sum(coalesce(AMOUNT,0)),2) as amount
        FROM
        t_biz_non_incoming_goods_list t where t.head_id = #{headId}
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_non_incoming_goods_list t where t.id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <delete id="deleteByHeadId" parameterType="java.lang.String">
        delete from  t_biz_non_incoming_goods_list t where t.head_id = #{headId}
    </delete>

    <insert id="insertByHeadId">
        insert into t_biz_non_incoming_goods_list(
            id,
            business_type,
            trade_code,
            create_by,
            create_time,
            insert_user_name,
            goods_name,
            product_model,
            quantity,
            unit,
            unit_price,
            amount,
            delivery_date,
            remarks,
            head_id,
            in_quantity,
            in_unit,
            contract_list_id
        )
        select
            sys_guid(),
            '6',
            t.trade_code,
            #{userNo},
            now(),
            #{userName},
            t.goods_name,
            t.GOODS_DESC,
            t.qty,
            t.unit,
            t.unit_price,
            t.amount,
            t.delivery_date,
            t.REMARK,
            #{headId},
            t.qty,
            t.unit,
            t.id
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST t
        where t.id in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </insert>

</mapper>