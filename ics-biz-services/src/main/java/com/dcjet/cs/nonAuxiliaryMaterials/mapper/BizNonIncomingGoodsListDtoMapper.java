package com.dcjet.cs.nonAuxiliaryMaterials.mapper;

import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsListDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsListParam;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizIncomingGoodsListDto
 *
 * <AUTHOR>
 * @date 2025-05-23 13:32:21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizNonIncomingGoodsListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizNonIncomingGoodsListDto toDto(BizNonIncomingGoodsList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizNonIncomingGoodsList toPo(BizNonIncomingGoodsListParam param);

    /**
     * 数据库原始数据更新
     * @param bizIncomingGoodsListParam
     * @param bizIncomingGoodsList
     */
    void updatePo(BizNonIncomingGoodsListParam bizIncomingGoodsListParam, @MappingTarget BizNonIncomingGoodsList bizIncomingGoodsList);

    default void patchPo(BizNonIncomingGoodsListParam bizIncomingGoodsListParam , BizNonIncomingGoodsList bizIncomingGoodsList) {
        // TODO 自行实现局部更新
    }
}