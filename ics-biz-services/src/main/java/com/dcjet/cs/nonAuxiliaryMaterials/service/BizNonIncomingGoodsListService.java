package com.dcjet.cs.nonAuxiliaryMaterials.service;


import com.dcjet.cs.dec.model.InComingListSumTotal;
import com.dcjet.cs.dto.dec.BizBatchUpdateInvoiceNoParams;

import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsListDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsListParam;
import com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsListMapper;
import com.dcjet.cs.nonAuxiliaryMaterials.mapper.BizNonIncomingGoodsListDtoMapper;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * BizNonIncomingGoodsList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-05-23 13:32:21
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizNonIncomingGoodsListService extends BaseService<BizNonIncomingGoodsList> {

    private static final Logger log = LoggerFactory.getLogger(BizNonIncomingGoodsListService.class);

    @Resource
    private BizNonIncomingGoodsListMapper BizNonIncomingGoodsListMapper;

    @Resource
    private BizNonIncomingGoodsListDtoMapper BizNonIncomingGoodsListDtoMapper;

    @Override
    public Mapper<BizNonIncomingGoodsList> getMapper() {
        return BizNonIncomingGoodsListMapper;
    }



    /**
     * 获取分页信息
     *
     * @param BizNonIncomingGoodsListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizNonIncomingGoodsListDto>> getListPaged(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizNonIncomingGoodsList BizNonIncomingGoodsList = BizNonIncomingGoodsListDtoMapper.toPo(BizNonIncomingGoodsListParam);
        BizNonIncomingGoodsList.setTradeCode(userInfo.getCompany());
        Page<BizNonIncomingGoodsList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> BizNonIncomingGoodsListMapper.getList( BizNonIncomingGoodsList));
        // 将PO转为DTO返回给前端
        List<BizNonIncomingGoodsListDto> BizNonIncomingGoodsListDtoList = page.getResult().stream()
            .map(BizNonIncomingGoodsListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(BizNonIncomingGoodsListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param BizNonIncomingGoodsListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizNonIncomingGoodsListDto insert(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        BizNonIncomingGoodsList BizNonIncomingGoodsList = BizNonIncomingGoodsListDtoMapper.toPo(BizNonIncomingGoodsListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        BizNonIncomingGoodsList.setId(sid);
        BizNonIncomingGoodsList.setCreateBy(userInfo.getUserNo());
        BizNonIncomingGoodsList.setCreateTime(new Date());
        BizNonIncomingGoodsList.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = BizNonIncomingGoodsListMapper.insert(BizNonIncomingGoodsList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? BizNonIncomingGoodsListDtoMapper.toDto(BizNonIncomingGoodsList) : null;
    }

    /**
     * 修改记录
     *
     * @param BizNonIncomingGoodsListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizNonIncomingGoodsListDto update(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        BizNonIncomingGoodsList BizNonIncomingGoodsList = BizNonIncomingGoodsListMapper.selectByPrimaryKey(BizNonIncomingGoodsListParam.getId());
        BizNonIncomingGoodsListDtoMapper.updatePo(BizNonIncomingGoodsListParam, BizNonIncomingGoodsList);
        BizNonIncomingGoodsList.setUpdateBy(userInfo.getUserNo());
        BizNonIncomingGoodsList.setUpdateTime(new Date());

        // 更新数据
        int update = BizNonIncomingGoodsListMapper.updateByPrimaryKey(BizNonIncomingGoodsList);
        return update > 0 ? BizNonIncomingGoodsListDtoMapper.toDto(BizNonIncomingGoodsList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       BizNonIncomingGoodsListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizNonIncomingGoodsListDto> selectAll(BizNonIncomingGoodsListParam exportParam, UserInfoToken userInfo) {
        BizNonIncomingGoodsList BizNonIncomingGoodsList = BizNonIncomingGoodsListDtoMapper.toPo(exportParam);
        BizNonIncomingGoodsList.setTradeCode(userInfo.getCompany());
        List<BizNonIncomingGoodsListDto> BizNonIncomingGoodsListDtos = new ArrayList<>();
        List<BizNonIncomingGoodsList> BizNonIncomingGoodsListLists = BizNonIncomingGoodsListMapper.getList(BizNonIncomingGoodsList);
        if (CollectionUtils.isNotEmpty(BizNonIncomingGoodsListLists)) {
           BizNonIncomingGoodsListDtos = BizNonIncomingGoodsListLists.stream().map(head -> {
                    BizNonIncomingGoodsListDto dto =  BizNonIncomingGoodsListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return BizNonIncomingGoodsListDtos;
    }

    public ResultObject<List<BizNonIncomingGoodsListDto>> getListSumByInvoice(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizNonIncomingGoodsListDto>> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizNonIncomingGoodsList po = BizNonIncomingGoodsListDtoMapper.toPo(BizNonIncomingGoodsListParam);
        if (StringUtils.isBlank(po.getHeadId())){
            throw new ErrorException(400, XdoI18nUtil.t("表头ID不能为空"));
        }


        String headId = po.getHeadId();
        String tradeCode = userInfo.getCompany();
        List<BizNonIncomingGoodsList> list = BizNonIncomingGoodsListMapper.getListSumByInvoice(headId,tradeCode);

        List<BizNonIncomingGoodsListDto> listDto = list.stream().map(head -> {
            BizNonIncomingGoodsListDto dto =  BizNonIncomingGoodsListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());

        resultObject.setData(listDto);

        return resultObject;
    }



    @Transactional(rollbackFor = Exception.class)
    public ResultObject batchUpdateInvoiceNo(BizBatchUpdateInvoiceNoParams BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        try {
            List<String> idList = BizNonIncomingGoodsListParam.getIds();
            String invoiceNo = BizNonIncomingGoodsListParam.getInvoiceNo();
            // 批量更新发票号
            if(CollectionUtils.isNotEmpty(idList)){
                BizNonIncomingGoodsListMapper.batchUpdateInvoiceNo(idList,invoiceNo,userInfo.getUserNo(),userInfo.getUserName());
            }
            return ResultObject.createInstance(true,"批量更新发票号成功！");
        }catch (Exception e){
            e.printStackTrace();
            throw new ErrorException(400, XdoI18nUtil.t("批量更新发票号失败"));
        }


    }

    public ResultObject<BizNonIncomingGoodsListDto> getIncomingGoodsListBySid(String id, UserInfoToken userInfo) {
        ResultObject<BizNonIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        if (StringUtils.isBlank(id)) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        BizNonIncomingGoodsList po = BizNonIncomingGoodsListMapper.selectByPrimaryKey(id);
        if (po == null) {
            throw new ErrorException(400, XdoI18nUtil.t("数据不存在"));
        }
        BizNonIncomingGoodsListDto dto = BizNonIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizNonIncomingGoodsListDto> updateInQuality(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizNonIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(BizNonIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        if (BizNonIncomingGoodsListParam.getInQuantity() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("进口数量不能为空"));
        }else {
            if (BizNonIncomingGoodsListParam.getInQuantity().compareTo(BigDecimal.ZERO) == 0) {
                throw new ErrorException(400, XdoI18nUtil.t("进口数量不能为0"));
            }
        }

        // 系统计算=进口数量*单价
        // 允许修改，修改保存时，根据进口数量重新计算金额
        BizNonIncomingGoodsList po = BizNonIncomingGoodsListDtoMapper.toPo(BizNonIncomingGoodsListParam);
        // 重新计算金额
        BigDecimal systemAmount = po.getInQuantity().multiply(po.getUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
        po.setAmount(systemAmount);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = BizNonIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizNonIncomingGoodsListDto dto = BizNonIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizNonIncomingGoodsListDto> updateQuantity(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {

        ResultObject<BizNonIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(BizNonIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        if (BizNonIncomingGoodsListParam.getQuantity() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("数量不能为空"));
        }else {
            if (BizNonIncomingGoodsListParam.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                throw new ErrorException(400, XdoI18nUtil.t("数量不能为0"));
            }
        }
        // 系统计算=进口数量*单价
        // 允许修改，修改保存时，根据进口数量重新计算金额
        BizNonIncomingGoodsList po = BizNonIncomingGoodsListDtoMapper.toPo(BizNonIncomingGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = BizNonIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizNonIncomingGoodsListDto dto = BizNonIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizNonIncomingGoodsListDto> updateAmount(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {

        ResultObject<BizNonIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(BizNonIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }

        if (BizNonIncomingGoodsListParam.getUnitPrice() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("单价不能为空"));
        }else {
            if (BizNonIncomingGoodsListParam.getUnitPrice().compareTo(BigDecimal.ZERO) == 0) {
                throw new ErrorException(400, XdoI18nUtil.t("单价不能为0"));
            }
        }
        BizNonIncomingGoodsList po = BizNonIncomingGoodsListDtoMapper.toPo(BizNonIncomingGoodsListParam);
        // 根据金额重新计算数量 保留六位小数
        BigDecimal amount = po.getUnitPrice().multiply(po.getInQuantity());
        po.setAmount(amount);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = BizNonIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizNonIncomingGoodsListDto dto = BizNonIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizNonIncomingGoodsListDto> updateInvoiceNo(BizNonIncomingGoodsListParam BizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        ResultObject<BizNonIncomingGoodsListDto> resultObject = ResultObject.createInstance(true,"更新成功！");
        if (StringUtils.isBlank(BizNonIncomingGoodsListParam.getId())) {
            throw new ErrorException(400, XdoI18nUtil.t("ID不能为空"));
        }
        // 修改进口发票号
        if (StringUtils.isBlank(BizNonIncomingGoodsListParam.getInvoiceNo())){
            throw new ErrorException(400, XdoI18nUtil.t("进口发票号不能为空"));
        }
        BizNonIncomingGoodsList po = BizNonIncomingGoodsListDtoMapper.toPo(BizNonIncomingGoodsListParam);
        po.setUpdateBy(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        int i = BizNonIncomingGoodsListMapper.updateByPrimaryKeySelective(po);
        resultObject.setMessage(i>0?"更新成功！":"更新失败！");
        resultObject.setSuccess(i>0);
        BizNonIncomingGoodsListDto dto = BizNonIncomingGoodsListDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject getSumTotalByHeadId(BizNonIncomingGoodsListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        String headId = param.getHeadId();
        if (StringUtils.isBlank(headId)) {
            return resultObject;
        }
        InComingListSumTotal total = BizNonIncomingGoodsListMapper.getSumTotalByHeadId(param.getHeadId());
        resultObject.setData(total);
        return resultObject;
    }

    public void batchInsert(BizNonIncomingGoodsListParam bizNonIncomingGoodsListParam, UserInfoToken userInfo) {
        if (CollectionUtils.isEmpty(bizNonIncomingGoodsListParam.getSids())){
            throw new ErrorException(400, "请选择需要新增的数据");
        }
        if (StringUtils.isBlank(bizNonIncomingGoodsListParam.getHeadId()) ){
            throw new ErrorException(400, "表头数据不存在, 请刷新");
        }
        BizNonIncomingGoodsListMapper.insertByHeadId(bizNonIncomingGoodsListParam.getSids(), bizNonIncomingGoodsListParam.getHeadId(), userInfo.getUserNo(), userInfo.getUserName());
    }
}