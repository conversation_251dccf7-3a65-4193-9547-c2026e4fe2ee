package com.dcjet.cs.nonAuxiliaryMaterials.mapper;

import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadDto;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * TBizIncomingGoodsHeadDto
 *
 * <AUTHOR>
 * @date 2025-05-22 15:21:28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizNonIncomingGoodsHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizNonIncomingGoodsHeadDto toDto(BizNonIncomingGoodsHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizNonIncomingGoodsHead toPo(BizNonIncomingGoodsHeadParam param);

    /**
     * 数据库原始数据更新
     * @param tBizIncomingGoodsHeadParam
     * @param tBizIncomingGoodsHead
     */
    void updatePo(BizNonIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, @MappingTarget BizNonIncomingGoodsHead tBizIncomingGoodsHead);

    default void patchPo(BizNonIncomingGoodsHeadParam tBizIncomingGoodsHeadParam , BizNonIncomingGoodsHead tBizIncomingGoodsHead) {
        // TODO 自行实现局部更新
    }
}