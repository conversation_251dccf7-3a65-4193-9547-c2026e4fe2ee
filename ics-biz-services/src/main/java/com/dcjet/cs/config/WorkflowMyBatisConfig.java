package com.dcjet.cs.config;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 工作流MyBatis配置
 * 专门为flowlong-mybatis-plus库配置MyBatis Plus功能
 *
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Configuration
public class WorkflowMyBatisConfig {

    /**
     * 为工作流创建专用的MyBatis Plus SqlSessionFactory
     * 使用MyBatis Plus的功能来支持BaseMapper的方法
     */
    @Bean(name = "workflowSqlSessionFactory")
    public SqlSessionFactory workflowSqlSessionFactory(@Qualifier("dataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);

        // 配置MyBatis Plus
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setJdbcTypeForNull(JdbcType.NULL);
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCallSettersOnNulls(true);
        sqlSessionFactory.setConfiguration(configuration);

        // 配置全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        // 设置主键类型为自增
        dbConfig.setIdType(com.baomidou.mybatisplus.annotation.IdType.AUTO);
        globalConfig.setDbConfig(dbConfig);
        sqlSessionFactory.setGlobalConfig(globalConfig);

        return sqlSessionFactory.getObject();
    }

    /**
     * 为工作流创建专用的SqlSessionTemplate
     */
    @Bean(name = "workflowSqlSessionTemplate")
    public SqlSessionTemplate workflowSqlSessionTemplate(@Qualifier("workflowSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 配置工作流相关的Mapper扫描
     * 使用专用的SqlSessionTemplate来避免冲突
     */
    @Bean
    public MapperScannerConfigurer workflowMapperScannerConfigurer() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        // 使用专用的SqlSessionTemplate
        mapperScannerConfigurer.setSqlSessionTemplateBeanName("workflowSqlSessionTemplate");
        // 扫描工作流相关的Mapper包
        mapperScannerConfigurer.setBasePackage("com.aizuda.bpm.mybatisplus.mapper");
        // 设置标记接口，扫描继承BaseMapper的接口
        mapperScannerConfigurer.setMarkerInterface(com.baomidou.mybatisplus.core.mapper.BaseMapper.class);

        return mapperScannerConfigurer;
    }
}
