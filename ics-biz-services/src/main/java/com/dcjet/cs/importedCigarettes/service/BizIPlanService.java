package com.dcjet.cs.importedCigarettes.service;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.model.BizIOrderList;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dec.model.BizIPurchaseListBox;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.dto.attach.AttachedDto;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.CopyVersionData;
import com.dcjet.cs.importedCigarettes.dao.BizIContractListMapper;
import com.dcjet.cs.importedCigarettes.dao.BizIPlanListMapper;
import com.dcjet.cs.importedCigarettes.mapper.BizIPlanDtoMapper;
import com.dcjet.cs.importedCigarettes.mapper.BizIPlanListDtoMapper;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import com.dcjet.cs.importedCigarettes.model.BizIContractList;
import com.dcjet.cs.importedCigarettes.model.BizIPlanList;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.importedCigarettes.dao.BizIPlanMapper;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Slf4j
@Service
public class BizIPlanService extends BaseService<BizIPlan> implements ApprovalFlowService {
    @Resource
    private BizIPlanMapper bizIPlanMapper;
    @Resource
    private BizIPlanListMapper bizIPlanListMapper;
    @Resource
    private BizIContractListMapper bizIContractListMapper;
    @Resource
    private BizIPlanDtoMapper bizIPlanDtoMapper;
    @Resource
    private AttachedService attachedService;
    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;
    @Resource
    private BizIPlanListDtoMapper bizIPlanListDtoMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;

    public BizIPlanService() {
    }

    @Override
    public Mapper<BizIPlan> getMapper() {
        return bizIPlanMapper;
    }

    /**
     * 根据ID获取计划表头数据
     *
     * @param sid 主键
     * @return 计划表头DTO
     */
    public BizIPlanDto getById(String sid) {
        BizIPlan bizIPlan = bizIPlanMapper.selectByPrimaryKey(sid);
        if (bizIPlan != null) {
            return bizIPlanDtoMapper.toDto(bizIPlan);
        }
        return null;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIPlanParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIPlanDto>> getListPaged(BizIPlanParam bizIPlanParam, PageParam pageParam) {
        // 启用分页查询
        BizIPlan bizIPlan = bizIPlanDtoMapper.toPo(bizIPlanParam);
        Page<BizIPlan> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIPlanMapper.getList(bizIPlan));
        List<BizIPlanDto> BizIPlanDtos = page.getResult().stream().map(head -> {
            BizIPlanDto dto = bizIPlanDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIPlanDto>> paged = ResultObject.createInstance(BizIPlanDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIPlanParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPlanDto insert(BizIPlanParam bizIPlanParam, UserInfoToken userInfo) {
        BizIPlan bizIPlan = bizIPlanDtoMapper.toPo(bizIPlanParam);

        bizIPlan.setTradeCode(userInfo.getCompany());
        StringJoiner errorMes = checkPlan(bizIPlan);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败,")+errorMes);
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIPlan.setSid(sid);
        bizIPlan.setInsertUser(userInfo.getUserNo());
        bizIPlan.setInsertUserName(userInfo.getUserName());
        bizIPlan.setInsertTime(new Date());
        bizIPlan.setCreaterUser(userInfo.getUserName());
        bizIPlan.setCreaterTime(new Date());

        // 新增数据
        int insertStatus = bizIPlanMapper.insert(bizIPlan);
        return  insertStatus > 0 ? bizIPlanDtoMapper.toDto(bizIPlan) : null;
    }
   /**
     * 功能描述:新增表头和表体数据
     *
     * @param bizIPlanWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回包含表头和表体数据的完整信息
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPlanWithDetailDto insertPlanWithDetails(BizIPlanWithDetailParam bizIPlanWithDetailParam, UserInfoToken userInfo) {
        // 1. 处理表头数据
        BizIPlan bizIPlan = bizIPlanDtoMapper.toHeadWithDetailPo(bizIPlanWithDetailParam);

        bizIPlan.setTradeCode(userInfo.getCompany());
        StringJoiner errorMes = checkPlan(bizIPlan);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败,")+errorMes);
        }
        /**
         * 规范固定字段
         */
//        String sid = UUID.randomUUID().toString();
//        bizIPlan.setSid(sid);
        bizIPlan.setInsertUser(userInfo.getUserNo());
        bizIPlan.setInsertUserName(userInfo.getUserName());
        bizIPlan.setInsertTime(new Date());
        bizIPlan.setCreaterUser(userInfo.getUserName());
        bizIPlan.setCreaterTime(new Date());

        // 新增表头数据
        int insertStatus = bizIPlanMapper.updateByPrimaryKey(bizIPlan);
        if (insertStatus <= 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表头数据保存失败"));
        }

        // 构建返回结果
        BizIPlanWithDetailDto result = new BizIPlanWithDetailDto();
        result.setHead(bizIPlanDtoMapper.toDto(bizIPlan));

        // 2. 处理表体数据
        List<BizIPlanListParam> details = bizIPlanWithDetailParam.getDetails();
        if (CollectionUtils.isNotEmpty(details)) {


            // 收集所有表体数据的校验错误
            StringJoiner allErrors = new StringJoiner("\n");
            Map<String, Integer> productNameCount = new HashMap<>(); // 用于检查商品名称重复

            // 第一轮校验：收集所有错误
            int index = 1;
            for (BizIPlanListParam detail : details) {
                // 设置表体关联的表头ID
                detail.setHeadId(bizIPlan.getSid());
                detail.setTradeCode(userInfo.getCompany());

                // 对必填字段进行校验
                String validationErrors = validateDetailRequiredFields(detail, index);
                if (StringUtils.isNotBlank(validationErrors)) {
                    allErrors.add(validationErrors);
                }

                // 统计商品名称出现次数，用于后续检查重复
                if (StringUtils.isNotBlank(detail.getProductName())) {
                    productNameCount.put(detail.getProductName(),
                            productNameCount.getOrDefault(detail.getProductName(), 0) + 1);
                }

                index++;
            }

            // 检查商品名称重复
            for (Map.Entry<String, Integer> entry : productNameCount.entrySet()) {
                if (entry.getValue() > 1) {
                    allErrors.add("商品名称 '" + entry.getKey() + "' 在表体数据中重复出现" + entry.getValue() + "次");
                }
            }

            // 如果有错误，抛出异常
            if (allErrors.length() > 0) {
                throw new ErrorException(400, allErrors.toString());
            }

            // 所有校验通过，开始保存表体数据
            List<BizIPlanListDto> savedDetails = new ArrayList<>();
            for (BizIPlanListParam detail : details) {
                // 检查商品名称是否已存在于数据库
                if (StringUtils.isNotBlank(detail.getProductName())) {
                    BizIPlanList checkEntity = new BizIPlanList();
                    checkEntity.setHeadId(bizIPlan.getSid());
                    checkEntity.setProductName(detail.getProductName());
                    List<BizIPlanList> check = bizIPlanListMapper.checkProductName(checkEntity);
                    if (check.size() > 0) {
                        throw new ErrorException(400, "商品名称 '" + detail.getProductName() + "' 已存在于数据库中！");
                    }
                }

                // 设置表体固定字段
                String detailSid = UUID.randomUUID().toString();
                BizIPlanList bizIPlanList = bizIPlanListDtoMapper.toPo(detail);
                bizIPlanList.setSid(detailSid);
                bizIPlanList.setInsertUser(userInfo.getUserNo());
                bizIPlanList.setInsertUserName(userInfo.getUserName());
                bizIPlanList.setInsertTime(new Date());
                bizIPlanList.setTradeCode(userInfo.getCompany());

                // 插入表体数据
                int listInsertStatus = bizIPlanListMapper.insert(bizIPlanList);
                if (listInsertStatus <= 0) {
                    throw new ErrorException(400, "表体数据保存失败，商品名称: " + detail.getProductName());
                }

                // 将保存的表体数据添加到返回列表中
                savedDetails.add(bizIPlanListDtoMapper.toDto(bizIPlanList));
            }
            result.setDetails(savedDetails);
        }

        return result;
    }
    /**
     * 功能描述:修改表头和表体数据
     *
     * @param bizIPlanWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回包含表头和表体数据的完整信息
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPlanWithDetailDto updatePlanWithDetails(BizIPlanWithDetailParam bizIPlanWithDetailParam, UserInfoToken userInfo) {
        // 1. 处理表头数据
        BizIPlan bizIPlan = bizIPlanDtoMapper.toHeadWithDetailPo(bizIPlanWithDetailParam);

        bizIPlan.setTradeCode(userInfo.getCompany());
        StringJoiner errorMes = checkPlan(bizIPlan);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败,")+errorMes);
        }
        /**
         * 规范固定字段
         */
//        String sid = UUID.randomUUID().toString();
//        bizIPlan.setSid(sid);
        bizIPlan.setInsertUser(userInfo.getUserNo());
        bizIPlan.setInsertUserName(userInfo.getUserName());
        bizIPlan.setInsertTime(new Date());
        bizIPlan.setCreaterUser(userInfo.getUserName());
        bizIPlan.setCreaterTime(new Date());

        // 新增表头数据
        int insertStatus = bizIPlanMapper.updateByPrimaryKey(bizIPlan);
        if (insertStatus <= 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("表头数据保存失败"));
        }

        // 构建返回结果
        BizIPlanWithDetailDto result = new BizIPlanWithDetailDto();
        result.setHead(bizIPlanDtoMapper.toDto(bizIPlan));

        // 2. 处理表体数据
        List<BizIPlanListParam> details = bizIPlanWithDetailParam.getDetails();
        if (CollectionUtils.isNotEmpty(details)) {


            // 收集所有表体数据的校验错误
            StringJoiner allErrors = new StringJoiner("\n");
            Map<String, Integer> productNameCount = new HashMap<>(); // 用于检查商品名称重复

            // 第一轮校验：收集所有错误
            int index = 1;
            for (BizIPlanListParam detail : details) {
                // 设置表体关联的表头ID
                detail.setHeadId(bizIPlan.getSid());
                detail.setTradeCode(userInfo.getCompany());

                // 对必填字段进行校验
                String validationErrors = validateDetailRequiredFields(detail, index);
                if (StringUtils.isNotBlank(validationErrors)) {
                    allErrors.add(validationErrors);
                }

                // 统计商品名称出现次数，用于后续检查重复
                if (StringUtils.isNotBlank(detail.getProductName())) {
                    productNameCount.put(detail.getProductName(),
                            productNameCount.getOrDefault(detail.getProductName(), 0) + 1);
                }

                index++;
            }

            // 检查商品名称重复
            for (Map.Entry<String, Integer> entry : productNameCount.entrySet()) {
                if (entry.getValue() > 1) {
                    allErrors.add("商品名称 '" + entry.getKey() + "' 在表体数据中重复出现" + entry.getValue() + "次");
                }
            }

            // 如果有错误，抛出异常
            if (allErrors.length() > 0) {
                throw new ErrorException(400, allErrors.toString());
            }

            // 所有校验通过，开始保存表体数据
            List<BizIPlanListDto> savedDetails = new ArrayList<>();
            for (BizIPlanListParam detail : details) {
                // 检查商品名称是否已存在于数据库
                if (StringUtils.isNotBlank(detail.getProductName())) {
                    BizIPlanList checkEntity = new BizIPlanList();
                    checkEntity.setHeadId(bizIPlan.getSid());
                    checkEntity.setProductName(detail.getProductName());
                    checkEntity.setSid(detail.getSid());
                    List<BizIPlanList> check = bizIPlanListMapper.checkProductNameWithOutSelf(checkEntity);
                    if (check.size() > 0) {
                        throw new ErrorException(400, "商品名称 '" + detail.getProductName() + "' 已存在于数据库中！");
                    }
                }

                // 设置表体固定字段
                BizIPlanList bizIPlanList = bizIPlanListDtoMapper.toPo(detail);
                bizIPlanList.setTradeCode(userInfo.getCompany());

                // 检查数据是否存在于数据库中
                int resultList;
                if (StringUtils.isNotBlank(detail.getSid())) {
                    // 尝试查询数据是否存在
                    BizIPlanList existingRecord = bizIPlanListMapper.selectByPrimaryKey(detail.getSid());

                    if (existingRecord != null) {
                        // 数据存在，执行更新操作
                        bizIPlanList.setUpdateUser(userInfo.getUserNo());
                        bizIPlanList.setUpdateTime(new Date());
                        resultList = bizIPlanListMapper.updateByPrimaryKey(bizIPlanList);
                    } else {
                        // 数据不存在，执行新增操作
                        String detailSid = UUID.randomUUID().toString();
                        bizIPlanList.setSid(detailSid);
                        bizIPlanList.setInsertUser(userInfo.getUserNo());
                        bizIPlanList.setInsertUserName(userInfo.getUserName());
                        bizIPlanList.setInsertTime(new Date());
                        resultList = bizIPlanListMapper.insert(bizIPlanList);
                    }
                } else {
                    // 没有SID，执行新增操作
                    String detailSid = UUID.randomUUID().toString();
                    bizIPlanList.setSid(detailSid);
                    bizIPlanList.setInsertUser(userInfo.getUserNo());
                    bizIPlanList.setInsertUserName(userInfo.getUserName());
                    bizIPlanList.setInsertTime(new Date());
                    resultList = bizIPlanListMapper.insert(bizIPlanList);
                }

                if (resultList <= 0) {
                    throw new ErrorException(400, "表体数据保存失败，商品名称: " + detail.getProductName());
                }

                // 将保存的表体数据添加到返回列表中
                savedDetails.add(bizIPlanListDtoMapper.toDto(bizIPlanList));
            }
            result.setDetails(savedDetails);
        }

        return result;
    }


    public StringJoiner checkPlan(BizIPlan bizIPlan){
        StringJoiner errorMes =new StringJoiner(",");
//        int checkPlanYear = bizIPlanMapper.checkPlanYear(BizIPlan);
        int checkPlanId = bizIPlanMapper.checkPlanId(bizIPlan);
//        if (checkPlanYear > 0){
//            errorMes.add(xdoi18n.XdoI18nUtil.t("计划年份+上下半年已存在"));
//        }
        if (checkPlanId > 0){
            errorMes.add(xdoi18n.XdoI18nUtil.t("计划编号已存在"));
        }
        return errorMes;
    }

    /**
     * 校验单行表体数据（公共接口）
     * @param detail 表体数据
     * @return 错误信息，如果没有错误则返回空字符串
     */
    public String validateSingleDetailData(BizIPlanListParam detail) {
        String productName = StringUtils.isNotBlank(detail.getProductName()) ?
            detail.getProductName() : "未知商品";
        return validateDetailRequiredFields(detail, productName);
    }

    /**
     * 校验表体必填字段
     * @param detail 表体数据
     * @param index 当前处理的是第几条表体数据
     * @return 错误信息列表，如果没有错误则返回空字符串
     */
    private String validateDetailRequiredFields(BizIPlanListParam detail, int index) {
        StringJoiner errors = new StringJoiner("\n");

  /*      // 1. 商品名称校验
        if (StringUtils.isBlank(detail.getProductName())) {
            errors.add("第" + index + "条表体数据的商品名称不能为空");
        } else if (detail.getProductName().length() > 160) {
            errors.add("第" + index + "条表体数据的商品名称长度不能超过160个字节");
        }*/

        // 2. 供应商校验
        if (StringUtils.isBlank(detail.getSupplier())) {
            errors.add(detail.getProductName()+"的供应商不能为空");
        } else if (detail.getSupplier().length() > 400) {
            errors.add(detail.getProductName()+"的供应商长度不能超过400个字节");
        }

        // 3. 英文品牌校验
        if (detail.getEnglishBrand() != null && detail.getEnglishBrand().length() > 400) {
            errors.add(detail.getProductName()+"的英文品牌长度不能超过400个字节");
        }

        // 4. 原产地校验
        if (detail.getOrigin() != null && detail.getOrigin().length() > 200) {
            errors.add(detail.getProductName()+"的原产地长度不能超过200个字节");
        }

        // 5. 计划数量校验
        if (detail.getPlanQuantity() == null) {
            errors.add(detail.getProductName()+"的计划数量不能为空");
        } else {
            // 检查数字格式
            String planQuantityStr = detail.getPlanQuantity().toString();
            String[] parts = planQuantityStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 13) {
                    errors.add(detail.getProductName()+"的计划数量整数部分不能超过13位");
                }
                if (parts[1].length() > 6) {
                    errors.add(detail.getProductName()+"的计划数量小数部分不能超过6位");
                }
            } else if (parts[0].length() > 13) {
                errors.add(detail.getProductName()+"的计划数量整数部分不能超过13位");
            }
        }

        // 6. 计划数量单位校验
        if (StringUtils.isBlank(detail.getUnit())) {
            errors.add(detail.getProductName()+"的计划数量单位不能为空");
        } else if (detail.getUnit().length() > 40) {
            errors.add(detail.getProductName()+"的计划数量单位长度不能超过40个字节");
        }

        // 7. 币种校验
        if (StringUtils.isBlank(detail.getCurr())) {
            errors.add(detail.getProductName()+"的币种不能为空");
        } else if (detail.getCurr().length() > 20) {
            errors.add(detail.getProductName()+"的币种长度不能超过20个字节");
        }

        // 8. 计划单价校验
        if (detail.getUnitPrice() == null) {
            errors.add(detail.getProductName()+"的计划单价不能为空");
        } else {
            // 检查数字格式
            String unitPriceStr = detail.getUnitPrice().toString();
            String[] parts = unitPriceStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(detail.getProductName()+"的计划单价整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(detail.getProductName()+"的计划单价小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(detail.getProductName()+"的计划单价整数部分不能超过14位");
            }
        }

        // 9. 计划总金额校验
        if (detail.getTotalAmount() == null) {
            errors.add(detail.getProductName()+"的计划总金额不能为空");
        } else {
            // 检查数字格式
            String totalAmountStr = detail.getTotalAmount().toString();
            String[] parts = totalAmountStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(detail.getProductName()+"的计划总金额整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(detail.getProductName()+"的计划总金额小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(detail.getProductName()+"的计划总金额整数部分不能超过14位");
            }
        }

        // 10. 折扣率校验
        if (detail.getDiscountRate() != null) {
            String discountRateStr = detail.getDiscountRate().toString();
            String[] parts = discountRateStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 15) {
                    errors.add(detail.getProductName()+"的折扣率整数部分不能超过15位");
                }
                if (parts[1].length() > 4) {
                    errors.add(detail.getProductName()+"的折扣率小数部分不能超过4位");
                }
            } else if (parts[0].length() > 15) {
                errors.add(detail.getProductName()+"的折扣率整数部分不能超过15位");
            }
        }

        // 11. 折扣金额校验
        if (detail.getDiscountAmount() != null) {
            String discountAmountStr = detail.getDiscountAmount().toString();
            String[] parts = discountAmountStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(detail.getProductName()+"的折扣金额整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(detail.getProductName()+"的折扣金额小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(detail.getProductName()+"的折扣金额整数部分不能超过14位");
            }
        }

        return errors.length() > 0 ? errors.toString() : "";
    }

    /**
     * 校验表体必填字段（使用商品名称标识）
     * @param detail 表体数据
     * @param productName 商品名称（用于错误信息标识）
     * @return 错误信息列表，如果没有错误则返回空字符串
     */
    private String validateDetailRequiredFields(BizIPlanListParam detail, String productName) {
        StringJoiner errors = new StringJoiner("\n");

        // 使用传入的商品名称，如果为空则使用detail中的商品名称
        String displayName = StringUtils.isNotBlank(productName) ? productName :
            (StringUtils.isNotBlank(detail.getProductName()) ? detail.getProductName() : "未知商品");

        // 2. 供应商校验
        if (StringUtils.isBlank(detail.getSupplier())) {
            errors.add(displayName + "的供应商不能为空");
        } else if (detail.getSupplier().length() > 400) {
            errors.add(displayName + "的供应商长度不能超过400个字节");
        }

        // 3. 英文品牌校验
        if (detail.getEnglishBrand() != null && detail.getEnglishBrand().length() > 400) {
            errors.add(displayName + "的英文品牌长度不能超过400个字节");
        }

        // 4. 原产地校验
        if (detail.getOrigin() != null && detail.getOrigin().length() > 200) {
            errors.add(displayName + "的原产地长度不能超过200个字节");
        }

        // 5. 计划数量校验
        if (detail.getPlanQuantity() == null) {
            errors.add(displayName + "的计划数量不能为空");
        } else {
            // 检查数字格式
            String planQuantityStr = detail.getPlanQuantity().toString();
            String[] parts = planQuantityStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 13) {
                    errors.add(displayName + "的计划数量整数部分不能超过13位");
                }
                if (parts[1].length() > 6) {
                    errors.add(displayName + "的计划数量小数部分不能超过6位");
                }
            } else if (parts[0].length() > 13) {
                errors.add(displayName + "的计划数量整数部分不能超过13位");
            }
        }

        // 6. 计划数量单位校验
        if (StringUtils.isBlank(detail.getUnit())) {
            errors.add(displayName + "的计划数量单位不能为空");
        } else if (detail.getUnit().length() > 40) {
            errors.add(displayName + "的计划数量单位长度不能超过40个字节");
        }

        // 7. 币种校验
        if (StringUtils.isBlank(detail.getCurr())) {
            errors.add(displayName + "的币种不能为空");
        } else if (detail.getCurr().length() > 20) {
            errors.add(displayName + "的币种长度不能超过20个字节");
        }

        // 8. 计划单价校验
        if (detail.getUnitPrice() == null) {
            errors.add(displayName + "的计划单价不能为空");
        } else {
            // 检查数字格式
            String unitPriceStr = detail.getUnitPrice().toString();
            String[] parts = unitPriceStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(displayName + "的计划单价整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(displayName + "的计划单价小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(displayName + "的计划单价整数部分不能超过14位");
            }
        }

        // 9. 计划总金额校验
        if (detail.getTotalAmount() == null) {
            errors.add(displayName + "的计划总金额不能为空");
        } else {
            // 检查数字格式
            String totalAmountStr = detail.getTotalAmount().toString();
            String[] parts = totalAmountStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(displayName + "的计划总金额整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(displayName + "的计划总金额小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(displayName + "的计划总金额整数部分不能超过14位");
            }
        }

        // 10. 折扣率校验
        if (detail.getDiscountRate() != null) {
            String discountRateStr = detail.getDiscountRate().toString();
            String[] parts = discountRateStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 15) {
                    errors.add(displayName + "的折扣率整数部分不能超过15位");
                }
                if (parts[1].length() > 4) {
                    errors.add(displayName + "的折扣率小数部分不能超过4位");
                }
            } else if (parts[0].length() > 15) {
                errors.add(displayName + "的折扣率整数部分不能超过15位");
            }
        }

        // 11. 折扣金额校验
        if (detail.getDiscountAmount() != null) {
            String discountAmountStr = detail.getDiscountAmount().toString();
            String[] parts = discountAmountStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(displayName + "的折扣金额整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(displayName + "的折扣金额小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(displayName + "的折扣金额整数部分不能超过14位");
            }
        }

        return errors.length() > 0 ? errors.toString() : "";
    }

    /**
     * 功能描述:修改
     *
     * @param BizIPlanParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPlanDto update(BizIPlanParam BizIPlanParam, UserInfoToken userInfo) {
        BizIPlan bizIPlan = bizIPlanMapper.selectByPrimaryKey(BizIPlanParam.getSid());
        bizIPlanDtoMapper.updatePo(BizIPlanParam, bizIPlan);

        StringJoiner errorMes = checkPlan(bizIPlan);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败,")+errorMes);
        }
        bizIPlan.setUpdateUser(userInfo.getUserNo());
        bizIPlan.setUpdateUserName(userInfo.getUserName());
        bizIPlan.setUpdateTime(new Date());
        bizIPlan.setCreaterUser(userInfo.getUserName());
        bizIPlan.setCreaterTime(new Date());
        // 更新数据
        int update = bizIPlanMapper.updateByPrimaryKey(bizIPlan);
        return update > 0 ? bizIPlanDtoMapper.toDto(bizIPlan) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        //校验数据状态为0编制的允许操作删除
        int checkStatus =bizIPlanMapper.checkPlanStatus(sids,"0");
        if (checkStatus > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("仅编制状态数据允许删除。"));
        }

        int checkContractUsed = bizIPlanMapper.checkContractUsed(sids);
        if (checkContractUsed > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("该单据在关联模块已产生数据，不允许删除"));
        }
		bizIPlanMapper.deleteBySids(sids);
        bizIPlanListMapper.deleteByHeadIds(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIPlanDto> selectAll(BizIPlanParam exportParam, UserInfoToken userInfo) {
        BizIPlan bizIPlan = bizIPlanDtoMapper.toPo(exportParam);
        // BizIPlan.setTradeCode(userInfo.getCompany());
        List<BizIPlanDto> bizIPlanDtos = new ArrayList<>();
        List<BizIPlan> BizIPlans = bizIPlanMapper.getList(bizIPlan);
        if (CollectionUtils.isNotEmpty(BizIPlans)) {
            bizIPlanDtos = BizIPlans.stream().map(head -> {
                BizIPlanDto dto = bizIPlanDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIPlanDtos;
    }

    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizIPlan bizIPlan = bizIPlanMapper.selectByPrimaryKey(sid);
        if (bizIPlan == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizIPlan.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
//        // 更新状态为1 确认
//        bizIPlan.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
//        bizIPlan.setConfirmTime(new Date());
//        bizIPlan.setUpdateUser(userInfo.getUserNo());
//        bizIPlan.setUpdateUserName(userInfo.getUserName());
//        updateStatus(bizIPlan); // 调用更新状态的方法
        BizIPlan update = new BizIPlan();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
//        update.setUpdateUser(userInfo.getUserNo());
//        update.setUpdateUserName(userInfo.getUserName());
        bizIPlanMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(BizIPlan bizIPlan) {
        bizIPlanMapper.updateStatus(bizIPlan); // 调用更新状态的方法
    }

    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizIPlan bizIPlan = bizIPlanMapper.selectByPrimaryKey(sid);
        if (bizIPlan == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizIPlan.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizIPlan.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizIPlan); // 调用更新审批状态的方法

        return resultObject;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizIPlan bizIPlan) {
        bizIPlanMapper.updateApprovalStatus(bizIPlan); // 调用更新审批状态的方法
    }

    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizIPlan bizIPlan = bizIPlanMapper.selectByPrimaryKey(sid);
        if (bizIPlan == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        // 检查是否存在有效合同
        int contractCount = bizIPlanMapper.checkContractUsed(Collections.singletonList(sid));
        if (contractCount > 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage("进口计划存在有效合同，不允许作废");
            return resultObject;
        }

        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizIPlan.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }

//        // 更新状态为2（作废）
//        bizIPlan.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
//        bizIPlan.setConfirmTime(null);
//        bizIPlan.setUpdateUser(userInfo.getUserNo());
//        bizIPlan.setUpdateUserName(userInfo.getUserName());
//        updateStatus(bizIPlan); // 调用更新状态的方法

        BizIPlan update = new BizIPlan();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizIPlanMapper.updateByPrimaryKeySelective(update);

        return resultObject;
    }



    /**
     * 校验是否存在 同一个订单号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject checkPlanIdNotCancel(BizIPlanParam params,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        if (StringUtils.isBlank(params.getSid())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要复制数据！"));
        }
        List<String> sids = bizIPlanMapper.checkPlanIdNotCancel(params.getSid());
        if (CollectionUtils.isNotEmpty((sids))){
            resultObject.setSuccess(false);
            return resultObject;
        }
        return resultObject;
    }

    /**
     * 复制版本
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyVersion(BizIPlanParam params, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("版本复制成功"));
        BizIPlan bizIPlan = bizIPlanMapper.selectByPrimaryKey(params.getSid());

        //查询最大版本号
        BizIPlan maxVersionNoData = bizIPlanMapper.getMaxVersionNoByContract(bizIPlan);
        if (maxVersionNoData == null) {
            throw new ErrorException(400, XdoI18nUtil.t("查询不到有效数据，请刷新后再试！"));
        }
        Integer maxVersionNoInt = Integer.valueOf(maxVersionNoData.getVersionNo()) + 1;

        //新表头id
        String sid = UUID.randomUUID().toString();

        //使用最大版本号找下游数据 更新下游数据关联id
        List<BizIPlanList> oldListSids = bizIPlanListMapper.getContractListByHeadId(maxVersionNoData.getSid());
        for (BizIPlanList old : oldListSids) {
            //版本复制数据 再原sid上拼接版本号

            String newListId = old.getSid().split("_")[0] + "_" + maxVersionNoInt;
            String oldListId = old.getSid().split("_")[0];
            if (!"1".equals(maxVersionNoData.getVersionNo())) {
                oldListId = oldListId + "_" + maxVersionNoData.getVersionNo();
            }

            bizIContractListMapper.updateCorrelationID(newListId, oldListId);
            old.setSid(newListId);
            old.setHeadId(sid);
            old.setInsertUser(userInfo.getUserNo());
            old.setInsertUserName(userInfo.getUserName());
            old.setInsertTime(new Date());
            old.setUpdateUser(null);
            old.setUpdateUserName(null);
            old.setUpdateTime(null);
        }

        //更新现在合同号数据为 2 作废
        bizIPlanMapper.updateCancelByContract(bizIPlan.getPlanId(), bizIPlan.getTradeCode());

        //bizIContractListMapper.copyListByHeadId(bizIContractHead.getSid(), sid, bizIContractHead.getTradeCode());

        bizIPlan.setSid(sid);
        bizIPlan.setInsertTime(new Date());
        bizIPlan.setInsertUser(userInfo.getUserNo());
        bizIPlan.setInsertUserName(userInfo.getUserName());
        bizIPlan.setUpdateUser(null);
        bizIPlan.setUpdateUserName(null);
        bizIPlan.setUpdateTime(null);
        bizIPlan.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizIPlan.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        bizIPlan.setVersionNo(maxVersionNoInt.toString());
        bizIPlan.setConfirmTime(null);
        bizIPlanMapper.insert(bizIPlan);

        if (CollectionUtils.isNotEmpty(oldListSids)){
            oldListSids.stream().forEach(item -> {
                bizIPlanListMapper.insert(item);
            });
        }
        // 复制随附单证文件
        List<Attached> attachedList = bizIOrderHeadMapper.getAttachmentFile(params.getSid());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(sid);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制计划表头【"+bizIPlan.getPlanId()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }

        return result;
    }

    @Override
    public void startFlowBatch(NextNodeInfoBatchVo batchVo, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        // 更新表头状态
        String id = approvalFlowParam.getIds().get(0);
        BizIPlan plan = this.bizIPlanMapper.selectByPrimaryKey(id);
        String[] allowStartApprStatus = {CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue(),
                CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue()};
        if (!Arrays.asList(allowStartApprStatus).contains(plan.getApprStatus())) {
            throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
        }
        plan.setUpdateUser(userInfo.getUserNo());
        plan.setUpdateUserName(userInfo.getUserName());
        plan.setUpdateTime(new Date());
        plan.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        // 记录流程实例id
        plan.setExtend1(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));
        this.bizIPlanMapper.updateByPrimaryKey(plan);
        // 新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(plan.getSid());
        this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核"
                , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
    }

    @Override
    public void audit(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, Map<String, String> flowInstanceMap, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            String businessId = flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId());
            BizIPlan plan = this.bizIPlanMapper.selectByPrimaryKey(businessId);
            if (nextNodeInfoVo.isFinish()) {
                plan.setUpdateUser(userInfo.getUserNo());
                plan.setUpdateUserName(userInfo.getUserName());
                plan.setUpdateTime(new Date());
                plan.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                this.bizIPlanMapper.updateByPrimaryKey(plan);
                nextNodeInfoVo.setNodeName("审核通过");
            }
            // 新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(businessId);
            this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName()
                    , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public void reject(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            // 回退到发起人（仅单条退回）
            String businessId = approvalFlowParam.getIds().get(0);
            BizIPlan plan = this.bizIPlanMapper.selectByPrimaryKey(businessId);
            plan.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
            plan.setUpdateUser(userInfo.getUserNo());
            plan.setUpdateUserName(userInfo.getUserName());
            plan.setUpdateTime(new Date());
            this.bizIPlanMapper.updateByPrimaryKey(plan);

            // 新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(businessId);
            aeoAuditInfo.setApprNote(approvalFlowParam.getApprMessage());
            this.aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回"
                    , userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public List<WorkFlowParam> getFlowList(List<String> ids) {
        return this.bizIPlanMapper.getFlowList(ids);
    }
}
