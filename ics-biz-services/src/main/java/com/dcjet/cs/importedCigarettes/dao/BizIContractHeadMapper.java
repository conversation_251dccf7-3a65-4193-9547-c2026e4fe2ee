package com.dcjet.cs.importedCigarettes.dao;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIContractHead
* <AUTHOR>
* @date: 2025-3-7
*/
public interface BizIContractHeadMapper extends Mapper<BizIContractHead> {
    /**
     * 查询获取数据
     * @param bizIContractHead
     * @return
     */
    List<BizIContractHead> getList(BizIContractHead bizIContractHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkContractNoExits(BizIContractHead bizIContractHead);

    List<BizIContractHead>  getPlanListPaged(BizIContractHead bizIContractHead);

    void updateContractAmountAndQty(@Param("sid") String sid);

    BizIContractHead getAllListAmountAndQty(@Param("sid") String sid);

    int checkContractIsUsed(@Param("contractNo") String contractNo);

    Integer checkStatusByContractNo(@Param("sid") String sid);

    void updateCancelByContract(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    int checkCanDelBySids(List<String> sids);

    int checkContractIsUsedBySids(List<String> sids);

    BizIContractHead getMaxVersionNoByContract(BizIContractHead bizIContractHead);

    List<BizIContractHead> getSellerList(BizIContractHead bizIContractHead);

    String getBuyerCodeByName(@Param("name") String name, @Param("tradeCode") String tradeCode);

    List<WorkFlowParam> selectBySids(List<String> sids);

    List<BizIContractHead> getAeoList(BizIContractHead bizIContractHead);

    int checkApprovalStatus(@Param("sids") List<String> sids, @Param("status") String status);

    String getCustomerByContractNo(@Param("contractNo") String contractNo);
}
