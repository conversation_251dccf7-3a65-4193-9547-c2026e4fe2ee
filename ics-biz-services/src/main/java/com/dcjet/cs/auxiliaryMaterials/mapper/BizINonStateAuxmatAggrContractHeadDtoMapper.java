package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizINonStateAuxmatAggrContractHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizINonStateAuxmatAggrContractHeadDto toDto(BizINonStateAuxmatAggrContractHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizINonStateAuxmatAggrContractHead toPo(BizINonStateAuxmatAggrContractHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param bizINonStateAuxmatAggrContractHead
     */
    void updatePo(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, @MappingTarget BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead);

    /**
     * 转换包含表头和表体的参数到数据库对象
     * @param param
     * @return
     */
    BizINonStateAuxmatAggrContractHead toHeadWithDetailPo(BizINonStateAuxmatAggrContractWithDetailParam param);

    default void patchPo(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead) {
        // TODO 自行实现局部更新
    }
}
