package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* BizIAuxmatBuyContract
* <AUTHOR>
* @date: 2025-5-28
*/
public interface BizIAuxmatBuyContractMapper extends Mapper<BizIAuxmatBuyContract> {
    /**
     * 查询获取数据
     * @param bizIAuxmatBuyContract
     * @return
     */
    List<BizIAuxmatBuyContract> getList(BizIAuxmatBuyContract bizIAuxmatBuyContract);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkContractId(BizIAuxmatBuyContract bizIAuxmatBuyContract);

    int checkOrderStatus(@Param("sids") List<String> sids, @Param("status") String status);

    int checkOrderUsed(@Param("sids") List<String> sids);

    /**
     * 校验是否存在 同一个购销合同号是否存在未作废的数据
     * @param id 合同表头id
     * @return 返回订单号集合
     */
    List<String> checkContractIdNotCancel(@Param("id") String id);

    BizIAuxmatBuyContract getMaxVersionNoByContractNo(BizIAuxmatBuyContract bizIContractHead);

    void updateCancelByContract(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 计算表头金额相关字段
     * @param headId 表头ID
     * @return 计算后的表头对象
     */
    BizIAuxmatBuyContract calculateContractAmounts(@Param("headId") String headId, @Param("exchangeRate") BigDecimal exchangeRate, @Param("billingWeight") BigDecimal billingWeight, @Param("paymentAmount") BigDecimal paymentAmount);

    void saveTransferNotice(BizIAuxmatBuyContract bizIAuxmatBuyContract);

    String getRequestDeliverDate(String sid);
}
