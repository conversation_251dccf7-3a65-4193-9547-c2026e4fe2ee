package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizENonStateAuxmatAggrContractHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizENonStateAuxmatAggrContractHeadDto toDto(BizENonStateAuxmatAggrContractHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizENonStateAuxmatAggrContractHead toPo(BizENonStateAuxmatAggrContractHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizENonStateAuxmatAggrContractHeadParam
     * @param bizENonStateAuxmatAggrContractHead
     */
    void updatePo(BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, @MappingTarget BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead);
    default void patchPo(BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead) {
        // TODO 自行实现局部更新
    }
}
