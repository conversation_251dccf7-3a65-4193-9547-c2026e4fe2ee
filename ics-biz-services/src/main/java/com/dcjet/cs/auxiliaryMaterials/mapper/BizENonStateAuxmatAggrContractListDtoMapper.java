package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizENonStateAuxmatAggrContractListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizENonStateAuxmatAggrContractListDto toDto(BizENonStateAuxmatAggrContractList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizENonStateAuxmatAggrContractList toPo(BizENonStateAuxmatAggrContractListParam param);
    /**
     * 数据库原始数据更新
     * @param bizENonStateAuxmatAggrContractListParam
     * @param bizENonStateAuxmatAggrContractList
     */
    void updatePo(BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, @MappingTarget BizENonStateAuxmatAggrContractList bizENonStateAuxmatAggrContractList);
    default void patchPo(BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, BizENonStateAuxmatAggrContractList bizENonStateAuxmatAggrContractList) {
        // TODO 自行实现局部更新
    }
}
