<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractListMapper">
    <resultMap id="bizENonStateAuxmatAggrContractListResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="g_model" property="gModel" jdbcType="VARCHAR" />
		<result column="specifications" property="specifications" jdbcType="VARCHAR" />
		<result column="gram_weight" property="gramWeight" jdbcType="NUMERIC" />
		<result column="supplier" property="supplier" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="unit_price" property="unitPrice" jdbcType="NUMERIC" />
		<result column="amount" property="amount" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="item_remarks" property="itemRemarks" jdbcType="VARCHAR" />
		<result column="category" property="category" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_by_name" property="createByName" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_by_name" property="updateByName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,head_id
     ,trade_code
     ,g_name
     ,g_model
     ,specifications
     ,gram_weight
     ,supplier
     ,qty
     ,unit
     ,unit_price
     ,amount
     ,curr
     ,item_remarks
     ,category
     ,sys_org_code
     ,create_by
     ,create_by_name
     ,create_time
     ,update_by
     ,update_by_name
     ,update_time
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
    </sql>
    <sql id="Base_Column_List_T" >
         t.id
        ,t.head_id
        ,t.trade_code
        ,t.g_name
        ,t.g_model
        ,t.specifications
        ,t.gram_weight
        ,t.supplier
        ,t.qty
        ,t.unit
        ,t.unit_price
        ,t.amount
        ,t.curr
        ,t.item_remarks
        ,t.category
        ,t.sys_org_code
        ,t.create_by
        ,t.create_by_name
        ,t.create_time
        ,t.update_by
        ,t.update_by_name
        ,t.update_time
        ,t.extend1
        ,t.extend2
        ,t.extend3
        ,t.extend4
        ,t.extend5
        ,t.extend6
        ,t.extend7
        ,t.extend8
        ,t.extend9
        ,t.extend10
    </sql>
    <sql id="condition">
    <if test="headId != null and headId != ''">
		and head_id = #{headId}
	</if>
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizENonStateAuxmatAggrContractListResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_e_non_state_auxmat_aggr_contract_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getBizIAuxmatForContractListByHeadid"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList">
        select
        <include refid="Base_Column_List"></include>
        from t_biz_e_non_state_auxmat_aggr_contract_list t
        where t.head_id = #{headId}
    </select>
    <select id="getListByHeadId" resultType="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList">
        select
            <include refid="Base_Column_List"></include>
        from t_biz_e_non_state_auxmat_aggr_contract_list t
        where t.head_id = #{id}
    </select>
    <select id="getListByContractNo"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList">
        select
            <include refid="Base_Column_List_T"></include>
        from t_biz_e_non_state_auxmat_aggr_contract_list t
        left join t_biz_e_non_state_auxmat_aggr_contract_head h on  t.head_id = h.id
        where t.trade_code = #{tradeCode} and h.contract_no = #{contractNo}
        order by  t.create_time desc
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_e_non_state_auxmat_aggr_contract_list t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
