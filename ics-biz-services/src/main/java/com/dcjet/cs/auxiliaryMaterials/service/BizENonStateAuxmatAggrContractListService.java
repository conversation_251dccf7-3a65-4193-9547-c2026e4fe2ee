package com.dcjet.cs.auxiliaryMaterials.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizENonStateAuxmatAggrContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@Service
public class BizENonStateAuxmatAggrContractListService extends BaseService<BizENonStateAuxmatAggrContractList> {
    @Resource
    private BizENonStateAuxmatAggrContractListMapper bizENonStateAuxmatAggrContractListMapper;
    @Resource
    private BizENonStateAuxmatAggrContractListDtoMapper bizENonStateAuxmatAggrContractListDtoMapper;
    @Override
    public Mapper<BizENonStateAuxmatAggrContractList> getMapper() {
        return bizENonStateAuxmatAggrContractListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizENonStateAuxmatAggrContractListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizENonStateAuxmatAggrContractListDto>> getListPaged(BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizENonStateAuxmatAggrContractList bizENonStateAuxmatAggrContractList = bizENonStateAuxmatAggrContractListDtoMapper.toPo(bizENonStateAuxmatAggrContractListParam);
        bizENonStateAuxmatAggrContractList.setTradeCode(userInfo.getCompany());
        Page<BizENonStateAuxmatAggrContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizENonStateAuxmatAggrContractListMapper.getList(bizENonStateAuxmatAggrContractList));
        List<BizENonStateAuxmatAggrContractListDto> bizENonStateAuxmatAggrContractListDtos = page.getResult().stream().map(head -> {
            BizENonStateAuxmatAggrContractListDto dto = bizENonStateAuxmatAggrContractListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizENonStateAuxmatAggrContractListDto>> paged = ResultObject.createInstance(bizENonStateAuxmatAggrContractListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizENonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizENonStateAuxmatAggrContractListDto insert(BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        BizENonStateAuxmatAggrContractList bizENonStateAuxmatAggrContractList = bizENonStateAuxmatAggrContractListDtoMapper.toPo(bizENonStateAuxmatAggrContractListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizENonStateAuxmatAggrContractList.setId(sid);
        bizENonStateAuxmatAggrContractList.setCreateBy(userInfo.getUserNo());
        bizENonStateAuxmatAggrContractList.setCreateByName(userInfo.getUserName());
        bizENonStateAuxmatAggrContractList.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizENonStateAuxmatAggrContractListMapper.insert(bizENonStateAuxmatAggrContractList);
        return  insertStatus > 0 ? bizENonStateAuxmatAggrContractListDtoMapper.toDto(bizENonStateAuxmatAggrContractList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizENonStateAuxmatAggrContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizENonStateAuxmatAggrContractListDto update(BizENonStateAuxmatAggrContractListParam bizENonStateAuxmatAggrContractListParam, UserInfoToken userInfo) {
        BizENonStateAuxmatAggrContractList bizENonStateAuxmatAggrContractList = bizENonStateAuxmatAggrContractListMapper.selectByPrimaryKey(bizENonStateAuxmatAggrContractListParam.getSid());
        bizENonStateAuxmatAggrContractListDtoMapper.updatePo(bizENonStateAuxmatAggrContractListParam, bizENonStateAuxmatAggrContractList);
        bizENonStateAuxmatAggrContractList.setUpdateBy(userInfo.getUserNo());
        bizENonStateAuxmatAggrContractList.setUpdateByName(userInfo.getUserName());
        bizENonStateAuxmatAggrContractList.setUpdateTime(new Date());
        // 更新数据
        int update = bizENonStateAuxmatAggrContractListMapper.updateByPrimaryKey(bizENonStateAuxmatAggrContractList);
        return update > 0 ? bizENonStateAuxmatAggrContractListDtoMapper.toDto(bizENonStateAuxmatAggrContractList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizENonStateAuxmatAggrContractListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizENonStateAuxmatAggrContractListDto> selectAll(BizENonStateAuxmatAggrContractListParam exportParam, UserInfoToken userInfo) {
        BizENonStateAuxmatAggrContractList bizENonStateAuxmatAggrContractList = bizENonStateAuxmatAggrContractListDtoMapper.toPo(exportParam);
         bizENonStateAuxmatAggrContractList.setTradeCode(userInfo.getCompany());
        List<BizENonStateAuxmatAggrContractListDto> bizENonStateAuxmatAggrContractListDtos = new ArrayList<>();
        List<BizENonStateAuxmatAggrContractList> bizENonStateAuxmatAggrContractLists = bizENonStateAuxmatAggrContractListMapper.getList(bizENonStateAuxmatAggrContractList);
        if (CollectionUtils.isNotEmpty(bizENonStateAuxmatAggrContractLists)) {
            bizENonStateAuxmatAggrContractListDtos = bizENonStateAuxmatAggrContractLists.stream().map(head -> {
                BizENonStateAuxmatAggrContractListDto dto = bizENonStateAuxmatAggrContractListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizENonStateAuxmatAggrContractListDtos;
    }
}
