package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* BizIAuxmatForContractHead
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizIAuxmatForContractHeadMapper extends Mapper<BizIAuxmatForContractHead> {
    /**
     * 查询获取数据
     * @param bizIAuxmatForContractHead
     * @return
     */
    List<BizIAuxmatForContractHead> getList(BizIAuxmatForContractHead bizIAuxmatForContractHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkContractNo(@Param("contractNo") String contractNo, @Param("id") String id);

    int checkCanDelBySids(List<String> sids);

    /**
     * 获取最大版本号
     * @param bizIAuxmatForContractHead
     * @return
     */
    BizIAuxmatForContractHead getMaxVersionNoByContract(BizIAuxmatForContractHead bizIAuxmatForContractHead);

    /**
     * 按合同号作废数据
     * @param contractNo
     * @param tradeCode
     */
    void updateCancelByContract(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 检查合同号是否在下游数据使用
     * @param contractNo
     * @return
     */
    int checkContractIsUsed(@Param("contractNo") String contractNo);

    Integer checkStatusByContractNo(@Param("sid") String sid);

    List<BizIAuxmatForContractHead> getSellerList(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam);

    List<BizIAuxmatForContractHead> getBuyerList(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam);

    int getCountByOrderHeadIds(@Param("orderHeadIds") List<String> orderHeadIds, @Param("tradeCode") String tradeCode);
}
