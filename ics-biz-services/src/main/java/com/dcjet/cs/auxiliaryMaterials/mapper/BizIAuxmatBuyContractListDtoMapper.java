package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-29
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIAuxmatBuyContractListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIAuxmatBuyContractListDto toDto(BizIAuxmatBuyContractList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIAuxmatBuyContractList toPo(BizIAuxmatBuyContractListParam param);
    /**
     * 数据库原始数据更新
     * @param bizIAuxmatBuyContractListParam
     * @param bizIAuxmatBuyContractList
     */
    void updatePo(BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, @MappingTarget BizIAuxmatBuyContractList bizIAuxmatBuyContractList);
    default void patchPo(BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, BizIAuxmatBuyContractList bizIAuxmatBuyContractList) {
        // TODO 自行实现局部更新
    }
}
