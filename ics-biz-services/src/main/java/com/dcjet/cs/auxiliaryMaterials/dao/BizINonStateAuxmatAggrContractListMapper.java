package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* BizINonStateAuxmatAggrContractList
* <AUTHOR>
* @date: 2025-6-18
*/
public interface BizINonStateAuxmatAggrContractListMapper extends Mapper<BizINonStateAuxmatAggrContractList> {
    /**
     * 查询获取数据
     * @param bizINonStateAuxmatAggrContractList
     * @return
     */
    List<BizINonStateAuxmatAggrContractList> getList(BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<BizINonStateAuxmatAggrContractList> getBizIAuxmatForContractListByHeadid(String id);

    List<BizINonStateAuxmatAggrContractList> getContractListByHeadId(@Param("id") String id);
    int deleteByHeadIds(List<String> sids);

    List<BizINonStateAuxmatAggrContractList> getInList(BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList);
}
