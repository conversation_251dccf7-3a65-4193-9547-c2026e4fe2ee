package com.dcjet.cs.auxiliaryMaterials.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.xdo.common.exception.ErrorException;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsHeadMapper;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizINonStateAuxmatAggrContractHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizINonStateAuxmatAggrContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import com.xdo.pcode.service.PCodeHolder;
import jdk.nashorn.internal.runtime.options.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import com.xdo.common.exception.ErrorException;
import xdoi18n.XdoI18nUtil;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Slf4j
@Service
public class BizINonStateAuxmatAggrContractHeadService extends BaseService<BizINonStateAuxmatAggrContractHead> {
    @Resource
    private BizINonStateAuxmatAggrContractHeadMapper bizINonStateAuxmatAggrContractHeadMapper;
    @Resource
    private BizINonStateAuxmatAggrContractHeadDtoMapper bizINonStateAuxmatAggrContractHeadDtoMapper;
    @Resource
    private BizINonStateAuxmatAggrContractListMapper bizINonStateAuxmatAggrContractListMapper;
    @Resource
    private BizINonStateAuxmatAggrContractListDtoMapper bizINonStateAuxmatAggrContractListDtoMapper;
    @Resource
    private BizNonIncomingGoodsHeadMapper bizNonIncomingGoodsHeadMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private AttachedService attachedService;
    @Resource
    private ExportService exportService;
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;
    @Resource
    private BizMerchantService bizMerchantService;
    @Resource
    private CommonService commonService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Override
    public Mapper<BizINonStateAuxmatAggrContractHead> getMapper() {
        return bizINonStateAuxmatAggrContractHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> getListPaged(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadDtoMapper.toPo(bizINonStateAuxmatAggrContractHeadParam);
        Page<BizINonStateAuxmatAggrContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizINonStateAuxmatAggrContractHeadMapper.getList(bizINonStateAuxmatAggrContractHead));
        List<BizINonStateAuxmatAggrContractHeadDto> bizINonStateAuxmatAggrContractHeadDtos = page.getResult().stream().map(head -> {
            BizINonStateAuxmatAggrContractHeadDto dto = bizINonStateAuxmatAggrContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> paged = ResultObject.createInstance(bizINonStateAuxmatAggrContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizINonStateAuxmatAggrContractHeadDto insert(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadDtoMapper.toPo(bizINonStateAuxmatAggrContractHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizINonStateAuxmatAggrContractHead.setId(sid);
        bizINonStateAuxmatAggrContractHead.setTradeCode(userInfo.getCompany());
        bizINonStateAuxmatAggrContractHead.setCreateBy(userInfo.getUserNo());
        bizINonStateAuxmatAggrContractHead.setCreatetUserName(userInfo.getUserName());
        bizINonStateAuxmatAggrContractHead.setInsertUserName(userInfo.getUserName());
        bizINonStateAuxmatAggrContractHead.setCreateTime(new Date());
        bizINonStateAuxmatAggrContractHead.setInsertTime(new Date());
        bizINonStateAuxmatAggrContractHead.setIsTransferNotice("0");
        bizINonStateAuxmatAggrContractHead.setPriceTermPort("1");
        // 新增数据
        int insertStatus = bizINonStateAuxmatAggrContractHeadMapper.insert(bizINonStateAuxmatAggrContractHead);
        return  insertStatus > 0 ? bizINonStateAuxmatAggrContractHeadDtoMapper.toDto(bizINonStateAuxmatAggrContractHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizINonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizINonStateAuxmatAggrContractHeadDto update(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizINonStateAuxmatAggrContractHeadParam.getId());
        bizINonStateAuxmatAggrContractHeadDtoMapper.updatePo(bizINonStateAuxmatAggrContractHeadParam, bizINonStateAuxmatAggrContractHead);
        bizINonStateAuxmatAggrContractHead.setUpdateBy(userInfo.getUserNo());
        bizINonStateAuxmatAggrContractHead.setUpdateUserName(userInfo.getUserName());
        bizINonStateAuxmatAggrContractHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizINonStateAuxmatAggrContractHeadMapper.updateByPrimaryKey(bizINonStateAuxmatAggrContractHead);
        return update > 0 ? bizINonStateAuxmatAggrContractHeadDtoMapper.toDto(bizINonStateAuxmatAggrContractHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        //校验数据状态为0编制的允许操作删除
        int checkStatus =bizINonStateAuxmatAggrContractHeadMapper.checkPlanStatus(sids,"0");
        if (checkStatus > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("仅编制状态数据允许删除。"));
        }
        // 检查是否存在有效合同

        int contractCount = bizINonStateAuxmatAggrContractHeadMapper.checkOrderUsed(sids);
        if (contractCount > 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("该单据在关联模块已产生数据，不允许删除"));
        }

		bizINonStateAuxmatAggrContractHeadMapper.deleteBySids(sids);
        bizINonStateAuxmatAggrContractListMapper.deleteByHeadIds(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizINonStateAuxmatAggrContractHeadDto> selectAll(BizINonStateAuxmatAggrContractHeadParam exportParam, UserInfoToken userInfo) {
        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadDtoMapper.toPo(exportParam);
        // bizINonStateAuxmatAggrContractHead.setTradeCode(userInfo.getCompany());
        List<BizINonStateAuxmatAggrContractHeadDto> bizINonStateAuxmatAggrContractHeadDtos = new ArrayList<>();
        List<BizINonStateAuxmatAggrContractHead> bizINonStateAuxmatAggrContractHeads = bizINonStateAuxmatAggrContractHeadMapper.getList(bizINonStateAuxmatAggrContractHead);
        if (CollectionUtils.isNotEmpty(bizINonStateAuxmatAggrContractHeads)) {
            bizINonStateAuxmatAggrContractHeadDtos = bizINonStateAuxmatAggrContractHeads.stream().map(head -> {
                BizINonStateAuxmatAggrContractHeadDto dto = bizINonStateAuxmatAggrContractHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizINonStateAuxmatAggrContractHeadDtos;
    }

    /**
     * 确认数据状态接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizINonStateAuxmatAggrContractHead contract = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        if ("1".equals(contract.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }

        contract.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        contract.setConfirmTime(new Date());
        contract.setUpdateBy(userInfo.getUserNo());
        contract.setUpdateUserName(userInfo.getUserName());
        contract.setUpdateTime(new Date());

        bizINonStateAuxmatAggrContractHeadMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "确认成功");
    }

    /**
     * 发送审批接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizINonStateAuxmatAggrContractHead contract = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(contract.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("只有数据状态为确认的数据允许操作发送审批");
            return resultObject;
        }

        // 假设审批状态字段为 apprStatus，2表示审批中
        if ("2".equals(contract.getApprStatus())) {
            throw new ErrorException(400, "该数据已在审批中，无需重复操作");
        }

        contract.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        contract.setUpdateBy(userInfo.getUserNo());
        contract.setUpdateUserName(userInfo.getUserName());
        contract.setUpdateTime(new Date());

        bizINonStateAuxmatAggrContractHeadMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "发送审批成功");
    }

    /**
     * 作废接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizINonStateAuxmatAggrContractHead contract = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            throw new ErrorException(400, "非国营辅料合同数据不存在，请联系管理员");
        }

        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(contract.getApprStatus())) {
            throw new ErrorException(400, "审批中的数据不允许作废");
        }
      /*  if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(contract.getStatus())) {
            throw new ErrorException(400, "该数据已确认，不允许作废");
        }*/
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(contract.getStatus())) {
            throw new ErrorException(400, "该数据已经作废，无需重复操作");
        }

        // 检查是否存在有效合同
        int contractCount = bizINonStateAuxmatAggrContractHeadMapper.checkOrderUsed(Collections.singletonList(sid));
        if (contractCount > 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage("进货管理存在有效合同，不允许作废");
            return resultObject;
        }

        contract.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());

        bizINonStateAuxmatAggrContractHeadMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "作废成功");
    }

    /**
     * 校验是否存在同一个合同号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject checkContractIdNotCancel(BizINonStateAuxmatAggrContractHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");

        if (StringUtils.isBlank(params.getId())){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请选择需要复制数据！"));
        }

        // 查询同一合同号下未作废的数据数量
//        BizINonStateAuxmatAggrContractHead queryParam = new BizINonStateAuxmatAggrContractHead();
//        queryParam.setContractNo(params.getContractNo());
        // queryParam.setTradeCode(userInfo.getCompany());

        List<String> sids  = bizINonStateAuxmatAggrContractHeadMapper.checkContractIdNotCancel(params.getId());
        if (CollectionUtils.isNotEmpty((sids))){
            resultObject.setSuccess(false);
            return resultObject;
        }

        return resultObject;
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyVersion(BizINonStateAuxmatAggrContractHeadParam params, UserInfoToken userInfo) {

        if (StringUtils.isBlank(params.getId())) {
            throw new ErrorException(400, "原数据ID不能为空");
        }
        BizINonStateAuxmatAggrContractHead originalContract = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(params.getId());
        if (originalContract == null) {
            throw new ErrorException(400, "原数据不存在，请联系管理员");
        }

        BizINonStateAuxmatAggrContractHead maxVersionNoData = bizINonStateAuxmatAggrContractHeadMapper.getMaxVersionNoByContractNo(originalContract);
        if (maxVersionNoData == null) {
            throw new ErrorException(400, XdoI18nUtil.t("查询不到有效数据，请刷新后再试！"));
        }

        Integer maxVersionNoInt = Integer.valueOf(maxVersionNoData.getVersionNo()) + 1;

        //新表头id
        String sid = UUID.randomUUID().toString();

        //使用最大版本号找下游数据 更新下游数据关联id
        List<BizINonStateAuxmatAggrContractList> oldListSids = bizINonStateAuxmatAggrContractListMapper.getContractListByHeadId(maxVersionNoData.getId());
        for (BizINonStateAuxmatAggrContractList old : oldListSids){
            String newListId = old.getId().split("_")[0] + "_" + maxVersionNoInt;
            String oldListId = old.getId().split("_")[0];
            if (!"1".equals(maxVersionNoData.getVersionNo())) {
                oldListId = oldListId + "_" + maxVersionNoData.getVersionNo();
            }
            bizNonIncomingGoodsHeadMapper.updateCorrelationID(newListId, oldListId);
            old.setId(newListId);
            old.setHeadId(sid);
            old.setCreateBy(userInfo.getUserNo());
            old.setInsertUserName(userInfo.getUserName());
            old.setCreateTime(new Date());
            old.setUpdateBy(null);
            old.setUpdateUserName(null);
            old.setUpdateTime(null);
        }

        //更新现在合同号数据为 2 作废
        bizINonStateAuxmatAggrContractHeadMapper.updateCancelByContract(originalContract.getContractNo(), originalContract.getTradeCode());

        // 插入新的表头数据
        originalContract.setId(sid);
        originalContract.setCreateTime(new Date());
        originalContract.setCreateBy(userInfo.getUserNo());
        originalContract.setInsertUserName(userInfo.getUserName());
        originalContract.setUpdateBy(null);
        originalContract.setUpdateUserName(null);
        originalContract.setUpdateTime(null);
        originalContract.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        originalContract.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        originalContract.setVersionNo(maxVersionNoInt.toString());
        originalContract.setConfirmTime(null);
        bizINonStateAuxmatAggrContractHeadMapper.insert(originalContract);
        if (CollectionUtils.isNotEmpty(oldListSids)){
            oldListSids.stream().forEach(item -> {
                bizINonStateAuxmatAggrContractListMapper.insert(item);
            });
        }

        // 复制随附单证文件
        List<Attached> attachedList = bizINonStateAuxmatAggrContractHeadMapper.getAttachmentFile(params.getId());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(sid);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制合同与协议表头【"+originalContract.getContractNo()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }

        return ResultObject.createInstance(true, "版本复制成功");
    }

    /**
     * 校验单行表体数据
     * @param detail 表体数据
     * @return 返回校验结果错误信息，如果为空则校验通过
     */
    public String validateSingleDetailData(BizINonStateAuxmatAggrContractListParam detail) {
        try {
            // 校验商品名称（必填字段）
            if (StringUtils.isBlank(detail.getGoodsName())) {
                return "商品名称不能为空";
            }
            // 校验商品名称（必填字段）
            if (StringUtils.isBlank(detail.getUnit())) {
                return "单位不能为空";
            }

            // 校验商品名称长度（字符型160）
            if (detail.getGoodsName().length() > 160) {
                return "商品名称【" + detail.getGoodsName() + "】长度不能超过160个字符";
            }

            // 校验商品描述长度（字符型200，非必填）
            if (StringUtils.isNotBlank(detail.getGoodsDesc()) && detail.getGoodsDesc().length() > 200) {
                return "商品名称【" + detail.getGoodsName() + "】的商品描述长度不能超过200个字符";
            }

            // 校验数量（数值型，必填）
            if (detail.getQty() == null) {
                return "商品名称【" + detail.getGoodsName() + "】的数量不能为空";
            }
            if (detail.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGoodsName() + "】的数量必须大于0";
            }

            // 校验单位（字符型40，非必填）
            if (StringUtils.isNotBlank(detail.getUnit()) && detail.getUnit().length() > 40) {
                return "商品名称【" + detail.getGoodsName() + "】的单位长度不能超过40个字符";
            }

            // 校验单价（数值型，必填）
            if (detail.getUnitPrice() == null) {
                return "商品名称【" + detail.getGoodsName() + "】的单价不能为空";
            }
            if (detail.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGoodsName() + "】的单价必须大于0";
            }

            // 校验金额（数值型，必填）
            if (detail.getAmount() == null) {
                return "商品名称【" + detail.getGoodsName() + "】的金额不能为空";
            }
            if (detail.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGoodsName() + "】的金额必须大于0";
            }

            // 校验备注长度（字符型400，非必填）
            if (StringUtils.isNotBlank(detail.getRemark()) && detail.getRemark().length() > 400) {
                return "商品名称【" + detail.getGoodsName() + "】的备注长度不能超过400个字符";
            }

            // 校验商品类别长度（字符型160，非必填）
            if (StringUtils.isNotBlank(detail.getGoodsCategory()) && detail.getGoodsCategory().length() > 160) {
                return "商品名称【" + detail.getGoodsName() + "】的商品类别长度不能超过160个字符";
            }

            // 校验金额计算是否正确（数量 × 单价 = 金额）
            BigDecimal calculatedAmount = detail.getQty().multiply(detail.getUnitPrice());
            // 由于金额小数位最大4位，所以允许的误差为0.0001
            if (calculatedAmount.subtract(detail.getAmount()).abs().compareTo(new BigDecimal("0.0001")) > 0) {
                return "商品名称【" + detail.getGoodsName() + "】的金额计算不正确，应为：" + calculatedAmount.setScale(4, java.math.RoundingMode.HALF_UP);
            }

            return ""; // 校验通过
        } catch (Exception e) {
            String productName = detail != null && StringUtils.isNotBlank(detail.getGoodsName()) ? detail.getGoodsName() : "未知商品";
            return "商品名称【" + productName + "】校验时发生异常：" + e.getMessage();
        }
    }

    /**
     * 新增表头和表体数据
     * @param bizINonStateAuxmatAggrContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回包含表头和表体数据的完整信息
     */
    @Transactional(rollbackFor = Exception.class)
    public BizINonStateAuxmatAggrContractWithDetailDto insertAggrContractWithDetails(BizINonStateAuxmatAggrContractWithDetailParam bizINonStateAuxmatAggrContractWithDetailParam, UserInfoToken userInfo) {
        List<BizINonStateAuxmatAggrContractListParam> contractDetailList = bizINonStateAuxmatAggrContractWithDetailParam.getDetails();

        // 1. 处理表头数据
        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadDtoMapper.toHeadWithDetailPo(bizINonStateAuxmatAggrContractWithDetailParam);
        bizINonStateAuxmatAggrContractHead.setTradeCode(userInfo.getCompany());
        // 校验合同数据
        StringJoiner errorMes = checkContract(bizINonStateAuxmatAggrContractHead);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败,")+errorMes);
        }

        // 设置表头基本信息
        String id = bizINonStateAuxmatAggrContractHead.getId();
        bizINonStateAuxmatAggrContractHead.setCreateBy(userInfo.getUserNo());
        bizINonStateAuxmatAggrContractHead.setInsertUserName(userInfo.getUserName());
        bizINonStateAuxmatAggrContractHead.setCreateTime(new Date());
        bizINonStateAuxmatAggrContractHead.setCreatetUserName(userInfo.getUserName());
        bizINonStateAuxmatAggrContractHead.setInsertTime(new Date());

        // 插入表头数据
        int insertStatus = bizINonStateAuxmatAggrContractHeadMapper.updateByPrimaryKeySelective(bizINonStateAuxmatAggrContractHead);
        if (insertStatus <= 0) {
            throw new ErrorException(400, "表头数据插入失败");
        }

        // 构建返回结果
        BizINonStateAuxmatAggrContractWithDetailDto result = new BizINonStateAuxmatAggrContractWithDetailDto();
        result.setHead(bizINonStateAuxmatAggrContractHeadDtoMapper.toDto(bizINonStateAuxmatAggrContractHead));

        // 2. 处理表体数据
        List<BizINonStateAuxmatAggrContractListDto> savedDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contractDetailList)) {
            for (BizINonStateAuxmatAggrContractListParam detail : contractDetailList) {
                // 校验表体数据
                String errorMessage = validateSingleDetailData(detail);
                if (StringUtils.isNotBlank(errorMessage)) {
                    throw new ErrorException(400, errorMessage);
                }

                // 设置关联关系
                detail.setHeadId(id);

                BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList = bizINonStateAuxmatAggrContractListDtoMapper.toPo(detail);
                bizINonStateAuxmatAggrContractList.setCreateBy(userInfo.getUserNo());
                bizINonStateAuxmatAggrContractList.setInsertUserName(userInfo.getUserName());
                bizINonStateAuxmatAggrContractList.setCreateTime(new Date());

                // 生成表体ID
                String detailId = UUID.randomUUID().toString();
                bizINonStateAuxmatAggrContractList.setId(detailId);

                // 插入表体数据
                int detailInsertStatus = bizINonStateAuxmatAggrContractListMapper.insert(bizINonStateAuxmatAggrContractList);
                if (detailInsertStatus > 0) {
                    savedDetails.add(bizINonStateAuxmatAggrContractListDtoMapper.toDto(bizINonStateAuxmatAggrContractList));
                }
            }
        }

        result.setDetails(savedDetails);
        return result;
    }

    /**
     * 修改表头和表体数据
     * @param bizINonStateAuxmatAggrContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回包含表头和表体数据的完整信息
     */
    @Transactional(rollbackFor = Exception.class)
    public BizINonStateAuxmatAggrContractWithDetailDto updateAggrContractWithDetails(BizINonStateAuxmatAggrContractWithDetailParam bizINonStateAuxmatAggrContractWithDetailParam, UserInfoToken userInfo) {
        List<BizINonStateAuxmatAggrContractListParam> contractDetailList = bizINonStateAuxmatAggrContractWithDetailParam.getDetails();

        // 1. 处理表头数据
        BizINonStateAuxmatAggrContractHead existingContract = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizINonStateAuxmatAggrContractWithDetailParam.getId());
        if (existingContract == null) {
            throw new ErrorException(400, "非国营辅料合同数据不存在，请联系管理员");
        }

        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadDtoMapper.toHeadWithDetailPo(bizINonStateAuxmatAggrContractWithDetailParam);
        bizINonStateAuxmatAggrContractHead.setTradeCode(userInfo.getCompany());
        // 校验合同数据
        StringJoiner errorMes = checkContract(bizINonStateAuxmatAggrContractHead);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败,")+errorMes);
        }

        // 更新表头数据
        BizINonStateAuxmatAggrContractHeadParam headParam = new BizINonStateAuxmatAggrContractHeadParam();
        headParam.setId(bizINonStateAuxmatAggrContractWithDetailParam.getId());
        headParam.setBusinessType(bizINonStateAuxmatAggrContractWithDetailParam.getBusinessType());
        headParam.setContractNo(bizINonStateAuxmatAggrContractWithDetailParam.getContractNo());
        headParam.setBuyer(bizINonStateAuxmatAggrContractWithDetailParam.getBuyer());
        headParam.setSupplier(bizINonStateAuxmatAggrContractWithDetailParam.getSupplier());
        headParam.setSigningDate(bizINonStateAuxmatAggrContractWithDetailParam.getSigningDate());
        headParam.setDomesticPrincipal(bizINonStateAuxmatAggrContractWithDetailParam.getDomesticPrincipal());
        headParam.setTransportMode(bizINonStateAuxmatAggrContractWithDetailParam.getTransportMode());
        headParam.setEffectiveDate(bizINonStateAuxmatAggrContractWithDetailParam.getEffectiveDate());
        headParam.setExpiryDate(bizINonStateAuxmatAggrContractWithDetailParam.getExpiryDate());
        headParam.setSigningLocationCn(bizINonStateAuxmatAggrContractWithDetailParam.getSigningLocationCn());
        headParam.setSigningLocationEn(bizINonStateAuxmatAggrContractWithDetailParam.getSigningLocationEn());
        headParam.setPortOfLoading(bizINonStateAuxmatAggrContractWithDetailParam.getPortOfLoading());
        headParam.setPortOfDestination(bizINonStateAuxmatAggrContractWithDetailParam.getPortOfDestination());
        headParam.setCustomsPort(bizINonStateAuxmatAggrContractWithDetailParam.getCustomsPort());
        headParam.setPaymentMethod(bizINonStateAuxmatAggrContractWithDetailParam.getPaymentMethod());
        headParam.setCurrency(bizINonStateAuxmatAggrContractWithDetailParam.getCurrency());
        headParam.setPriceTerm(bizINonStateAuxmatAggrContractWithDetailParam.getPriceTerm());
        headParam.setPriceTermPort(bizINonStateAuxmatAggrContractWithDetailParam.getPriceTermPort());
        headParam.setSuggestedSigner(bizINonStateAuxmatAggrContractWithDetailParam.getSuggestedSigner());
        headParam.setShortageOverflowPercent(bizINonStateAuxmatAggrContractWithDetailParam.getShortageOverflowPercent());
        headParam.setRemarks(bizINonStateAuxmatAggrContractWithDetailParam.getRemarks());
        headParam.setVersionNo(bizINonStateAuxmatAggrContractWithDetailParam.getVersionNo());
        headParam.setStatus(bizINonStateAuxmatAggrContractWithDetailParam.getStatus());
        headParam.setConfirmTime(bizINonStateAuxmatAggrContractWithDetailParam.getConfirmTime());
        headParam.setApprStatus(bizINonStateAuxmatAggrContractWithDetailParam.getApprStatus());
        headParam.setCreateTime(bizINonStateAuxmatAggrContractWithDetailParam.getCreateTime());

        bizINonStateAuxmatAggrContractHeadDtoMapper.updatePo(headParam, existingContract);
        existingContract.setUpdateBy(userInfo.getUserNo());
        existingContract.setUpdateUserName(userInfo.getUserName());
        existingContract.setUpdateTime(new Date());
        existingContract.setCreatetUserName(userInfo.getUserName());
        existingContract.setInsertTime(new Date());
        existingContract.setTradeCode(userInfo.getCompany());

        int updateStatus = bizINonStateAuxmatAggrContractHeadMapper.updateByPrimaryKeySelective(existingContract);
        if (updateStatus <= 0) {
            throw new ErrorException(400, "表头数据更新失败");
        }

        // 构建返回结果
        BizINonStateAuxmatAggrContractWithDetailDto result = new BizINonStateAuxmatAggrContractWithDetailDto();
        result.setHead(bizINonStateAuxmatAggrContractHeadDtoMapper.toDto(existingContract));

        // 2. 处理表体数据
        List<BizINonStateAuxmatAggrContractListDto> savedDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contractDetailList)) {
            for (BizINonStateAuxmatAggrContractListParam detail : contractDetailList) {
                // 校验表体数据
                String errorMessage = validateSingleDetailData(detail);
                if (StringUtils.isNotBlank(errorMessage)) {
                    throw new ErrorException(400, errorMessage);
                }

                // 设置关联关系
                detail.setHeadId(bizINonStateAuxmatAggrContractWithDetailParam.getId());

                BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList = bizINonStateAuxmatAggrContractListDtoMapper.toPo(detail);
                int resultList;
                if (StringUtils.isNotBlank(detail.getId())) {
                    // 更新现有表体数据
                    BizINonStateAuxmatAggrContractList existingDetail = bizINonStateAuxmatAggrContractListMapper.selectByPrimaryKey(detail.getId());
                    if (existingDetail != null) {
                        bizINonStateAuxmatAggrContractListDtoMapper.updatePo(detail, existingDetail);
                        existingDetail.setUpdateBy(userInfo.getUserNo());
                        existingDetail.setUpdateUserName(userInfo.getUserName());
                        existingDetail.setUpdateTime(new Date());

                         resultList = bizINonStateAuxmatAggrContractListMapper.updateByPrimaryKey(existingDetail);
                        if (resultList > 0) {
                            savedDetails.add(bizINonStateAuxmatAggrContractListDtoMapper.toDto(existingDetail));
                        }
                    }else {
                        // 数据不存在，执行新增操作
                        String detailSid = UUID.randomUUID().toString();
                        bizINonStateAuxmatAggrContractList.setId(detailSid);
                        bizINonStateAuxmatAggrContractList.setCreateBy(userInfo.getUserNo());
                        bizINonStateAuxmatAggrContractList.setInsertUserName(userInfo.getUserName());
                        bizINonStateAuxmatAggrContractList.setCreateTime(new Date());
                        resultList = bizINonStateAuxmatAggrContractListMapper.insert(bizINonStateAuxmatAggrContractList);
                    }
                } else {
                    // 新增表体数据
                    bizINonStateAuxmatAggrContractList.setCreateBy(userInfo.getUserNo());
                    bizINonStateAuxmatAggrContractList.setInsertUserName(userInfo.getUserName());
                    bizINonStateAuxmatAggrContractList.setCreateTime(new Date());

                    // 生成表体ID
                    String detailId = UUID.randomUUID().toString();
                    bizINonStateAuxmatAggrContractList.setId(detailId);

                    // 插入表体数据
                    resultList = bizINonStateAuxmatAggrContractListMapper.insert(bizINonStateAuxmatAggrContractList);
                    if (resultList > 0) {
                        savedDetails.add(bizINonStateAuxmatAggrContractListDtoMapper.toDto(bizINonStateAuxmatAggrContractList));
                    }
                }
                if (resultList <= 0) {
                    throw new ErrorException(400, "表体数据保存失败，商品名称: " + detail.getGoodsName());
                }
            }
        }


        result.setDetails(savedDetails);
        return result;
    }

    /**
     * 校验合同数据
     * @param contract 合同数据
     * @return 错误信息
     */
    public StringJoiner checkContract(BizINonStateAuxmatAggrContractHead contract){
        StringJoiner errorMes = new StringJoiner(",");

        int checkPlanId =bizINonStateAuxmatAggrContractHeadMapper.checkContractId(contract);

        if (checkPlanId > 0){
            errorMes.add(xdoi18n.XdoI18nUtil.t("合同号已存在"));
        }

        return errorMes;
    }
    public ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> getListPagedToCustomerAccount(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadDtoMapper.toPo(bizINonStateAuxmatAggrContractHeadParam);
        Page<BizINonStateAuxmatAggrContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizINonStateAuxmatAggrContractHeadMapper.getListPagedToCustomerAccount(bizINonStateAuxmatAggrContractHead));
        List<BizINonStateAuxmatAggrContractHeadDto> bizINonStateAuxmatAggrContractHeadDtos = page.getResult().stream().map(head -> {
            BizINonStateAuxmatAggrContractHeadDto dto = bizINonStateAuxmatAggrContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizINonStateAuxmatAggrContractHeadDto>> paged = ResultObject.createInstance(bizINonStateAuxmatAggrContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public String printAgreement(String name,BizINonStateAuxmatAggrContractHeadParam param, UserInfoToken userInfo) {
        String templateName = name + ".xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizINonStateAuxmatAggrContractHead> heads = new ArrayList<>();
        List<BizINonStateAuxmatAggrContractList> lists = new ArrayList<>();
        if (StringUtil.isEmpty(param.getId())){
            throw new ErrorException(400, "协议会签单表头不能为空");
        }
        BizINonStateAuxmatAggrContractHead head = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(param.getId());
        //表头数据处理
        Map<String, String> merchantMap = bizMerchantService.getMerchantMap(userInfo);

        head.setDomesticPrincipal(merchantMap.get(head.getDomesticPrincipal()));
        head.setSupplier(merchantMap.get(head.getSupplier()));

        //表体数据获取
        BizINonStateAuxmatAggrContractList listParam=new BizINonStateAuxmatAggrContractList();
        listParam.setHeadId(param.getId());
        lists = bizINonStateAuxmatAggrContractListMapper.select(listParam);

        if (CollectionUtils.isEmpty(lists)) {
            throw new ErrorException(400, "表体数据不能为空");
        }

        BigDecimal totalAmount = lists.stream().filter(Objects::nonNull).map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalQty = lists.stream().filter(Objects::nonNull).map(BizINonStateAuxmatAggrContractList::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

        String unitStr = StringUtils.isBlank(pCodeHolder.getValue(PCodeType.UNIT, lists.get(0).getUnit())) ? "": pCodeHolder.getValue(PCodeType.UNIT, lists.get(0).getUnit());
        head.setTotalAmountStr("(小写)：" + head.getCurrency() + NumberFormatterUtils.formatNumber(totalAmount));
        head.setTotalAmountUpStr("(大写)：" + commonService.getCurrCn(head.getCurrency(), PCodeType.CURR_ALL) + NumberFormatterUtils.convertToChineseAmount(totalAmount));
        head.setQtyAllStr(NumberFormatterUtils.formatNumber(totalQty) + unitStr);

        head.setRemarks("我司代理"+ head.getDomesticPrincipal() +"向"+ head.getSupplier() +"进口白卡纸" +
                head.getQtyAllStr() + "。合同总价为" +
                head.getCurrency() + NumberFormatterUtils.formatNumber(totalAmount) + " CIF SHANGHAI。产品的技术规格、付款方式以及交货期均经过合同双方确认。");

        heads.add(head);

        String exportFileName = exportService.export(heads,lists, fileName, templateName);

        return exportFileName;
    }

    /**
     * 划款通知
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject handlerTransferNotice(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("划款通知成功"));
        BizINonStateAuxmatAggrContractHead transferNotice;

        if ("0".equals(bizINonStateAuxmatAggrContractHead.getIsTransferNotice())) {
            BigDecimal exchangeRate = bizINonStateAuxmatAggrContractHead.getExchangeRate() == null ? null : bizINonStateAuxmatAggrContractHead.getExchangeRate();
            BigDecimal billingWeight = bizINonStateAuxmatAggrContractHead.getBillingWeight() == null ? null : bizINonStateAuxmatAggrContractHead.getBillingWeight();
            BigDecimal paymentAmount = bizINonStateAuxmatAggrContractHead.getPaymentAmount() == null ? null : bizINonStateAuxmatAggrContractHead.getPaymentAmount();
            transferNotice = bizINonStateAuxmatAggrContractHeadMapper.calculateContractAmounts(bizINonStateAuxmatAggrContractHead.getId(), exchangeRate, billingWeight, paymentAmount);
        }else {
            transferNotice=bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizINonStateAuxmatAggrContractHead.getId());
        }
        resultObject.setData(transferNotice);

        return resultObject;
    }
    /**
     * 划款通知保存
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject saveTransferNotice(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead, UserInfoToken userInfo)  {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("划款通知保存成功"));
        List<BizINonStateAuxmatAggrContractList> buyContractLists=bizINonStateAuxmatAggrContractListMapper.getContractListByHeadId(bizINonStateAuxmatAggrContractHead.getId());
        if (CollectionUtils.isEmpty(buyContractLists)){
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("表体没有数据，请补充表体再操作"));
        }
        BigDecimal exchangeRate=bizINonStateAuxmatAggrContractHead.getExchangeRate()==null?null:bizINonStateAuxmatAggrContractHead.getExchangeRate();
        BigDecimal billingWeight=bizINonStateAuxmatAggrContractHead.getBillingWeight()==null?null:bizINonStateAuxmatAggrContractHead.getBillingWeight();
        BigDecimal paymentAmount=bizINonStateAuxmatAggrContractHead.getPaymentAmount()==null?null:bizINonStateAuxmatAggrContractHead.getPaymentAmount();
        BizINonStateAuxmatAggrContractHead transferNotice=bizINonStateAuxmatAggrContractHeadMapper.calculateContractAmounts(bizINonStateAuxmatAggrContractHead.getId(),exchangeRate,billingWeight,paymentAmount);
        bizINonStateAuxmatAggrContractHeadMapper.saveTransferNotice(transferNotice);

        resultObject.setData(transferNotice);

        return resultObject;
    }

    /**
     * 划款通知保存校验
     *
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject checkTransferNotice(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead)  {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("划款通知保存成功"));
        List<BizINonStateAuxmatAggrContractList> buyContractLists=bizINonStateAuxmatAggrContractListMapper.getContractListByHeadId(bizINonStateAuxmatAggrContractHead.getId());
        if (CollectionUtils.isEmpty(buyContractLists)){
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("表体没有数据，请补充表体再操作"));
        }
        return resultObject;
    }

    /**
     * 划款通知保存打印
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity saveTransferNoticePrint(BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead, UserInfoToken userInfo) throws Exception {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);

        BigDecimal exchangeRate=bizINonStateAuxmatAggrContractHead.getExchangeRate()==null?null:bizINonStateAuxmatAggrContractHead.getExchangeRate();
        BigDecimal billingWeight=bizINonStateAuxmatAggrContractHead.getBillingWeight()==null?null:bizINonStateAuxmatAggrContractHead.getBillingWeight();
        BigDecimal paymentAmount=bizINonStateAuxmatAggrContractHead.getPaymentAmount()==null?null:bizINonStateAuxmatAggrContractHead.getPaymentAmount();
        BizINonStateAuxmatAggrContractHead transferNotice=bizINonStateAuxmatAggrContractHeadMapper.calculateContractAmounts(bizINonStateAuxmatAggrContractHead.getId(),exchangeRate,billingWeight,paymentAmount);
        bizINonStateAuxmatAggrContractHeadMapper.saveTransferNotice(transferNotice);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        transferNotice.setInsertTimeFrom("日期 "+sdf.format(new Date()));
        // 创建日期格式化对象
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

        // 获取当前日期并加10天
        LocalDate futureDate = LocalDate.now().plusDays(10);

        transferNotice.setDomesticPrincipal("致： "+getMerchantNameSafely(bizMerchantMap,bizINonStateAuxmatAggrContractHead.getDomesticPrincipal()));

        transferNotice.setExtend1("   中国烟草上海进出口有限责任公司代理"+getMerchantNameSafely(bizMerchantMap,bizINonStateAuxmatAggrContractHead.getDomesticPrincipal())+
                "向"+getMerchantNameSafely(bizMerchantMap,bizINonStateAuxmatAggrContractHead.getSupplier())+"进口"+
                (transferNotice.getGName()==null?" ":transferNotice.getGName()) +transferNotice.getContractQuantity()+"吨，代理进口委托协议号"+
                (bizINonStateAuxmatAggrContractHead.getAgreementNo()==null?" ":bizINonStateAuxmatAggrContractHead.getAgreementNo())+"，为确保及时对外开立信用证，请贵司将该协议项下的有关货款、税款、货运代理费等全额于"+
                futureDate.format(dtf)+"前汇至我司帐户");

        transferNotice.setGName("事由：预付进口"+(transferNotice.getGName()==null?" ":transferNotice.getGName())+"货款等事宜");

        if (paymentAmount!=null){
            paymentAmount=paymentAmount.divide(new BigDecimal("10000"));
        }

//        transferNotice.setContractNo(transferNotice.getContractNo()+(paymentAmount==null?" ":paymentAmount)+" 万元  淮阴卷烟厂");
        transferNotice.setPaymentAmountStr("汇款金额："+(paymentAmount==null?" ":paymentAmount)+"万元人民币");

        String templateName = "划款通知_非国营进口辅料_合同与协议.xlsx";
        String finalOutName = xdoi18n.XdoI18nUtil.t("划款通知")+bizINonStateAuxmatAggrContractHead.getFileType();
        String tempFileName = UUID.randomUUID() + ".xlsx";

        String exportFileName = exportService.export(
                Arrays.asList(transferNotice),
                tempFileName,
                templateName
        );
       /* HttpHeaders h = new HttpHeaders();
        finalOutName = URLEncoder.encode(finalOutName, CommonVariable.UTF8);
        finalOutName = finalOutName.replaceAll("\\+", "%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(finalOutName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
       */
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        // 根据文件类型选择导出方式
        if (".pdf".equalsIgnoreCase(bizINonStateAuxmatAggrContractHead.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, finalOutName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(finalOutName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    /**
     * 创建商户映射表，处理重复key的情况
     * @param bizMerchants 商户列表
     * @return 商户编码到中文名称的映射
     */
    public Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }


    /**
     * 安全地获取商户中文名称
     * @param bizMerchantMap 商户映射表
     * @param merchantCode 商户编码
     * @return 商户中文名称，如果不存在则返回空字符串
     */
    public String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }
}
