package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizENonStateAuxmatAggrContractList
* <AUTHOR>
* @date: 2025-8-6
*/
public interface BizENonStateAuxmatAggrContractListMapper extends Mapper<BizENonStateAuxmatAggrContractList> {
    /**
     * 查询获取数据
     * @param bizENonStateAuxmatAggrContractList
     * @return
     */
    List<BizENonStateAuxmatAggrContractList> getList(BizENonStateAuxmatAggrContractList bizENonStateAuxmatAggrContractList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<BizENonStateAuxmatAggrContractList> getBizIAuxmatForContractListByHeadid(String headId);


    /**
     * 根据合同头ID获取合同表体信息
     * @param id 合同头ID
     * @param tradeCode 公司
     * @return 合同表体信息
     */
    List<BizENonStateAuxmatAggrContractList> getListByHeadId(@Param("id") String id, @Param("tradeCode") String tradeCode);


    /**
     * 根据合同编号获取合同表体信息
     * @param contractNo 合同编号
     * @param tradeCode 公司代码
     * @return 合同表体信息
     */
    List<BizENonStateAuxmatAggrContractList> getListByContractNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);
}
