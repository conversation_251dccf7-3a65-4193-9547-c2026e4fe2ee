<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractHeadMapper">
    <resultMap id="bizENonStateAuxmatAggrContractHeadResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="buyer" property="buyer" jdbcType="VARCHAR" />
		<result column="supplier" property="supplier" jdbcType="VARCHAR" />
		<result column="signing_date" property="signingDate" jdbcType="TIMESTAMP" />
		<result column="shipment_deadline" property="shipmentDeadline" jdbcType="VARCHAR" />
		<result column="effective_date" property="effectiveDate" jdbcType="TIMESTAMP" />
		<result column="expiry_date" property="expiryDate" jdbcType="TIMESTAMP" />
		<result column="signing_location_cn" property="signingLocationCn" jdbcType="VARCHAR" />
		<result column="signing_location_en" property="signingLocationEn" jdbcType="VARCHAR" />
		<result column="port_of_loading" property="portOfLoading" jdbcType="VARCHAR" />
		<result column="port_of_destination" property="portOfDestination" jdbcType="VARCHAR" />
		<result column="payment_method" property="paymentMethod" jdbcType="VARCHAR" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="price_term" property="priceTerm" jdbcType="VARCHAR" />
		<result column="price_term_port" property="priceTermPort" jdbcType="VARCHAR" />
		<result column="suggested_signer" property="suggestedSigner" jdbcType="VARCHAR" />
		<result column="shortage_overflow_percent" property="shortageOverflowPercent" jdbcType="NUMERIC" />
		<result column="incoterms" property="incoterms" jdbcType="VARCHAR" />
		<result column="mark" property="mark" jdbcType="VARCHAR" />
		<result column="contract_terms" property="contractTerms" jdbcType="VARCHAR" />
		<result column="remarks" property="remarks" jdbcType="VARCHAR" />
		<result column="agreement_no" property="agreementNo" jdbcType="VARCHAR" />
		<result column="agreement_signing_date" property="agreementSigningDate" jdbcType="TIMESTAMP" />
		<result column="agreement_total_amount" property="agreementTotalAmount" jdbcType="NUMERIC" />
		<result column="agreement_agent_rate" property="agreementAgentRate" jdbcType="NUMERIC" />
		<result column="agreement_agent_fee" property="agreementAgentFee" jdbcType="NUMERIC" />
		<result column="agreement_suggested_signer" property="agreementSuggestedSigner" jdbcType="VARCHAR" />
		<result column="agreement_terms" property="agreementTerms" jdbcType="VARCHAR" />
		<result column="agreement_remarks" property="agreementRemarks" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="appr_status" property="apprStatus" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_by_user_name" property="createByUserName" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_by_user_name" property="updateByUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,trade_code
     ,business_type
     ,contract_no
     ,buyer
     ,supplier
     ,signing_date
     ,shipment_deadline
     ,effective_date
     ,expiry_date
     ,signing_location_cn
     ,signing_location_en
     ,port_of_loading
     ,port_of_destination
     ,payment_method
     ,curr
     ,price_term
     ,price_term_port
     ,suggested_signer
     ,shortage_overflow_percent
     ,incoterms
     ,mark
     ,contract_terms
     ,remarks
     ,agreement_no
     ,agreement_signing_date
     ,agreement_total_amount
     ,agreement_agent_rate
     ,agreement_agent_fee
     ,agreement_suggested_signer
     ,agreement_terms
     ,agreement_remarks
     ,version_no
     ,status
     ,confirm_time
     ,appr_status
     ,sys_org_code
     ,create_by
     ,create_by_user_name
     ,create_time
     ,update_by
     ,update_by_user_name
     ,update_time
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
    </sql>
    <sql id="condition">
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    <if test="businessType != null and businessType != ''">
		and business_type = #{businessType}
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
    <if test="buyer != null and buyer != ''">
	  and buyer like '%'|| #{buyer} || '%'
	</if>
    <if test="supplier != null and supplier != ''">
	  and supplier like '%'|| #{supplier} || '%'
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizENonStateAuxmatAggrContractHeadResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_e_non_state_auxmat_aggr_contract_head t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_e_non_state_auxmat_aggr_contract_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getListPagedToCustomerAccount"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead">
        SELECT t.id,t.contract_no,t.supplier as domesticPrincipal ,t.curr as currency,t.CREATE_TIME,
        sum(tl.amount) AS decTotalToList, sum(tl.qty) AS qtyToList, max(tl.unit) AS unitToList, max(tl.g_name) AS merchandiseCategoriesToList
        FROM
        T_BIZ_E_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD t
        LEFT JOIN T_BIZ_E_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST tl ON t.id = tl.head_id
        WHERE NOT EXISTS (SELECT 1 FROM T_BIZ_CUSTOMER_ACCOUNT tc WHERE tc.CONTRACT_NO like '%' || t.contract_no || '%' AND tc.STATUS != '2' )
        AND t.STATUS = '1'
        <if test="contractNo != null and contractNo != ''">
            and CONTRACT_NO like '%'|| #{contractNo} || '%'
        </if>
        <if test="businessType != null and businessType != ''">
            and BUSINESS_TYPE = #{businessType}
        </if>
        GROUP BY t.id,t.contract_no,t.supplier,t.curr,t.CREATE_TIME  ORDER BY t.create_time DESC
    </select>
    <select id="getExtractAuxmatContractList" resultType="com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead">
        select
            distinct h.id,
                     h.contract_no as contractNo,
                     h.signing_date as signingDate,
                     h.buyer || ' ' || (select merchant_name_cn from t_biz_merchant q where h.buyer = q.merchant_code and h.trade_code = q.trade_code limit 1  )as buyer,
                     h.supplier || '  ' || (select merchant_name_cn from t_biz_merchant q where h.supplier = q.merchant_code and h.trade_code = q.trade_code limit 1  ) as supplier,
                     (
                         select
                             coalesce(sum(qty), 0)
                         from
                            t_biz_e_non_state_auxmat_aggr_contract_list l
                         where
                             l.head_id = h.id
                     ) as totalQty
        from
            t_biz_e_non_state_auxmat_aggr_contract_head h
        where
            h.status = '1'
            <if test="contractNo != null and contractNo != ''">
                and h.contract_no like '%' || #{contractNo} || '%'
            </if>
        order by h.contract_no
    </select>


    <select id="getExportExtractCountByContractNo" resultType="java.math.BigDecimal">
        select sum(il.qty)
        from t_biz_export_goods_head ih
                 inner join t_biz_export_goods_list il on ih.id = il.parent_id
        where ih.contract_no = #{contractNo}
          and ih.trade_code = #{tradeCode}
          and ih.data_state != '1'
    </select>


</mapper>
