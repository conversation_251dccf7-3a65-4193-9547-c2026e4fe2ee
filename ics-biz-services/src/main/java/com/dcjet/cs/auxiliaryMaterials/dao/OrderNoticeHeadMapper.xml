<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeHeadMapper">
    <resultMap id="orderNoticeHeadResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
        <result column="PREV_VERSION_ID" property="prevVersionId" jdbcType="VARCHAR"/>
        <result column="BUY_HEAD_IDS" property="buyHeadIds" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_DATE" property="orderDate" jdbcType="TIMESTAMP"/>
        <result column="CUSTOMER" property="customer" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR"/>
        <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR"/>
        <result column="DATA_STATUS" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="PURCHASE_SALES_CONTRACT_NO" property="purchaseSalesContractNo" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY_NAME" property="createByName" jdbcType="VARCHAR"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY_NAME" property="updateByName" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_MAKER" property="documentMaker" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_MAKER_NAME" property="documentMakerName" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_MAKE_TIME" property="documentMakeTime" jdbcType="TIMESTAMP"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="selectBuyContractResultMap"
               type="com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeSelectBuyContractDto">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="PS_CONTRACT_NO" property="psContractNo" jdbcType="VARCHAR"/>
        <result column="PS_YEAR" property="psYear" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        ID,
        PARENT_ID,
        PREV_VERSION_ID,
        BUY_HEAD_IDS,
        BUSINESS_TYPE,
        ORDER_NO,
        ORDER_DATE,
        CUSTOMER,
        CUSTOMER_NAME,
        VERSION_NO,
        DATA_STATUS,
        PURCHASE_SALES_CONTRACT_NO,
        NOTE,
        CONFIRM_TIME,
        APPR_STATUS,
        TRADE_CODE,
        SYS_ORG_CODE,
        CREATE_BY,
        CREATE_TIME,
        CREATE_BY_NAME,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_BY_NAME,
        DOCUMENT_MAKER,
        DOCUMENT_MAKER_NAME,
        DOCUMENT_MAKE_TIME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>

    <sql id="condition">
        <if test="true">
            TRADE_CODE = #{tradeCode}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and ORDER_NO like concat('%', #{orderNo}, '%')
        </if>
        <if test="customer != null and customer != ''">
            and CUSTOMER = #{customer}
        </if>
        <choose>
            <when test="dataStatus != null and dataStatus != ''">
                and DATA_STATUS = #{dataStatus}
            </when>
            <otherwise>
                and DATA_STATUS &lt;> '2'
            </otherwise>
        </choose>
        <if test="documentMakeDateFrom != null and documentMakeDateFrom != ''">
            and DOCUMENT_MAKE_TIME >= TO_DATE(#{documentMakeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="documentMakeDateTo != null and documentMakeDateTo != ''">
            and DOCUMENT_MAKE_TIME &lt; DATEADD(DAY, 1, TO_DATE(#{documentMakeDateTo}, 'yyyy-MM-dd hh24:mi:ss'))
        </if>
    </sql>

    <select id="getList" resultMap="orderNoticeHeadResultMap"
            parameterType="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        <where>
            <include refid="condition"/>
        </where>
        order by DOCUMENT_MAKE_TIME desc
    </select>

    <select id="getAllCustomers" resultType="java.lang.String">
        select distinct CUSTOMER
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        <where>
            <if test="true">
                and TRADE_CODE = #{tradeCode}
            </if>
        </where>
    </select>

    <select id="getByIds" resultMap="orderNoticeHeadResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by DOCUMENT_MAKE_TIME desc
    </select>

    <delete id="deleteByIds" parameterType="java.util.List">
        delete from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getSelectBuyContractList" resultMap="selectBuyContractResultMap">
        select
            ID,
            CONTRACT_NO as   PS_CONTRACT_NO,
            CONTRACT_YEAR as PS_YEAR
        from
            T_BIZ_I_AUXMAT_BUY_CONTRACT
        where
            TRADE_CODE = #{tradeCode}
            and STATUS = '1'
            and BUSINESS_TYPE = '2'
            and ID in (select DISTINCT
                           BUY_LIST.HEAD_ID
                       from
                           T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST BUY_LIST
                           left join (select
                                          BUY_LIST_ID,
                                          SUM(QTY) as TOTAL_QTY
                                      from
                                          T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
                                      where
                                          TRADE_CODE = #{tradeCode}
                                          and DATA_STATUS &lt;> '2'
                                      group by
                                          BUY_LIST_ID) NOTICE_LIST on BUY_LIST.ID = NOTICE_LIST.BUY_LIST_ID
                       where
                           BUY_LIST.TRADE_CODE = #{tradeCode}
                           and BUY_LIST.QTY - COALESCE(NOTICE_LIST.TOTAL_QTY, 0) > 0)
        order by COALESCE(UPDATE_TIME, CREATE_TIME) desc
    </select>

    <select id="getCountByOrderNo" resultType="java.lang.Integer">
        select count(1)
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        where TRADE_CODE = #{tradeCode}
          and ORDER_NO = #{orderNo}
    </select>

    <select id="getByOrderNo" resultMap="orderNoticeHeadResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        where TRADE_CODE = #{tradeCode} and ORDER_NO = #{orderNo}
        order by DOCUMENT_MAKE_TIME desc
    </select>

    <update id="confirmById">
        update T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        set DATA_STATUS    = '1',
            CONFIRM_TIME   = current_timestamp,
            UPDATE_TIME    = current_timestamp,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName}
        where ID = #{id}
    </update>

    <update id="invalidateById">
        update T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_TIME    = current_timestamp,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName}
        where ID = #{id}
    </update>

    <select id="getMaxVersionDataByOrderNo" resultMap="orderNoticeHeadResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        where ORDER_NO = #{orderNo}
        and TRADE_CODE = #{tradeCode}
        order by CAST(VERSION_NO AS INTEGER) desc limit 1
    </select>

    <update id="invalidateByOrderNo">
        update T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_TIME    = current_timestamp,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName}
        where ORDER_NO = #{orderNo}
          and TRADE_CODE = #{tradeCode}
          and DATA_STATUS &lt;> '2'
    </update>
</mapper>