package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;
/**
* generated by Generate 神码
* BizENonStateAuxmatAggrContractHead
* <AUTHOR>
* @date: 2025-8-6
*/
public interface BizENonStateAuxmatAggrContractHeadMapper extends Mapper<BizENonStateAuxmatAggrContractHead> {
    /**
     * 查询获取数据
     * @param bizENonStateAuxmatAggrContractHead
     * @return
     */
    List<BizENonStateAuxmatAggrContractHead> getList(BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<BizENonStateAuxmatAggrContractHead> getListPagedToCustomerAccount(BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead);


    /**
     * 根据合同号查询外商合同头信息
     * @param contractNo 合同号
     * @param tradeCode 公司编码
     * @return List<BizENonStateAuxmatAggrContractHead> 外商合同头信息
     */
    List<BizENonStateAuxmatAggrContractHead> getExtractAuxmatContractList(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

    /**
     * 根据合同号 获取出口信息 已经提取的数量
     * @param contractNo 合同号
     * @param tradeCode 公司编码
     * @return BigDecimal 数量总和
     */
    BigDecimal getExportExtractCountByContractNo(@Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);


}
