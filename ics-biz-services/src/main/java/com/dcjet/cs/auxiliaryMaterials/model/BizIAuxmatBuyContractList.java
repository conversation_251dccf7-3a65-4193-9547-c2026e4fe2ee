package com.dcjet.cs.auxiliaryMaterials.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-29
 */
@Setter
@Getter
@Table(name = "t_biz_i_auxmat_buy_contract_list")
public class BizIAuxmatBuyContractList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 关联SID
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 商品名称
     */
	@Column(name = "g_name")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 产品型号
     */
	@Column(name = "g_model")
	@JsonProperty("gModel")
	private  String gModel;
	/**
     * 规格
     */
	@Column(name = "specifications")
	private  String specifications;
	/**
     * 克重
     */
	@Column(name = "gram_weight")
	private  BigDecimal gramWeight;
	/**
     * 供应商
     */
	@Column(name = "supplier")
	private  String supplier;
	/**
     * 数量
     */
	@Column(name = "qty")
	private  BigDecimal qty;
	/**
     * 单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 单价
     */
	@Column(name = "unit_price")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Column(name = "amount")
	private  BigDecimal amount;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
	 * 创建人
	 */
	@Column(name = "create_by_name")
	private  String createByName;
	/**
	 * 创建人
	 */
	@Column(name = "update_by_name")
	private  String updateByName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "EXTEND10")
	private  String extend10;
}
