package com.dcjet.cs.auxiliaryMaterials.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizENonStateAuxmatAggrContractHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@Service
public class BizENonStateAuxmatAggrContractHeadService extends BaseService<BizENonStateAuxmatAggrContractHead> {
    @Resource
    private BizENonStateAuxmatAggrContractHeadMapper bizENonStateAuxmatAggrContractHeadMapper;
    @Resource
    private BizENonStateAuxmatAggrContractHeadDtoMapper bizENonStateAuxmatAggrContractHeadDtoMapper;
    @Override
    public Mapper<BizENonStateAuxmatAggrContractHead> getMapper() {
        return bizENonStateAuxmatAggrContractHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizENonStateAuxmatAggrContractHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> getListPaged(BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead = bizENonStateAuxmatAggrContractHeadDtoMapper.toPo(bizENonStateAuxmatAggrContractHeadParam);
        bizENonStateAuxmatAggrContractHead.setTradeCode(userInfo.getCompany());
        Page<BizENonStateAuxmatAggrContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizENonStateAuxmatAggrContractHeadMapper.getList(bizENonStateAuxmatAggrContractHead));
        List<BizENonStateAuxmatAggrContractHeadDto> bizENonStateAuxmatAggrContractHeadDtos = page.getResult().stream().map(head -> {
            BizENonStateAuxmatAggrContractHeadDto dto = bizENonStateAuxmatAggrContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> paged = ResultObject.createInstance(bizENonStateAuxmatAggrContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizENonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizENonStateAuxmatAggrContractHeadDto insert(BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead = bizENonStateAuxmatAggrContractHeadDtoMapper.toPo(bizENonStateAuxmatAggrContractHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizENonStateAuxmatAggrContractHead.setId(sid);
        bizENonStateAuxmatAggrContractHead.setCreateBy(userInfo.getUserNo());
        bizENonStateAuxmatAggrContractHead.setCreateByUserName(userInfo.getUserName());
        bizENonStateAuxmatAggrContractHead.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizENonStateAuxmatAggrContractHeadMapper.insert(bizENonStateAuxmatAggrContractHead);
        return  insertStatus > 0 ? bizENonStateAuxmatAggrContractHeadDtoMapper.toDto(bizENonStateAuxmatAggrContractHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizENonStateAuxmatAggrContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizENonStateAuxmatAggrContractHeadDto update(BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, UserInfoToken userInfo) {
        BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead = bizENonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizENonStateAuxmatAggrContractHeadParam.getSid());
        bizENonStateAuxmatAggrContractHeadDtoMapper.updatePo(bizENonStateAuxmatAggrContractHeadParam, bizENonStateAuxmatAggrContractHead);
        bizENonStateAuxmatAggrContractHead.setUpdateBy(userInfo.getUserNo());
        bizENonStateAuxmatAggrContractHead.setUpdateByUserName(userInfo.getUserName());
        bizENonStateAuxmatAggrContractHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizENonStateAuxmatAggrContractHeadMapper.updateByPrimaryKey(bizENonStateAuxmatAggrContractHead);
        return update > 0 ? bizENonStateAuxmatAggrContractHeadDtoMapper.toDto(bizENonStateAuxmatAggrContractHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizENonStateAuxmatAggrContractHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizENonStateAuxmatAggrContractHeadDto> selectAll(BizENonStateAuxmatAggrContractHeadParam exportParam, UserInfoToken userInfo) {
        BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead = bizENonStateAuxmatAggrContractHeadDtoMapper.toPo(exportParam);
         bizENonStateAuxmatAggrContractHead.setTradeCode(userInfo.getCompany());
        List<BizENonStateAuxmatAggrContractHeadDto> bizENonStateAuxmatAggrContractHeadDtos = new ArrayList<>();
        List<BizENonStateAuxmatAggrContractHead> bizENonStateAuxmatAggrContractHeads = bizENonStateAuxmatAggrContractHeadMapper.getList(bizENonStateAuxmatAggrContractHead);
        if (CollectionUtils.isNotEmpty(bizENonStateAuxmatAggrContractHeads)) {
            bizENonStateAuxmatAggrContractHeadDtos = bizENonStateAuxmatAggrContractHeads.stream().map(head -> {
                BizENonStateAuxmatAggrContractHeadDto dto = bizENonStateAuxmatAggrContractHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizENonStateAuxmatAggrContractHeadDtos;
    }

    public ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> getListPagedToCustomerAccount(BizENonStateAuxmatAggrContractHeadParam bizENonStateAuxmatAggrContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead = bizENonStateAuxmatAggrContractHeadDtoMapper.toPo(bizENonStateAuxmatAggrContractHeadParam);
//        bizENonStateAuxmatAggrContractHead.setTradeCode(userInfo.getCompany());
        Page<BizENonStateAuxmatAggrContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizENonStateAuxmatAggrContractHeadMapper.getListPagedToCustomerAccount(bizENonStateAuxmatAggrContractHead));
        List<BizENonStateAuxmatAggrContractHeadDto> bizENonStateAuxmatAggrContractHeadDtos = page.getResult().stream().map(head -> {
            BizENonStateAuxmatAggrContractHeadDto dto = bizENonStateAuxmatAggrContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizENonStateAuxmatAggrContractHeadDto>> paged = ResultObject.createInstance(bizENonStateAuxmatAggrContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
}
