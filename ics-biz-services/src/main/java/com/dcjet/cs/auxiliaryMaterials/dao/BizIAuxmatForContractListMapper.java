package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
/**
* generated by Generate dcits
* BizIAuxmatForContractList
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizIAuxmatForContractListMapper extends Mapper<BizIAuxmatForContractList> {
    /**
     * 根据参数查询
     *
     * @param bizIAuxmatForContractList
     * @return
     */
    List<BizIAuxmatForContractList> getList(BizIAuxmatForContractList bizIAuxmatForContractList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据表头headId批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);
    /**
     * 根据表头headId查询是否存在表体数据
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);

    BizIAuxmatForContractListDto getContractTotal(BizIAuxmatForContractList bizIAuxmatForContractList);

    List<BizIAuxmatForContractList> getPlanListPaged(BizIAuxmatForContractList bizIAuxmatForContractList);

    void insertByOrderNoList(@Param("orderNoList") List<String> orderNoList, @Param("sid") String sid, @Param("userNo") String userNo, @Param("userName") String userName);

    List<BizIAuxmatForContractList> getBizIAuxmatForContractListByHeadid(@Param("headId") String headId);

    /**
     * 根据表头ID获取表体数据
     * @param headId
     * @return
     */
    List<BizIAuxmatForContractList> getContractListByHeadId(@Param("headId") String headId);

    void updateCorrelationID(@Param("newListId") String newListId, @Param("oldSid") String oldSid, @Param("headId") String headId, @Param("oldHeadId") String oldHeadId);

    int checkNextModuleExistEffectiveData(@Param("id") String id);

    void insertByHeadId(@Param("sids") List<String> sids,@Param("headId") String headId, @Param("userNo") String userNo, @Param("userName") String userName);

    String getPurchaseSalesContractNo(@Param("orderNoList") List<String> orderNoList);

    void updateCopyAssociationId(@Param("associationIds") List<BizIAuxmatForContractList> associationIds);
}
