<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractHeadMapper">
    <resultMap id="bizINonStateAuxmatAggrContractHeadResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead">
		<id column="ID" property="id" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR" />
		<result column="BUYER" property="buyer" jdbcType="VARCHAR" />
		<result column="SUPPLIER" property="supplier" jdbcType="VARCHAR" />
		<result column="SIGNING_DATE" property="signingDate" jdbcType="TIMESTAMP" />
		<result column="DOMESTIC_PRINCIPAL" property="domesticPrincipal" jdbcType="VARCHAR" />
		<result column="TRANSPORT_MODE" property="transportMode" jdbcType="VARCHAR" />
		<result column="EFFECTIVE_DATE" property="effectiveDate" jdbcType="TIMESTAMP" />
		<result column="EXPIRY_DATE" property="expiryDate" jdbcType="TIMESTAMP" />
		<result column="SIGNING_LOCATION_CN" property="signingLocationCn" jdbcType="VARCHAR" />
		<result column="SIGNING_LOCATION_EN" property="signingLocationEn" jdbcType="VARCHAR" />
		<result column="PORT_OF_LOADING" property="portOfLoading" jdbcType="VARCHAR" />
		<result column="PORT_OF_DESTINATION" property="portOfDestination" jdbcType="VARCHAR" />
		<result column="CUSTOMS_PORT" property="customsPort" jdbcType="VARCHAR" />
		<result column="PAYMENT_METHOD" property="paymentMethod" jdbcType="VARCHAR" />
		<result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
		<result column="PRICE_TERM" property="priceTerm" jdbcType="VARCHAR" />
		<result column="PRICE_TERM_PORT" property="priceTermPort" jdbcType="VARCHAR" />
		<result column="SUGGESTED_SIGNER" property="suggestedSigner" jdbcType="VARCHAR" />
		<result column="SHORTAGE_OVERFLOW_PERCENT" property="shortageOverflowPercent" jdbcType="NUMERIC" />
		<result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
		<result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="PARENT_ID" property="parentId" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="NUMERIC" />
		<result column="CONTRACT_AMOUNT" property="contractAmount" jdbcType="NUMERIC" />
		<result column="EXCHANGE_RATE" property="exchangeRate" jdbcType="NUMERIC" />
		<result column="TARIFF_RATE" property="tariffRate" jdbcType="NUMERIC" />
		<result column="TARIFF_AMOUNT" property="tariffAmount" jdbcType="NUMERIC" />
		<result column="CONSUMPTION_TAX_RATE" property="consumptionTaxRate" jdbcType="NUMERIC" />
		<result column="CONSUMPTION_TAX_AMOUNT" property="consumptionTaxAmount" jdbcType="NUMERIC" />
		<result column="VAT_RATE" property="vatRate" jdbcType="NUMERIC" />
		<result column="VAT_AMOUNT" property="vatAmount" jdbcType="NUMERIC" />
		<result column="IMPORT_EXPORT_AGENCY_RATE" property="importExportAgencyRate" jdbcType="NUMERIC" />
		<result column="IMPORT_EXPORT_AGENCY_FEE" property="importExportAgencyFee" jdbcType="NUMERIC" />
		<result column="HEADQUARTERS_AGENCY_RATE" property="headquartersAgencyRate" jdbcType="NUMERIC" />
		<result column="HEADQUARTERS_AGENCY_FEE" property="headquartersAgencyFee" jdbcType="NUMERIC" />
		<result column="CONTRACT_QUANTITY" property="contractQuantity" jdbcType="NUMERIC" />
		<result column="BILLING_WEIGHT" property="billingWeight" jdbcType="NUMERIC" />
		<result column="CUSTOMS_CLEARANCE_FEE" property="customsClearanceFee" jdbcType="NUMERIC" />
		<result column="CONTAINER_INSPECTION_FEE" property="containerInspectionFee" jdbcType="NUMERIC" />
		<result column="FREIGHT_FORWARDER_FEE" property="freightForwarderFee" jdbcType="NUMERIC" />
		<result column="INSURANCE_RATE" property="insuranceRate" jdbcType="NUMERIC" />
		<result column="INSURANCE_FEE" property="insuranceFee" jdbcType="NUMERIC" />
		<result column="AGREEMENT_NO" property="agreementNo" jdbcType="VARCHAR" />
		<result column="AGREEMENT_SIGNING_DATE" property="agreementSigningDate" jdbcType="TIMESTAMP" />
		<result column="AGREEMENT_AGENT_FEE_RATE" property="agreementAgentFeeRate" jdbcType="NUMERIC" />
		<result column="AGREEMENT_SUGGESTED_SIGNER" property="agreementSuggestedSigner" jdbcType="VARCHAR" />
		<result column="AGREEMENT_TERMS" property="agreementTerms" jdbcType="VARCHAR" />
		<result column="AGREEMENT_REMARKS" property="agreementRemarks" jdbcType="VARCHAR" />
		<result column="createtUserName" property="createtUserName" jdbcType="VARCHAR" />
		<result column="IS_TRANSFER_NOTICE" property="isTransferNotice" jdbcType="VARCHAR" />
		<result column="insertTime" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="totalAmount" property="totalAmount" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     ID
     ,TRADE_CODE
     ,BUSINESS_TYPE
     ,CONTRACT_NO
     ,BUYER
     ,SUPPLIER
     ,SIGNING_DATE
     ,DOMESTIC_PRINCIPAL
     ,TRANSPORT_MODE
     ,EFFECTIVE_DATE
     ,EXPIRY_DATE
     ,SIGNING_LOCATION_CN
     ,SIGNING_LOCATION_EN
     ,PORT_OF_LOADING
     ,PORT_OF_DESTINATION
     ,CUSTOMS_PORT
     ,PAYMENT_METHOD
     ,CURRENCY
     ,PRICE_TERM
     ,PRICE_TERM_PORT
     ,SUGGESTED_SIGNER
     ,SHORTAGE_OVERFLOW_PERCENT
     ,REMARKS
     ,VERSION_NO
     ,STATUS
     ,CONFIRM_TIME
     ,APPR_STATUS
     ,SYS_ORG_CODE
     ,PARENT_ID
     ,CREATE_BY
     ,CREATE_TIME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
     ,PAYMENT_AMOUNT
     ,CONTRACT_AMOUNT
     ,EXCHANGE_RATE
     ,TARIFF_RATE
     ,TARIFF_AMOUNT
     ,CONSUMPTION_TAX_RATE
     ,CONSUMPTION_TAX_AMOUNT
     ,VAT_RATE
     ,VAT_AMOUNT
     ,IMPORT_EXPORT_AGENCY_RATE
     ,IMPORT_EXPORT_AGENCY_FEE
     ,HEADQUARTERS_AGENCY_RATE
     ,HEADQUARTERS_AGENCY_FEE
     ,CONTRACT_QUANTITY
     ,BILLING_WEIGHT
     ,CUSTOMS_CLEARANCE_FEE
     ,CONTAINER_INSPECTION_FEE
     ,FREIGHT_FORWARDER_FEE
     ,INSURANCE_RATE
     ,INSURANCE_FEE
     ,AGREEMENT_NO
     ,AGREEMENT_SIGNING_DATE
     ,AGREEMENT_AGENT_FEE_RATE
     ,AGREEMENT_SUGGESTED_SIGNER
     ,AGREEMENT_TERMS
     ,AGREEMENT_REMARKS
     ,IS_TRANSFER_NOTICE
     ,COALESCE(t.UPDATE_USER_NAME,t.INSERT_USER_NAME) as createtUserName
     ,COALESCE(t.UPDATE_TIME,t.create_time) as insertTime
    </sql>
    <sql id="condition">
    <if test="contractNo != null and contractNo != ''">
	  and CONTRACT_NO like '%'|| #{contractNo} || '%'
	</if>
    <if test="supplier != null and supplier != ''">
		and SUPPLIER = #{supplier}
	</if>
    <if test="domesticPrincipal != null and domesticPrincipal != ''">
		and DOMESTIC_PRINCIPAL = #{domesticPrincipal}
	</if>
    <if test="status == null or status == ''">
            and STATUS !='2'
    </if>
    <if test="status != null and status != ''">
            and status = #{status}
    </if>
    <if test="tradeCode != null and tradeCode != ''">
            and TRADE_CODE = #{tradeCode}
    </if>

    <if test=" createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= to_date(#{createTimeFrom}, 'yyyy-MM-dd hh24:mi:ss') ]]>
    </if>
    <if test=" createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time)  <= DATEADD(DAY, 1, to_date(#{createTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
    </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizINonStateAuxmatAggrContractHeadResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead">
        SELECT
        <include refid="Base_Column_List" />
        ,(select sum(amount) from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST aggrList where aggrList.HEAD_ID = t.ID) as totalAmount
        ,case  when (select count(*) from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD head
        left join T_BIZ_NON_INCOMING_GOODS_HEAD ctr on ctr.CONTRACT_NO=head.contract_no
        where head.id=t.id and ctr.DOCUMENT_STATUS!='2') > 0 Then '1' ELSE '0' END as hasHeadNotice,
        (SELECT CASE WHEN head.version_no != 1 THEN 1 ELSE 0 END
        FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD head
        WHERE head.id = t.id ) as isCopy
        FROM
        T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.UPDATE_TIME,t.create_time) desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD t where t.ID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="selectByContractNo" resultMap="bizINonStateAuxmatAggrContractHeadResultMap">
        select * from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD
        where CONTRACT_NO = #{contractNo} and trade_code = #{tradeCode}
        limit 1
    </select>
    <select id="getListPagedToCustomerAccount"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead">
        SELECT t.id,t.contract_no,t.DOMESTIC_PRINCIPAL ,t.currency,t.CREATE_TIME,
               sum(tl.amount) AS decTotalToList, sum(tl.qty) AS qtyToList, max(tl.unit) AS unitToList, max(tl.goods_category) AS merchandiseCategoriesToList
        FROM
            T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD t
                LEFT JOIN T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST tl ON t.id = tl.head_id
        WHERE NOT EXISTS (SELECT 1 FROM T_BIZ_CUSTOMER_ACCOUNT tc WHERE tc.CONTRACT_NO like '%' || t.contract_no || '%' AND tc.STATUS != '2' )
        AND t.STATUS = '1'
        <if test="contractNo != null and contractNo != ''">
            and CONTRACT_NO like '%'|| #{contractNo} || '%'
        </if>
        <if test="businessType != null and businessType != ''">
            and BUSINESS_TYPE = #{businessType}
        </if>
        GROUP BY t.id,t.contract_no,t.DOMESTIC_PRINCIPAL,t.currency,t.CREATE_TIME  ORDER BY t.create_time DESC
    </select>
    <select id="checkContractId" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD t
        where t.contract_no = #{contractNo} and TRADE_CODE = #{tradeCode}
        and status != '2'
        <if test="id != null and id != ''">
            AND t.ID != #{id}
        </if>
    </select>
    <select id="getMaxVersionNoByContractNo"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead">
        select id,VERSION_NO  from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD
        where contract_no = #{contractNo}
          and trade_code = #{tradeCode}
        order by CAST(VERSION_NO AS INTEGER) desc limit 1
    </select>

    <select id="getAttachmentFile" resultType="com.dcjet.cs.attach.model.Attached">
        SELECT
            SID,
            TRADE_CODE,
            HEAD_ID,
            BUSINESS_TYPE,
            ACMP_TYPE,
            ACMP_NO,
            FILE_NAME,
            NOTE,
            ORIGIN_FILE_NAME,
            FDFS_ID,
            INSERT_USER,
            INSERT_TIME,
            UPDATE_USER,
            UPDATE_TIME,
            INSERT_USER_NAME,
            UPDATE_USER_NAME,
            FILE_SIZE,
            DATA_SOURCE
        FROM
            T_ATTACHED
        WHERE
            HEAD_ID = #{headId};
    </select>
    <select id="checkContractIdNotCancel" resultType="java.lang.String">
        select
            id
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD t
        where t.contract_no in  (select  distinct contract_no
                                 from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD t
                                 where ID = #{id}
        ) and  t.STATUS !=2;
    </select>
    <select id="checkOrderUsed" resultType="java.lang.Integer">
        select count(*)
        from T_BIZ_NON_INCOMING_GOODS_HEAD
        where contract_No in (
        select distinct contract_no
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD
        where id in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>
    <select id="calculateContractAmounts"
            resultType="com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead">
        WITH contract_amounts AS (
            SELECT
                a.id,
                a.business_type,
                a.trade_code,
                COALESCE((SELECT SUM(amount) FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST WHERE head_id = a.id), 0) as CONTRACT_AMOUNT,
                COALESCE(<![CDATA[#{exchangeRate}]]>, (SELECT coalesce(latest_rate.latest_rate_value, 0) *
                                                              (1 +  case when floating.FLOAT_RATE is null then 0 else
                                                                  floating.FLOAT_RATE / 100 end) AS calculated_value
                                                       FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD detail
                                                                LEFT JOIN (SELECT rate_inner.CURR,
                                                                                  rate_inner.TRADE_CODE,
                                                                                  rate_inner.RATE AS latest_rate_value,
                                                                                  rate_inner.MONTH
                                                                           FROM (SELECT *,
                                                                                        ROW_NUMBER() OVER(PARTITION BY CURR, TRADE_CODE ORDER BY MONTH DESC ) AS rn
                                                                                 FROM T_BIZ_ENTERPRISE_RATE) rate_inner
                                                                           WHERE rate_inner.rn = 1) latest_rate
                                                                          ON latest_rate.CURR = detail.currency AND
                                                                             latest_rate.TRADE_CODE = detail.TRADE_CODE
                                                                LEFT JOIN T_BIZ_RATE_TABLE floating
                                                                          ON floating.curr = detail.currency
                                                                           AND detail.trade_code =floating.trade_code
                                                                           AND floating.BUSINESS_TYPE like '%' || detail.BUSINESS_TYPE || '%'
                                                       WHERE detail.id = a.id
                                  LIMIT 1)) as EXCHANGE_RATE,
                COALESCE(tr.TARIFF_RATE, 0) as TARIFF_RATE,
                COALESCE(tr.CONSUMPTION_TAX_RATE, 0) as CONSUMPTION_TAX_RATE,
                COALESCE(tr.VAT_RATE, 0) as VAT_RATE,
                COALESCE(tr.IE_AGENT_FEE_RATE, 0) as IMPORT_EXPORT_AGENCY_RATE,
                COALESCE(tr.HQ_AGENT_FEE_RATE, 0) as HEADQUARTERS_AGENCY_RATE,
                COALESCE(tr.INTL_FREIGHT_AMT, 0) as INTL_FREIGHT_AMT,
                COALESCE(tr.PORT_CHARGES_AMT, 0) as PORT_CHARGES_AMT,
                COALESCE(tr.LAND_FREIGHT_AMT, 0) as LAND_FREIGHT_AMT,
                COALESCE(tr.CUSTOMS_FEE_AMT, 0) as CUSTOMS_CLEARANCE_FEE,
                COALESCE(tr.CNTR_INSP_FEE_AMT, 0) as CONTAINER_INSPECTION_FEE,
                COALESCE(tr.INSURANCE_RATE, 0) as INSURANCE_RATE,
                COALESCE(tr.CONTAINER_CAP, '20') as CONTAINER_CAP,
                COALESCE((SELECT SUM(qty) FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST WHERE head_id = a.id), 0) as CONTRACT_QUANTITY,
                COALESCE((SELECT currency FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD WHERE id = a.id LIMIT 1), 'USD') as currency,
                (select GOODS_NAME from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST WHERE head_id = a.id LIMIT 1) as g_name,
                (select supplier from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST WHERE head_id = a.id LIMIT 1) as supplier
        FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD a
            left join T_BIZ_TRANSCODE tr on a.business_type = tr.biz_type AND tr.trade_code = a.trade_code
        WHERE a.id = #{headId}
            ),
            base_calculations AS (
        SELECT
            ca.*,
            COALESCE(ca.CONTRACT_AMOUNT * NULLIF(ca.EXCHANGE_RATE,0), 0) as base_amount,
            COALESCE(#{billingWeight},CEIL(COALESCE ((
            SELECT SUM (
            CASE
            WHEN bclist.unit = '035'
            THEN NULLIF (bclist.qty, 0) / 1000 / CASE WHEN ca.CONTAINER_CAP = '1' THEN 10 ELSE 20 END
            ELSE bclist.qty
            END
            )
            FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST bclist
            WHERE bclist.head_id = ca.id
            ), 0))) as BILLING_WEIGHT
        FROM contract_amounts ca
            ),
            tax_calculations AS (
        SELECT
            bc.*,

            ROUND(COALESCE(bc.base_amount * (NULLIF(bc.TARIFF_RATE,0)/100), 0), 2) as TARIFF_AMOUNT,

            ROUND(COALESCE((bc.base_amount + ROUND(COALESCE(bc.base_amount * (NULLIF(bc.TARIFF_RATE,0)/100), 0), 2)) * (NULLIF(bc.CONSUMPTION_TAX_RATE,0)/100), 0), 2) as CONSUMPTION_TAX_AMOUNT,

            ROUND(COALESCE((bc.base_amount +
            ROUND(COALESCE(bc.base_amount * (NULLIF(bc.TARIFF_RATE,0)/100), 0), 2) +
            ROUND(COALESCE((bc.base_amount + ROUND(COALESCE(bc.base_amount * (NULLIF(bc.TARIFF_RATE,0)/100), 0), 2)) * (NULLIF(bc.CONSUMPTION_TAX_RATE,0)/100), 0), 2)) * (NULLIF(bc.VAT_RATE,0)/100), 0), 2) as VAT_AMOUNT,

            ROUND(COALESCE(bc.base_amount * (NULLIF(bc.IMPORT_EXPORT_AGENCY_RATE,0)/100), 0), 2) as IMPORT_EXPORT_AGENCY_FEE,

            ROUND(COALESCE(bc.base_amount * (NULLIF(bc.HEADQUARTERS_AGENCY_RATE,0)/100), 0), 2) as HEADQUARTERS_AGENCY_FEE,

            ROUND(COALESCE((bc.INTL_FREIGHT_AMT + bc.PORT_CHARGES_AMT + bc.LAND_FREIGHT_AMT) * COALESCE(#{billingWeight},bc.BILLING_WEIGHT) +
            bc.CUSTOMS_CLEARANCE_FEE + bc.CONTAINER_INSPECTION_FEE, 0), 2) as FREIGHT_FORWARDER_FEE,

            ROUND(COALESCE(bc.CONTRACT_AMOUNT * 1.1 * bc.EXCHANGE_RATE * (NULLIF(bc.INSURANCE_RATE,0)/100), 0), 2) as INSURANCE_FEE
        FROM base_calculations bc
            )
        SELECT
            a.contract_no,

            tc.*,

            CASE
                WHEN COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                                                        tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) >= 10000000
                    THEN CEIL(COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                                                                 tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) / 1000000) * 1000000
                WHEN COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                                                        tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) >= 1000000
                    THEN CEIL(COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                                                                 tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) / 100000) * 100000
                ELSE CEIL(COALESCE(#{paymentAmount},COALESCE(tc.base_amount + tc.TARIFF_AMOUNT + tc.CONSUMPTION_TAX_AMOUNT + tc.VAT_AMOUNT +
                                                             tc.IMPORT_EXPORT_AGENCY_FEE + tc.HEADQUARTERS_AGENCY_FEE + tc.FREIGHT_FORWARDER_FEE + tc.INSURANCE_FEE, 0)) / 10000) * 10000
                END as PAYMENT_AMOUNT
        FROM T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD a
                 JOIN tax_calculations tc ON a.id = tc.id
        WHERE a.id = #{headId}

    </select>
    <select id="saveTransferNotice">
        update T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD
        set
            is_transfer_notice = '1',
            CONTRACT_AMOUNT = #{contractAmount},
            EXCHANGE_RATE = #{exchangeRate},
            TARIFF_RATE = #{tariffRate},
            TARIFF_AMOUNT = #{tariffAmount},
            CONSUMPTION_TAX_RATE = #{consumptionTaxRate},
            CONSUMPTION_TAX_AMOUNT = #{consumptionTaxAmount},
            VAT_RATE = #{vatRate},
            VAT_AMOUNT = #{vatAmount},
            IMPORT_EXPORT_AGENCY_RATE = #{importExportAgencyRate},
            IMPORT_EXPORT_AGENCY_FEE = #{importExportAgencyFee},
            HEADQUARTERS_AGENCY_RATE = #{headquartersAgencyRate},
            HEADQUARTERS_AGENCY_FEE = #{headquartersAgencyFee},
            CONTRACT_QUANTITY = #{contractQuantity},
            BILLING_WEIGHT = #{billingWeight},
            CUSTOMS_CLEARANCE_FEE = #{customsClearanceFee},
            CONTAINER_INSPECTION_FEE = #{containerInspectionFee},
            FREIGHT_FORWARDER_FEE = #{freightForwarderFee},
            INSURANCE_RATE = #{insuranceRate},
            INSURANCE_FEE = #{insuranceFee},
            PAYMENT_AMOUNT = #{paymentAmount}
        where id = #{id}
    </select>
    <select id="checkPlanStatus" resultType="java.lang.Integer">
        select count(*)
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD
        where status != #{status}
        and id in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="checkContractUsed" resultType="java.lang.Integer">
        select count(*)
        from T_BIZ_NON_INCOMING_GOODS_HEAD
        where plan_no in (
        select distinct plan_id
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD
        where sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>
    <update id="updateCancelByContract">
        UPDATE T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h
        SET status = '2'
        where contract_no = #{contractNo}
          and trade_code = #{tradeCode}
    </update>

</mapper>
