package com.dcjet.cs.auxiliaryMaterials.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Setter
@Getter
@Table(name = "T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD")
public class BizINonStateAuxmatAggrContractHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	 @Id
	@Column(name = "ID")
	private  String id;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 业务类型
     */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
     * 合同号
     */
	@Column(name = "CONTRACT_NO")
	private  String contractNo;
	/**
     * 买方
     */
	@Column(name = "BUYER")
	private  String buyer;
	/**
     * 供应商
     */
	@Column(name = "SUPPLIER")
	private  String supplier;
	/**
     * 签约日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "SIGNING_DATE")
	private  Date signingDate;
	/**
     * 国内委托方
     */
	@Column(name = "DOMESTIC_PRINCIPAL")
	private  String domesticPrincipal;
	/**
     * 运输方式
     */
	@Column(name = "TRANSPORT_MODE")
	private  String transportMode;
	/**
     * 合同生效期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EFFECTIVE_DATE")
	private  Date effectiveDate;
	/**
     * 合同有效期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "EXPIRY_DATE")
	private  Date expiryDate;
	/**
     * 签约地点(中文)
     */
	@Column(name = "SIGNING_LOCATION_CN")
	private  String signingLocationCn;
	/**
     * 签约地点(英文)
     */
	@Column(name = "SIGNING_LOCATION_EN")
	private  String signingLocationEn;
	/**
     * 装运港
     */
	@Column(name = "PORT_OF_LOADING")
	private  String portOfLoading;
	/**
     * 目的港
     */
	@Column(name = "PORT_OF_DESTINATION")
	private  String portOfDestination;
	/**
     * 报关口岸
     */
	@Column(name = "CUSTOMS_PORT")
	private  String customsPort;
	/**
     * 付款方式
     */
	@Column(name = "PAYMENT_METHOD")
	private  String paymentMethod;
	/**
     * 币种
     */
	@Column(name = "CURRENCY")
	private  String currency;
	/**
     * 价格条款
     */
	@Column(name = "PRICE_TERM")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@Column(name = "PRICE_TERM_PORT")
	private  String priceTermPort;
	/**
     * 建议授权签约人
     */
	@Column(name = "SUGGESTED_SIGNER")
	private  String suggestedSigner;
	/**
     * 短溢数%
     */
	@Column(name = "SHORTAGE_OVERFLOW_PERCENT")
	private  BigDecimal shortageOverflowPercent;
	/**
     * 备注
     */
	@Column(name = "REMARKS")
	private  String remarks;
	/**
     * 版本号
     */
	@Column(name = "VERSION_NO")
	private  String versionNo;
	/**
     * 单据状态
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CONFIRM_TIME")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@Column(name = "APPR_STATUS")
	private  String apprStatus;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 
     */
	@Column(name = "PARENT_ID")
	private  String parentId;
	/**
     * 制单人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 制单日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 制单日期-开始
     */
	@Transient
	private String createTimeFrom;
	/**
     * 制单日期-结束
     */
	@Transient
    private String createTimeTo;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 插入用户名
     */
	@Column(name = "INSERT_USER_NAME")
	private  String insertUserName;
	/**
     * 更新用户名
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "EXTEND10")
	private  String extend10;
	/**
     * 划款金额
     */
	@Column(name = "PAYMENT_AMOUNT")
	private  BigDecimal paymentAmount;
	@Transient
	private String paymentAmountStr;
	/**
     * 合同金额
     */
	@Column(name = "CONTRACT_AMOUNT")
	private  BigDecimal contractAmount;
	/**
     * 汇率
     */
	@Column(name = "EXCHANGE_RATE")
	private  BigDecimal exchangeRate;
	/**
     * 关税率%
     */
	@Column(name = "TARIFF_RATE")
	private  BigDecimal tariffRate;
	/**
     * 关税金额
     */
	@Column(name = "TARIFF_AMOUNT")
	private  BigDecimal tariffAmount;
	/**
     * 消费税率
     */
	@Column(name = "CONSUMPTION_TAX_RATE")
	private  BigDecimal consumptionTaxRate;
	/**
     * 消费税金额
     */
	@Column(name = "CONSUMPTION_TAX_AMOUNT")
	private  BigDecimal consumptionTaxAmount;
	/**
     * 增值税率%
     */
	@Column(name = "VAT_RATE")
	private  BigDecimal vatRate;
	/**
     * 增值税金额
     */
	@Column(name = "VAT_AMOUNT")
	private  BigDecimal vatAmount;
	/**
     * 进出口公司代理费率%
     */
	@Column(name = "IMPORT_EXPORT_AGENCY_RATE")
	private  BigDecimal importExportAgencyRate;
	/**
     * 进出口公司代理费
     */
	@Column(name = "IMPORT_EXPORT_AGENCY_FEE")
	private  BigDecimal importExportAgencyFee;
	/**
     * 总公司代理费率%
     */
	@Column(name = "HEADQUARTERS_AGENCY_RATE")
	private  BigDecimal headquartersAgencyRate;
	/**
     * 总公司代理费
     */
	@Column(name = "HEADQUARTERS_AGENCY_FEE")
	private  BigDecimal headquartersAgencyFee;
	/**
     * 合同数量
     */
	@Column(name = "CONTRACT_QUANTITY")
	private  BigDecimal contractQuantity;
	/**
     * 计费重量（箱）
     */
	@Column(name = "BILLING_WEIGHT")
	private  BigDecimal billingWeight;
	/**
     * 通关费
     */
	@Column(name = "CUSTOMS_CLEARANCE_FEE")
	private  BigDecimal customsClearanceFee;
	/**
     * 验柜服务费
     */
	@Column(name = "CONTAINER_INSPECTION_FEE")
	private  BigDecimal containerInspectionFee;
	/**
     * 货代费用
     */
	@Column(name = "FREIGHT_FORWARDER_FEE")
	private  BigDecimal freightForwarderFee;
	/**
     * 保险费率
     */
	@Column(name = "INSURANCE_RATE")
	private  BigDecimal insuranceRate;
	/**
     * 保险费
     */
	@Column(name = "INSURANCE_FEE")
	private  BigDecimal insuranceFee;
	/**
     * 协议编号
     */
	@Column(name = "AGREEMENT_NO")
	private  String agreementNo;
	/**
     * 签约日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "AGREEMENT_SIGNING_DATE")
	private  Date agreementSigningDate;
	/**
     * 代理费率
     */
	@Column(name = "AGREEMENT_AGENT_FEE_RATE")
	private  BigDecimal agreementAgentFeeRate;
	/**
     * 建议授权签约人
     */
	@Column(name = "AGREEMENT_SUGGESTED_SIGNER")
	private  String agreementSuggestedSigner;
	/**
     * 协议条款
     */
	@Column(name = "AGREEMENT_TERMS")
	private  String agreementTerms;
	/**
     * 协议备注
     */
	@Column(name = "AGREEMENT_REMARKS")
	private  String agreementRemarks;
	/**
	 * 是否划款通知保存过(0否;1是)
	 */
	@Column(name = "IS_TRANSFER_NOTICE")
	private String isTransferNotice;
	@Transient
	private  BigDecimal decTotalToList;
	@Transient
	private  BigDecimal qtyToList;
	@Transient
	private  String unitToList;
	@Transient
	private  String merchandiseCategoriesToList;

	@Transient
	private BigDecimal totalAmount;

	@Transient
	private String createtUserName;
	@Transient
	private Date insertTime;


	/**
	 * 打印币制中文
	 */
	@Transient
	private String currStr;
	/**
	 * 打印总数量
	 */
	@Transient
	private String qtyAllStr;
	/**
	 * 打印总金额小写
	 */
	@Transient
	private String totalAmountStr;
	/**
	 * 打印总金额大写
	 */
	@Transient
	private String totalAmountUpStr;
	/**
	 * 制单时间，系统自动生成-开始
	 */
	@Transient
	private String insertTimeFrom;
	@Transient
	private String fileType;
	/**
	 * 商品名称
	 */
	@Transient
	private  String gName;
	@Transient
	private String hasHeadNotice;
	@Transient
	private String isCopy;
}
