package com.dcjet.cs.auxiliaryMaterials.mapper;

import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListParam;
import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderNoticeListDtoMapper {

    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    OrderNoticeListDto toDto(OrderNoticeList po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    OrderNoticeList toPo(OrderNoticeListParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(OrderNoticeListParam param, @MappingTarget OrderNoticeList po);
}