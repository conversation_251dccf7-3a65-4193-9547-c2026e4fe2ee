package com.dcjet.cs.auxiliaryMaterials.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-28
 */
@Setter
@Getter
@Table(name = "t_biz_i_auxmat_buy_contract")
public class BizIAuxmatBuyContract implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 业务类型
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 购销合同号
     */
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 购销年份
     */
    @Column(name = "contract_year")
    private String contractYear;

    /**
     * 业务区分
     */
    @Column(name = "business_distinction")
    private String businessDistinction;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 版本号
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 单据状态
     */
    @Column(name = "status")
    private String status;

    /**
     * 确认时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "confirm_time")
    private Date confirmTime;

    /**
     * 审批状态
     */
    @Column(name = "appr_status")
    private String apprStatus;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建人
     */
    @Column(name = "create_by_name")
    private String createByName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建时间-开始
     */
    @Transient
    private String createTimeFrom;

    /**
     * 创建时间-结束
     */
    @Transient
    private String createTimeTo;

    /**
     * 最后修改人
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 最后修改人
     */
    @Column(name = "update_by_name")
    private String updateByName;

    /**
     * 最后修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人部门编码
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 企业编码
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 合同金额
     */
    @Column(name = "CONTRACT_AMOUNT")
    private BigDecimal contractAmount;

    /**
     * 汇率
     */
    @Column(name = "EXCHANGE_RATE")
    private BigDecimal exchangeRate;

    /**
     * 关税税率
     */
    @Column(name = "TARIFF_RATE")
    private BigDecimal tariffRate;

    /**
     * 关税金额
     */
    @Column(name = "TARIFF_AMOUNT")
    private BigDecimal tariffAmount;

    /**
     * 消费税率
     */
    @Column(name = "CONSUMPTION_TAX_RATE")
    private BigDecimal consumptionTaxRate;

    /**
     * 消费税金额
     */
    @Column(name = "CONSUMPTION_TAX_AMOUNT")
    private BigDecimal consumptionTaxAmount;

    /**
     * 增值税税率
     */
    @Column(name = "VAT_RATE")
    private BigDecimal vatRate;

    /**
     * 增值税金额
     */
    @Column(name = "VAT_AMOUNT")
    private BigDecimal vatAmount;

    /**
     * 进出口代理费率
     */
    @Column(name = "IMPORT_EXPORT_AGENCY_RATE")
    private BigDecimal importExportAgencyRate;

    /**
     * 进出口代理费
     */
    @Column(name = "IMPORT_EXPORT_AGENCY_FEE")
    private BigDecimal importExportAgencyFee;

    /**
     * 总部代理费率
     */
    @Column(name = "HEADQUARTERS_AGENCY_RATE")
    private BigDecimal headquartersAgencyRate;

    /**
     * 总部代理费
     */
    @Column(name = "HEADQUARTERS_AGENCY_FEE")
    private BigDecimal headquartersAgencyFee;

    /**
     * 合同数量
     */
    @Column(name = "CONTRACT_QUANTITY")
    private BigDecimal contractQuantity;

    /**
     * 计费重量
     */
    @Column(name = "BILLING_WEIGHT")
    private BigDecimal billingWeight;

    /**
     * 通关费
     */
    @Column(name = "CUSTOMS_CLEARANCE_FEE")
    private BigDecimal customsClearanceFee;

    /**
     * 验柜服务费
     */
    @Column(name = "CONTAINER_INSPECTION_FEE")
    private BigDecimal containerInspectionFee;

    /**
     * 货运代理费
     */
    @Column(name = "FREIGHT_FORWARDER_FEE")
    private BigDecimal freightForwarderFee;

    /**
     * 保险费率
     */
    @Column(name = "INSURANCE_RATE")
    private BigDecimal insuranceRate;

    /**
     * 保险费
     */
    @Column(name = "INSURANCE_FEE")
    private BigDecimal insuranceFee;

    /**
     * 人民币划款金额
     */
    @Column(name = "PAYMENT_AMOUNT")
    private BigDecimal paymentAmount;

    @Transient
    private String paymentAmountStr;
    /**
     * 是否划款通知保存过(0否;1是)
     */
    @Column(name = "is_transfer_notice")
    private String isTransferNotice;


    /**
     * 
     */
    @Column(name = "EXTEND1")
    private String extend1;

    /**
     * 
     */
    @Column(name = "EXTEND2")
    private String extend2;

    /**
     * 
     */
    @Column(name = "EXTEND3")
    private String extend3;

    /**
     * 
     */
    @Column(name = "EXTEND4")
    private String extend4;

    /**
     * 
     */
    @Column(name = "EXTEND5")
    private String extend5;

    /**
     * 
     */
    @Column(name = "EXTEND6")
    private String extend6;

    /**
     * 
     */
    @Column(name = "EXTEND7")
    private String extend7;

    /**
     * 
     */
    @Column(name = "EXTEND8")
    private String extend8;

    /**
     * 
     */
    @Column(name = "EXTEND9")
    private String extend9;

    /**
     * 
     */
    @Column(name = "EXTEND10")
    private String extend10;

    @Transient
    private String createrUser;

    @Transient
    private Date createrTime;

    /**
     * 制单时间，系统自动生成-开始
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 制单时间，系统自动生成-结束
     */
    @Transient
    private String insertTimeTo;

    @Transient
    private String fileType;

    /**
     * 商品名称
     */
    @Transient
    private  String gName;

    /**
     * 供应商
     */
    @Transient
    private  String supplier;

    @Transient
    private String hasHeadNotice;
    @Transient
    private String isCopy;

}