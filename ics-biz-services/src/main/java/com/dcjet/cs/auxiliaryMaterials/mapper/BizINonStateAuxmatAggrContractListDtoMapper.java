package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizINonStateAuxmatAggrContractListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizINonStateAuxmatAggrContractListDto toDto(BizINonStateAuxmatAggrContractList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizINonStateAuxmatAggrContractList toPo(BizINonStateAuxmatAggrContractListParam param);
    /**
     * 数据库原始数据更新
     * @param bizINonStateAuxmatAggrContractListParam
     * @param bizINonStateAuxmatAggrContractList
     */
    void updatePo(BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, @MappingTarget BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList);
    default void patchPo(BizINonStateAuxmatAggrContractListParam bizINonStateAuxmatAggrContractListParam, BizINonStateAuxmatAggrContractList bizINonStateAuxmatAggrContractList) {
        // TODO 自行实现局部更新
    }
}
