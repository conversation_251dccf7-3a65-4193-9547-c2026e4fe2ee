package com.dcjet.cs.auxiliaryMaterials.model;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Getter;
import lombok.Setter;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Setter
@Getter
@Table(name = "t_biz_i_auxmat_for_contract_list")
@ToString
@Accessors(chain = true)
public class BizIAuxmatForContractList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键ID
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 关联主表ID
     */
	@Column(name = "head_id")
	private  String headId;

	@Column(name = "parent_id")
	private String parentId;

	/**
     * 商品名称
     */
	@Column(name = "product_name")
	private  String productName;
	/**
     * 进口数量
     */
	@Column(name = "import_quantity")
	private  BigDecimal importQuantity;
	/**
     * 进口计量单位
     */
	@Column(name = "import_unit")
	private  String importUnit;
	/**
     * 数量
     */
	@Column(name = "quantity")
	private  BigDecimal quantity;
	/**
     * 单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 单价
     */
	@Column(name = "unit_price")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Column(name = "amount")
	private  BigDecimal amount;
	/**
     * 发货日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "delivery_date")
	private  Date deliveryDate;
	/**
     * 总值折美元
     */
	@Column(name = "usd_total")
	private  BigDecimal usdTotal;
	/**
     * 规格
     */
	@Column(name = "specification")
	private  String specification;
	/**
     * 商品类别
     */
	@Column(name = "product_category")
	private  String productCategory;
	/**
     * 数据状态
     */
	@Column(name = "data_state")
	private  String dataState;
	/**
     * 版本号
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 业务编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 所属机构编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 创建人姓名
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 扩展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "extend10")
	private  String extend10;

	@Column(name = "order_no")
	private String orderNo;
	@Column(name = "supplier")
	private String supplier;
	@Transient
	private Date orderDate;
	@Transient
	private BigDecimal diffQty;

	@Transient
	private String oldParentId;
}
