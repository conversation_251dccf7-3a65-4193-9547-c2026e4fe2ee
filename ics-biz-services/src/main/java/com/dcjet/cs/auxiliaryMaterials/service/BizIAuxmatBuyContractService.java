package com.dcjet.cs.auxiliaryMaterials.service;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeListMapper;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatBuyContractDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatBuyContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.dcjet.cs.auxiliaryMaterials.service.BizIAuxmatBuyContractListService;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import jdk.nashorn.internal.runtime.options.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import com.xdo.common.exception.ErrorException;
import xdoi18n.XdoI18nUtil;
import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-28
 */
@Slf4j
@Service
public class BizIAuxmatBuyContractService extends BaseService<BizIAuxmatBuyContract> {
    @Resource
    private BizIAuxmatBuyContractMapper bizIAuxmatBuyContractMapper;
    @Resource
    private BizIAuxmatBuyContractDtoMapper bizIAuxmatBuyContractDtoMapper;
    @Resource
    private BizIAuxmatBuyContractListMapper bizIAuxmatBuyContractListMapper;
    @Resource
    private BizIAuxmatBuyContractListDtoMapper bizIAuxmatBuyContractListDtoMapper;
    @Resource
    private OrderNoticeListMapper orderNoticeListMapper;
    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;
    @Resource
    private AttachedService attachedService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private ExportService exportService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;


    @Override
    public Mapper<BizIAuxmatBuyContract> getMapper() {
        return bizIAuxmatBuyContractMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIAuxmatBuyContractParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIAuxmatBuyContractDto>> getListPaged(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toPo(bizIAuxmatBuyContractParam);
        Page<BizIAuxmatBuyContract> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatBuyContractMapper.getList(bizIAuxmatBuyContract));
        List<BizIAuxmatBuyContractDto> bizIAuxmatBuyContractDtos = page.getResult().stream().map(head -> {
            BizIAuxmatBuyContractDto dto = bizIAuxmatBuyContractDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIAuxmatBuyContractDto>> paged = ResultObject.createInstance(bizIAuxmatBuyContractDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractDto insert(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toPo(bizIAuxmatBuyContractParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIAuxmatBuyContract.setId(sid);
        bizIAuxmatBuyContract.setTradeCode(userInfo.getCompany());
        bizIAuxmatBuyContract.setCreateBy(userInfo.getUserNo());
        bizIAuxmatBuyContract.setCreateTime(new Date());
        bizIAuxmatBuyContract.setCreateByName(userInfo.getUserName());
        bizIAuxmatBuyContract.setCreaterUser(userInfo.getUserName());
        bizIAuxmatBuyContract.setCreaterTime(new Date());
        bizIAuxmatBuyContract.setIsTransferNotice("0");
        // 新增数据
        int insertStatus = bizIAuxmatBuyContractMapper.insert(bizIAuxmatBuyContract);
        return  insertStatus > 0 ? bizIAuxmatBuyContractDtoMapper.toDto(bizIAuxmatBuyContract) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractDto update(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(bizIAuxmatBuyContractParam.getId());
        bizIAuxmatBuyContractDtoMapper.updatePo(bizIAuxmatBuyContractParam, bizIAuxmatBuyContract);
        bizIAuxmatBuyContract.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatBuyContract.setUpdateTime(new Date());
        // 更新数据
        int update = bizIAuxmatBuyContractMapper.updateByPrimaryKey(bizIAuxmatBuyContract);
        return update > 0 ? bizIAuxmatBuyContractDtoMapper.toDto(bizIAuxmatBuyContract) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        //校验数据状态为0编制的允许操作删除
        int checkStatus =bizIAuxmatBuyContractMapper.checkOrderStatus(sids,"0");
        if (checkStatus > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("仅编制状态数据允许删除。"));
        }
        int checkOrderUsed = bizIAuxmatBuyContractMapper.checkOrderUsed(sids);
        if (checkOrderUsed > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("该单据在关联模块已产生数据，不允许删除"));
        }

		bizIAuxmatBuyContractMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIAuxmatBuyContractDto> selectAll(BizIAuxmatBuyContractParam exportParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toPo(exportParam);
        // bizIAuxmatBuyContract.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatBuyContractDto> bizIAuxmatBuyContractDtos = new ArrayList<>();
        List<BizIAuxmatBuyContract> bizIAuxmatBuyContracts = bizIAuxmatBuyContractMapper.getList(bizIAuxmatBuyContract);
        if (CollectionUtils.isNotEmpty(bizIAuxmatBuyContracts)) {
            bizIAuxmatBuyContractDtos = bizIAuxmatBuyContracts.stream().map(head -> {
                BizIAuxmatBuyContractDto dto = bizIAuxmatBuyContractDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIAuxmatBuyContractDtos;
    }

    /**
     * 确认数据状态接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizIAuxmatBuyContract contract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        if ("1".equals(contract.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }

        contract.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
//        contract.setUpdateBy(userInfo.getUserNo());
//        contract.setUpdateTime(new Date());
        contract.setConfirmTime(new Date());

        bizIAuxmatBuyContractMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "确认成功");
    }

    /**
     * 发送审批接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizIAuxmatBuyContract contract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(contract.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("只有数据状态为确认的数据允许操作发送审批");
            return resultObject;
        }

        // 假设审批状态字段为 apprStatus，2表示审批中
        if ("2".equals(contract.getApprStatus())) {
            throw new ErrorException(400, "该数据已在审批中，无需重复操作");
        }

        contract.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
//        contract.setUpdateBy(userInfo.getUserNo());
//        contract.setUpdateTime(new Date());

        bizIAuxmatBuyContractMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "发送审批成功");
    }

    /**
     * 作废接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizIAuxmatBuyContract contract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            throw new ErrorException(400, "辅料购销合同数据不存在，请联系管理员");
        }

        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(contract.getApprStatus())) {
            throw new ErrorException(400, "审批中的数据不允许作废");
        }
/*        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(contract.getStatus())) {
            throw new ErrorException(400, "该数据已确认，不允许作废");
        }*/
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(contract.getStatus())) {
            throw new ErrorException(400, "该数据已经作废，无需重复操作");
        }

        // 检查是否存在有效合同
        int contractCount = bizIAuxmatBuyContractMapper.checkOrderUsed(Collections.singletonList(sid));
        if (contractCount > 0) {
            resultObject.setSuccess(false);
            resultObject.setMessage("订货通知存在有效合同，不允许作废");
            return resultObject;
        }


        contract.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
//        contract.setUpdateBy(userInfo.getUserNo());
//        contract.setUpdateTime(new Date());

        bizIAuxmatBuyContractMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "作废成功");
    }

    /**
     * 新增表头和表体数据
     * @param bizIAuxmatBuyContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回包含表头和表体数据的完整信息
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractWithDetailDto insertBuyContractWithDetails(BizIAuxmatBuyContractWithDetailParam bizIAuxmatBuyContractWithDetailParam, UserInfoToken userInfo) {
        List<BizIAuxmatBuyContractListParam> contractDetailList = bizIAuxmatBuyContractWithDetailParam.getDetails();
        // 1. 处理表头数据
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toHeadWithDetailPo(bizIAuxmatBuyContractWithDetailParam);

        StringJoiner errorMes = checkContract(bizIAuxmatBuyContract);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败,")+errorMes);
        }

        // 设置固定字段
        String sid = bizIAuxmatBuyContractWithDetailParam.getId();
        bizIAuxmatBuyContract.setCreateBy(userInfo.getUserNo());
        bizIAuxmatBuyContract.setCreateByName(userInfo.getUserName());
        bizIAuxmatBuyContract.setCreateTime(new Date());
        bizIAuxmatBuyContract.setTradeCode(userInfo.getCompany());
        bizIAuxmatBuyContract.setCreaterUser(userInfo.getUserName());
        bizIAuxmatBuyContract.setCreaterTime(new Date());
        bizIAuxmatBuyContract.setIsTransferNotice("0");

        // 新增表头数据
        int insertStatus = bizIAuxmatBuyContractMapper.updateByPrimaryKey(bizIAuxmatBuyContract);
        if (insertStatus <= 0) {
            throw new ErrorException(400, "表头数据保存失败");
        }

        // 构建返回结果
        BizIAuxmatBuyContractWithDetailDto result = new BizIAuxmatBuyContractWithDetailDto();
        result.setHead(bizIAuxmatBuyContractDtoMapper.toDto(bizIAuxmatBuyContract));

        // 2. 处理表体数据
        List<BizIAuxmatBuyContractListDto> savedDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contractDetailList)) {
            for (BizIAuxmatBuyContractListParam detail : contractDetailList) {
                // 校验表体数据
                String errorMessage = validateSingleDetailData(detail);
                if (StringUtils.isNotBlank(errorMessage)) {
                    throw new ErrorException(400, errorMessage);
                }

                // 设置关联关系
                detail.setHeadId(sid);

                // 设置表体固定字段
                String detailSid = UUID.randomUUID().toString();
                BizIAuxmatBuyContractList bizIAuxmatBuyContractList = bizIAuxmatBuyContractListDtoMapper.toPo(detail);
                bizIAuxmatBuyContractList.setId(detailSid);
                bizIAuxmatBuyContractList.setCreateBy(userInfo.getUserNo());
                bizIAuxmatBuyContractList.setCreateTime(new Date());
                bizIAuxmatBuyContractList.setTradeCode(userInfo.getCompany());

                // 插入表体数据
                int listInsertStatus = bizIAuxmatBuyContractListMapper.insert(bizIAuxmatBuyContractList);
                if (listInsertStatus <= 0) {
                    throw new ErrorException(400, "表体数据保存失败，商品名称: " + detail.getGName());
                }

                // 将保存的表体数据添加到返回列表中
                savedDetails.add(bizIAuxmatBuyContractListDtoMapper.toDto(bizIAuxmatBuyContractList));
            }
            result.setDetails(savedDetails);
        }

        return result;
    }

    /**
     * 修改表头和表体数据
     * @param bizIAuxmatBuyContractWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回包含表头和表体数据的完整信息
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractWithDetailDto updateBuyContractWithDetails(BizIAuxmatBuyContractWithDetailParam bizIAuxmatBuyContractWithDetailParam, UserInfoToken userInfo) {
        List<BizIAuxmatBuyContractListParam> contractDetailList = bizIAuxmatBuyContractWithDetailParam.getDetails();
        // 1. 处理表头数据
        BizIAuxmatBuyContract existingContract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(bizIAuxmatBuyContractWithDetailParam.getId());
        if (existingContract == null) {
            throw new ErrorException(400, "辅料购销合同数据不存在，请联系管理员");
        }

        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toHeadWithDetailPo(bizIAuxmatBuyContractWithDetailParam);

        StringJoiner errorMes = checkContract(bizIAuxmatBuyContract);
        if (errorMes.length() > 0){
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败,")+errorMes);
        }

        bizIAuxmatBuyContract.setId(bizIAuxmatBuyContractWithDetailParam.getId());
        bizIAuxmatBuyContract.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatBuyContract.setUpdateByName(userInfo.getUserName());
        bizIAuxmatBuyContract.setUpdateTime(new Date());
        bizIAuxmatBuyContract.setCreaterUser(userInfo.getUserName());
        bizIAuxmatBuyContract.setCreaterTime(new Date());
        bizIAuxmatBuyContract.setTradeCode(userInfo.getCompany());

        // 更新表头数据
        int updateStatus = bizIAuxmatBuyContractMapper.updateByPrimaryKey(bizIAuxmatBuyContract);
        if (updateStatus <= 0) {
            throw new ErrorException(400, "表头数据更新失败");
        }

        // 构建返回结果
        BizIAuxmatBuyContractWithDetailDto result = new BizIAuxmatBuyContractWithDetailDto();
        result.setHead(bizIAuxmatBuyContractDtoMapper.toDto(bizIAuxmatBuyContract));

        // 2. 处理表体数据
        List<BizIAuxmatBuyContractListDto> savedDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contractDetailList)) {
            for (BizIAuxmatBuyContractListParam detail : contractDetailList) {
                // 校验表体数据
                String errorMessage = validateSingleDetailData(detail);
                if (StringUtils.isNotBlank(errorMessage)) {
                    throw new ErrorException(400, errorMessage);
                }

                // 设置关联关系
                detail.setHeadId(bizIAuxmatBuyContractWithDetailParam.getId());

                BizIAuxmatBuyContractList bizIAuxmatBuyContractList = bizIAuxmatBuyContractListDtoMapper.toPo(detail);
                bizIAuxmatBuyContractList.setTradeCode(userInfo.getCompany());

                // 检查数据是否存在于数据库中
                int resultList;
                if (StringUtils.isNotBlank(detail.getId())) {
                    // 尝试查询数据是否存在
                    BizIAuxmatBuyContractList existingRecord = bizIAuxmatBuyContractListMapper.selectByPrimaryKey(detail.getId());

                    if (existingRecord != null) {
                        // 数据存在，执行更新操作
                        bizIAuxmatBuyContractList.setUpdateBy(userInfo.getUserNo());
                        bizIAuxmatBuyContractList.setUpdateTime(new Date());
                        resultList = bizIAuxmatBuyContractListMapper.updateByPrimaryKey(bizIAuxmatBuyContractList);
                    } else {
                        // 数据不存在，执行新增操作
                        String detailSid = UUID.randomUUID().toString();
                        bizIAuxmatBuyContractList.setId(detailSid);
                        bizIAuxmatBuyContractList.setCreateBy(userInfo.getUserNo());
                        bizIAuxmatBuyContractList.setCreateTime(new Date());
                        resultList = bizIAuxmatBuyContractListMapper.insert(bizIAuxmatBuyContractList);
                    }
                } else {
                    // 没有ID，执行新增操作
                    String detailSid = UUID.randomUUID().toString();
                    bizIAuxmatBuyContractList.setId(detailSid);
                    bizIAuxmatBuyContractList.setCreateBy(userInfo.getUserNo());
                    bizIAuxmatBuyContractList.setCreateTime(new Date());
                    resultList = bizIAuxmatBuyContractListMapper.insert(bizIAuxmatBuyContractList);
                }

                if (resultList <= 0) {
                    throw new ErrorException(400, "表体数据保存失败，商品名称: " + detail.getGName());
                }

                // 将保存的表体数据添加到返回列表中
                savedDetails.add(bizIAuxmatBuyContractListDtoMapper.toDto(bizIAuxmatBuyContractList));
            }
            result.setDetails(savedDetails);
        }

        return result;
    }

    /**
     * 校验是否存在同一个合同号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject checkContractIdNotCancel(BizIAuxmatBuyContractParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");

        if (StringUtils.isBlank(params.getId())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要复制数据！"));
        }

        List<String> sids = bizIAuxmatBuyContractMapper.checkContractIdNotCancel(params.getId());
        if (CollectionUtils.isNotEmpty((sids))){
            resultObject.setSuccess(false);
            return resultObject;
        }

       /* // 查询同一合同号下未作废的数据数量
        BizIAuxmatBuyContract queryParam = new BizIAuxmatBuyContract();
        queryParam.setContractNo(params.getContractNo());
        queryParam.setTradeCode(userInfo.getCompany());

        List<BizIAuxmatBuyContract> contracts = bizIAuxmatBuyContractMapper.getList(queryParam);
        long notCancelledCount = contracts.stream()
                .filter(contract -> !"2".equals(contract.getStatus()))
                .count();

        if (notCancelledCount > 1) {
            return ResultObject.createInstance(false, "同一合同号存在多条未作废的数据，请检查", notCancelledCount);
        }*/

        return resultObject;
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyVersion(BizIAuxmatBuyContractParam params, UserInfoToken userInfo) {

        if (StringUtils.isBlank(params.getId())) {
            throw new ErrorException(400, "原数据ID不能为空");
        }
        BizIAuxmatBuyContract originalContract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(params.getId());
        if (originalContract == null) {
            throw new ErrorException(400, "原数据不存在，请联系管理员");
        }

        //查询最大版本号
        BizIAuxmatBuyContract maxVersionNoData = bizIAuxmatBuyContractMapper.getMaxVersionNoByContractNo(originalContract);
        if (maxVersionNoData == null) {
            throw new ErrorException(400, XdoI18nUtil.t("查询不到有效数据，请刷新后再试！"));
        }

        Integer maxVersionNoInt = Integer.valueOf(maxVersionNoData.getVersionNo()) + 1;


        //新表头id
        String sid = UUID.randomUUID().toString();

        //使用最大版本号找下游数据 更新下游数据关联id
        List<BizIAuxmatBuyContractList> oldListSids = bizIAuxmatBuyContractListMapper.getContractListByHeadId(maxVersionNoData.getId());
        for (BizIAuxmatBuyContractList old : oldListSids) {
            //版本复制数据 再原sid上拼接版本号

            String newListId = old.getId().split("_")[0] + "_" + maxVersionNoInt;
            String oldListId = old.getId().split("_")[0];
            if (!"1".equals(maxVersionNoData.getVersionNo())) {
                oldListId = oldListId + "-" + maxVersionNoData.getVersionNo();
            }

            orderNoticeListMapper.updateCorrelationID(newListId, oldListId);
            old.setId(newListId);
            old.setHeadId(sid);
            old.setCreateBy(userInfo.getUserNo());
            old.setCreateByName(userInfo.getUserName());
            old.setCreateTime(new Date());
            old.setUpdateBy(null);
            old.setUpdateByName(null);
            old.setUpdateTime(null);
        }

        //更新现在合同号数据为 2 作废
        bizIAuxmatBuyContractMapper.updateCancelByContract(originalContract.getContractNo(), originalContract.getTradeCode());

        originalContract.setId(sid);
        originalContract.setCreateTime(new Date());
        originalContract.setCreateBy(userInfo.getUserNo());
        originalContract.setCreaterTime(new Date());
        originalContract.setCreaterUser(userInfo.getUserName());
        originalContract.setCreateByName(userInfo.getUserName());
        originalContract.setUpdateBy(null);
        originalContract.setUpdateByName(null);
        originalContract.setUpdateTime(null);
        originalContract.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        originalContract.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        originalContract.setVersionNo(maxVersionNoInt.toString());
        originalContract.setConfirmTime(null);
        originalContract.setIsTransferNotice("0");
        bizIAuxmatBuyContractMapper.insert(originalContract);

        if (CollectionUtils.isNotEmpty(oldListSids)){
            oldListSids.stream().forEach(item -> {
                bizIAuxmatBuyContractListMapper.insert(item);
            });
        }

        // 复制随附单证文件
        List<Attached> attachedList = bizIOrderHeadMapper.getAttachmentFile(params.getId());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(sid);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制计划表头【"+originalContract.getContractNo()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }

        return ResultObject.createInstance(true, "版本复制成功");
    }

    /**
     * 复制功能 - 创建一条完整的新数据记录
     * @param params 请求参数，包含原数据ID和新购销合同号
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyContract(BizIAuxmatBuyContractParam params, UserInfoToken userInfo) {

        if (StringUtils.isBlank(params.getId())) {
            throw new ErrorException(400, "原数据ID不能为空");
        }

        if (StringUtils.isBlank(params.getContractNo())) {
            throw new ErrorException(400, "新购销合同号不能为空");
        }

        // 查询原数据
        BizIAuxmatBuyContract originalContract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(params.getId());
        if (originalContract == null) {
            throw new ErrorException(400, "原数据不存在，请联系管理员");
        }

        // 校验新购销合同号是否已存在
        BizIAuxmatBuyContract checkContract = new BizIAuxmatBuyContract();
        checkContract.setContractNo(params.getContractNo());
        checkContract.setTradeCode(userInfo.getCompany());
        int existCount = bizIAuxmatBuyContractMapper.checkContractId(checkContract);
        if (existCount > 0) {
            throw new ErrorException(400, "新购销合同号【" + params.getContractNo() + "】已存在，请使用其他合同号");
        }

        // 创建新的表头ID
        String newHeadId = UUID.randomUUID().toString();

        // 复制表头数据
        originalContract.setId(newHeadId);
        originalContract.setContractNo(params.getContractNo());
        originalContract.setCreateTime(new Date());
        originalContract.setCreateBy(userInfo.getUserNo());
        originalContract.setCreaterTime(new Date());
        originalContract.setCreaterUser(userInfo.getUserName());
        originalContract.setCreateByName(userInfo.getUserName());
        originalContract.setUpdateBy(null);
        originalContract.setUpdateByName(null);
        originalContract.setUpdateTime(null);
        originalContract.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        originalContract.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        originalContract.setVersionNo("1"); // 新数据版本号从1开始
        originalContract.setConfirmTime(null);
        originalContract.setIsTransferNotice("0");

        // 插入新表头数据
        int insertResult = bizIAuxmatBuyContractMapper.insert(originalContract);
        if (insertResult <= 0) {
            throw new ErrorException(400, "复制表头数据失败");
        }

        // 复制表体数据
        List<BizIAuxmatBuyContractList> originalDetailList = bizIAuxmatBuyContractListMapper.getContractListByHeadId(params.getId());
        if (CollectionUtils.isNotEmpty(originalDetailList)) {
            for (BizIAuxmatBuyContractList originalDetail : originalDetailList) {
                String newDetailId = UUID.randomUUID().toString();
                originalDetail.setId(newDetailId);
                originalDetail.setHeadId(newHeadId);
                originalDetail.setCreateBy(userInfo.getUserNo());
                originalDetail.setCreateByName(userInfo.getUserName());
                originalDetail.setCreateTime(new Date());
                originalDetail.setUpdateBy(null);
                originalDetail.setUpdateByName(null);
                originalDetail.setUpdateTime(null);
                originalDetail.setTradeCode(userInfo.getCompany());

                int detailInsertResult = bizIAuxmatBuyContractListMapper.insert(originalDetail);
                if (detailInsertResult <= 0) {
                    throw new ErrorException(400, "复制表体数据失败，商品名称: " + originalDetail.getGName());
                }
            }
        }

        // 复制附件
        List<Attached> attachedList = bizIOrderHeadMapper.getAttachmentFile(params.getId());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newAttachId = UUID.randomUUID().toString();
                attached.setSid(newAttachId);
                attached.setBusinessSid(newHeadId);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制合同【" + originalContract.getContractNo() + "】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}", tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }

        return ResultObject.createInstance(true, "复制成功，新合同号：" + params.getContractNo());
    }

    /**
     * 校验单行表体数据
     * @param detail 表体数据
     * @return 返回校验结果错误信息，如果为空则校验通过
     */
    public String validateSingleDetailData(BizIAuxmatBuyContractListParam detail) {
        try {
            // 校验商品名称（必填字段）
            if (StringUtils.isBlank(detail.getGName())) {
                return "商品名称不能为空";
            }

            // 校验商品名称长度（字符型60）
            if (detail.getGName().length() > 60) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】" +"规格【"+detail.getSpecifications() + "】长度不能超过60个字符";
            }

            // 校验产品型号长度（字符型100，非必填）
            if (StringUtils.isNotBlank(detail.getGModel()) && detail.getGModel().length() > 100) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的产品型号长度不能超过100个字符";
            }

            // 校验规格长度（字符型100，非必填）
            if (StringUtils.isNotBlank(detail.getSpecifications()) && detail.getSpecifications().length() > 100) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的规格长度不能超过100个字符";
            }

            // 校验克重（数值型19,4，非必填）
            if (detail.getGramWeight() != null) {
                if (detail.getGramWeight().compareTo(java.math.BigDecimal.ZERO) < 0) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的克重不能为负数";
                }
                // 检查精度：整数位最大19位，小数位最大4位
                String gramWeightStr = detail.getGramWeight().toPlainString();
                if (gramWeightStr.contains(".")) {
                    String[] parts = gramWeightStr.split("\\.");
                    if (parts[0].length() > 19) {
                        return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的克重整数位不能超过15位";
                    }
                    if (parts[1].length() > 4) {
                        return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的克重小数位不能超过4位";
                    }
                } else {
                    if (gramWeightStr.length() > 19) {
                        return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的克重整数位不能超过15位";
                    }
                }
            }

            // 校验供应商长度（字符型200，非必填）
            if (StringUtils.isNotBlank(detail.getSupplier()) && detail.getSupplier().length() > 200) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的供应商长度不能超过200个字符";
            }

            // 校验数量（数值型19,6，必填）
            if (detail.getQty() == null) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的数量不能为空";
            }
            if (detail.getQty().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的数量必须大于0";
            }
            // 检查数量精度：整数位最大19位，小数位最大6位
            String qtyStr = detail.getQty().toPlainString();
            if (qtyStr.contains(".")) {
                String[] parts = qtyStr.split("\\.");
                if (parts[0].length() > 19) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的数量整数位不能超过13位";
                }
                if (parts[1].length() > 6) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的数量小数位不能超过6位";
                }
            } else {
                if (qtyStr.length() > 19) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的数量整数位不能超过13位";
                }
            }

            // 校验单位长度（字符型30，非必填）
            if (StringUtils.isNotBlank(detail.getUnit()) && detail.getUnit().length() > 30) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的单位长度不能超过30个字符";
            }

            // 校验单价（数值型19,8，必填）
            if (detail.getUnitPrice() == null) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的单价不能为空";
            }
            if (detail.getUnitPrice().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的单价必须大于0";
            }
            // 检查单价精度：整数位最大19位，小数位最大8位
            String unitPriceStr = detail.getUnitPrice().toPlainString();
            if (unitPriceStr.contains(".")) {
                String[] parts = unitPriceStr.split("\\.");
                if (parts[0].length() > 19) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的单价整数位不能超过11位";
                }
                if (parts[1].length() > 8) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的单价小数位不能超过8位";
                }
            } else {
                if (unitPriceStr.length() > 19) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的单价整数位不能超过11位";
                }
            }

            // 校验金额（数值型19,4，必填，系统计算=数量*单价）
            if (detail.getAmount() == null) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的金额不能为空";
            }
            if (detail.getAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的金额必须大于0";
            }
            // 检查金额精度：整数位最大19位，小数位最大4位
            String amountStr = detail.getAmount().toPlainString();
            if (amountStr.contains(".")) {
                String[] parts = amountStr.split("\\.");
                if (parts[0].length() > 19) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的金额整数位不能超过15位";
                }
                if (parts[1].length() > 4) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的金额小数位不能超过4位";
                }
            } else {
                if (amountStr.length() > 19) {
                    return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的金额整数位不能超过15位";
                }
            }

            // 校验币种（字符型10，必填）
            if (StringUtils.isBlank(detail.getCurr())) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的币种不能为空";
            }
            if (detail.getCurr().length() > 10) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的币种长度不能超过10个字符";
            }

            // 校验金额计算是否正确（数量 × 单价 = 金额）
            java.math.BigDecimal calculatedAmount = detail.getQty().multiply(detail.getUnitPrice());
            // 由于金额小数位最大4位，所以允许的误差为0.0001
            if (calculatedAmount.subtract(detail.getAmount()).abs().compareTo(new java.math.BigDecimal("0.0001")) > 0) {
                return "商品名称【" + detail.getGName() + "】" +"规格【"+detail.getSpecifications() + "】的金额计算不正确，应为：" + calculatedAmount.setScale(4, java.math.RoundingMode.HALF_UP);
            }

            return ""; // 校验通过
        } catch (Exception e) {
            String productName = detail != null && StringUtils.isNotBlank(detail.getGName()) ? detail.getGName() : "未知商品";
            return "商品名称【" + productName + "】校验时发生异常：" + e.getMessage();
        }
    }
    public StringJoiner checkContract(BizIAuxmatBuyContract buyContract){
        StringJoiner errorMes =new StringJoiner(",");

        int checkPlanId = bizIAuxmatBuyContractMapper.checkContractId(buyContract);

        if (checkPlanId > 0){
            errorMes.add(xdoi18n.XdoI18nUtil.t("购销合同号已存在"));
        }
        return errorMes;
    }

    /**
     * 划款通知
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject handlerTransferNotice(BizIAuxmatBuyContract bizIAuxmatBuyContract, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("划款通知成功"));
        BizIAuxmatBuyContract transferNotice;

        if ("0".equals(bizIAuxmatBuyContract.getIsTransferNotice())) {
            BigDecimal exchangeRate = bizIAuxmatBuyContract.getExchangeRate() == null ? null : bizIAuxmatBuyContract.getExchangeRate();
            BigDecimal billingWeight = bizIAuxmatBuyContract.getBillingWeight() == null ? null : bizIAuxmatBuyContract.getBillingWeight();
            BigDecimal paymentAmount = bizIAuxmatBuyContract.getPaymentAmount() == null ? null : bizIAuxmatBuyContract.getPaymentAmount();
            transferNotice = bizIAuxmatBuyContractMapper.calculateContractAmounts(bizIAuxmatBuyContract.getId(), exchangeRate, billingWeight, paymentAmount);
        }else {
            transferNotice=bizIAuxmatBuyContractMapper.selectByPrimaryKey(bizIAuxmatBuyContract.getId());
        }
        resultObject.setData(transferNotice);

        return resultObject;
    }
    /**
     * 划款通知保存
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject saveTransferNotice(BizIAuxmatBuyContract bizIAuxmatBuyContract, UserInfoToken userInfo)  {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("划款通知保存成功"));
        List<BizIAuxmatBuyContractList> buyContractLists=bizIAuxmatBuyContractListMapper.getContractListByHeadId(bizIAuxmatBuyContract.getId());
        if (CollectionUtils.isEmpty(buyContractLists)){
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("表体没有数据，请补充表体再操作"));
        }
        BigDecimal exchangeRate=bizIAuxmatBuyContract.getExchangeRate()==null?null:bizIAuxmatBuyContract.getExchangeRate();
        BigDecimal billingWeight=bizIAuxmatBuyContract.getBillingWeight()==null?null:bizIAuxmatBuyContract.getBillingWeight();
        BigDecimal paymentAmount=bizIAuxmatBuyContract.getPaymentAmount()==null?null:bizIAuxmatBuyContract.getPaymentAmount();
        BizIAuxmatBuyContract transferNotice=bizIAuxmatBuyContractMapper.calculateContractAmounts(bizIAuxmatBuyContract.getId(),exchangeRate,billingWeight,paymentAmount);
        bizIAuxmatBuyContractMapper.saveTransferNotice(transferNotice);

        resultObject.setData(transferNotice);

        return resultObject;
    }
    /**
     * 划款通知保存校验
     *
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject checkTransferNotice(BizIAuxmatBuyContract bizIAuxmatBuyContract)  {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("划款通知保存成功"));
        List<BizIAuxmatBuyContractList> buyContractLists=bizIAuxmatBuyContractListMapper.getContractListByHeadId(bizIAuxmatBuyContract.getId());
        if (CollectionUtils.isEmpty(buyContractLists)){
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("表体没有数据，请补充表体再操作"));
        }
        return resultObject;
    }


    /**
     * 划款通知保存打印
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity saveTransferNoticePrint(BizIAuxmatBuyContract bizIAuxmatBuyContract, UserInfoToken userInfo) throws Exception {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);

        BigDecimal exchangeRate=bizIAuxmatBuyContract.getExchangeRate()==null?null:bizIAuxmatBuyContract.getExchangeRate();
        BigDecimal billingWeight=bizIAuxmatBuyContract.getBillingWeight()==null?null:bizIAuxmatBuyContract.getBillingWeight();
        BigDecimal paymentAmount=bizIAuxmatBuyContract.getPaymentAmount()==null?null:bizIAuxmatBuyContract.getPaymentAmount();
        BizIAuxmatBuyContract transferNotice=bizIAuxmatBuyContractMapper.calculateContractAmounts(bizIAuxmatBuyContract.getId(),exchangeRate,billingWeight,paymentAmount);
        bizIAuxmatBuyContractMapper.saveTransferNotice(transferNotice);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        transferNotice.setInsertTimeFrom("日期 "+sdf.format(new Date()));
        String requestDeliverDate = bizIAuxmatBuyContractMapper.getRequestDeliverDate(transferNotice.getId());
        transferNotice.setSupplier("        贵司向"+getMerchantNameSafely(bizMerchantMap,transferNotice.getSupplier())+"采购"+
                (requestDeliverDate==null?" ":requestDeliverDate)+"到货之一批"+(transferNotice.getGName()==null?" ":transferNotice.getGName())+
                "，合同号为"+(transferNotice.getContractNo()==null?" ":transferNotice.getContractNo())+
                "，为确保我司及时划至中烟国际对外付款，请贵司将货款、税款、货代费、代理费等汇至我司下列账户为盼。");

        transferNotice.setGName("事由：预付进口"+(transferNotice.getGName()==null?" ":transferNotice.getGName())+"货款等事宜");
        if (paymentAmount!=null){
            paymentAmount=paymentAmount.divide(new BigDecimal("10000"));
        }

        transferNotice.setContractNo(transferNotice.getContractNo()+" "+(paymentAmount==null?" ":paymentAmount)+" 万元  淮阴卷烟厂");
        transferNotice.setPaymentAmountStr("汇款金额："+(paymentAmount==null?" ":paymentAmount)+"万元人民币");

        String templateName = "划款通知.xlsx";
        String finalOutName = xdoi18n.XdoI18nUtil.t("划款通知")+bizIAuxmatBuyContract.getFileType();
        String tempFileName = UUID.randomUUID() + ".xlsx";

        String exportFileName = exportService.export(
                Arrays.asList(transferNotice),
                tempFileName,
                templateName
        );
       /* HttpHeaders h = new HttpHeaders();
        finalOutName = URLEncoder.encode(finalOutName, CommonVariable.UTF8);
        finalOutName = finalOutName.replaceAll("\\+", "%20");
        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(finalOutName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(exportFileName)), h, HttpStatus.OK);
       */
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        // 根据文件类型选择导出方式
        if (".pdf".equalsIgnoreCase(bizIAuxmatBuyContract.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, finalOutName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(finalOutName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    /**
     * 创建商户映射表，处理重复key的情况
     * @param bizMerchants 商户列表
     * @return 商户编码到中文名称的映射
     */
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }


    /**
     * 安全地获取商户中文名称
     * @param bizMerchantMap 商户映射表
     * @param merchantCode 商户编码
     * @return 商户中文名称，如果不存在则返回空字符串
     */
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }

}
