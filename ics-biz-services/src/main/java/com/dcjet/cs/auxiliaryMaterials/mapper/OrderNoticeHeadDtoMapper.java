package com.dcjet.cs.auxiliaryMaterials.mapper;

import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeHeadParam;
import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderNoticeHeadDtoMapper {

    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    OrderNoticeHeadDto toDto(OrderNoticeHead po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    OrderNoticeHead toPo(OrderNoticeHeadParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(OrderNoticeHeadParam param, @MappingTarget OrderNoticeHead po);
}