package com.dcjet.cs.auxiliaryMaterials.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@Setter
@Getter
@Table(name = "t_biz_e_non_state_auxmat_aggr_contract_head")
@ToString
public class BizENonStateAuxmatAggrContractHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 业务类型
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 合同号
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 客户
     */
	@Column(name = "buyer")
	private  String buyer;
	/**
     * 供应商
     */
	@Column(name = "supplier")
	private  String supplier;
	/**
     * 签约日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	@Column(name = "signing_date")
	private  Date signingDate;
	/**
     * 装运期限
     */
	@Column(name = "shipment_deadline")
	private  String shipmentDeadline;
	/**
     * 合同生效期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "effective_date")
	private  Date effectiveDate;
	/**
     * 合同有效期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "expiry_date")
	private  Date expiryDate;
	/**
     * 签约地点(中文)
     */
	@Column(name = "signing_location_cn")
	private  String signingLocationCn;
	/**
     * 签约地点(英文)
     */
	@Column(name = "signing_location_en")
	private  String signingLocationEn;
	/**
     * 装运港
     */
	@Column(name = "port_of_loading")
	private  String portOfLoading;
	/**
     * 目的港
     */
	@Column(name = "port_of_destination")
	private  String portOfDestination;
	/**
     * 收汇方式
     */
	@Column(name = "payment_method")
	private  String paymentMethod;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 价格条款
     */
	@Column(name = "price_term")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@Column(name = "price_term_port")
	private  String priceTermPort;
	/**
     * 建议授权签约人
     */
	@Column(name = "suggested_signer")
	private  String suggestedSigner;
	/**
     * 短溢数%
     */
	@Column(name = "shortage_overflow_percent")
	private  BigDecimal shortageOverflowPercent;
	/**
     * INCOTERMS 国际贸易术语
     */
	@Column(name = "incoterms")
	private  String incoterms;
	/**
     * 唛头
     */
	@Column(name = "mark")
	private  String mark;
	/**
     * 合同条款
     */
	@Column(name = "contract_terms")
	private  String contractTerms;
	/**
     * 备注
     */
	@Column(name = "remarks")
	private  String remarks;
	/**
     * 协议编号
     */
	@Column(name = "agreement_no")
	private  String agreementNo;
	/**
     * 协议签约日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "agreement_signing_date")
	private  Date agreementSigningDate;
	/**
     * 总金额
     */
	@Column(name = "agreement_total_amount")
	private  BigDecimal agreementTotalAmount;
	/**
     * 代理费率%
     */
	@Column(name = "agreement_agent_rate")
	private  BigDecimal agreementAgentRate;
	/**
     * 代理费
     */
	@Column(name = "agreement_agent_fee")
	private  BigDecimal agreementAgentFee;
	/**
     * 建议授权签约人（协议相关）
     */
	@Column(name = "agreement_suggested_signer")
	private  String agreementSuggestedSigner;
	/**
     * 协议条款
     */
	@Column(name = "agreement_terms")
	private  String agreementTerms;
	/**
     * 备注（协议相关）
     */
	@Column(name = "agreement_remarks")
	private  String agreementRemarks;
	/**
     * 版本号
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 单据状态
     */
	@Column(name = "status")
	private  String status;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@Column(name = "appr_status")
	private  String apprStatus;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 制单人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 最后修改人姓名
     */
	@Column(name = "create_by_user_name")
	private  String createByUserName;
	/**
     * 制单日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改人姓名
     */
	@Column(name = "update_by_user_name")
	private  String updateByUserName;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 扩展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "extend10")
	private  String extend10;

	@Transient
	private  BigDecimal decTotalToList;
	@Transient
	private  BigDecimal qtyToList;
	@Transient
	private  String unitToList;
	@Transient
	private  String merchandiseCategoriesToList;
	@Transient
	private  String domesticPrincipal;


	/**
	 * 表体总数量
	 */
	@Transient
	private BigDecimal totalQty;
}
