package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIAuxmatBuyContractDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIAuxmatBuyContractDto toDto(BizIAuxmatBuyContract po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIAuxmatBuyContract toPo(BizIAuxmatBuyContractParam param);
    /**
     * 数据库原始数据更新
     * @param bizIAuxmatBuyContractParam
     * @param bizIAuxmatBuyContract
     */
    void updatePo(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, @MappingTarget BizIAuxmatBuyContract bizIAuxmatBuyContract);
    default void patchPo(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, BizIAuxmatBuyContract bizIAuxmatBuyContract) {
        // TODO 自行实现局部更新
    }

    /**
     * 转换包含表头和表体的参数到数据库对象
     * @param param
     * @return
     */
    BizIAuxmatBuyContract toHeadWithDetailPo(BizIAuxmatBuyContractWithDetailParam param);
}
