package com.dcjet.cs.auxiliaryMaterials.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@Setter
@Getter
@Table(name = "t_biz_e_non_state_auxmat_aggr_contract_list")
@ToString
public class BizENonStateAuxmatAggrContractList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 关联表头ID
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 商品名称
     */
	@Column(name = "g_name")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 产品型号
     */
	@Column(name = "g_model")
	@JsonProperty("gModel")
	private  String gModel;
	/**
     * 规格
     */
	@Column(name = "specifications")
	private  String specifications;
	/**
     * 克重
     */
	@Column(name = "gram_weight")
	private  BigDecimal gramWeight;
	/**
     * 供应商
     */
	@Column(name = "supplier")
	private  String supplier;
	/**
     * 数量
     */
	@Column(name = "qty")
	private  BigDecimal qty;
	/**
     * 单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 单价
     */
	@Column(name = "unit_price")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Column(name = "amount")
	private  BigDecimal amount;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 备注
     */
	@Column(name = "item_remarks")
	private  String itemRemarks;
	/**
     * 商品类别
     */
	@Column(name = "category")
	private  String category;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建人姓名
     */
	@Column(name = "create_by_name")
	private  String createByName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改人姓名
     */
	@Column(name = "update_by_name")
	private  String updateByName;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 扩展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
}
