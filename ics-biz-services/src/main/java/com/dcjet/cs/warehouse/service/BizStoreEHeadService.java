package com.dcjet.cs.warehouse.service;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadDto;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.warehouse.dao.BizStoreEListMapper;
import com.dcjet.cs.warehouse.dao.BizStoreIHeadMapper;
import com.dcjet.cs.warehouse.model.BizStoreEList;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.warehouse.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.warehouse.dao.BizStoreEHeadMapper;
import com.dcjet.cs.warehouse.mapper.BizStoreEHeadDtoMapper;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.pcode.service.PCodeHolder;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizStoreEHeadService extends BaseService<BizStoreEHead> {
    @Resource
    private BizStoreEHeadMapper bizStoreEHeadMapper;
    @Resource
    private BizStoreEHeadDtoMapper bizStoreEHeadDtoMapper;
    @Resource
    private BizStoreEListMapper bizStoreEListMapper;
    @Resource
    private PCodeHolder pCodeServiceHolder;
    @Resource
    private BizMerchantMapper bizMerchantMapper;

    @Override
    public Mapper<BizStoreEHead> getMapper() {
        return bizStoreEHeadMapper;
    }
    @Resource
    private BizStoreIHeadMapper bizStoreIHeadMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizStoreEHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizStoreEHeadDto>> getListPaged(BizStoreEHeadParam bizStoreEHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizStoreEHead bizStoreEHead = bizStoreEHeadDtoMapper.toPo(bizStoreEHeadParam);
        bizStoreEHead.setTradeCode(userInfo.getCompany());
        Page<BizStoreEHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizStoreEHeadMapper.getList(bizStoreEHead));
        List<BizStoreEHeadDto> bizStoreEHeadDtos = page.getResult().stream().map(head -> {
            BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizStoreEHeadDto>> paged = ResultObject.createInstance(bizStoreEHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizStoreEHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreEHeadDto insert(BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        BizStoreEHead bizStoreEHead = bizStoreEHeadDtoMapper.toPo(bizStoreEHeadParam);
        bizStoreEHead.setTradeCode(userInfo.getCompany());
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizStoreEHead.setSid(sid);
        bizStoreEHead.setCreateBy(userInfo.getUserNo());
        bizStoreEHead.setCreateTime(new Date());
        bizStoreEHead.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = bizStoreEHeadMapper.insert(bizStoreEHead);
        return  insertStatus > 0 ? bizStoreEHeadDtoMapper.toDto(bizStoreEHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizStoreEHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreEHeadDto update(BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(bizStoreEHeadParam.getSid());
        bizStoreEHeadDtoMapper.updatePo(bizStoreEHeadParam, bizStoreEHead);
        bizStoreEHead.setUpdateBy(userInfo.getUserNo());
        bizStoreEHead.setUpdateTime(new Date());
        bizStoreEHead.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = bizStoreEHeadMapper.updateByPrimaryKey(bizStoreEHead);
        return update > 0 ? bizStoreEHeadDtoMapper.toDto(bizStoreEHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizStoreEHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizStoreEHeadDto> selectAll(BizStoreEHeadParam exportParam, UserInfoToken userInfo) {
        BizStoreEHead bizStoreEHead = bizStoreEHeadDtoMapper.toPo(exportParam);
         bizStoreEHead.setTradeCode(userInfo.getCompany());
        List<BizStoreEHeadDto> bizStoreEHeadDtos = new ArrayList<>();
        List<BizStoreEHead> bizStoreEHeads = bizStoreEHeadMapper.getList(bizStoreEHead);
        if (CollectionUtils.isNotEmpty(bizStoreEHeads)) {
            bizStoreEHeadDtos = bizStoreEHeads.stream().map(head -> {
                BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizStoreEHeadDtos;
    }

    public ResultObject getListBySid(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");
        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(sid);
        BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(bizStoreEHead);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizStoreEHeadDto> getStoreEHeadByHeadSid(BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        ResultObject<BizStoreEHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.getStoreEHeadByHeadSid(bizStoreEHeadParam.getHeadId());
        if (bizStoreEHead != null) {
            BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(bizStoreEHead);
            resultObject.setData(dto);
        }
        return resultObject;
    }

    public ResultObject confirmStatus(BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(bizStoreEHeadParam.getSid());
        if (bizStoreEHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizStoreEHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizStoreEHead update = bizStoreEHeadDtoMapper.toPo(bizStoreEHeadParam);
//        BizStoreEHead update = new BizStoreEHead();
//        bizStoreEHead.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        bizStoreEHeadMapper.updateByPrimaryKeySelective(update);
        BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(update);
        if(StringUtils.isBlank(dto.getUpdateUserName())){
            dto.setUpdateUserName(dto.getCreateUserName());
            dto.setUpdateTime(dto.getCreateTime());
        }
        resultObject.setData(dto);

        BizStoreIHead bizStoreIHead = new BizStoreIHead();
        bizStoreIHead.setSid(bizStoreEHead.getHeadId());
        bizStoreIHead.setStoreEStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(bizStoreIHead);
        return resultObject;
    }
    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreEHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(bizStoreEHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
//        BizStoreEHead update = bizStoreEHeadDtoMapper.toPo(bizStoreEHeadParam);
//        BizStoreEHead update = new BizStoreEHead();
//        bizStoreEHead.setSid(sid);
        bizStoreEHead.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizStoreEHead.setConfirmTime(null);
        bizStoreEHeadMapper.updateByPrimaryKeySelective(bizStoreEHead);
        BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(bizStoreEHead);
        if(StringUtils.isBlank(dto.getUpdateUserName())){
            dto.setUpdateUserName(dto.getCreateUserName());
            dto.setUpdateTime(dto.getCreateTime());
        }
        resultObject.setData(dto);

        BizStoreIHead bizStoreIHead = new BizStoreIHead();
        bizStoreIHead.setSid(bizStoreEHead.getHeadId());
        bizStoreIHead.setStoreEStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(bizStoreIHead);
        return resultObject;
    }

    public ResultObject redFlush(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("红冲成功"));

        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreEHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        BizStoreEHead update = new BizStoreEHead();
        update.setSid(sid);
        update.setRedFlush(CommonEnum.RED_FLUSH_ENUM.YES.getValue());
        bizStoreEHeadMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public void deleteByHeadSids(List<String> sids) {
        List<BizStoreEHead> storeEHeadByHeadSids = bizStoreEHeadMapper.getStoreEHeadByHeadSids(sids);
        if(!ObjectUtils.isEmpty(storeEHeadByHeadSids)){
            List<String> collect = storeEHeadByHeadSids.stream().map(BizStoreEHead::getSid).collect(Collectors.toList());
            bizStoreEListMapper.deleteByHeadSids(collect);
        }
        bizStoreEHeadMapper.deleteByHeadSids(sids);
    }

    public ConversionPrintOutBoundReceiptHead conversionMessageHead(BizStoreEHead storeEHeadByHeadSid, UserInfoToken userInfo) {
        ConversionPrintOutBoundReceiptHead conversionPrintOutBoundReceiptHead = new ConversionPrintOutBoundReceiptHead();
        conversionPrintOutBoundReceiptHead.setStoreENo(storeEHeadByHeadSid.getStoreENo());
        conversionPrintOutBoundReceiptHead.setContractNo(storeEHeadByHeadSid.getContractNo());
        conversionPrintOutBoundReceiptHead.setConsignee(storeEHeadByHeadSid.getConsignee());
        if(storeEHeadByHeadSid.getDeliveryDate() != null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(storeEHeadByHeadSid.getDeliveryDate());
            conversionPrintOutBoundReceiptHead.setDeliveryDate(formattedDate);
        }
        conversionPrintOutBoundReceiptHead.setProductAmountTotal(storeEHeadByHeadSid.getProductAmountTotal());
        conversionPrintOutBoundReceiptHead.setTariffPrice(storeEHeadByHeadSid.getTariffPrice());
        conversionPrintOutBoundReceiptHead.setInsuranceFee(storeEHeadByHeadSid.getInsuranceFee());
        conversionPrintOutBoundReceiptHead.setAgentFee(storeEHeadByHeadSid.getAgentFee());
        conversionPrintOutBoundReceiptHead.setNote(storeEHeadByHeadSid.getNote());
        conversionPrintOutBoundReceiptHead.setTotal(storeEHeadByHeadSid.getTotal());
        conversionPrintOutBoundReceiptHead.setCreaterUserName("制单人："+
         (storeEHeadByHeadSid.getCreaterUserName()==null?"":storeEHeadByHeadSid.getCreaterUserName()));
        List<BizMerchant> select1 = bizMerchantMapper.select(new BizMerchant() {{
            setMerchantCode(storeEHeadByHeadSid.getConsignee());
            setTradeCode(userInfo.getCompany());
        }});
        if(!org.springframework.util.CollectionUtils.isEmpty(select1)){
            conversionPrintOutBoundReceiptHead.setConsignee(select1.get(0).getMerchantNameCn());
        }
        return conversionPrintOutBoundReceiptHead;
    }

    public List<BizStoreEList> conversionMessageList(ConversionPrintOutBoundReceiptHead conversionMessageHead,BizStoreEHead storeEHeadByHeadSid, UserInfoToken userInfo) {
        List<BizStoreEList> select = bizStoreEListMapper.select(new BizStoreEList() {{
            setHeadId(storeEHeadByHeadSid.getSid());
        }});
        conversionMessageHead.setQtyDeli(select.stream().map(BizStoreEList::getQtyDeli).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        conversionMessageHead.setQtyIss(select.stream().map(BizStoreEList::getQtyIss).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        if(CollectionUtils.isNotEmpty(select)){
            for (BizStoreEList bizStoreEList : select) {
                bizStoreEList.setUnit(pCodeServiceHolder.getValue(PCodeType.UNIT, bizStoreEList.getUnit()));
            }
        }
        return select;
    }
}
