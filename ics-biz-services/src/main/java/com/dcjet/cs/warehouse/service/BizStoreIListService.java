package com.dcjet.cs.warehouse.service;
import com.dcjet.cs.warehouse.model.BizStoreEList;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.warehouse.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.warehouse.dao.BizStoreIListMapper;
import com.dcjet.cs.warehouse.mapper.BizStoreIListDtoMapper;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizStoreIListService extends BaseService<BizStoreIList> {
    @Resource
    private BizStoreIListMapper bizStoreIListMapper;
    @Resource
    private BizStoreIListDtoMapper bizStoreIListDtoMapper;
    @Override
    public Mapper<BizStoreIList> getMapper() {
        return bizStoreIListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizStoreIListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizStoreIListDto>> getListPaged(BizStoreIListParam bizStoreIListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizStoreIList bizStoreIList = bizStoreIListDtoMapper.toPo(bizStoreIListParam);
        bizStoreIList.setTradeCode(userInfo.getCompany());
        Page<BizStoreIList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizStoreIListMapper.getList(bizStoreIList));
        List<BizStoreIListDto> bizStoreIListDtos = page.getResult().stream().map(head -> {
            BizStoreIListDto dto = bizStoreIListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizStoreIListDto>> paged = ResultObject.createInstance(bizStoreIListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizStoreIListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreIListDto insert(BizStoreIListParam bizStoreIListParam, UserInfoToken userInfo) {
        BizStoreIList bizStoreIList = bizStoreIListDtoMapper.toPo(bizStoreIListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizStoreIList.setSid(sid);
        bizStoreIList.setCreateBy(userInfo.getUserNo());
        bizStoreIList.setCreateTime(new Date());
        bizStoreIList.setTradeCode(userInfo.getCompany());
        bizStoreIList.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = bizStoreIListMapper.insert(bizStoreIList);
        return  insertStatus > 0 ? bizStoreIListDtoMapper.toDto(bizStoreIList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizStoreIListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreIListDto update(BizStoreIListParam bizStoreIListParam, UserInfoToken userInfo) {
        BizStoreIList bizStoreIList = bizStoreIListMapper.selectByPrimaryKey(bizStoreIListParam.getSid());
        bizStoreIListDtoMapper.updatePo(bizStoreIListParam, bizStoreIList);
        bizStoreIList.setUpdateBy(userInfo.getUserNo());
        bizStoreIList.setUpdateTime(new Date());
        bizStoreIList.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = bizStoreIListMapper.updateByPrimaryKey(bizStoreIList);
        return update > 0 ? bizStoreIListDtoMapper.toDto(bizStoreIList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizStoreIListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizStoreIListDto> selectAll(BizStoreIListParam exportParam, UserInfoToken userInfo) {
        BizStoreIList bizStoreIList = bizStoreIListDtoMapper.toPo(exportParam);
         bizStoreIList.setTradeCode(userInfo.getCompany());
        List<BizStoreIListDto> bizStoreIListDtos = new ArrayList<>();
        List<BizStoreIList> bizStoreILists = bizStoreIListMapper.getList(bizStoreIList);
        if (CollectionUtils.isNotEmpty(bizStoreILists)) {
            bizStoreIListDtos = bizStoreILists.stream().map(head -> {
                BizStoreIListDto dto = bizStoreIListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizStoreIListDtos;
    }
    public ResultObject getListBySid(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");
        BizStoreIList bizStoreIList = bizStoreIListMapper.selectByPrimaryKey(sid);
        BizStoreIListDto dto = bizStoreIListDtoMapper.toDto(bizStoreIList);
        resultObject.setData(dto);
        return resultObject;
    }
}
