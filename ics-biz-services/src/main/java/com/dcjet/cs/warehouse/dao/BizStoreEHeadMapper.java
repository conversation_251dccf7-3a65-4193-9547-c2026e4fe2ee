package com.dcjet.cs.warehouse.dao;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizStoreEHead
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizStoreEHeadMapper extends Mapper<BizStoreEHead> {
    /**
     * 查询获取数据
     * @param bizStoreEHead
     * @return
     */
    List<BizStoreEHead> getList(BizStoreEHead bizStoreEHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    BizStoreEHead getStoreEHeadByHeadSid(String headId);
    List<BizStoreEHead> getStoreEHeadByHeadSids(List<String> sids);

    void deleteByHeadSids(List<String> sids);
}
