package com.dcjet.cs.warehouse.mapper;
import com.dcjet.cs.dto.warehouse.*;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizStoreIListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizStoreIListDto toDto(BizStoreIList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizStoreIList toPo(BizStoreIListParam param);
    /**
     * 数据库原始数据更新
     * @param bizStoreIListParam
     * @param bizStoreIList
     */
    void updatePo(BizStoreIListParam bizStoreIListParam, @MappingTarget BizStoreIList bizStoreIList);
    default void patchPo(BizStoreIListParam bizStoreIListParam, BizStoreIList bizStoreIList) {
        // TODO 自行实现局部更新
    }
}
