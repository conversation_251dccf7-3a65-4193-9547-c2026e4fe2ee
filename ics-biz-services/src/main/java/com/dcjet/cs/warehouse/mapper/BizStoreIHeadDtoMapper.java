package com.dcjet.cs.warehouse.mapper;
import com.dcjet.cs.dto.warehouse.*;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizStoreIHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizStoreIHeadDto toDto(BizStoreIHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizStoreIHead toPo(BizStoreIHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizStoreIHeadParam
     * @param bizStoreIHead
     */
    void updatePo(BizStoreIHeadParam bizStoreIHeadParam, @MappingTarget BizStoreIHead bizStoreIHead);
    default void patchPo(BizStoreIHeadParam bizStoreIHeadParam, BizStoreIHead bizStoreIHead) {
        // TODO 自行实现局部更新
    }
}
