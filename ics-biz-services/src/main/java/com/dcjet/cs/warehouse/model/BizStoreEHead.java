package com.dcjet.cs.warehouse.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Setter
@Getter
@Table(name = "t_biz_store_e_head")
public class BizStoreEHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "create_user_name")
	private  String createUserName;
	/**
     * 创建人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 出库回单编号
     */
	@Column(name = "store_e_no")
	private  String storeENo;
	/**
     * 合同号
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 进货单号
     */
	@Column(name = "purchase_order_no")
	private  String purchaseOrderNo;
	/**
     * 购销合同号
     */
	@Column(name = "pur_sale_contract_no")
	private  String purSaleContractNo;
	/**
     * 提货人
     */
	@Column(name = "consignee")
	private  String consignee;
	/**
     * 出库日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "delivery_date")
	private  Date deliveryDate;
	/**
     * 金额
     */
	@Column(name = "product_amount_total")
	private  BigDecimal productAmountTotal;
	/**
     * 关税
     */
	@Column(name = "tariff_price")
	private  BigDecimal tariffPrice;
	/**
     * 保险费用
     */
	@Column(name = "insurance_fee")
	private  BigDecimal insuranceFee;
	/**
     * 代理费用
     */
	@Column(name = "agent_fee")
	private  BigDecimal agentFee;
	/**
     * 业务日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "business_date")
	private  Date businessDate;
	/**
     * 发送财务系统
     */
	@Column(name = "send_finance")
	private  String sendFinance;
	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;
	/**
     * 出库数据状态
     */
	@Column(name = "status")
	private  String status;
	@Column(name = "head_id")
	private  String headId;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	@Column(name = "red_flush")
	private  String redFlush;

	@Transient
	private String createrBy;

	@Transient
	private String createrUserName;

	@Transient
	private Date createrTime;

	public BigDecimal getTotal() {
		BigDecimal total = BigDecimal.ZERO;

		if (this.getProductAmountTotal() != null) {
			total = total.add(this.getProductAmountTotal());
		}

		if (this.getTariffPrice() != null) {
			total = total.add(this.getTariffPrice());
		}

		if (this.getInsuranceFee() != null) {
			total = total.add(this.getInsuranceFee());
		}

		if (this.getAgentFee() != null) {
			total = total.add(this.getAgentFee());
		}

		return total;
	}
}
