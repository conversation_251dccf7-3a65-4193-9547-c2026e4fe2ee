package com.dcjet.cs.warehouse.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import com.dcjet.cs.dec.dao.BizIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizIncomingGoodsListMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsHeadDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.Digit2EnUtils;
import com.dcjet.cs.util.RMBConverterUtil;
import com.dcjet.cs.warehouse.dao.BizStoreEHeadMapper;
import com.dcjet.cs.warehouse.dao.BizStoreIListMapper;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.warehouse.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.warehouse.dao.BizStoreIHeadMapper;
import com.dcjet.cs.warehouse.mapper.BizStoreIHeadDtoMapper;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizStoreIHeadService extends BaseService<BizStoreIHead> {
    @Resource
    private BizStoreIHeadMapper bizStoreIHeadMapper;
    @Resource
    private BizStoreIHeadDtoMapper bizStoreIHeadDtoMapper;
    @Override
    public Mapper<BizStoreIHead> getMapper() {
        return bizStoreIHeadMapper;
    }
    @Resource
    private BizStoreEHeadService bizStoreEHeadService;
    @Resource
    private BizStoreEListService bizStoreEListService;
    @Resource
    private BizStoreIListMapper bizStoreIListMapper;
    @Resource
    private BizIncomingGoodsHeadMapper tBizIncomingGoodsHeadMapper;
    @Resource
    private BizIncomingGoodsListMapper bizIncomingGoodsListMapper;
    @Resource
    private BizIncomingGoodsHeadDtoMapper tBizIncomingGoodsHeadDtoMapper;
    @Resource
    private BizStoreEHeadMapper bizStoreEHeadMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BizIncomingGoodsHeadMapper bizIncomingGoodsHeadMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizStoreIHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizStoreIHeadDto>> getListPaged(BizStoreIHeadParam bizStoreIHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizStoreIHead bizStoreIHead = bizStoreIHeadDtoMapper.toPo(bizStoreIHeadParam);
        bizStoreIHead.setTradeCode(userInfo.getCompany());
        Page<BizStoreIHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizStoreIHeadMapper.getList(bizStoreIHead));
        List<BizStoreIHeadDto> bizStoreIHeadDtos = page.getResult().stream().map(head -> {
            BizStoreIHeadDto dto = bizStoreIHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizStoreIHeadDto>> paged = ResultObject.createInstance(bizStoreIHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizStoreIHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreIHeadDto insert(BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        BizStoreIHead bizStoreIHead = bizStoreIHeadDtoMapper.toPo(bizStoreIHeadParam);
        bizStoreIHead.setTradeCode(userInfo.getCompany());
        int check = bizStoreIHeadMapper.checkKey(bizStoreIHead);
        if(check>0){
            throw new ErrorException(400, "入库回单编号已经存在！");
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizStoreIHead.setSid(sid);
        bizStoreIHead.setApprStatus("0");
        bizStoreIHead.setCreateBy(userInfo.getUserNo());
        bizStoreIHead.setCreateTime(new Date());
        bizStoreIHead.setCreateUserName(userInfo.getUserName());
        bizStoreIHead.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = bizStoreIHeadMapper.insert(bizStoreIHead);
        return  insertStatus > 0 ? bizStoreIHeadDtoMapper.toDto(bizStoreIHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizStoreIHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreIHeadDto update(BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(bizStoreIHeadParam.getSid());
        boolean b = false;
        if(null != bizStoreIHead.getSellingRate()){
            if(bizStoreIHead.getSellingRate().compareTo(bizStoreIHeadParam.getSellingRate()) != 0){
                b = true;
            }
        }else {
            b= true;
        }

        bizStoreIHeadDtoMapper.updatePo(bizStoreIHeadParam, bizStoreIHead);
        bizStoreIHead.setUpdateBy(userInfo.getUserNo());
        bizStoreIHead.setUpdateTime(new Date());
        bizStoreIHead.setUpdateUserName(userInfo.getUserName());

        int check = bizStoreIHeadMapper.checkKey(bizStoreIHead);
        if(check>0){
            throw new ErrorException(400, "入库回单编号已经存在！");
        }
        // 更新数据
        int update = bizStoreIHeadMapper.updateByPrimaryKey(bizStoreIHead);
        if(b){
            BizStoreIList bizStoreIList = new BizStoreIList();
            bizStoreIList.setHeadId(bizStoreIHeadParam.getSid());
            List<BizStoreIList> list = bizStoreIListMapper.getList(bizStoreIList);
            if(!ObjectUtils.isEmpty(list)){
                for (BizStoreIList storeIList : list) {
//                    if(null != storeIList.getCurrTotalPrice()){
                        storeIList.setRmbTotalPrice(storeIList.getCurrTotalPrice().multiply(bizStoreIHeadParam.getSellingRate()).setScale(2,RoundingMode.HALF_UP));
                        storeIList.setRmbPrice(storeIList.getRmbTotalPrice().divide(storeIList.getQty(), 5, RoundingMode.HALF_UP));
//                    }
                    bizStoreIListMapper.updateByPrimaryKeySelective(storeIList);
                }
            }
        }
        return update > 0 ? bizStoreIHeadDtoMapper.toDto(bizStoreIHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizStoreIHeadMapper.deleteBySids(sids);
        bizStoreEHeadService.deleteByHeadSids(sids);
        bizStoreIListMapper.deleteByHeadSids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizStoreIHeadDto> selectAll(BizStoreIHeadParam exportParam, UserInfoToken userInfo) {
        BizStoreIHead bizStoreIHead = bizStoreIHeadDtoMapper.toPo(exportParam);
         bizStoreIHead.setTradeCode(userInfo.getCompany());
        List<BizStoreIHeadDto> bizStoreIHeadDtos = new ArrayList<>();
        List<BizStoreIHead> bizStoreIHeads = bizStoreIHeadMapper.getList(bizStoreIHead);
        if (CollectionUtils.isNotEmpty(bizStoreIHeads)) {
            bizStoreIHeadDtos = bizStoreIHeads.stream().map(head -> {
                BizStoreIHeadDto dto = bizStoreIHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizStoreIHeadDtos;
    }

    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizStoreIHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizStoreIHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizStoreIHead); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizStoreIHead bizStoreIHead) {
        BizStoreIHead update = new BizStoreIHead();
        update.setSid(bizStoreIHead.getSid());
        update.setApprStatus(bizStoreIHead.getApprStatus());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(update);
    }

    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizStoreIHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizStoreIHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizStoreIHead update = new BizStoreIHead();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(update);
        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.getStoreEHeadByHeadSid(sid);
        if(!ObjectUtils.isEmpty(bizStoreEHead)){
            bizStoreEHead.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
            bizStoreEHeadMapper.updateByPrimaryKeySelective(bizStoreEHead);
        }

        return resultObject;
    }
    public ResultObject confirmStatus(BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(bizStoreIHeadParam.getSid());
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizStoreIHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizStoreIHead update = bizStoreIHeadDtoMapper.toPo(bizStoreIHeadParam);

//        BizStoreIHead update = new BizStoreIHead();
//        bizStoreIHead.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsNext("1");
        update.setStoreEStatus("0");

        List<ExpenseIList> costIList = bizStoreIHeadMapper.getCostIList(bizStoreIHead.getPurchaseNoMark());
        if(!ObjectUtils.isEmpty(costIList)){
            BigDecimal tariffPrice = costIList.stream().filter(x -> x.getCostName().contains("关税")).map(ExpenseIList::getExpenseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            update.setTariffPrice(tariffPrice);
            BigDecimal vatPrice = costIList.stream().filter(x -> x.getCostName().contains("增值税")).map(ExpenseIList::getExpenseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            update.setVatPrice(vatPrice);
//            BigDecimal insuranceFee = costIList.stream().filter(x -> x.getCostName().contains("保险费")).map(ExpenseIList::getNoTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//            update.setInsuranceFee(insuranceFee);
        }

        bizStoreIHeadMapper.updateByPrimaryKeySelective(update);
        BizStoreIHeadDto dto = bizStoreIHeadDtoMapper.toDto(update);
        if(StringUtils.isBlank(dto.getUpdateUserName())){
            dto.setUpdateUserName(dto.getCreateUserName());
            dto.setUpdateTime(dto.getCreateTime());
        }

        BizStoreIList bizStoreIList = new BizStoreIList();
        bizStoreIList.setHeadId(bizStoreIHeadParam.getSid());
        List<BizStoreIList> iLists = bizStoreIListMapper.getList(bizStoreIList);

        //出库回单
        BizStoreEHeadParam bizStoreEHeadParam = new BizStoreEHeadParam();
        bizStoreEHeadParam.setHeadId(bizStoreIHeadParam.getSid());
        bizStoreEHeadParam.setStoreENo(bizStoreIHead.getStoreINo());
        bizStoreEHeadParam.setContractNo(bizStoreIHead.getContractNo());
        bizStoreEHeadParam.setPurchaseOrderNo(bizStoreIHead.getPurchaseOrderNo());
        bizStoreEHeadParam.setProductAmountTotal(bizStoreIHead.getProductAmountTotal());
        bizStoreEHeadParam.setTariffPrice(bizStoreIHead.getTariffPrice());
        bizStoreEHeadParam.setInsuranceFee(bizStoreIHead.getInsuranceFee());
        bizStoreEHeadParam.setAgentFee(bizStoreIHead.getAgentFee());
        bizStoreEHeadParam.setBusinessDate(new Date());
        bizStoreEHeadParam.setDeliveryDate(new Date());
        bizStoreEHeadParam.setSendFinance("0");
        bizStoreEHeadParam.setStatus("0");
        bizStoreEHeadParam.setRedFlush("1");
        BizStoreEHeadDto insertE = bizStoreEHeadService.insert(bizStoreEHeadParam, userInfo);
        if(!ObjectUtils.isEmpty(insertE) && !ObjectUtils.isEmpty(iLists)){
            if(StringUtils.isNotBlank(insertE.getSid())){
                for (BizStoreIList iList : iLists) {
                    BizStoreEListParam listParam = new BizStoreEListParam();
                    listParam.setHeadId(insertE.getSid());
                    listParam.setGNameNo(iList.getGName());
                    listParam.setUnit(iList.getUnit());
                    listParam.setQtyDeli(iList.getQty());
                    listParam.setQtyIss(iList.getQty());
                    bizStoreEListService.insert(listParam,userInfo);
                }
            }
        }
        dto.setEHeadId(insertE.getSid());
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject redFlush(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("红冲成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        BizStoreIHead update = new BizStoreIHead();
        update.setSid(sid);
        update.setRedFlush(CommonEnum.RED_FLUSH_ENUM.YES.getValue());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizStoreIHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许退单");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(bizStoreIHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
//        BizStoreIHead update = new BizStoreIHead();
//        update.setSid(sid);
        bizStoreIHead.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizStoreIHead.setConfirmTime(null);
        bizStoreIHead.setIsNext("0");
        bizStoreIHead.setStoreEStatus(null);
        int i = bizStoreIHeadMapper.updateByPrimaryKeySelective(bizStoreIHead);
        if(i > 0){
            bizStoreEHeadService.deleteByHeadSids(Arrays.asList(sid));
        }
        BizStoreIHeadDto dto = bizStoreIHeadDtoMapper.toDto(bizStoreIHead);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject checkIsNextModule(BizStoreIHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        String sid = params.getSid();
        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        StoreIHeadIsNextDto isNextDto = new StoreIHeadIsNextDto();
        if (bizStoreIHead.getUpdateTime() != null){
            isNextDto.setShowBody(1);
        }else {
            isNextDto.setShowBody(0);
        }
        if (bizStoreIHead.getConfirmTime() != null){
            isNextDto.setShowBodyIsNext(1);
        }else {
            isNextDto.setShowBodyIsNext(0);
        }
        if (StringUtils.isNotBlank(bizStoreIHead.getIsNext()) && bizStoreIHead.getIsNext().equals("1")){
            isNextDto.setShowBodyStoreEHead(1);
        }else {
            isNextDto.setShowBodyStoreEHead(0);
        }
        resultObject.setData(isNextDto);
        return resultObject;
    }

    public ResultObject generateIOrder(BizIStoreExtractParams params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "新增成功!");
        Assert.notEmpty(params.getSids(),"请选择要生成的进货信息数据！");
        List<BizIncomingGoodsHead> listBySids = tBizIncomingGoodsHeadMapper.getListBySids(params.getSids());
        Assert.notEmpty(listBySids,"请选择要生成的进货信息数据！");
        String sid = UUID.randomUUID().toString();
        if(!ObjectUtils.isEmpty(listBySids)){
            List<BizIncomingGoodsList> listList =bizIncomingGoodsListMapper.getListByHeadSids(params.getSids());
            List<BizStoreIList> bizStoreILists = new ArrayList<>();
            BigDecimal sum = BigDecimal.ZERO;
            for (BizIncomingGoodsList bizIncomingGoodsList : listList) {
                BizStoreIList storeIList = new BizStoreIList();
                String storeIListSid = UUID.randomUUID().toString();
                storeIList.setSid(storeIListSid);
                storeIList.setHeadId(sid);
                storeIList.setGName(bizIncomingGoodsList.getGoodsName());
                storeIList.setSpecifications(bizIncomingGoodsList.getProductModel());
                storeIList.setQty(bizIncomingGoodsList.getInQuantity());
                storeIList.setUnit(bizIncomingGoodsList.getUnit());
                storeIList.setCurrPrice(bizIncomingGoodsList.getUnitPrice());
                storeIList.setCurrTotalPrice(bizIncomingGoodsList.getUnitPrice().multiply(bizIncomingGoodsList.getQuantity()).setScale(2, RoundingMode.HALF_UP));

                storeIList.setCreateBy(userInfo.getUserNo());
                storeIList.setCreateTime(new Date());
                storeIList.setTradeCode(userInfo.getCompany());

                bizStoreIListMapper.insert(storeIList);
                bizStoreILists.add(storeIList);
                sum = sum.add(storeIList.getCurrTotalPrice());
            }

            BizStoreIHead insert = new BizStoreIHead();
            insert.setSid(sid);
            insert.setStoreINo(listBySids.get(0).getPurchaseNo());
            insert.setContractNo(listBySids.get(0).getContractNo());
            insert.setMerchantCode(listBySids.get(0).getSupplier());
            insert.setTariffPrice(new BigDecimal(0));
            insert.setVatPrice(new BigDecimal(0));
            insert.setInsuranceFee(new BigDecimal(0));
            insert.setAgentFee(new BigDecimal(0));
            if(!ObjectUtils.isEmpty(listList)){
                insert.setForeignCurrPrice(sum);
                insert.setCurr(listList.get(0).getCurr());
                insert.setPurchaseNoMark(listBySids.stream().map(BizIncomingGoodsHead::getPurchaseNo).collect(Collectors.joining(";")));
                List<ExpenseIList> costIList = bizStoreIHeadMapper.getCostIList(insert.getPurchaseNoMark());
                if(!ObjectUtils.isEmpty(costIList)){
                    BigDecimal tariffPrice = costIList.stream().filter(x -> x.getCostName().contains("关税")).map(ExpenseIList::getExpenseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    insert.setTariffPrice(tariffPrice);
                    BigDecimal vatPrice = costIList.stream().filter(x -> x.getCostName().contains("增值税")).map(ExpenseIList::getExpenseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    insert.setVatPrice(vatPrice);
                    BigDecimal insuranceFee = costIList.stream().filter(x -> x.getCostName().contains("保险费")).map(ExpenseIList::getNoTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    insert.setInsuranceFee(insuranceFee);
                }
            }
            insert.setPriceTerm(listBySids.get(0).getPriceTerm());


            insert.setBusinessDate(new Date());
            insert.setSendFinance("0");
            insert.setRedFlush("1");
            insert.setStatus("0");
            insert.setApprStatus("0");

            insert.setCreateBy(userInfo.getUserNo());
            insert.setCreateTime(new Date());
            insert.setCreateUserName(userInfo.getUserName());
            insert.setTradeCode(userInfo.getCompany());
            bizStoreIHeadMapper.insert(insert);
            insert.setCreaterBy(insert.getCreateBy());
            insert.setCreaterTime(insert.getCreateTime());
            insert.setCreaterUserName(insert.getCreateUserName());

            resultObject.setData(bizStoreIHeadDtoMapper.toDto(insert));
        }
        return resultObject;
    }
    public ResultObject<List<BizIncomingGoodsHeadDto>> getBizIncomingGoodsHeadListPaged(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(tBizIncomingGoodsHeadParam);
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        tBizIncomingGoodsHead.setDataState("1");
        Page<BizIncomingGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> tBizIncomingGoodsHeadMapper.getListToStore( tBizIncomingGoodsHead));
        List<String> collect = page.getResult().stream().map(BizIncomingGoodsHead::getId).collect(Collectors.toList());
        List<BizIncomingGoodsList> listByHeadSids = new ArrayList<>();
        if(!ObjectUtils.isEmpty(collect)){
            listByHeadSids = bizIncomingGoodsListMapper.getListByHeadSids(collect);
        }
        // 将PO转为DTO返回给前端
        List<BizIncomingGoodsList> finalListByHeadSids = listByHeadSids;
        List<BizIncomingGoodsHeadDto> tBizIncomingGoodsHeadDtoList = page.getResult().stream().map(head ->{
                    BizIncomingGoodsHeadDto dto = tBizIncomingGoodsHeadDtoMapper.toDto(head);
                    if(finalListByHeadSids.size() > 0){
                        dto.setInvoiceNo(finalListByHeadSids.stream().filter(x -> x.getHeadId().equals(head.getId())).map(BizIncomingGoodsList::getInvoiceNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("，")));
                        dto.setQtyToStore(finalListByHeadSids.stream().filter(x -> x.getHeadId().equals(head.getId())).map(BizIncomingGoodsList::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                        dto.setTotalPriceToStore(finalListByHeadSids.stream().filter(x -> x.getHeadId().equals(head.getId())).map(BizIncomingGoodsList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    return dto;
                }).collect(Collectors.toList());
        return ResultObject.createInstance(tBizIncomingGoodsHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }
    public List<Map<String,String>> getSupplierList(UserInfoToken userInfoToken) {
        return bizStoreIHeadMapper.getOrderSupplierList(userInfoToken.getCompany());
    }

    public ResponseEntity printReceiptContainer(BizStoreIHead param, UserInfoToken userInfo) throws Exception {
        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(param.getSid());

        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);


        if(bizStoreIHead == null){
            throw new ErrorException(400,"入库回单不存在！");
        }else {
            convertHeadToPrint(bizStoreIHead,bizMerchantMap);
        }

        List<BizStoreIList> bizStoreIList=bizStoreIListMapper.getSumByHeadId(bizStoreIHead.getSid());
        if (CollectionUtils.isNotEmpty(bizStoreIList) ){
            bizStoreIHead.setGName(bizStoreIList.get(0).getGName());
            bizStoreIHead.setQtyStr(NumberFormatterUtils.formatNumber(bizStoreIList.get(0).getQty()));
            bizStoreIHead.setCurrPriceStr(NumberFormatterUtils.formatNumber(bizStoreIList.get(0).getCurrPrice()));
            bizStoreIHead.setRmbPriceStr(NumberFormatterUtils.formatNumber(bizStoreIList.get(0).getRmbPrice()));
            bizStoreIHead.setCurrTotalPriceStr(NumberFormatterUtils.formatNumber(bizStoreIList.get(0).getCurrTotalPrice()));
            bizStoreIHead.setRmbTotalPriceStr(NumberFormatterUtils.formatNumber(bizStoreIList.get(0).getRmbTotalPrice()));
        }

        String tempName = "入库回单-辅料.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());

        String outName = xdoi18n.XdoI18nUtil.t("入库回单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(bizStoreIHead),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    /**
     * 创建商户映射表，处理重复key的情况
     * @param bizMerchants 商户列表
     * @return 商户编码到中文名称的映射
     */
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    /**
     * 安全地获取商户中文名称
     * @param bizMerchantMap 商户映射表
     * @param merchantCode 商户编码
     * @return 商户中文名称，如果不存在则返回空字符串
     */
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }


    private void convertHeadToPrint(BizStoreIHead bizStoreIHead, Map<String, String> bizMerchantMap){
        bizStoreIHead.setStoreINo("NO."+bizStoreIHead.getStoreINo()+"01");
        bizStoreIHead.setMerchantCode("来货公司："+getMerchantNameSafely(bizMerchantMap,bizStoreIHead.getMerchantCode()));
        bizStoreIHead.setContractNo("合同号："+bizStoreIHead.getContractNo());
        bizStoreIHead.setInvoiceNo("发票号："+(bizStoreIHead.getInvoiceNo()==null?"":bizStoreIHead.getInvoiceNo()));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(bizStoreIHead.getCreateTime());
        bizStoreIHead.setInsertTimeFrom("制单日期："+formattedDate);
        bizStoreIHead.setCreaterBy("制单："+bizStoreIHead.getCreateUserName());
        String priceTerm = bizIncomingGoodsHeadMapper.getPriceTermsByCode(bizStoreIHead.getTradeCode(),bizStoreIHead.getPriceTerm());
        bizStoreIHead.setPriceTerm("价格条款："+(priceTerm==null?"":priceTerm));
        bizStoreIHead.setCurr("保险费"+bizStoreIHead.getCurr()+"金额:"+NumberFormatterUtils.formatNumber(bizStoreIHead.getInsuranceFeeCurr()));
        bizStoreIHead.setSellingRateStr("卖出价："+NumberFormatterUtils.formatNumber(bizStoreIHead.getSellingRate()));
        bizStoreIHead.setCnTotalPirce("大       写："+ RMBConverterUtil.convertToRMBTraditional(bizStoreIHead.getTotalPrice().toString()));

        bizStoreIHead.setTariffPriceStr(NumberFormatterUtils.formatNumber(bizStoreIHead.getTariffPrice()));
        bizStoreIHead.setVatPriceStr(NumberFormatterUtils.formatNumber(bizStoreIHead.getVatPrice()));
        bizStoreIHead.setAgentFeeStr(NumberFormatterUtils.formatNumber(bizStoreIHead.getAgentFee()));
        bizStoreIHead.setInsuranceFeeStr(NumberFormatterUtils.formatNumber(bizStoreIHead.getInsuranceFee()));
        bizStoreIHead.setCostAmountTotalStr(NumberFormatterUtils.formatNumber(bizStoreIHead.getCostAmountTotal()));
        bizStoreIHead.setTotalPriceStr(NumberFormatterUtils.formatNumber(bizStoreIHead.getTotalPrice()));
    }

    public ResultObject getOrderSupplierList(BizStoreIHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizStoreIHeadMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
}
