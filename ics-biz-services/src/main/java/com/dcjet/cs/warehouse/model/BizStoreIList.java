package com.dcjet.cs.warehouse.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Setter
@Getter
@Table(name = "t_biz_store_i_list")
public class BizStoreIList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 表头主键
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "create_user_name")
	private  String createUserName;
	/**
     * 创建人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 商品名称
     */
	@Column(name = "g_name")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 规格
     */
	@Column(name = "specifications")
	private  String specifications;
	/**
     * 数量
     */
	@Column(name = "qty")
	private  BigDecimal qty;
	/**
     * 单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 外币单价
     */
	@Column(name = "curr_price")
	private  BigDecimal currPrice;
	/**
     * 人民币单价
     */
	@Column(name = "rmb_price")
	private  BigDecimal rmbPrice;
	/**
     * 外币货价
     */
	@Column(name = "curr_total_price")
	private  BigDecimal currTotalPrice;
	/**
     * 人民币货价
     */
	@Column(name = "rmb_total_price")
	private  BigDecimal rmbTotalPrice;
}
