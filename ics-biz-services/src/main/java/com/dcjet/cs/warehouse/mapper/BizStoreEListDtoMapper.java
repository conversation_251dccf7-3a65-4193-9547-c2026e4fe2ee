package com.dcjet.cs.warehouse.mapper;
import com.dcjet.cs.dto.warehouse.*;
import com.dcjet.cs.warehouse.model.BizStoreEList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizStoreEListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizStoreEListDto toDto(BizStoreEList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizStoreEList toPo(BizStoreEListParam param);
    /**
     * 数据库原始数据更新
     * @param bizStoreEListParam
     * @param bizStoreEList
     */
    void updatePo(BizStoreEListParam bizStoreEListParam, @MappingTarget BizStoreEList bizStoreEList);
    default void patchPo(BizStoreEListParam bizStoreEListParam, BizStoreEList bizStoreEList) {
        // TODO 自行实现局部更新
    }
}
