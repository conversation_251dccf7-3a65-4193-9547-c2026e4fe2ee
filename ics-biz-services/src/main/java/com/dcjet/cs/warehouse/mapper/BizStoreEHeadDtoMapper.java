package com.dcjet.cs.warehouse.mapper;
import com.dcjet.cs.dto.warehouse.*;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizStoreEHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizStoreEHeadDto toDto(BizStoreEHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizStoreEHead toPo(BizStoreEHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizStoreEHeadParam
     * @param bizStoreEHead
     */
    void updatePo(BizStoreEHeadParam bizStoreEHeadParam, @MappingTarget BizStoreEHead bizStoreEHead);
    default void patchPo(BizStoreEHeadParam bizStoreEHeadParam, BizStoreEHead bizStoreEHead) {
        // TODO 自行实现局部更新
    }
}
