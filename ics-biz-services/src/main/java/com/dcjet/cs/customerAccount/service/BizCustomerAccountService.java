package com.dcjet.cs.customerAccount.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizExportGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsListMapper;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dec.model.BizExportGoodsList;
import com.dcjet.cs.dto.warehouse.BizStoreIHeadParam;
import com.dcjet.cs.importedCigarettes.mapper.BizIPlanListDtoMapper;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsHeadMapper;
import com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsListMapper;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.customerAccount.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountMapper;
import com.dcjet.cs.customerAccount.mapper.BizCustomerAccountDtoMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccount;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.file.XdoFileHandler;
import jdk.nashorn.internal.runtime.options.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-6-16
 */
@Service
@Slf4j
public class BizCustomerAccountService extends BaseService<BizCustomerAccount> {
    @Resource
    private BizCustomerAccountMapper bizCustomerAccountMapper;
    @Resource
    private BizCustomerAccountDtoMapper bizCustomerAccountDtoMapper;
    @Resource
    private AttachedService attachedService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Override
    public Mapper<BizCustomerAccount> getMapper() {
        return bizCustomerAccountMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizCustomerAccountParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizCustomerAccountDto>> getListPaged(BizCustomerAccountParam bizCustomerAccountParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizCustomerAccount bizCustomerAccount = bizCustomerAccountDtoMapper.toPo(bizCustomerAccountParam);
        bizCustomerAccount.setTradeCode(userInfo.getCompany());
        Page<BizCustomerAccount> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountMapper.getList(bizCustomerAccount));
        List<BizCustomerAccountDto> bizCustomerAccountDtos = page.getResult().stream().map(head -> {
            BizCustomerAccountDto dto = bizCustomerAccountDtoMapper.toDto(head);
            if("1".equals(dto.getPurchaseMark())){
                List<BizINonStateAuxmatAggrContractList> byHeadid = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(dto.getPurchaseNoMark());
                dto.setCurrPrice(byHeadid.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }else if("2".equals(dto.getPurchaseMark())){
                List<BizNonIncomingGoodsList> byHeadid2 = BizNonIncomingGoodsListMapper.getListByHeadSids(Arrays.asList(dto.getPurchaseNoMark()));
                dto.setCurrPrice(byHeadid2.stream().map(BizNonIncomingGoodsList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizCustomerAccountDto>> paged = ResultObject.createInstance(bizCustomerAccountDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizCustomerAccountParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountDto insert(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        BizCustomerAccount bizCustomerAccount = bizCustomerAccountDtoMapper.toPo(bizCustomerAccountParam);
        /**
         * 规范固定字段
         */
        bizCustomerAccount.setTradeCode(userInfo.getCompany());
        int check = bizCustomerAccountMapper.checkKey(bizCustomerAccount);
        if(check>0){
            throw new ErrorException(400, "结算单号已经存在！");
        }
        String sid = UUID.randomUUID().toString();
        bizCustomerAccount.setSid(sid);
        bizCustomerAccount.setVersionNo("1");
        bizCustomerAccount.setTradeCode(userInfo.getCompany());
        bizCustomerAccount.setCreateBy(userInfo.getUserNo());
        bizCustomerAccount.setCreateTime(new Date());
        bizCustomerAccount.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = bizCustomerAccountMapper.insert(bizCustomerAccount);
        return  insertStatus > 0 ? bizCustomerAccountDtoMapper.toDto(bizCustomerAccount) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizCustomerAccountParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountDto update(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
        bizCustomerAccountDtoMapper.updatePo(bizCustomerAccountParam, bizCustomerAccount);

        bizCustomerAccount.setUpdateBy(userInfo.getUserNo());
        bizCustomerAccount.setUpdateTime(new Date());
        bizCustomerAccount.setUpdateUserName(userInfo.getUserName());

        int check = bizCustomerAccountMapper.checkKey(bizCustomerAccount);
        if(check>0){
            throw new ErrorException(400, "结算单号已经存在！");
        }
        // 更新数据
        int update = bizCustomerAccountMapper.updateByPrimaryKey(bizCustomerAccount);
        return update > 0 ? bizCustomerAccountDtoMapper.toDto(bizCustomerAccount) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizCustomerAccountMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizCustomerAccountDto> selectAll(BizCustomerAccountParam exportParam, UserInfoToken userInfo) {
        BizCustomerAccount bizCustomerAccount = bizCustomerAccountDtoMapper.toPo(exportParam);
         bizCustomerAccount.setTradeCode(userInfo.getCompany());
        List<BizCustomerAccountDto> bizCustomerAccountDtos = new ArrayList<>();
        List<BizCustomerAccount> bizCustomerAccounts = bizCustomerAccountMapper.getList(bizCustomerAccount);
        if (CollectionUtils.isNotEmpty(bizCustomerAccounts)) {
            bizCustomerAccountDtos = bizCustomerAccounts.stream().map(head -> {
                BizCustomerAccountDto dto = bizCustomerAccountDtoMapper.toDto(head);
                if("1".equals(dto.getPurchaseMark())){
                    List<BizINonStateAuxmatAggrContractList> byHeadid = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(dto.getPurchaseNoMark());
                    dto.setCurrPrice(byHeadid.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }else if("2".equals(dto.getPurchaseMark())){
                    List<BizNonIncomingGoodsList> byHeadid2 = BizNonIncomingGoodsListMapper.getListByHeadSids(Arrays.asList(dto.getPurchaseNoMark()));
                    dto.setCurrPrice(byHeadid2.stream().map(BizNonIncomingGoodsList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return bizCustomerAccountDtos;
    }

    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccount == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccount.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizCustomerAccount.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizCustomerAccount); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizCustomerAccount bizCustomerAccount) {
        BizCustomerAccount update = new BizCustomerAccount();
        update.setSid(bizCustomerAccount.getSid());
        update.setApprStatus(bizCustomerAccount.getApprStatus());
        bizCustomerAccountMapper.updateByPrimaryKeySelective(update);
    }

    public ResultObject confirmStatus(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
        if (bizCustomerAccount == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if(StringUtils.isBlank(bizCustomerAccount.getAccountNo()) || StringUtils.isBlank(bizCustomerAccount.getContractNo())
                || StringUtils.isBlank(bizCustomerAccount.getCustomer()) || StringUtils.isBlank(bizCustomerAccount.getCurr()) || null == bizCustomerAccount.getExchangeRate()
                || null == bizCustomerAccount.getGoodsPrice() || null == bizCustomerAccount.getBusinessDate() || StringUtils.isBlank(bizCustomerAccount.getProducrSome())){
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不不完整，请完善数据");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccount.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
//        BizCustomerAccount update = bizCustomerAccountDtoMapper.toPo(bizCustomerAccountParam);
        bizCustomerAccount.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizCustomerAccount.setConfirmTime(new Date());
        bizCustomerAccount.setIsConfirm("1");
        bizCustomerAccountMapper.updateByPrimaryKeySelective(bizCustomerAccount);
        BizCustomerAccountDto dto = bizCustomerAccountDtoMapper.toDto(bizCustomerAccount);
        resultObject.setData(dto);
        return resultObject;
    }
    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccount == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccount.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许退单");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(bizCustomerAccount.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
        BizCustomerAccount update = new BizCustomerAccount();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizCustomerAccountMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccount == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccount.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizCustomerAccount.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizCustomerAccount update = new BizCustomerAccount();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizCustomerAccountMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject redFlush(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("红冲成功"));

        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccount == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        BizCustomerAccount update = new BizCustomerAccount();
        update.setSid(sid);
        update.setRedFlush(CommonEnum.RED_FLUSH_ENUM.YES.getValue());
        bizCustomerAccountMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }


    public ResultObject checkIdNotCancel(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        if (StringUtils.isBlank(bizCustomerAccountParam.getSid())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要复制数据！"));
        }
        List<String> sids = bizCustomerAccountMapper.checkIdNotCancel(bizCustomerAccountParam.getSid());
        if (CollectionUtils.isNotEmpty((sids))){
            resultObject.setSuccess(false);
            return resultObject;
        }
        return resultObject;
    }

    public ResultObject copyVersion(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("版本复制成功"));
        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
        BizCustomerAccount maxVersionNoData = bizCustomerAccountMapper.getMaxVersionNoByContract(bizCustomerAccount);
        if (maxVersionNoData == null) {
            throw new ErrorException(400, XdoI18nUtil.t("查询不到有效数据，请刷新后再试！"));
        }
        Integer maxVersionNoInt = Integer.valueOf(maxVersionNoData.getVersionNo()) + 1;
        //新表头id
        String sid = UUID.randomUUID().toString();
        invalidate(bizCustomerAccount.getSid(),userInfo);

        bizCustomerAccount.setSid(sid);
        bizCustomerAccount.setCreateTime(new Date());
        bizCustomerAccount.setCreateBy(userInfo.getUserNo());
        bizCustomerAccount.setCreateUserName(userInfo.getUserName());
        bizCustomerAccount.setUpdateBy(null);
        bizCustomerAccount.setUpdateUserName(null);
        bizCustomerAccount.setUpdateTime(null);
        bizCustomerAccount.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizCustomerAccount.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        bizCustomerAccount.setVersionNo(maxVersionNoInt.toString());
        bizCustomerAccount.setConfirmTime(null);
        bizCustomerAccountMapper.insert(bizCustomerAccount);

        List<Attached> attachedList = bizCustomerAccountMapper.getAttachmentFile(bizCustomerAccountParam.getSid());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(sid);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制计划表头【"+bizCustomerAccount.getAccountNo()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }
        return result;
    }

    public ResponseEntity print(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) throws Exception {
        BizCustomerAccount bizCustomerAccount = bizCustomerAccountMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
        if(bizCustomerAccount == null){
            throw new ErrorException(400,"客户结算单不存在！");
        }
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        if(!ObjectUtils.isEmpty(bizMerchants)){
            Optional<BizMerchant> first = bizMerchants.stream().filter(x -> x.getMerchantCode().equals(bizCustomerAccount.getCustomer())).findFirst();
            first.ifPresent(merchant -> bizCustomerAccount.setCustomer(merchant.getMerchantNameCn()));
        }
        String tempName = "";
        bizCustomerAccount.setAgentFeeRateStr(NumberFormatterUtils.formatNumber(bizCustomerAccount.getAgentFeeRate()) + "%");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        if(StringUtils.equals(bizCustomerAccount.getBusinessType(),"6")){
            tempName = "biz_customer_account.xlsx";
            bizCustomerAccount.setExchangeRateStr(NumberFormatterUtils.formatNumber(bizCustomerAccount.getExchangeRate()));
            bizCustomerAccount.setGoodsPriceStr(NumberFormatterUtils.formatNumber(bizCustomerAccount.getGoodsPrice())+"CIF SHANGHAI PORT");
            bizCustomerAccount.setAgentFeeTotalStr(NumberFormatterUtils.formatNumber(bizCustomerAccount.getAgentFeeTotal()));
            bizCustomerAccount.setCreaterUserName(StringUtils.isEmpty(bizCustomerAccount.getUpdateUserName()) ? bizCustomerAccount.getCreateUserName() : bizCustomerAccount.getUpdateUserName());
            bizCustomerAccount.setCreaterTimeStr(null != bizCustomerAccount.getUpdateTime() ? sdf2.format(bizCustomerAccount.getUpdateTime()) : sdf2.format(bizCustomerAccount.getCreateTime()));
            bizCustomerAccount.setNowTimeStr(sdf2.format(new Date()));
        }else if (StringUtils.equals(bizCustomerAccount.getBusinessType(),"9")) {
            tempName = "biz_customer_account2.xlsx";
            bizCustomerAccount.setAccountNoStr("结算单号："+bizCustomerAccount.getAccountNo());
            int separatorIndex = bizCustomerAccount.getContractNo().indexOf(",");
            String partBeforeSeparator = "";
            if (separatorIndex != -1) {
                partBeforeSeparator = bizCustomerAccount.getContractNo().substring(0, separatorIndex);
            } else {
                partBeforeSeparator = bizCustomerAccount.getContractNo();
            }
            String priceTerm = " ";
            Example example = new Example(BizMaterialInformation.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("contractNo", partBeforeSeparator);
            List<BizENonStateAuxmatAggrContractHead> bizENonStateAuxmatAggrContractHeads = bizENonStateAuxmatAggrContractHeadMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(bizENonStateAuxmatAggrContractHeads)){
                priceTerm = bizENonStateAuxmatAggrContractHeads.get(0).getPriceTerm();
            }
            bizCustomerAccount.setGoodsPriceStr("USD "+NumberFormatterUtils.formatNumber(bizCustomerAccount.getGoodsPrice()) + priceTerm);
            bizCustomerAccount.setExchangeRateStr(NumberFormatterUtils.formatNumber(bizCustomerAccount.getExchangeRate())+"(吨)");
            bizCustomerAccount.setNowTimeStr(sdf2.format(bizCustomerAccount.getBusinessDate()));
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());
        String outName = xdoi18n.XdoI18nUtil.t("客户结算单")+formattedDate + bizCustomerAccountParam.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(bizCustomerAccount),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(bizCustomerAccountParam.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    @Resource
    private BizNonIncomingGoodsHeadMapper tBizNonIncomingGoodsHeadMapper;
    @Resource
    private BizNonIncomingGoodsListMapper BizNonIncomingGoodsListMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    @Resource
    private BaseInfoCustomerParamsMapper customerParamsMapper;
    @Resource
    private BizINonStateAuxmatAggrContractHeadMapper bizINonStateAuxmatAggrContractHeadMapper;
    @Resource
    private BizINonStateAuxmatAggrContractListMapper bizINonStateAuxmatAggrContractListMapper;
    @Resource
    private BizExportGoodsHeadMapper bizExportGoodsHeadMapper;
    @Resource
    private BizExportGoodsListMapper bizExportGoodsListMapper;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    public ResultObject insertByShipping(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        if(StringUtils.isBlank(bizCustomerAccountParam.getBusinessType())){
            return ResultObject.createInstance(false, xdoi18n.XdoI18nUtil.t("新增失败，业务类型不能为空！"));
        }
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BizCustomerAccount insert = new BizCustomerAccount();
        if("6".equals(bizCustomerAccountParam.getBusinessType())){
            BizNonIncomingGoodsHead bizNonIncomingGoodsHead = tBizNonIncomingGoodsHeadMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
            if(ObjectUtils.isEmpty(bizNonIncomingGoodsHead)){
                resultObject.setSuccess(false);
                resultObject.setMessage("进货明细数据不存在无法生成客户结算数据");
                return resultObject;
            }
            BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizNonIncomingGoodsHead.getHeadId());
            List<BizNonIncomingGoodsList> listByHeadSids = BizNonIncomingGoodsListMapper.getListByHeadSids(Collections.singletonList(bizNonIncomingGoodsHead.getId()));
//            List<BizINonStateAuxmatAggrContractList> bizIAuxmatForContractList = new ArrayList<>();
//            if(!ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
//                bizIAuxmatForContractList = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(bizINonStateAuxmatAggrContractHead.getId());
//            }
            List<EnterpriseRate> select = enterpriseRateMapper.selectMessage(new EnterpriseRate() {{
                setTradeCode(userInfo.getCompany());
                setCurr(bizNonIncomingGoodsHead.getCurr());
            }});

            insert.setBusinessType("6");
            insert.setAccountNo(bizNonIncomingGoodsHead.getContractNo());
            insert.setPurchaseOrderNo(bizNonIncomingGoodsHead.getPurchaseNo());
            insert.setContractNo(bizNonIncomingGoodsHead.getContractNo());
            if(!ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
                insert.setCustomer(bizINonStateAuxmatAggrContractHead.getDomesticPrincipal());
                insert.setAgentFeeRate(bizINonStateAuxmatAggrContractHead.getAgreementAgentFeeRate());
            }
            insert.setCurr(bizNonIncomingGoodsHead.getCurr());
            if(CollectionUtils.isNotEmpty(select)){
                insert.setExchangeRate(select.get(0).getRate());
            }
//            if(CollectionUtils.isNotEmpty(bizIAuxmatForContractList)){
//                BigDecimal reduce = bizIAuxmatForContractList.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                insert.setGoodsPrice(reduce);
//            }

            if(CollectionUtils.isNotEmpty(listByHeadSids)){
                BigDecimal reduce = listByHeadSids.stream().map(BizNonIncomingGoodsList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                insert.setGoodsPrice(reduce);
                insert.setGName(listByHeadSids.get(0).getGoodsName());
                List<BaseInfoCustomerParams> unitList = customerParamsMapper.getList(new BaseInfoCustomerParams() {{
                    setTradeCode(userInfo.getCompany());
                    setParamsType("UNIT");
                }});
                String ps = "";
                for (BizNonIncomingGoodsList goodsList : listByHeadSids) {
                    String unitStr;
                    Optional<BaseInfoCustomerParams> first = unitList.stream().filter(x -> x.getParamsCode().equals(goodsList.getUnit())).findFirst();
                    if(first.isPresent()){
                        unitStr = first.get().getParamsName();
                    }else {
                        unitStr = goodsList.getProductModel();
                    }
                    String goodStr = goodsList.getGoodsName() + "(" + (StringUtils.isNotBlank(goodsList.getProductModel()) ? goodsList.getProductModel() : "")
                            + ")" + NumberFormatterUtils.formatNumber(goodsList.getQuantity()) + "(" + (StringUtils.isNotBlank(unitStr) ? unitStr : "") + ")";
                    ps = ps + ";" + goodStr;
                }
                if(StringUtils.isNotBlank(ps)){
                    insert.setProducrSome(ps.substring(1));
                }
            }
            insert.setPurchaseNoMark(bizNonIncomingGoodsHead.getId());
        }else if("9".equals(bizCustomerAccountParam.getBusinessType())){
            BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
            List<BizExportGoodsList> listByHeadId = bizExportGoodsListMapper.getListByHeadId(bizExportGoodsHead.getId());
            insert.setAccountNo(getSerialNo(userInfo));
            insert.setBusinessType("9");
            insert.setPurchaseNoMark(bizExportGoodsHead.getId());
            insert.setContractNo(bizExportGoodsHead.getContractNo());
            insert.setPurchaseOrderNo(bizExportGoodsHead.getExportNo());
            insert.setCustomer(bizExportGoodsHead.getCustomer());
            insert.setCurr(bizExportGoodsHead.getCurrency());
            //代理费率 等合同

            if(CollectionUtils.isNotEmpty(listByHeadId)){
                insert.setExchangeRate(listByHeadId.stream().map(BizExportGoodsList::getQty).reduce(BigDecimal.ZERO,BigDecimal::add));
                insert.setGoodsPrice(listByHeadId.stream().map(BizExportGoodsList::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
                Example example = new Example(BizMaterialInformation.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("gname", listByHeadId.get(0).getProductName());
                List<BizMaterialInformation> bizMaterialInformations = bizMaterialInformationMapper.selectByExample(example);
                if(CollectionUtils.isNotEmpty(bizMaterialInformations)){
                    insert.setProducrSome(bizMaterialInformations.get(0).getMerchandiseCategories());
                }
            }
            if(null != insert.getGoodsPrice() && null != insert.getAgentFeeRate()){
                insert.setAgentFeeTotal(insert.getGoodsPrice().multiply(insert.getAgentFeeRate()).divide(new BigDecimal(100),6, RoundingMode.HALF_UP));
            }
            insert.setNote("该合同现已执行完毕，请及时结算，多谢配合！");
        }

        String sid = UUID.randomUUID().toString();
        insert.setPurchaseMark("2");
        insert.setBusinessDate(new Date());
        insert.setSid(sid);
        insert.setVersionNo("1");
        insert.setStatus("0");
        insert.setRedFlush("1");
        insert.setSendFinance("0");
        insert.setTradeCode(userInfo.getCompany());
        insert.setCreateBy(userInfo.getUserNo());
        insert.setCreateTime(new Date());
        insert.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = bizCustomerAccountMapper.insert(insert);
        insert.setCreaterBy(userInfo.getUserNo());
        insert.setCreaterTime(new Date());
        insert.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? bizCustomerAccountDtoMapper.toDto(insert) : null);
        return resultObject;
    }
    public String getSerialNo(UserInfoToken userInfo){
        String head = "CKKHJS";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(new Date());
        String serialNo = "";
        String serialNo1 = bizCustomerAccountMapper.getSerialNo(head + date);
        if(StringUtils.isNotBlank(serialNo1)){
            serialNo = head + date + String.format("%03d",Integer.parseInt(serialNo1.substring(serialNo1.length() - 3)) + 1);
        }else {
            serialNo = head + date + "001";
        }
        return serialNo;
    }
    @Resource
    private BizENonStateAuxmatAggrContractHeadMapper bizENonStateAuxmatAggrContractHeadMapper;
    @Resource
    private BizENonStateAuxmatAggrContractListMapper bizENonStateAuxmatAggrContractListMapper;
    public ResultObject insertByContract(BizCustomerAccountParam bizCustomerAccountParam, UserInfoToken userInfo) {
        if(StringUtils.isBlank(bizCustomerAccountParam.getBusinessType())){
            return ResultObject.createInstance(false, xdoi18n.XdoI18nUtil.t("新增失败，业务类型不能为空！"));
        }
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BizCustomerAccount insert = new BizCustomerAccount();
        if("6".equals(bizCustomerAccountParam.getBusinessType())){

        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
//        BizNonIncomingGoodsHead bizNonIncomingGoodsHead = tBizNonIncomingGoodsHeadMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
        if(ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
            resultObject.setSuccess(false);
            resultObject.setMessage("合同与协议数据不存在无法生成客户结算数据");
            return resultObject;
        }

//        List<BizNonIncomingGoodsList> listByHeadSids = BizNonIncomingGoodsListMapper.getListByHeadSids(Collections.singletonList(bizNonIncomingGoodsHead.getId()));
        List<BizINonStateAuxmatAggrContractList> bizIAuxmatForContractList = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(bizINonStateAuxmatAggrContractHead.getId());
        List<EnterpriseRate> select = enterpriseRateMapper.selectMessage(new EnterpriseRate() {{
            setTradeCode(userInfo.getCompany());
            setCurr(bizINonStateAuxmatAggrContractHead.getCurrency());
        }});

        insert.setBusinessType("6");
        insert.setBusinessDate(new Date());
        insert.setAccountNo(bizINonStateAuxmatAggrContractHead.getContractNo());
        insert.setPurchaseOrderNo(null);
        insert.setContractNo(bizINonStateAuxmatAggrContractHead.getContractNo());
        if(!ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
            insert.setCustomer(bizINonStateAuxmatAggrContractHead.getDomesticPrincipal());
            insert.setAgentFeeRate(bizINonStateAuxmatAggrContractHead.getAgreementAgentFeeRate());
        }
        insert.setCurr(bizINonStateAuxmatAggrContractHead.getCurrency());
        if(CollectionUtils.isNotEmpty(select)){
            insert.setExchangeRate(select.get(0).getRate());
        }
        if(!ObjectUtils.isEmpty(bizIAuxmatForContractList)){
            BigDecimal reduce = bizIAuxmatForContractList.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            insert.setGoodsPrice(reduce);
        }

        if(CollectionUtils.isNotEmpty(bizIAuxmatForContractList)){
            insert.setGName(bizIAuxmatForContractList.get(0).getGoodsName());
            List<BaseInfoCustomerParams> unitList = customerParamsMapper.getList(new BaseInfoCustomerParams() {{
                setTradeCode(userInfo.getCompany());
                setParamsType("UNIT");
            }});
            String ps = "";
            for (BizINonStateAuxmatAggrContractList goodsList : bizIAuxmatForContractList) {
                String unitStr;
                Optional<BaseInfoCustomerParams> first = unitList.stream().filter(x -> x.getParamsCode().equals(goodsList.getUnit())).findFirst();
                if(first.isPresent()){
                    unitStr = first.get().getParamsName();
                }else {
                    unitStr = goodsList.getGoodsDesc();
                }
                String goodStr = goodsList.getGoodsName() + "(" + (StringUtils.isNotBlank(goodsList.getGoodsDesc())?goodsList.getGoodsDesc():"") + ")" + NumberFormatterUtils.formatNumber(goodsList.getQty())
                        + "(" + (StringUtils.isNotBlank(unitStr)?unitStr:"") + ")";
                ps = ps + ";" + goodStr;
            }
            if(StringUtils.isNotBlank(ps)){
                insert.setProducrSome(ps.substring(1));
            }
            insert.setPurchaseMark("1");
            insert.setPurchaseNoMark(bizINonStateAuxmatAggrContractHead.getId());
        }
        }else if("9".equals(bizCustomerAccountParam.getBusinessType())){
            BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead = bizENonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizCustomerAccountParam.getSid());
            if(ObjectUtils.isEmpty(bizENonStateAuxmatAggrContractHead)){
                resultObject.setSuccess(false);
                resultObject.setMessage("合同与协议数据不存在无法生成客户结算数据");
                return resultObject;
            }
            List<BizENonStateAuxmatAggrContractList> bizEAuxmatForContractList = bizENonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(bizENonStateAuxmatAggrContractHead.getId());
            insert.setBusinessType("9");
            insert.setBusinessDate(new Date());
            insert.setAccountNo(bizENonStateAuxmatAggrContractHead.getContractNo());
            insert.setPurchaseOrderNo(null);
            insert.setContractNo(bizENonStateAuxmatAggrContractHead.getContractNo());
            insert.setCustomer(bizENonStateAuxmatAggrContractHead.getSupplier());
            insert.setCurr(bizENonStateAuxmatAggrContractHead.getCurr());
            BigDecimal reduceQty = new BigDecimal(0);
            BigDecimal reduceAmount = new BigDecimal(0);
            if(CollectionUtils.isNotEmpty(bizEAuxmatForContractList)){
                reduceQty = bizEAuxmatForContractList.stream().map(BizENonStateAuxmatAggrContractList::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                reduceAmount = bizEAuxmatForContractList.stream().map(BizENonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Example example = new Example(BizMaterialInformation.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("gname", bizEAuxmatForContractList.get(0).getGName());
                List<BizMaterialInformation> bizMaterialInformations = bizMaterialInformationMapper.selectByExample(example);
                if(CollectionUtils.isNotEmpty(bizMaterialInformations)){
                    insert.setProducrSome(bizMaterialInformations.get(0).getMerchandiseCategories());
                }
            }
            insert.setGoodsPrice(reduceAmount);
            insert.setExchangeRate(reduceQty);
            insert.setAgentFeeRate(bizENonStateAuxmatAggrContractHead.getAgreementAgentRate());
            BigDecimal bigDecimal = new BigDecimal(0);
            if(null != insert.getAgentFeeRate() && null != insert.getGoodsPrice()){
                bigDecimal = insert.getGoodsPrice().multiply(insert.getAgentFeeRate()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP);
            }
            insert.setAgentFeeTotal(bigDecimal);
            insert.setNote("该合同现已执行完毕，请及时结算，多谢配合！");
            insert.setPurchaseMark("2");
            insert.setPurchaseNoMark(bizENonStateAuxmatAggrContractHead.getId());
        }

        String sid = UUID.randomUUID().toString();
        insert.setSid(sid);
        insert.setVersionNo("1");
        insert.setStatus("0");
        insert.setRedFlush("1");
        insert.setSendFinance("0");
        insert.setTradeCode(userInfo.getCompany());
        insert.setCreateBy(userInfo.getUserNo());
        insert.setCreateTime(new Date());
        insert.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = bizCustomerAccountMapper.insert(insert);
        insert.setCreaterBy(userInfo.getUserNo());
        insert.setCreaterTime(new Date());
        insert.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? bizCustomerAccountDtoMapper.toDto(insert) : null);
        return resultObject;
    }

    public ResultObject getSupplierList(BizCustomerAccountParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    public ResultObject getCreateUserList(BizCustomerAccountParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
}
