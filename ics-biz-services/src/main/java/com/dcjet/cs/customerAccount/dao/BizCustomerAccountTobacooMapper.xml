<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.customerAccount.dao.BizCustomerAccountTobacooMapper">
    <resultMap id="bizCustomerAccountTobacooResultMap" type="com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="account_no" property="accountNo" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="exchange_rate" property="exchangeRate" jdbcType="NUMERIC" />
		<result column="goods_price" property="goodsPrice" jdbcType="NUMERIC" />
		<result column="agent_fee_rate" property="agentFeeRate" jdbcType="NUMERIC" />
		<result column="agent_fee" property="agentFee" jdbcType="NUMERIC" />
		<result column="agent_tax_fee" property="agentTaxFee" jdbcType="NUMERIC" />
		<result column="agent_fee_total" property="agentFeeTotal" jdbcType="NUMERIC" />
		<result column="business_date" property="businessDate" jdbcType="TIMESTAMP" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="send_finance" property="sendFinance" jdbcType="VARCHAR" />
		<result column="producr_some" property="producrSome" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="red_flush" property="redFlush" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="appr_status" property="apprStatus" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="is_confirm" property="isConfirm" jdbcType="VARCHAR" />
		<result column="purchase_mark" property="purchaseMark" jdbcType="VARCHAR" />
		<result column="purchase_no_mark" property="purchaseNoMark" jdbcType="VARCHAR" />
		<result column="goods_price_rmb" property="goodsPriceRmb" jdbcType="NUMERIC" />
		<result column="vat_rate" property="vatRate" jdbcType="NUMERIC" />
		<result column="freight_forwarding_fee" property="freightForwardingFee" jdbcType="NUMERIC" />
		<result column="insurance_fee" property="insuranceFee" jdbcType="NUMERIC" />
		<result column="cost_fee" property="costFee" jdbcType="NUMERIC" />
		<result column="deposit_received" property="depositReceived" jdbcType="NUMERIC" />
		<result column="refund_fee" property="refundFee" jdbcType="NUMERIC" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="freight_ratio" property="freightRatio" jdbcType="VARCHAR" />
		<result column="business_location" property="businessLocation" jdbcType="VARCHAR" />
        <result column="total_agent_fee_rate" property="totalAgentFeeRate" jdbcType="NUMERIC" />
        <result column="total_agent_fee" property="totalAgentFee" jdbcType="NUMERIC" />
        <result column="total_agent_tax_fee" property="totalAgentTaxFee" jdbcType="NUMERIC" />
        <result column="total_agent_fee_total" property="totalAgentFeeTotal" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,COALESCE(update_by,create_by) as createrBy
     ,COALESCE(update_user_name,create_user_name) as createrUserName
     ,COALESCE(update_time,create_time) as createrTime
     ,create_by
     ,create_time
     ,create_user_name
     ,update_by
     ,update_time
     ,update_user_name
     ,trade_code
     ,sys_org_code
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,business_type
     ,account_no
     ,contract_no
     ,curr
     ,exchange_rate
     ,goods_price
     ,goods_price as currPrice
     ,agent_fee_rate
     ,agent_fee
     ,agent_tax_fee
     ,agent_fee_total
     ,business_date
     ,g_name
     ,send_finance
     ,producr_some
     ,note
     ,red_flush
     ,status
     ,appr_status
     ,confirm_time
     ,is_confirm
     ,purchase_mark
     ,purchase_no_mark
     ,goods_price_rmb
     ,goods_price_rmb as rmbPrice
     ,vat_rate
     ,freight_forwarding_fee
     ,insurance_fee
     ,cost_fee
     ,deposit_received
     ,refund_fee
     ,customer
     ,freight_ratio
     ,business_location
     ,total_agent_fee_rate
     ,total_agent_fee
     ,total_agent_tax_fee
     ,total_agent_fee_total
    </sql>
    <sql id="condition">
        <if test="createBy != null and createBy != ''">
            and COALESCE(update_by,create_by) like '%'|| #{createBy} || '%'
        </if>
        <if test="createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= to_date(#{createTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) < DATEADD(DAY, 1, to_date(#{createTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    <if test="businessType != null and businessType != ''">
		and business_type = #{businessType}
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
    <if test="status != null and status != ''">
		and status = #{status}
	</if>
    <if test="customer != null and customer != ''">
	  and customer like '%'|| #{customer} || '%'
	</if>
        <if test="tradeCode != null and tradeCode != ''">
            and trade_code = #{tradeCode}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizCustomerAccountTobacooResultMap" parameterType="com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_customer_account_tobacoo t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.update_time,t.create_time) desc
    </select>
    <select id="getForeignContractHeadList" resultType="com.dcjet.cs.equipment.model.ForeignContractHead" parameterType="com.dcjet.cs.equipment.model.ForeignContractHead">
        select t.ID,t.CONTRACT_NO,t.BUYER,t.SELLER, t.SIGN_DATE, sum(l.MONEY_AMOUNT) AS AMOUNT
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD t
        LEFT JOIN T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST l ON l.head_id = t.id
        where NOT EXISTS (SELECT 1 FROM t_biz_customer_account_tobacoo tc WHERE tc.CONTRACT_NO like '%' || t.contract_no || '%' AND tc.STATUS != '2' )
            and t.TRADE_CODE = #{tradeCode} and t.DATA_STATUS = '1'
        <if test="contractNo != null and contractNo != ''">
            and t.CONTRACT_NO like '%'|| #{contractNo} || '%'
        </if>
        <if test="buyer != null and buyer != ''">
            and t.BUYER like '%'|| #{buyer} || '%'
        </if>
        <if test="seller != null and seller != ''">
            and t.SELLER like '%'|| #{seller} || '%'
        </if>
        GROUP BY t.ID,t.CONTRACT_NO,t.BUYER,t.SELLER, t.SIGN_DATE

    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_customer_account_tobacoo t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getSerialNo" resultType="java.lang.String">
        SELECT * FROM (
                          SELECT ACCOUNT_NO from T_BIZ_CUSTOMER_ACCOUNT_SUMMARY tbcas
                          UNION
                          SELECT ACCOUNT_NO from T_BIZ_CUSTOMER_ACCOUNT_TOBACOO tbcat
                          UNION
                          SELECT ACCOUNT_NO from T_BIZ_CUSTOMER_ACCOUNT_SLICE tbcasl) t
        WHERE t.ACCOUNT_NO like '%'|| #{prefix} || '%' ORDER BY t.ACCOUNT_NO DESC limit 1
    </select>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            t.customer as "value",
            m.MERCHANT_NAME_CN as "label"
        from  t_biz_customer_account_tobacoo t LEFT JOIN T_BIZ_MERCHANT m ON t.customer = m.merchant_code
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="getCreateUserList" resultType="java.util.Map">
        select
            distinct
            COALESCE(update_by,create_by) as "value"
                   ,COALESCE(update_user_name,create_user_name) as "label"
        from  t_biz_customer_account_tobacoo t
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="selectByPurchaseNoMark"
            resultType="com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo">
        select t.* from t_biz_customer_account_tobacoo t where t.purchase_no_mark = #{purchaseNoMark}
    </select>

</mapper>
