package com.dcjet.cs.customerAccount.mapper;
import com.dcjet.cs.dto.customerAccount.*;
import com.dcjet.cs.customerAccount.model.BizCustomerAccount;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-6-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizCustomerAccountDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizCustomerAccountDto toDto(BizCustomerAccount po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizCustomerAccount toPo(BizCustomerAccountParam param);
    /**
     * 数据库原始数据更新
     * @param bizCustomerAccountParam
     * @param bizCustomerAccount
     */
    void updatePo(BizCustomerAccountParam bizCustomerAccountParam, @MappingTarget BizCustomerAccount bizCustomerAccount);
    default void patchPo(BizCustomerAccountParam bizCustomerAccountParam, BizCustomerAccount bizCustomerAccount) {
        // TODO 自行实现局部更新
    }
}
