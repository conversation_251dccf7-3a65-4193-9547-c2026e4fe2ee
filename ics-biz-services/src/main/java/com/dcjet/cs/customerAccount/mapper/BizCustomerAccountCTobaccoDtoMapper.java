package com.dcjet.cs.customerAccount.mapper;

import com.dcjet.cs.customerAccount.model.BizCustomerAccountCTobacco;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountCTobaccoDto;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountCTobaccoParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-6-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizCustomerAccountCTobaccoDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizCustomerAccountCTobaccoDto toDto(BizCustomerAccountCTobacco po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizCustomerAccountCTobacco toPo(BizCustomerAccountCTobaccoParam param);
    /**
     * 数据库原始数据更新
     * @param BizCustomerAccountCTobaccoParam
     * @param BizCustomerAccountCTobacco
     */
    void updatePo(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, @MappingTarget BizCustomerAccountCTobacco BizCustomerAccountCTobacco);
    default void patchPo(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, BizCustomerAccountCTobacco BizCustomerAccountCTobacco) {
        // TODO 自行实现局部更新
    }
}
