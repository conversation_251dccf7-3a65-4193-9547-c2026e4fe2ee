package com.dcjet.cs.customerAccount.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.dao.ForeignContractListMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractHeadDtoMapper;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.equipment.model.ForeignContractList;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.dao.TransCodeMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.params.model.TransCode;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.customerAccount.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountTobacooMapper;
import com.dcjet.cs.customerAccount.mapper.BizCustomerAccountTobacooDtoMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Service
public class BizCustomerAccountTobacooService extends BaseService<BizCustomerAccountTobacoo> {
    @Resource
    private BizCustomerAccountTobacooMapper bizCustomerAccountTobacooMapper;
    @Resource
    private BizCustomerAccountTobacooDtoMapper bizCustomerAccountTobacooDtoMapper;
    @Override
    public Mapper<BizCustomerAccountTobacoo> getMapper() {
        return bizCustomerAccountTobacooMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizCustomerAccountTobacooParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizCustomerAccountTobacooDto>> getListPaged(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooDtoMapper.toPo(bizCustomerAccountTobacooParam);
        bizCustomerAccountTobacoo.setTradeCode(userInfo.getCompany());
        Page<BizCustomerAccountTobacoo> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountTobacooMapper.getList(bizCustomerAccountTobacoo));
        List<BizCustomerAccountTobacooDto> bizCustomerAccountTobacooDtos = page.getResult().stream().map(head -> {
            BizCustomerAccountTobacooDto dto = bizCustomerAccountTobacooDtoMapper.toDto(head);
//            if(StringUtils.isNotBlank(head.getPurchaseNoMark())){
//                List<ForeignContractList> listByHeadId = foreignContractListMapper.getListByHeadId(head.getPurchaseNoMark(),userInfo.getCompany());
//                if(CollectionUtils.isNotEmpty(listByHeadId)){
//                    dto.setTotalAmount(listByHeadId.stream().map(ForeignContractList::getMoneyAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
//                }
//            }
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizCustomerAccountTobacooDto>> paged = ResultObject.createInstance(bizCustomerAccountTobacooDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizCustomerAccountTobacooParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountTobacooDto insert(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooDtoMapper.toPo(bizCustomerAccountTobacooParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizCustomerAccountTobacoo.setSid(sid);
        bizCustomerAccountTobacoo.setTradeCode(userInfo.getCompany());
        bizCustomerAccountTobacoo.setCreateBy(userInfo.getUserNo());
        bizCustomerAccountTobacoo.setCreateTime(new Date());
        bizCustomerAccountTobacoo.setCreateUserName(userInfo.getUserName());
        bizCustomerAccountTobacoo.setBusinessType("3");
        bizCustomerAccountTobacoo.setApprStatus("0");
        bizCustomerAccountTobacoo.setStatus("0");
        bizCustomerAccountTobacoo.setSendFinance("0");
        bizCustomerAccountTobacoo.setVatRate(new BigDecimal(6));
        bizCustomerAccountTobacoo.setBusinessDate(new Date());
        // 新增数据
        int insertStatus = bizCustomerAccountTobacooMapper.insert(bizCustomerAccountTobacoo);
        return  insertStatus > 0 ? bizCustomerAccountTobacooDtoMapper.toDto(bizCustomerAccountTobacoo) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizCustomerAccountTobacooParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountTobacooDto update(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(bizCustomerAccountTobacooParam.getSid());
        bizCustomerAccountTobacooDtoMapper.updatePo(bizCustomerAccountTobacooParam, bizCustomerAccountTobacoo);
        bizCustomerAccountTobacoo.setUpdateBy(userInfo.getUserNo());
        bizCustomerAccountTobacoo.setUpdateTime(new Date());
        bizCustomerAccountTobacoo.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = bizCustomerAccountTobacooMapper.updateByPrimaryKey(bizCustomerAccountTobacoo);
        return update > 0 ? bizCustomerAccountTobacooDtoMapper.toDto(bizCustomerAccountTobacoo) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizCustomerAccountTobacooMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizCustomerAccountTobacooDto> selectAll(BizCustomerAccountTobacooParam exportParam, UserInfoToken userInfo) {
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooDtoMapper.toPo(exportParam);
         bizCustomerAccountTobacoo.setTradeCode(userInfo.getCompany());
        List<BizCustomerAccountTobacooDto> bizCustomerAccountTobacooDtos = new ArrayList<>();
        List<BizCustomerAccountTobacoo> bizCustomerAccountTobacoos = bizCustomerAccountTobacooMapper.getList(bizCustomerAccountTobacoo);
        if (CollectionUtils.isNotEmpty(bizCustomerAccountTobacoos)) {
            bizCustomerAccountTobacooDtos = bizCustomerAccountTobacoos.stream().map(head -> {
                BizCustomerAccountTobacooDto dto = bizCustomerAccountTobacooDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizCustomerAccountTobacooDtos;
    }
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountTobacoo == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountTobacoo.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizCustomerAccountTobacoo.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizCustomerAccountTobacoo); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizCustomerAccountTobacoo bizCustomerAccountTobacoo) {
        BizCustomerAccountTobacoo update = new BizCustomerAccountTobacoo();
        update.setSid(bizCustomerAccountTobacoo.getSid());
        update.setApprStatus(bizCustomerAccountTobacoo.getApprStatus());
        bizCustomerAccountTobacooMapper.updateByPrimaryKeySelective(update);
    }
    public ResultObject confirmStatus(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(bizCustomerAccountTobacooParam.getSid());
        if (bizCustomerAccountTobacoo == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountTobacoo.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizCustomerAccountTobacoo update = bizCustomerAccountTobacooDtoMapper.toPo(bizCustomerAccountTobacooParam);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsConfirm("1");
        bizCustomerAccountTobacooMapper.updateByPrimaryKeySelective(update);
        BizCustomerAccountTobacooDto dto = bizCustomerAccountTobacooDtoMapper.toDto(update);
        resultObject.setData(dto);
        return resultObject;
    }
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountTobacoo == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccountTobacoo.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizCustomerAccountTobacoo.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizCustomerAccountTobacoo update = new BizCustomerAccountTobacoo();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizCustomerAccountTobacooMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));

        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountTobacoo == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccountTobacoo.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许退单");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(bizCustomerAccountTobacoo.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
        BizCustomerAccountTobacoo update = new BizCustomerAccountTobacoo();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizCustomerAccountTobacooMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResultObject getSupplierList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountTobacooMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    public ResultObject getCreateUserList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountTobacooMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    public String getSerialNo(UserInfoToken userInfo){
        String head = "YJSKHJS";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(new Date());
        String serialNo = "";
        String serialNo1 = bizCustomerAccountTobacooMapper.getSerialNo(head + date);
        if(StringUtils.isNotBlank(serialNo1)){
            serialNo = head + date + String.format("%03d",Integer.parseInt(serialNo1.substring(serialNo1.length() - 3)) + 1);
        }else {
            serialNo = head + date + "001";
        }
        return serialNo;
    }
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private ExportService exportService;
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }
    public ResponseEntity print(BizCustomerAccountTobacooParam param, UserInfoToken userInfo) throws Exception{
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(param.getSid());
        SimpleDateFormat bus = new SimpleDateFormat("yyyy.MM.dd");

        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);

        if(bizCustomerAccountTobacoo == null){
            throw new ErrorException(400,"中烟结算单不存在！");
        }else {
//            convertHeadToPrint(bizStoreIHead,bizMerchantMap);
            bizCustomerAccountTobacoo.setCustomer(getMerchantNameSafely(bizMerchantMap,bizCustomerAccountTobacoo.getCustomer()));
            List<ForeignContractList> listByHeadId = foreignContractListMapper.getListByHeadId(bizCustomerAccountTobacoo.getPurchaseNoMark(),userInfo.getCompany());
            String s = "";
            if(CollectionUtils.isNotEmpty(listByHeadId)){
                s = listByHeadId.get(0).getGName();
            }
            bizCustomerAccountTobacoo.setBusinessDateStr(null != bizCustomerAccountTobacoo.getUpdateTime() ? bus.format(bizCustomerAccountTobacoo.getUpdateTime()) : bus.format(bizCustomerAccountTobacoo.getCreateTime()));
            bizCustomerAccountTobacoo.setProducrSome(s+"("+bizCustomerAccountTobacoo.getFreightRatio()+")");
            bizCustomerAccountTobacoo.setCurrPriceStr(bizCustomerAccountTobacoo.getCurr()+NumberFormatterUtils.formatNumber(bizCustomerAccountTobacoo.getGoodsPrice()));
            bizCustomerAccountTobacoo.setTotalAgentFeeRateStr(NumberFormatterUtils.formatNumber(bizCustomerAccountTobacoo.getTotalAgentFeeRate()));
            bizCustomerAccountTobacoo.setExchangeRateStr(NumberFormatterUtils.formatNumber(bizCustomerAccountTobacoo.getExchangeRate()));
            bizCustomerAccountTobacoo.setTotalAgentFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountTobacoo.getTotalAgentTaxFee()));
            bizCustomerAccountTobacoo.setTotalAgentTaxFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountTobacoo.getTotalAgentTaxFee()));
            bizCustomerAccountTobacoo.setCreaterUserName(StringUtils.isNotBlank(bizCustomerAccountTobacoo.getUpdateUserName()) ? bizCustomerAccountTobacoo.getUpdateUserName() : bizCustomerAccountTobacoo.getCreateUserName());
        }

        String tempName = "biz_account_customer_tobacco.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());

        String outName = xdoi18n.XdoI18nUtil.t("中烟结算单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(bizCustomerAccountTobacoo),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    @Resource
    private ForeignContractHeadDtoMapper foreignContractHeadDtoMapper;
    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;

    public ResultObject<List<ForeignContractHeadDto>> getForeignContractHeadList(ForeignContractHeadParam foreignContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        ForeignContractHead foreignContractHead = foreignContractHeadDtoMapper.toPo(foreignContractHeadParam);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        Page<ForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountTobacooMapper.getForeignContractHeadList(foreignContractHead));
        List<ForeignContractHeadDto> dtos = page.getResult().stream().map(head -> {
            ForeignContractHeadDto dto = foreignContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<ForeignContractHeadDto>> paged = ResultObject.createInstance(dtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    @Resource
    private ForeignContractListMapper foreignContractListMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    @Resource
    private TransCodeMapper transCodeMapper;
    public ResultObject insertByContract(BizCustomerAccountTobacooParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(param.getSid());
        if(ObjectUtils.isEmpty(foreignContractHead)){
            resultObject.setSuccess(false);
            resultObject.setMessage("外商合同数据不存在无法生成客户结算数据");
            return resultObject;
        }
        List<ForeignContractList> listByHeadId = foreignContractListMapper.getListByHeadId(param.getSid(),userInfo.getCompany());
        TransCode transCode = new TransCode();
        transCode.setTradeCode(userInfo.getCompany());
        transCode.setBizType("3");
        List<TransCode> storehouses = transCodeMapper.getList(transCode);
        List<EnterpriseRate> select = enterpriseRateMapper.selectMessage(new EnterpriseRate() {{
            setTradeCode(userInfo.getCompany());
            setCurr(foreignContractHead.getCurr());
        }});
        String sid = UUID.randomUUID().toString();
        BizCustomerAccountTobacoo insert = new BizCustomerAccountTobacoo();
        insert.setContractNo(foreignContractHead.getContractNo());
        insert.setAccountNo(getSerialNo(userInfo));
        insert.setCustomer(foreignContractHead.getBuyer());
        insert.setCurr(foreignContractHead.getCurr());
        insert.setExchangeRate(new BigDecimal(0));
        insert.setBusinessLocation("0");
        if(StringUtils.isNotBlank(foreignContractHead.getBusinessPlace())){
            insert.setBusinessLocation(foreignContractHead.getBusinessPlace());
        }
        if(StringUtils.equals("1",insert.getBusinessLocation())){
            if(CollectionUtils.isNotEmpty(select)){
                insert.setExchangeRate(select.get(0).getRate());
            }
        }
        insert.setVatRate(new BigDecimal(6));
        insert.setGoodsPrice(new BigDecimal(0));
        insert.setGoodsPriceRmb(insert.getGoodsPrice().multiply(insert.getExchangeRate()).setScale(2,RoundingMode.HALF_UP));
        if(CollectionUtils.isNotEmpty(storehouses)){
            insert.setAgentFeeRate(storehouses.get(0).getHqAgentFeeRate());
        }
        if(null != insert.getAgentFeeRate()){
            if(StringUtils.equals("1",insert.getBusinessLocation())){
                insert.setTotalAgentFeeRate(insert.getAgentFeeRate().add(new BigDecimal(1)));
            }else if(StringUtils.equals("0",insert.getBusinessLocation())){
                insert.setTotalAgentFeeRate(insert.getAgentFeeRate().add(new BigDecimal("2.5")));
            }
        }
        if(CollectionUtils.isNotEmpty(listByHeadId)){
            insert.setDepositReceived(listByHeadId.stream().map(ForeignContractList::getMoneyAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        }
//        if(CollectionUtils.isNotEmpty(listByHeadId)){
//            BigDecimal reduce = listByHeadId.stream().map(contract -> contract.getQty().multiply(contract.getUnitPrice())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
//
//            if(null != insert.getExchangeRate()){
//                insert.setGoodsPriceRmb(insert.getGoodsPrice().multiply(insert.getExchangeRate()).setScale(2,RoundingMode.HALF_UP));
//                if(null != insert.getAgentFeeRate()){
//                    insert.setAgentFee(insert.getGoodsPriceRmb().multiply(insert.getAgentFeeRate()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));
//                    insert.setAgentTaxFee(insert.getAgentFee().multiply(insert.getVatRate()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));
//                    insert.setAgentFeeTotal(insert.getAgentFee().add(insert.getAgentTaxFee()));
//                }
//            }
//        }
        insert.setBusinessDate(new Date());

        insert.setSid(sid);
        insert.setPurchaseMark("1");
        insert.setPurchaseNoMark(foreignContractHead.getId());
        insert.setStatus("0");
        insert.setRedFlush("1");
        insert.setSendFinance("0");
        insert.setTradeCode(userInfo.getCompany());
        insert.setCreateBy(userInfo.getUserNo());
        insert.setCreateTime(new Date());
        insert.setCreateUserName(userInfo.getUserName());
        insert.setBusinessType("3");
        insert.setApprStatus("0");

        int insertStatus = bizCustomerAccountTobacooMapper.insert(insert);
        insert.setCreaterBy(userInfo.getUserNo());
        insert.setCreaterTime(new Date());
        insert.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? bizCustomerAccountTobacooDtoMapper.toDto(insert) : null);
        return resultObject;
    }

}
