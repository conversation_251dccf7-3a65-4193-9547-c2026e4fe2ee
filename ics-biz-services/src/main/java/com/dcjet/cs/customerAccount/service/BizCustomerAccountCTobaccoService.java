package com.dcjet.cs.customerAccount.service;

import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizENonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizINonStateAuxmatAggrContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizENonStateAuxmatAggrContractList;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractHead;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountCTobaccoMapper;
import com.dcjet.cs.customerAccount.mapper.BizCustomerAccountCTobaccoDtoMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountCTobacco;
import com.dcjet.cs.dec.dao.BizExportGoodsHeadMapper;
import com.dcjet.cs.dec.dao.BizExportGoodsListMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.mapper.BizSmokeMachineIncomingGoodsHeadDtoMapper;
import com.dcjet.cs.dec.model.BizExportGoodsHead;
import com.dcjet.cs.dec.model.BizExportGoodsList;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountCTobaccoDto;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountCTobaccoParam;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizSmokeMachineIncomingGoodsHeadParam;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractHeadDtoMapper;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsHeadMapper;
import com.dcjet.cs.nonAuxiliaryMaterials.dao.BizNonIncomingGoodsListMapper;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsHead;
import com.dcjet.cs.nonAuxiliaryMaterials.model.BizNonIncomingGoodsList;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-6-16
 */
@Service
@Slf4j
public class BizCustomerAccountCTobaccoService extends BaseService<BizCustomerAccountCTobacco> {
    @Resource
    private BizCustomerAccountCTobaccoMapper BizCustomerAccountCTobaccoMapper;
    @Resource
    private BizCustomerAccountCTobaccoDtoMapper BizCustomerAccountCTobaccoDtoMapper;
    @Resource
    private AttachedService attachedService;

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Override
    public Mapper<BizCustomerAccountCTobacco> getMapper() {
        return BizCustomerAccountCTobaccoMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param BizCustomerAccountCTobaccoParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizCustomerAccountCTobaccoDto>> getListPaged(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoDtoMapper.toPo(BizCustomerAccountCTobaccoParam);
        BizCustomerAccountCTobacco.setTradeCode(userInfo.getCompany());
        Page<BizCustomerAccountCTobacco> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> BizCustomerAccountCTobaccoMapper.getList(BizCustomerAccountCTobacco));
        List<BizCustomerAccountCTobaccoDto> BizCustomerAccountCTobaccoDtos = page.getResult().stream().map(head -> {
            BizCustomerAccountCTobaccoDto dto = BizCustomerAccountCTobaccoDtoMapper.toDto(head);
            if("1".equals(dto.getPurchaseMark())){
                List<BizINonStateAuxmatAggrContractList> byHeadid = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(dto.getPurchaseNoMark());
                dto.setCurrPrice(byHeadid.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }else if("2".equals(dto.getPurchaseMark())){
                List<BizNonIncomingGoodsList> byHeadid2 = BizNonIncomingGoodsListMapper.getListByHeadSids(Arrays.asList(dto.getPurchaseNoMark()));
                dto.setCurrPrice(byHeadid2.stream().map(BizNonIncomingGoodsList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizCustomerAccountCTobaccoDto>> paged = ResultObject.createInstance(BizCustomerAccountCTobaccoDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param BizCustomerAccountCTobaccoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountCTobaccoDto insert(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoDtoMapper.toPo(BizCustomerAccountCTobaccoParam);
        /**
         * 规范固定字段
         */
        BizCustomerAccountCTobacco.setTradeCode(userInfo.getCompany());
        int check = BizCustomerAccountCTobaccoMapper.checkKey(BizCustomerAccountCTobacco);
        if(check>0){
            throw new ErrorException(400, "结算单号已经存在！");
        }
        String sid = UUID.randomUUID().toString();
        BizCustomerAccountCTobacco.setSid(sid);
        BizCustomerAccountCTobacco.setVersionNo("1");
        BizCustomerAccountCTobacco.setTradeCode(userInfo.getCompany());
        BizCustomerAccountCTobacco.setCreateBy(userInfo.getUserNo());
        BizCustomerAccountCTobacco.setCreateTime(new Date());
        BizCustomerAccountCTobacco.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = BizCustomerAccountCTobaccoMapper.insert(BizCustomerAccountCTobacco);
        return  insertStatus > 0 ? BizCustomerAccountCTobaccoDtoMapper.toDto(BizCustomerAccountCTobacco) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param BizCustomerAccountCTobaccoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountCTobaccoDto update(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
        BizCustomerAccountCTobaccoDtoMapper.updatePo(BizCustomerAccountCTobaccoParam, BizCustomerAccountCTobacco);

        BizCustomerAccountCTobacco.setUpdateBy(userInfo.getUserNo());
        BizCustomerAccountCTobacco.setUpdateTime(new Date());
        BizCustomerAccountCTobacco.setUpdateUserName(userInfo.getUserName());

        int check = BizCustomerAccountCTobaccoMapper.checkKey(BizCustomerAccountCTobacco);
        if(check>0){
            throw new ErrorException(400, "结算单号已经存在！");
        }
        // 更新数据
        int update = BizCustomerAccountCTobaccoMapper.updateByPrimaryKey(BizCustomerAccountCTobacco);
        return update > 0 ? BizCustomerAccountCTobaccoDtoMapper.toDto(BizCustomerAccountCTobacco) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		BizCustomerAccountCTobaccoMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizCustomerAccountCTobaccoDto> selectAll(BizCustomerAccountCTobaccoParam exportParam, UserInfoToken userInfo) {
        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoDtoMapper.toPo(exportParam);
         BizCustomerAccountCTobacco.setTradeCode(userInfo.getCompany());
        List<BizCustomerAccountCTobaccoDto> BizCustomerAccountCTobaccoDtos = new ArrayList<>();
        List<BizCustomerAccountCTobacco> BizCustomerAccountCTobaccos = BizCustomerAccountCTobaccoMapper.getList(BizCustomerAccountCTobacco);
        if (CollectionUtils.isNotEmpty(BizCustomerAccountCTobaccos)) {
            BizCustomerAccountCTobaccoDtos = BizCustomerAccountCTobaccos.stream().map(head -> {
                BizCustomerAccountCTobaccoDto dto = BizCustomerAccountCTobaccoDtoMapper.toDto(head);
                if("1".equals(dto.getPurchaseMark())){
                    List<BizINonStateAuxmatAggrContractList> byHeadid = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(dto.getPurchaseNoMark());
                    dto.setCurrPrice(byHeadid.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }else if("2".equals(dto.getPurchaseMark())){
                    List<BizNonIncomingGoodsList> byHeadid2 = BizNonIncomingGoodsListMapper.getListByHeadSids(Arrays.asList(dto.getPurchaseNoMark()));
                    dto.setCurrPrice(byHeadid2.stream().map(BizNonIncomingGoodsList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return BizCustomerAccountCTobaccoDtos;
    }

    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("发送审批成功"));

        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(sid);
        if (BizCustomerAccountCTobacco == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(BizCustomerAccountCTobacco.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        BizCustomerAccountCTobacco.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(BizCustomerAccountCTobacco); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizCustomerAccountCTobacco BizCustomerAccountCTobacco) {
        BizCustomerAccountCTobacco update = new BizCustomerAccountCTobacco();
        update.setSid(BizCustomerAccountCTobacco.getSid());
        update.setApprStatus(BizCustomerAccountCTobacco.getApprStatus());
        BizCustomerAccountCTobaccoMapper.updateByPrimaryKeySelective(update);
    }

    public ResultObject confirmStatus(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("确认成功"));

        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
        if (BizCustomerAccountCTobacco == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if(StringUtils.isBlank(BizCustomerAccountCTobacco.getAccountNo()) || StringUtils.isBlank(BizCustomerAccountCTobacco.getContractNo())
                || StringUtils.isBlank(BizCustomerAccountCTobacco.getCustomer()) || StringUtils.isBlank(BizCustomerAccountCTobacco.getCurr()) || null == BizCustomerAccountCTobacco.getExchangeRate()
                || null == BizCustomerAccountCTobacco.getGoodsPrice() || null == BizCustomerAccountCTobacco.getBusinessDate() || StringUtils.isBlank(BizCustomerAccountCTobacco.getProducrSome())){
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不不完整，请完善数据");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(BizCustomerAccountCTobacco.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
//        BizCustomerAccountCTobacco update = BizCustomerAccountCTobaccoDtoMapper.toPo(BizCustomerAccountCTobaccoParam);
        BizCustomerAccountCTobacco.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        BizCustomerAccountCTobacco.setConfirmTime(new Date());
        BizCustomerAccountCTobacco.setIsConfirm("1");
        BizCustomerAccountCTobaccoMapper.updateByPrimaryKeySelective(BizCustomerAccountCTobacco);
        BizCustomerAccountCTobaccoDto dto = BizCustomerAccountCTobaccoDtoMapper.toDto(BizCustomerAccountCTobacco);
        resultObject.setData(dto);
        return resultObject;
    }
    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("作废成功"));

        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(sid);
        if (BizCustomerAccountCTobacco == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(BizCustomerAccountCTobacco.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许退单");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(BizCustomerAccountCTobacco.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
        BizCustomerAccountCTobacco update = new BizCustomerAccountCTobacco();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        BizCustomerAccountCTobaccoMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("作废成功"));

        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(sid);
        if (BizCustomerAccountCTobacco == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(BizCustomerAccountCTobacco.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(BizCustomerAccountCTobacco.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizCustomerAccountCTobacco update = new BizCustomerAccountCTobacco();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        BizCustomerAccountCTobaccoMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject redFlush(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("红冲成功"));

        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(sid);
        if (BizCustomerAccountCTobacco == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        BizCustomerAccountCTobacco update = new BizCustomerAccountCTobacco();
        update.setSid(sid);
        update.setRedFlush(CommonEnum.RED_FLUSH_ENUM.YES.getValue());
        BizCustomerAccountCTobaccoMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }


    public ResultObject checkIdNotCancel(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        if (StringUtils.isBlank(BizCustomerAccountCTobaccoParam.getSid())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要复制数据！"));
        }
        List<String> sids = BizCustomerAccountCTobaccoMapper.checkIdNotCancel(BizCustomerAccountCTobaccoParam.getSid());
        if (CollectionUtils.isNotEmpty((sids))){
            resultObject.setSuccess(false);
            return resultObject;
        }
        return resultObject;
    }

    public ResultObject copyVersion(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("版本复制成功"));
        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
        BizCustomerAccountCTobacco maxVersionNoData = BizCustomerAccountCTobaccoMapper.getMaxVersionNoByContract(BizCustomerAccountCTobacco);
        if (maxVersionNoData == null) {
            throw new ErrorException(400, XdoI18nUtil.t("查询不到有效数据，请刷新后再试！"));
        }
        Integer maxVersionNoInt = Integer.valueOf(maxVersionNoData.getVersionNo()) + 1;
        //新表头id
        String sid = UUID.randomUUID().toString();
        invalidate(BizCustomerAccountCTobacco.getSid(),userInfo);

        BizCustomerAccountCTobacco.setSid(sid);
        BizCustomerAccountCTobacco.setCreateTime(new Date());
        BizCustomerAccountCTobacco.setCreateBy(userInfo.getUserNo());
        BizCustomerAccountCTobacco.setCreateUserName(userInfo.getUserName());
        BizCustomerAccountCTobacco.setUpdateBy(null);
        BizCustomerAccountCTobacco.setUpdateUserName(null);
        BizCustomerAccountCTobacco.setUpdateTime(null);
        BizCustomerAccountCTobacco.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        BizCustomerAccountCTobacco.setApprStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        BizCustomerAccountCTobacco.setVersionNo(maxVersionNoInt.toString());
        BizCustomerAccountCTobacco.setConfirmTime(null);
        BizCustomerAccountCTobaccoMapper.insert(BizCustomerAccountCTobacco);

        List<Attached> attachedList = BizCustomerAccountCTobaccoMapper.getAttachmentFile(BizCustomerAccountCTobaccoParam.getSid());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            // 临时记录已经上传的文件
            List<Attached> tempAttachList = new ArrayList<>();

            for (Attached attached : attachedList) {
                String newSid = UUID.randomUUID().toString();
                attached.setSid(newSid);
                attached.setBusinessSid(sid);
                attached.setTradeCode(userInfo.getCompany());
                attached.setInsertUser(userInfo.getUserNo());
                attached.setInsertTime(new Date());
                attached.setUpdateUser(null);
                attached.setUpdateTime(null);
                attached.setNote("复制计划表头【"+BizCustomerAccountCTobacco.getAccountNo()+"】归档文件！");

                byte[] bytes;
                String url;
                String oldFileName = attached.getFileName();
                String newFileName = "";
                try {
                    if (oldFileName.startsWith("TIANYI")) {
                        url = oldFileName;
                        bytes = fileHandler.downloadFile(oldFileName);
                    } else {
                        url = oldFileName;
                        bytes = otherFileHandler.downloadFile(oldFileName);
                    }
                    // 上传文件
                    newFileName = fileHandler.uploadFile(bytes, url);
                    attached.setFileName(newFileName);
                    attached.setFileSize(new BigDecimal(bytes.length).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));
                    attachedService.insert(attached);
                    tempAttachList.add(attached);
                } catch (Exception e) {
                    log.error("复制文件失败，已经上传的文件：{}",tempAttachList);
                    log.error("复制文件失败！{}", e.getMessage());
                    throw new ErrorException(400, "复制归档文件异常！");
                }
            }
        }
        return result;
    }

    public ResponseEntity print(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) throws Exception {
        BizCustomerAccountCTobacco BizCustomerAccountCTobacco = BizCustomerAccountCTobaccoMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
        if(BizCustomerAccountCTobacco == null){
            throw new ErrorException(400,"客户结算单不存在！");
        }
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        if(!ObjectUtils.isEmpty(bizMerchants)){
            Optional<BizMerchant> first = bizMerchants.stream().filter(x -> x.getMerchantCode().equals(BizCustomerAccountCTobacco.getCustomer())).findFirst();
            first.ifPresent(merchant -> BizCustomerAccountCTobacco.setCustomer(merchant.getMerchantNameCn()));
        }
        String tempName = "";
        BizCustomerAccountCTobacco.setAgentFeeRateStr(NumberFormatterUtils.formatNumber(BizCustomerAccountCTobacco.getAgentFeeRate()) + "%");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        if(StringUtils.equals(BizCustomerAccountCTobacco.getBusinessType(),"6")){
            tempName = "biz_customer_account.xlsx";
            BizCustomerAccountCTobacco.setExchangeRateStr(NumberFormatterUtils.formatNumber(BizCustomerAccountCTobacco.getExchangeRate()));
            BizCustomerAccountCTobacco.setGoodsPriceStr(NumberFormatterUtils.formatNumber(BizCustomerAccountCTobacco.getGoodsPrice())+"CIF SHANGHAI PORT");
            BizCustomerAccountCTobacco.setAgentFeeTotalStr(NumberFormatterUtils.formatNumber(BizCustomerAccountCTobacco.getAgentFeeTotal()));
            BizCustomerAccountCTobacco.setCreaterUserName(StringUtils.isEmpty(BizCustomerAccountCTobacco.getUpdateUserName()) ? BizCustomerAccountCTobacco.getCreateUserName() : BizCustomerAccountCTobacco.getUpdateUserName());
            BizCustomerAccountCTobacco.setCreaterTimeStr(null != BizCustomerAccountCTobacco.getUpdateTime() ? sdf2.format(BizCustomerAccountCTobacco.getUpdateTime()) : sdf2.format(BizCustomerAccountCTobacco.getCreateTime()));
            BizCustomerAccountCTobacco.setNowTimeStr(sdf2.format(new Date()));
        }else if (StringUtils.equals(BizCustomerAccountCTobacco.getBusinessType(),"9")) {
            tempName = "biz_customer_account2.xlsx";
            BizCustomerAccountCTobacco.setAccountNoStr("结算单号："+BizCustomerAccountCTobacco.getAccountNo());
            int separatorIndex = BizCustomerAccountCTobacco.getContractNo().indexOf(",");
            String partBeforeSeparator = "";
            if (separatorIndex != -1) {
                partBeforeSeparator = BizCustomerAccountCTobacco.getContractNo().substring(0, separatorIndex);
            } else {
                partBeforeSeparator = BizCustomerAccountCTobacco.getContractNo();
            }
            String priceTerm = " ";
            Example example = new Example(BizMaterialInformation.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("contractNo", partBeforeSeparator);
            List<BizENonStateAuxmatAggrContractHead> bizENonStateAuxmatAggrContractHeads = bizENonStateAuxmatAggrContractHeadMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(bizENonStateAuxmatAggrContractHeads)){
                priceTerm = bizENonStateAuxmatAggrContractHeads.get(0).getPriceTerm();
            }
            BizCustomerAccountCTobacco.setGoodsPriceStr("USD "+NumberFormatterUtils.formatNumber(BizCustomerAccountCTobacco.getGoodsPrice()) + priceTerm);
            BizCustomerAccountCTobacco.setExchangeRateStr(NumberFormatterUtils.formatNumber(BizCustomerAccountCTobacco.getExchangeRate())+"(吨)");
            BizCustomerAccountCTobacco.setNowTimeStr(sdf2.format(BizCustomerAccountCTobacco.getBusinessDate()));
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());
        String outName = XdoI18nUtil.t("客户结算单")+formattedDate + BizCustomerAccountCTobaccoParam.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(BizCustomerAccountCTobacco),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(BizCustomerAccountCTobaccoParam.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    @Resource
    private BizNonIncomingGoodsHeadMapper tBizNonIncomingGoodsHeadMapper;
    @Resource
    private BizNonIncomingGoodsListMapper BizNonIncomingGoodsListMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    @Resource
    private BaseInfoCustomerParamsMapper customerParamsMapper;
    @Resource
    private BizINonStateAuxmatAggrContractHeadMapper bizINonStateAuxmatAggrContractHeadMapper;
    @Resource
    private BizINonStateAuxmatAggrContractListMapper bizINonStateAuxmatAggrContractListMapper;
    @Resource
    private BizExportGoodsHeadMapper bizExportGoodsHeadMapper;
    @Resource
    private BizExportGoodsListMapper bizExportGoodsListMapper;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    public ResultObject insertByShipping(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        if(StringUtils.isBlank(BizCustomerAccountCTobaccoParam.getBusinessType())){
            return ResultObject.createInstance(false, XdoI18nUtil.t("新增失败，业务类型不能为空！"));
        }
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("新增成功"));
        BizCustomerAccountCTobacco insert = new BizCustomerAccountCTobacco();
        if("6".equals(BizCustomerAccountCTobaccoParam.getBusinessType())){
            BizNonIncomingGoodsHead bizNonIncomingGoodsHead = tBizNonIncomingGoodsHeadMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
            if(ObjectUtils.isEmpty(bizNonIncomingGoodsHead)){
                resultObject.setSuccess(false);
                resultObject.setMessage("进货明细数据不存在无法生成客户结算数据");
                return resultObject;
            }
            BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(bizNonIncomingGoodsHead.getHeadId());
            List<BizNonIncomingGoodsList> listByHeadSids = BizNonIncomingGoodsListMapper.getListByHeadSids(Collections.singletonList(bizNonIncomingGoodsHead.getId()));
            List<BizINonStateAuxmatAggrContractList> bizIAuxmatForContractList = new ArrayList<>();
            if(!ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
                bizIAuxmatForContractList = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(bizINonStateAuxmatAggrContractHead.getId());
            }
            List<EnterpriseRate> select = enterpriseRateMapper.selectMessage(new EnterpriseRate() {{
                setTradeCode(userInfo.getCompany());
                setCurr(bizNonIncomingGoodsHead.getCurr());
            }});

            insert.setBusinessType("6");
            insert.setAccountNo(bizNonIncomingGoodsHead.getContractNo());
            insert.setPurchaseOrderNo(bizNonIncomingGoodsHead.getPurchaseNo());
            insert.setContractNo(bizNonIncomingGoodsHead.getContractNo());
            if(!ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
                insert.setCustomer(bizINonStateAuxmatAggrContractHead.getDomesticPrincipal());
                insert.setAgentFeeRate(bizINonStateAuxmatAggrContractHead.getAgreementAgentFeeRate());
            }
            insert.setCurr(bizNonIncomingGoodsHead.getCurr());
            if(CollectionUtils.isNotEmpty(select)){
                insert.setExchangeRate(select.get(0).getRate());
            }
            if(CollectionUtils.isNotEmpty(bizIAuxmatForContractList)){
                BigDecimal reduce = bizIAuxmatForContractList.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                insert.setGoodsPrice(reduce);
            }

            if(CollectionUtils.isNotEmpty(listByHeadSids)){
                insert.setGName(listByHeadSids.get(0).getGoodsName());
                List<BaseInfoCustomerParams> unitList = customerParamsMapper.getList(new BaseInfoCustomerParams() {{
                    setTradeCode(userInfo.getCompany());
                    setParamsType("UNIT");
                }});
                String ps = "";
                for (BizNonIncomingGoodsList goodsList : listByHeadSids) {
                    String unitStr;
                    Optional<BaseInfoCustomerParams> first = unitList.stream().filter(x -> x.getParamsCode().equals(goodsList.getUnit())).findFirst();
                    if(first.isPresent()){
                        unitStr = first.get().getParamsName();
                    }else {
                        unitStr = goodsList.getProductModel();
                    }
                    String goodStr = goodsList.getGoodsName() + "(" + (StringUtils.isNotBlank(goodsList.getProductModel()) ? goodsList.getProductModel() : "")
                            + ")" + NumberFormatterUtils.formatNumber(goodsList.getQuantity()) + "(" + (StringUtils.isNotBlank(unitStr) ? unitStr : "") + ")";
                    ps = ps + ";" + goodStr;
                }
                if(StringUtils.isNotBlank(ps)){
                    insert.setProducrSome(ps.substring(1));
                }
            }
            insert.setPurchaseNoMark(bizNonIncomingGoodsHead.getId());
        }else if("9".equals(BizCustomerAccountCTobaccoParam.getBusinessType())){
            BizExportGoodsHead bizExportGoodsHead = bizExportGoodsHeadMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
            List<BizExportGoodsList> listByHeadId = bizExportGoodsListMapper.getListByHeadId(bizExportGoodsHead.getId());
            insert.setAccountNo(getSerialNo(userInfo));
            insert.setBusinessType("9");
            insert.setPurchaseNoMark(bizExportGoodsHead.getId());
            insert.setContractNo(bizExportGoodsHead.getContractNo());
            insert.setPurchaseOrderNo(bizExportGoodsHead.getExportNo());
            insert.setCustomer(bizExportGoodsHead.getCustomer());
            insert.setCurr(bizExportGoodsHead.getCurrency());
            //代理费率 等合同

            if(CollectionUtils.isNotEmpty(listByHeadId)){
                insert.setExchangeRate(listByHeadId.stream().map(BizExportGoodsList::getQty).reduce(BigDecimal.ZERO,BigDecimal::add));
                insert.setGoodsPrice(listByHeadId.stream().map(BizExportGoodsList::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
                Example example = new Example(BizMaterialInformation.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("gname", listByHeadId.get(0).getProductName());
                List<BizMaterialInformation> bizMaterialInformations = bizMaterialInformationMapper.selectByExample(example);
                if(CollectionUtils.isNotEmpty(bizMaterialInformations)){
                    insert.setProducrSome(bizMaterialInformations.get(0).getMerchandiseCategories());
                }
            }
            if(null != insert.getGoodsPrice() && null != insert.getAgentFeeRate()){
                insert.setAgentFeeTotal(insert.getGoodsPrice().multiply(insert.getAgentFeeRate()).divide(new BigDecimal(100),6, RoundingMode.HALF_UP));
            }
            insert.setNote("该合同现已执行完毕，请及时结算，多谢配合！");
        }

        String sid = UUID.randomUUID().toString();
        insert.setPurchaseMark("2");
        insert.setBusinessDate(new Date());
        insert.setSid(sid);
        insert.setVersionNo("1");
        insert.setStatus("0");
        insert.setRedFlush("1");
        insert.setSendFinance("0");
        insert.setTradeCode(userInfo.getCompany());
        insert.setCreateBy(userInfo.getUserNo());
        insert.setCreateTime(new Date());
        insert.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = BizCustomerAccountCTobaccoMapper.insert(insert);
        insert.setCreaterBy(userInfo.getUserNo());
        insert.setCreaterTime(new Date());
        insert.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? BizCustomerAccountCTobaccoDtoMapper.toDto(insert) : null);
        return resultObject;
    }
    public String getSerialNo(UserInfoToken userInfo){
        String head = "CKKHJS";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(new Date());
        String serialNo = "";
        String serialNo1 = BizCustomerAccountCTobaccoMapper.getSerialNo(head + date);
        if(StringUtils.isNotBlank(serialNo1)){
            serialNo = head + date + String.format("%03d",Integer.parseInt(serialNo1.substring(serialNo1.length() - 3)) + 1);
        }else {
            serialNo = head + date + "001";
        }
        return serialNo;
    }
    @Resource
    private BizENonStateAuxmatAggrContractHeadMapper bizENonStateAuxmatAggrContractHeadMapper;
    @Resource
    private BizENonStateAuxmatAggrContractListMapper bizENonStateAuxmatAggrContractListMapper;
    public ResultObject insertByContract(BizCustomerAccountCTobaccoParam BizCustomerAccountCTobaccoParam, UserInfoToken userInfo) {
        if(StringUtils.isBlank(BizCustomerAccountCTobaccoParam.getBusinessType())){
            return ResultObject.createInstance(false, XdoI18nUtil.t("新增失败，业务类型不能为空！"));
        }
        ResultObject resultObject = ResultObject.createInstance(true, XdoI18nUtil.t("新增成功"));
        BizCustomerAccountCTobacco insert = new BizCustomerAccountCTobacco();
        if("6".equals(BizCustomerAccountCTobaccoParam.getBusinessType())){

        BizINonStateAuxmatAggrContractHead bizINonStateAuxmatAggrContractHead = bizINonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
//        BizNonIncomingGoodsHead bizNonIncomingGoodsHead = tBizNonIncomingGoodsHeadMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
        if(ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
            resultObject.setSuccess(false);
            resultObject.setMessage("合同与协议数据不存在无法生成客户结算数据");
            return resultObject;
        }

//        List<BizNonIncomingGoodsList> listByHeadSids = BizNonIncomingGoodsListMapper.getListByHeadSids(Collections.singletonList(bizNonIncomingGoodsHead.getId()));
        List<BizINonStateAuxmatAggrContractList> bizIAuxmatForContractList = bizINonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(bizINonStateAuxmatAggrContractHead.getId());
        List<EnterpriseRate> select = enterpriseRateMapper.selectMessage(new EnterpriseRate() {{
            setTradeCode(userInfo.getCompany());
            setCurr(bizINonStateAuxmatAggrContractHead.getCurrency());
        }});

        insert.setBusinessType("6");
        insert.setBusinessDate(new Date());
        insert.setAccountNo(bizINonStateAuxmatAggrContractHead.getContractNo());
        insert.setPurchaseOrderNo(null);
        insert.setContractNo(bizINonStateAuxmatAggrContractHead.getContractNo());
        if(!ObjectUtils.isEmpty(bizINonStateAuxmatAggrContractHead)){
            insert.setCustomer(bizINonStateAuxmatAggrContractHead.getDomesticPrincipal());
            insert.setAgentFeeRate(bizINonStateAuxmatAggrContractHead.getAgreementAgentFeeRate());
        }
        insert.setCurr(bizINonStateAuxmatAggrContractHead.getCurrency());
        if(CollectionUtils.isNotEmpty(select)){
            insert.setExchangeRate(select.get(0).getRate());
        }
        if(!ObjectUtils.isEmpty(bizIAuxmatForContractList)){
            BigDecimal reduce = bizIAuxmatForContractList.stream().map(BizINonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            insert.setGoodsPrice(reduce);
        }

        if(CollectionUtils.isNotEmpty(bizIAuxmatForContractList)){
            insert.setGName(bizIAuxmatForContractList.get(0).getGoodsName());
            List<BaseInfoCustomerParams> unitList = customerParamsMapper.getList(new BaseInfoCustomerParams() {{
                setTradeCode(userInfo.getCompany());
                setParamsType("UNIT");
            }});
            String ps = "";
            for (BizINonStateAuxmatAggrContractList goodsList : bizIAuxmatForContractList) {
                String unitStr;
                Optional<BaseInfoCustomerParams> first = unitList.stream().filter(x -> x.getParamsCode().equals(goodsList.getUnit())).findFirst();
                if(first.isPresent()){
                    unitStr = first.get().getParamsName();
                }else {
                    unitStr = goodsList.getGoodsDesc();
                }
                String goodStr = goodsList.getGoodsName() + "(" + (StringUtils.isNotBlank(goodsList.getGoodsDesc())?goodsList.getGoodsDesc():"") + ")" + NumberFormatterUtils.formatNumber(goodsList.getQty())
                        + "(" + (StringUtils.isNotBlank(unitStr)?unitStr:"") + ")";
                ps = ps + ";" + goodStr;
            }
            if(StringUtils.isNotBlank(ps)){
                insert.setProducrSome(ps.substring(1));
            }
            insert.setPurchaseMark("1");
            insert.setPurchaseNoMark(bizINonStateAuxmatAggrContractHead.getId());
        }
        }else if("9".equals(BizCustomerAccountCTobaccoParam.getBusinessType())){
            BizENonStateAuxmatAggrContractHead bizENonStateAuxmatAggrContractHead = bizENonStateAuxmatAggrContractHeadMapper.selectByPrimaryKey(BizCustomerAccountCTobaccoParam.getSid());
            if(ObjectUtils.isEmpty(bizENonStateAuxmatAggrContractHead)){
                resultObject.setSuccess(false);
                resultObject.setMessage("合同与协议数据不存在无法生成客户结算数据");
                return resultObject;
            }
            List<BizENonStateAuxmatAggrContractList> bizEAuxmatForContractList = bizENonStateAuxmatAggrContractListMapper.getBizIAuxmatForContractListByHeadid(bizENonStateAuxmatAggrContractHead.getId());
            insert.setBusinessType("9");
            insert.setBusinessDate(new Date());
            insert.setAccountNo(bizENonStateAuxmatAggrContractHead.getContractNo());
            insert.setPurchaseOrderNo(null);
            insert.setContractNo(bizENonStateAuxmatAggrContractHead.getContractNo());
            insert.setCustomer(bizENonStateAuxmatAggrContractHead.getSupplier());
            insert.setCurr(bizENonStateAuxmatAggrContractHead.getCurr());
            BigDecimal reduceQty = new BigDecimal(0);
            BigDecimal reduceAmount = new BigDecimal(0);
            if(CollectionUtils.isNotEmpty(bizEAuxmatForContractList)){
                reduceQty = bizEAuxmatForContractList.stream().map(BizENonStateAuxmatAggrContractList::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                reduceAmount = bizEAuxmatForContractList.stream().map(BizENonStateAuxmatAggrContractList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Example example = new Example(BizMaterialInformation.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("gname", bizEAuxmatForContractList.get(0).getGName());
                List<BizMaterialInformation> bizMaterialInformations = bizMaterialInformationMapper.selectByExample(example);
                if(CollectionUtils.isNotEmpty(bizMaterialInformations)){
                    insert.setProducrSome(bizMaterialInformations.get(0).getMerchandiseCategories());
                }
            }
            insert.setGoodsPrice(reduceAmount);
            insert.setExchangeRate(reduceQty);
            insert.setAgentFeeRate(bizENonStateAuxmatAggrContractHead.getAgreementAgentRate());
            BigDecimal bigDecimal = new BigDecimal(0);
            if(null != insert.getAgentFeeRate() && null != insert.getGoodsPrice()){
                bigDecimal = insert.getGoodsPrice().multiply(insert.getAgentFeeRate()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP);
            }
            insert.setAgentFeeTotal(bigDecimal);
            insert.setNote("该合同现已执行完毕，请及时结算，多谢配合！");
            insert.setPurchaseMark("2");
            insert.setPurchaseNoMark(bizENonStateAuxmatAggrContractHead.getId());
        }

        String sid = UUID.randomUUID().toString();
        insert.setSid(sid);
        insert.setVersionNo("1");
        insert.setStatus("0");
        insert.setRedFlush("1");
        insert.setSendFinance("0");
        insert.setTradeCode(userInfo.getCompany());
        insert.setCreateBy(userInfo.getUserNo());
        insert.setCreateTime(new Date());
        insert.setCreateUserName(userInfo.getUserName());
        // 新增数据
        int insertStatus = BizCustomerAccountCTobaccoMapper.insert(insert);
        insert.setCreaterBy(userInfo.getUserNo());
        insert.setCreaterTime(new Date());
        insert.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? BizCustomerAccountCTobaccoDtoMapper.toDto(insert) : null);
        return resultObject;
    }

    public ResultObject getSupplierList(BizCustomerAccountCTobaccoParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = BizCustomerAccountCTobaccoMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    public ResultObject getCreateUserList(BizCustomerAccountCTobaccoParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = BizCustomerAccountCTobaccoMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    @Resource
    private BizSmokeMachineIncomingGoodsHeadMapper bizSmokeMachineIncomingGoodsHeadMapper;

    @Resource
    private BizSmokeMachineIncomingGoodsHeadDtoMapper bizSmokeMachineIncomingGoodsHeadDtoMapper;
    public ResultObject<List<BizSmokeMachineIncomingGoodsHeadDto>> getSmokeMachineIncomingGoodsHeadList(BizSmokeMachineIncomingGoodsHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        BizSmokeMachineIncomingGoodsHead headIn = bizSmokeMachineIncomingGoodsHeadDtoMapper.toPo(param);
        headIn.setTradeCode(userInfo.getCompany());
        Page<BizSmokeMachineIncomingGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> BizCustomerAccountCTobaccoMapper.getSmokeMachineIncomingGoodsHeadList(headIn));
        List<BizSmokeMachineIncomingGoodsHeadDto> dtos = page.getResult().stream().map(head -> {
            BizSmokeMachineIncomingGoodsHeadDto dto = bizSmokeMachineIncomingGoodsHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizSmokeMachineIncomingGoodsHeadDto>> paged = ResultObject.createInstance(dtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    @Resource
    private ForeignContractHeadDtoMapper foreignContractHeadDtoMapper;
    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;
    public ResultObject<List<ForeignContractHeadDto>> getForeignContractHeadList(ForeignContractHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        ForeignContractHead foreignContractHead = foreignContractHeadDtoMapper.toPo(param);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        Page<ForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> BizCustomerAccountCTobaccoMapper.getForeignContractHeadList(foreignContractHead));
        List<ForeignContractHeadDto> dtos = page.getResult().stream().map(head -> {
            ForeignContractHeadDto dto = foreignContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<ForeignContractHeadDto>> paged = ResultObject.createInstance(dtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
}
