package com.dcjet.cs.customerAccount.dao;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSlice;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizCustomerAccountSlice
* <AUTHOR>
* @date: 2025-7-28
*/
public interface BizCustomerAccountSliceMapper extends Mapper<BizCustomerAccountSlice> {
    /**
     * 查询获取数据
     * @param bizCustomerAccountSlice
     * @return
     */
    List<BizCustomerAccountSlice> getList(BizCustomerAccountSlice bizCustomerAccountSlice);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    List<String> checkIdNotCancel(String sid);

    String getSerialNo(String prefix);

    List<Map<String, String>> getOrderSupplierList(String company);

    List<Map<String, String>> getCreateUserList(String company);

    List<SevenForeignContractHead> getForeignContractHeadListQty(BizCustomerAccountSlice slice);
}
