package com.dcjet.cs.customerAccount.dao;

import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountCTobacco;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizCustomerAccount
* <AUTHOR>
* @date: 2025-6-16
*/
public interface BizCustomerAccountCTobaccoMapper extends Mapper<BizCustomerAccountCTobacco> {
    /**
     * 查询获取数据
     * @param BizCustomerAccountCTobacco
     * @return
     */
    List<BizCustomerAccountCTobacco> getList(BizCustomerAccountCTobacco BizCustomerAccountCTobacco);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    List<String> checkIdNotCancel(String sid);
    BizCustomerAccountCTobacco getMaxVersionNoByContract(BizCustomerAccountCTobacco BizCustomerAccountCTobacco);
    List<Attached> getAttachmentFile(@Param("headId") String headId);

    int checkKey(BizCustomerAccountCTobacco BizCustomerAccountCTobacco);
    String getSerialNo(String prefix);

    List<Map<String, String>> getOrderSupplierList(String company);

    List<Map<String, String>> getCreateUserList(String company);
    List<ForeignContractHead> getForeignContractHeadList(ForeignContractHead foreignContractHead);

    List<BizSmokeMachineIncomingGoodsHead>  getSmokeMachineIncomingGoodsHeadList(BizSmokeMachineIncomingGoodsHead headIn);
}
