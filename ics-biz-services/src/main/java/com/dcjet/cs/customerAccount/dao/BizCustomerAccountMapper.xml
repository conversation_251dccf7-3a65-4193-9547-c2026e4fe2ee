<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.customerAccount.dao.BizCustomerAccountMapper">
    <resultMap id="bizCustomerAccountResultMap" type="com.dcjet.cs.customerAccount.model.BizCustomerAccount">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="account_no" property="accountNo" jdbcType="VARCHAR" />
		<result column="purchase_order_no" property="purchaseOrderNo" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="exchange_rate" property="exchangeRate" jdbcType="NUMERIC" />
		<result column="goods_price" property="goodsPrice" jdbcType="NUMERIC" />
		<result column="agent_fee_rate" property="agentFeeRate" jdbcType="NUMERIC" />
		<result column="agent_fee" property="agentFee" jdbcType="NUMERIC" />
		<result column="agent_tax_fee" property="agentTaxFee" jdbcType="NUMERIC" />
		<result column="agent_fee_total" property="agentFeeTotal" jdbcType="NUMERIC" />
		<result column="business_date" property="businessDate" jdbcType="TIMESTAMP" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="send_finance" property="sendFinance" jdbcType="VARCHAR" />
		<result column="producr_some" property="producrSome" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="red_flush" property="redFlush" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="appr_status" property="apprStatus" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="is_confirm" property="isConfirm" jdbcType="VARCHAR" />
		<result column="purchase_mark" property="purchaseMark" jdbcType="VARCHAR" />
		<result column="purchase_no_mark" property="purchaseNoMark" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,COALESCE(update_by,create_by) as createrBy
     ,COALESCE(update_user_name,create_user_name) as createrUserName
     ,COALESCE(update_time,create_time) as createrTime
     ,create_by
     ,create_time
     ,create_user_name
     ,update_by
     ,update_time
     ,update_user_name
     ,trade_code
     ,sys_org_code
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,business_type
     ,account_no
     ,purchase_order_no
     ,contract_no
     ,customer
     ,curr
     ,exchange_rate
     ,goods_price
     ,agent_fee_rate
     ,agent_fee
     ,agent_tax_fee
     ,agent_fee_total
     ,business_date
     ,g_name
     ,send_finance
     ,producr_some
     ,note
     ,red_flush
     ,status
     ,appr_status
     ,confirm_time
     ,is_confirm
     ,purchase_mark
     ,purchase_no_mark
     ,version_no
    </sql>
    <sql id="condition">
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(update_time,create_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(update_time,create_time) < DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    <if test="businessType != null and businessType != ''">
		and business_type = #{businessType}
	</if>
    <if test="accountNo != null and accountNo != ''">
	  and account_no like '%'|| #{accountNo} || '%'
	</if>
    <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
	  and purchase_order_no like '%'|| #{purchaseOrderNo} || '%'
	</if>
    <if test="customer != null and customer != ''">
	  and customer like '%'|| #{customer} || '%'
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="status == null or status == ''">
            and t.STATUS !='2'
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizCustomerAccountResultMap" parameterType="com.dcjet.cs.customerAccount.model.BizCustomerAccount">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_customer_account t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.update_time,t.create_time) desc
    </select>
    <select id="checkIdNotCancel" resultType="java.lang.String">
        select sid
        from t_biz_customer_account t
        where t.account_no in  (select  distinct account_no
                             from t_biz_customer_account t
                             where SID = #{sid}
        ) and  t.STATUS !=2;
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_customer_account t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getMaxVersionNoByContract" resultType="com.dcjet.cs.customerAccount.model.BizCustomerAccount">
        select sid,VERSION_NO  from t_biz_customer_account
        where account_no = #{accountNo}
          and trade_code = #{tradeCode}
        order by CAST(VERSION_NO AS INTEGER) desc limit 1
    </select>
    <select id="getAttachmentFile" resultType="com.dcjet.cs.attach.model.Attached">
        SELECT
            SID,
            TRADE_CODE,
            HEAD_ID,
            BUSINESS_TYPE,
            ACMP_TYPE,
            ACMP_NO,
            FILE_NAME,
            NOTE,
            ORIGIN_FILE_NAME,
            FDFS_ID,
            INSERT_USER,
            INSERT_TIME,
            UPDATE_USER,
            UPDATE_TIME,
            INSERT_USER_NAME,
            UPDATE_USER_NAME,
            FILE_SIZE,
            DATA_SOURCE
        FROM
            T_ATTACHED
        WHERE
            HEAD_ID = #{headId};
    </select>
    <select id="checkKey" resultType="java.lang.Integer">
        select count(1) from t_biz_customer_account where account_no = #{accountNo} and TRADE_CODE = #{tradeCode} and STATUS !='2'
        <if test="sid != null and sid != ''"> and sid !=#{sid} </if>
    </select>
    <select id="getSerialNo" resultType="java.lang.String">
        SELECT * FROM (
                          SELECT ACCOUNT_NO from T_BIZ_CUSTOMER_ACCOUNT tbcas
                          ) t
        WHERE t.ACCOUNT_NO like '%'|| #{prefix} || '%' ORDER BY t.ACCOUNT_NO DESC limit 1
    </select>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            t.customer as "value",
            m.MERCHANT_NAME_CN as "label"
        from  t_biz_customer_account t LEFT JOIN T_BIZ_MERCHANT m ON t.customer = m.merchant_code
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="getCreateUserList" resultType="java.util.Map">
        select
            distinct
            COALESCE(update_by,create_by) as "value"
                   ,COALESCE(update_user_name,create_user_name) as "label"
        from  t_biz_customer_account t
        where t.TRADE_CODE = #{tradeCode};
    </select>
</mapper>
