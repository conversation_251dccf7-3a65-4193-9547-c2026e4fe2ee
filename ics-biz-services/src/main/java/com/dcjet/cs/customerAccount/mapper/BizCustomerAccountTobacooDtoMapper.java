package com.dcjet.cs.customerAccount.mapper;
import com.dcjet.cs.dto.customerAccount.*;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizCustomerAccountTobacooDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizCustomerAccountTobacooDto toDto(BizCustomerAccountTobacoo po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizCustomerAccountTobacoo toPo(BizCustomerAccountTobacooParam param);
    /**
     * 数据库原始数据更新
     * @param bizCustomerAccountTobacooParam
     * @param bizCustomerAccountTobacoo
     */
    void updatePo(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, @MappingTarget BizCustomerAccountTobacoo bizCustomerAccountTobacoo);
    default void patchPo(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, BizCustomerAccountTobacoo bizCustomerAccountTobacoo) {
        // TODO 自行实现局部更新
    }
}
