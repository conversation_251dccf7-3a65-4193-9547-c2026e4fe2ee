package com.dcjet.cs.customerAccount.dao;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizCustomerAccountTobacoo
* <AUTHOR>
* @date: 2025-7-2
*/
public interface BizCustomerAccountTobacooMapper extends Mapper<BizCustomerAccountTobacoo> {
    /**
     * 查询获取数据
     * @param bizCustomerAccountTobacoo
     * @return
     */
    List<BizCustomerAccountTobacoo> getList(BizCustomerAccountTobacoo bizCustomerAccountTobacoo);
    List<ForeignContractHead> getForeignContractHeadList(ForeignContractHead foreignContractHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    String getSerialNo(String prefix);
    List<Map<String, String>> getOrderSupplierList(String company);

    List<Map<String, String>> getCreateUserList(String company);

    List<BizCustomerAccountTobacoo> selectByPurchaseNoMark(@Param("purchaseNoMark") String purchaseNoMark);
}
