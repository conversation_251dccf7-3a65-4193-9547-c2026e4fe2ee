package com.dcjet.cs.customerAccount.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.customerAccount.model.BizCustomerAccount;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import com.dcjet.cs.deliveryOrder.model.BizDeliveryOrderHead;
import com.dcjet.cs.dto.deliveryOrder.BizDeliveryOrderHeadParam;
import com.dcjet.cs.dto.seven.SevenForeignContractHeadDto;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper;
import com.dcjet.cs.seven.dao.SevenForeignContractListMapper;
import com.dcjet.cs.seven.mapper.SevenForeignContractHeadDtoMapper;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.seven.model.SevenForeignContractList;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.customerAccount.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountSliceMapper;
import com.dcjet.cs.customerAccount.mapper.BizCustomerAccountSliceDtoMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSlice;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.pcode.service.PCodeHolder;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-28
 */
@Service
public class BizCustomerAccountSliceService extends BaseService<BizCustomerAccountSlice> {
    @Resource
    private BizCustomerAccountSliceMapper bizCustomerAccountSliceMapper;
    @Resource
    private BizCustomerAccountSliceDtoMapper bizCustomerAccountSliceDtoMapper;
    @Override
    public Mapper<BizCustomerAccountSlice> getMapper() {
        return bizCustomerAccountSliceMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizCustomerAccountSliceParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizCustomerAccountSliceDto>> getListPaged(BizCustomerAccountSliceParam bizCustomerAccountSliceParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizCustomerAccountSlice bizCustomerAccountSlice = bizCustomerAccountSliceDtoMapper.toPo(bizCustomerAccountSliceParam);
        bizCustomerAccountSlice.setTradeCode(userInfo.getCompany());
        Page<BizCustomerAccountSlice> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountSliceMapper.getList(bizCustomerAccountSlice));
        List<BizCustomerAccountSliceDto> bizCustomerAccountSliceDtos = page.getResult().stream().map(head -> {
            BizCustomerAccountSliceDto dto = bizCustomerAccountSliceDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizCustomerAccountSliceDto>> paged = ResultObject.createInstance(bizCustomerAccountSliceDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizCustomerAccountSliceParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountSliceDto insert(BizCustomerAccountSliceParam bizCustomerAccountSliceParam, UserInfoToken userInfo) {
        BizCustomerAccountSlice bizCustomerAccountSlice = bizCustomerAccountSliceDtoMapper.toPo(bizCustomerAccountSliceParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizCustomerAccountSlice.setSid(sid);
        bizCustomerAccountSlice.setTradeCode(userInfo.getCompany());
        bizCustomerAccountSlice.setCreateBy(userInfo.getUserNo());
        bizCustomerAccountSlice.setCreateUserName(userInfo.getUserName());
        bizCustomerAccountSlice.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizCustomerAccountSliceMapper.insert(bizCustomerAccountSlice);
        return  insertStatus > 0 ? bizCustomerAccountSliceDtoMapper.toDto(bizCustomerAccountSlice) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizCustomerAccountSliceParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountSliceDto update(BizCustomerAccountSliceParam bizCustomerAccountSliceParam, UserInfoToken userInfo) {
        BizCustomerAccountSlice bizCustomerAccountSlice = bizCustomerAccountSliceMapper.selectByPrimaryKey(bizCustomerAccountSliceParam.getSid());
        bizCustomerAccountSliceDtoMapper.updatePo(bizCustomerAccountSliceParam, bizCustomerAccountSlice);
        bizCustomerAccountSlice.setUpdateBy(userInfo.getUserNo());
        bizCustomerAccountSlice.setUpdateTime(new Date());
        bizCustomerAccountSlice.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = bizCustomerAccountSliceMapper.updateByPrimaryKey(bizCustomerAccountSlice);
        return update > 0 ? bizCustomerAccountSliceDtoMapper.toDto(bizCustomerAccountSlice) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizCustomerAccountSliceMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizCustomerAccountSliceDto> selectAll(BizCustomerAccountSliceParam exportParam, UserInfoToken userInfo) {
        BizCustomerAccountSlice bizCustomerAccountSlice = bizCustomerAccountSliceDtoMapper.toPo(exportParam);
         bizCustomerAccountSlice.setTradeCode(userInfo.getCompany());
        List<BizCustomerAccountSliceDto> bizCustomerAccountSliceDtos = new ArrayList<>();
        List<BizCustomerAccountSlice> bizCustomerAccountSlices = bizCustomerAccountSliceMapper.getList(bizCustomerAccountSlice);
        if (CollectionUtils.isNotEmpty(bizCustomerAccountSlices)) {
            bizCustomerAccountSliceDtos = bizCustomerAccountSlices.stream().map(head -> {
                BizCustomerAccountSliceDto dto = bizCustomerAccountSliceDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizCustomerAccountSliceDtos;
    }

    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizCustomerAccountSlice slice = bizCustomerAccountSliceMapper.selectByPrimaryKey(sid);
        if (slice == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(slice.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        slice.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(slice); // 调用更新审批状态的方法
        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizCustomerAccountSlice slice) {
        BizCustomerAccountSlice update = new BizCustomerAccountSlice();
        update.setSid(slice.getSid());
        update.setApprStatus(slice.getApprStatus());
        bizCustomerAccountSliceMapper.updateByPrimaryKeySelective(update);
    }

    public ResultObject confirmStatus(BizCustomerAccountSliceParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizCustomerAccountSlice slice = bizCustomerAccountSliceMapper.selectByPrimaryKey(param.getSid());
        if (slice == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if(StringUtils.isBlank(slice.getAccountNo()) || StringUtils.isBlank(slice.getPurchaseOrderNo()) || StringUtils.isBlank(slice.getContractNo())
                || StringUtils.isBlank(slice.getCustomer()) || StringUtils.isBlank(slice.getCurrI()) || StringUtils.isBlank(slice.getCurrE()) || null == slice.getExchangeRateE()
                || null == slice.getExchangeRateI() || null == slice.getGoodsPriceE() || null == slice.getGoodsPriceI() || null == slice.getBusinessDate()){
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不不完整，请完善数据");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(slice.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizCustomerAccountSlice update = bizCustomerAccountSliceDtoMapper.toPo(param);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsConfirm("1");
        bizCustomerAccountSliceMapper.updateByPrimaryKeySelective(update);
        BizCustomerAccountSliceDto dto = bizCustomerAccountSliceDtoMapper.toDto(update);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccountSlice slice = bizCustomerAccountSliceMapper.selectByPrimaryKey(sid);
        if (slice == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(slice.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许退单");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(slice.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
        BizCustomerAccountSlice update = new BizCustomerAccountSlice();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        update.setIsConfirm("0");
        bizCustomerAccountSliceMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccountSlice slice = bizCustomerAccountSliceMapper.selectByPrimaryKey(sid);
        if (slice == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(slice.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(slice.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizCustomerAccountSlice update = new BizCustomerAccountSlice();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        update.setIsConfirm("0");
        bizCustomerAccountSliceMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResultObject redFlush(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("红冲成功"));

        BizCustomerAccountSlice slice = bizCustomerAccountSliceMapper.selectByPrimaryKey(sid);
        if (null == slice) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        BizCustomerAccountSlice update = new BizCustomerAccountSlice();
        update.setSid(sid);
        update.setRedFlush(CommonEnum.RED_FLUSH_ENUM.YES.getValue());
        bizCustomerAccountSliceMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResultObject checkIdNotCancel(BizCustomerAccountSliceParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        if (StringUtils.isBlank(param.getSid())){
            throw new ErrorException(400, XdoI18nUtil.t("请选择需要复制数据！"));
        }
        List<String> sids = bizCustomerAccountSliceMapper.checkIdNotCancel(param.getSid());
        if (CollectionUtils.isNotEmpty((sids))){
            resultObject.setSuccess(false);
            return resultObject;
        }
        return resultObject;
    }
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private PCodeHolder pCodeHolder;

    public ResponseEntity print(BizCustomerAccountSliceParam param, UserInfoToken userInfo) throws Exception {
        BizCustomerAccountSlice slice = bizCustomerAccountSliceMapper.selectByPrimaryKey(param.getSid());
        if(slice == null){
            throw new ErrorException(400,"客户结算单不存在！");
        }
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        if(!ObjectUtils.isEmpty(bizMerchants)){
            Optional<BizMerchant> first = bizMerchants.stream().filter(x -> x.getMerchantCode().equals(slice.getCustomer())).findFirst();
            first.ifPresent(merchant -> slice.setCustomer(merchant.getMerchantNameCn()));
        }
        List<SevenForeignContractList> listByHeadId = sevenForeignContractListMapper.getListByHeadId(slice.getPurchaseNoMark(),userInfo.getCompany());
        if(CollectionUtils.isNotEmpty(listByHeadId)){
            String sI = "";
            String sE = "";
            BigDecimal bE = new BigDecimal(0);
            BigDecimal bI = new BigDecimal(0);
            BigDecimal bET = new BigDecimal(0);
            BigDecimal bIT = new BigDecimal(0);
            Optional<SevenForeignContractList> firstE = listByHeadId.stream().filter(x -> "process".equals(x.getBodyType())).findFirst();
            if(firstE.isPresent()){
                sE = firstE.get().getGName();
                bE = listByHeadId.stream().filter(x -> "process".equals(x.getBodyType())).map(SevenForeignContractList::getQty).reduce(BigDecimal.ZERO,BigDecimal::add);
                bET = bE.divide(new BigDecimal(1000),2,RoundingMode.HALF_UP);
            }
            Optional<SevenForeignContractList> firstI = listByHeadId.stream().filter(x -> "slice".equals(x.getBodyType())).findFirst();
            if(firstI.isPresent()){
                sI = firstI.get().getGName();
                bI = listByHeadId.stream().filter(x -> "slice".equals(x.getBodyType())).map(SevenForeignContractList::getQty).reduce(BigDecimal.ZERO,BigDecimal::add);
                bET = bI.divide(new BigDecimal(1000),2,RoundingMode.HALF_UP);
            }
            slice.setProducrSome(sE+NumberFormatterUtils.formatNumber(bET)+"吨;"+sI+NumberFormatterUtils.formatNumber(bIT)+"吨");
        }
        slice.setGoodsPriceEStr(slice.getCurrE()+NumberFormatterUtils.formatNumber(slice.getGoodsPriceE()));
        slice.setGoodsPriceIStr(slice.getCurrI()+NumberFormatterUtils.formatNumber(slice.getGoodsPriceI()));
        slice.setExchangeRateEStr(NumberFormatterUtils.formatNumber(slice.getExchangeRateE()));
        slice.setExchangeRateIStr(NumberFormatterUtils.formatNumber(slice.getExchangeRateI()));
        slice.setGoodsPriceIRmbStr("人民币" + NumberFormatterUtils.formatNumber(slice.getGoodsPriceIRmb()) + "元（1" + pCodeHolder.getValue(PCodeType.CURR, slice.getCurrI())
                + "=" + NumberFormatterUtils.formatNumber(slice.getExchangeRateI()) +"人民币）");
        slice.setGoodsPriceERmbStr("人民币" + NumberFormatterUtils.formatNumber(slice.getGoodsPriceERmb()) + "元（1" + pCodeHolder.getValue(PCodeType.CURR, slice.getCurrE())
                + "=" + NumberFormatterUtils.formatNumber(slice.getExchangeRateE()) +"人民币）");
        slice.setAgentFeeRateStr(NumberFormatterUtils.formatNumber(slice.getAgentFeeRate()) + "%");
        slice.setTotalAmountRmbStr("共计人民币" + NumberFormatterUtils.formatNumber(slice.getTotalAmount())+"元");

        String tempName = "biz_customer_account_slice.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        slice.setBusinessDateStr(sdf2.format(slice.getBusinessDate()));
        String formattedDate = sdf.format(new Date());
        slice.setCreaterUserName(StringUtils.isEmpty(slice.getUpdateUserName()) ? slice.getCreateUserName() : slice.getUpdateUserName());

        String outName = xdoi18n.XdoI18nUtil.t("出料加工进口薄片客户结算单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(slice),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
    public String getSerialNo(UserInfoToken userInfo){
        String head = "YJSKHJS";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(new Date());
        String serialNo = "";
        String serialNo1 = bizCustomerAccountSliceMapper.getSerialNo(head + date);
        if(StringUtils.isNotBlank(serialNo1)){
            serialNo = head + date + String.format("%03d",Integer.parseInt(serialNo1.substring(serialNo1.length() - 3)) + 1);
        }else {
            serialNo = head + date + "001";
        }
        return serialNo;
    }

    public ResultObject getSupplierList(BizCustomerAccountSliceParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountSliceMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    public ResultObject getCreateUserList(BizCustomerAccountSliceParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountSliceMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    @Resource
    private SevenForeignContractHeadMapper sevenForeignContractHeadMapper;
    @Resource
    private SevenForeignContractListMapper sevenForeignContractListMapper;
    @Resource
    private SevenForeignContractHeadDtoMapper sevenForeignContractHeadDtoMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    public ResultObject<List<SevenForeignContractHeadDto>> listInDeliveryOrderHead(BizCustomerAccountSliceParam params, PageParam pageParam, UserInfoToken userInfo) {
        BizCustomerAccountSlice slice = bizCustomerAccountSliceDtoMapper.toPo(params);
        slice.setTradeCode(userInfo.getCompany());
        Page<SevenForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountSliceMapper.getForeignContractHeadListQty(slice));
        List<SevenForeignContractHeadDto> headDtoList = page.getResult().stream()
                .map(fcd -> this.sevenForeignContractHeadDtoMapper.toDto(fcd))
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }

    public ResultObject insertByContract(BizCustomerAccountSliceParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        SevenForeignContractHead contractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(param.getSid());
        if(ObjectUtils.isEmpty(contractHead)){
            resultObject.setSuccess(false);
            resultObject.setMessage("外商合同数据不存在无法生成客户结算数据");
            return resultObject;
        }
        List<SevenForeignContractList> listByHeadId = sevenForeignContractListMapper.getListByHeadId(param.getSid(),userInfo.getCompany());
        List<EnterpriseRate> select = enterpriseRateMapper.selectMessage(new EnterpriseRate() {{
            setTradeCode(userInfo.getCompany());
            setCurr(contractHead.getCurr());
        }});

        String sid = UUID.randomUUID().toString();
        BizCustomerAccountSlice insert = new BizCustomerAccountSlice();
        insert.setCustomer(contractHead.getBuyer());
        insert.setContractNo(contractHead.getContractNo());
        insert.setAccountNo(getSerialNo(userInfo));
        insert.setCurrE(contractHead.getCurr());
        insert.setCurrI(contractHead.getCurr());
        insert.setAgentFeeRate(new BigDecimal(3));
        insert.setTotalAmount(new BigDecimal(6));
        if(CollectionUtils.isNotEmpty(select)){
            insert.setExchangeRateE(select.get(0).getRate());
            insert.setExchangeRateI(select.get(0).getRate());
        }
        if(CollectionUtils.isNotEmpty(listByHeadId)){
            BigDecimal reduceE = listByHeadId.stream().filter(x -> "process".equals(x.getBodyType())).map(SevenForeignContractList::getMoneyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            insert.setGoodsPriceE(reduceE);
            BigDecimal reduceI = listByHeadId.stream().filter(x -> "slice".equals(x.getBodyType())).map(SevenForeignContractList::getMoneyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            insert.setGoodsPriceI(reduceI);
            if(null != insert.getExchangeRateE()){
                insert.setGoodsPriceERmb(reduceE.multiply(insert.getExchangeRateE()).setScale(2, RoundingMode.HALF_UP));
            }
            if(null != insert.getExchangeRateI()){
                insert.setGoodsPriceIRmb(reduceI.multiply(insert.getExchangeRateI()).setScale(2, RoundingMode.HALF_UP));
            }
        }

        insert.setBusinessDate(new Date());
        insert.setSid(sid);
        insert.setPurchaseMark("1");
        insert.setPurchaseNoMark(contractHead.getId());
        insert.setStatus("0");
        insert.setRedFlush("1");
        insert.setSendFinance("0");
        insert.setTradeCode(userInfo.getCompany());
        insert.setCreateBy(userInfo.getUserNo());
        insert.setCreateTime(new Date());
        insert.setCreateUserName(userInfo.getUserName());
        insert.setBusinessType("7");
        insert.setApprStatus("0");
        insert.setIsConfirm("0");

        int insertStatus = bizCustomerAccountSliceMapper.insert(insert);
        insert.setCreaterBy(userInfo.getUserNo());
        insert.setCreaterTime(new Date());
        insert.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? bizCustomerAccountSliceDtoMapper.toDto(insert) : null);
        return resultObject;
    }
}
