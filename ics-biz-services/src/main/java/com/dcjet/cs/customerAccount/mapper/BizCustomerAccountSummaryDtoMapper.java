package com.dcjet.cs.customerAccount.mapper;
import com.dcjet.cs.dto.customerAccount.*;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizCustomerAccountSummaryDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizCustomerAccountSummaryDto toDto(BizCustomerAccountSummary po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizCustomerAccountSummary toPo(BizCustomerAccountSummaryParam param);
    /**
     * 数据库原始数据更新
     * @param bizCustomerAccountSummaryParam
     * @param bizCustomerAccountSummary
     */
    void updatePo(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, @MappingTarget BizCustomerAccountSummary bizCustomerAccountSummary);
    default void patchPo(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, BizCustomerAccountSummary bizCustomerAccountSummary) {
        // TODO 自行实现局部更新
    }
}
