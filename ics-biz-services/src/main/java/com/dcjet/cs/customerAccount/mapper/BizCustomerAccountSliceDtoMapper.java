package com.dcjet.cs.customerAccount.mapper;
import com.dcjet.cs.dto.customerAccount.*;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSlice;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-7-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizCustomerAccountSliceDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizCustomerAccountSliceDto toDto(BizCustomerAccountSlice po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizCustomerAccountSlice toPo(BizCustomerAccountSliceParam param);
    /**
     * 数据库原始数据更新
     * @param bizCustomerAccountSliceParam
     * @param bizCustomerAccountSlice
     */
    void updatePo(BizCustomerAccountSliceParam bizCustomerAccountSliceParam, @MappingTarget BizCustomerAccountSlice bizCustomerAccountSlice);
    default void patchPo(BizCustomerAccountSliceParam bizCustomerAccountSliceParam, BizCustomerAccountSlice bizCustomerAccountSlice) {
        // TODO 自行实现局部更新
    }
}
