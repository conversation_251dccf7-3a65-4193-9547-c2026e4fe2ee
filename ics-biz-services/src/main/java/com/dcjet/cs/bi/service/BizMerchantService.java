package com.dcjet.cs.bi.service;
import com.dcjet.cs.bi.mapper.BizMerchantDtoMapper;
import com.dcjet.cs.dto.bi.BizMaterialInformationKeyCode;
import com.dcjet.cs.dto.bi.BizMerchantDto;
import com.dcjet.cs.dto.bi.BizMerchantParam;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;

import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class BizMerchantService extends BaseService<BizMerchant> {
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BizMerchantDtoMapper bizMerchantDtoMapper;
    @Override
    public Mapper<BizMerchant> getMapper() {
        return bizMerchantMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizMerchantParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizMerchantDto>> getListPaged(BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(bizMerchantParam);
        bizMerchant.setTradeCode(userInfo.getCompany());
        Page<BizMerchant> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizMerchantMapper.getList(bizMerchant));
        List<BizMerchantDto> bizMerchantDtos = page.getResult().stream().map(head -> {
            BizMerchantDto dto = bizMerchantDtoMapper.toDto(head);
            if (StringUtils.isNotEmpty(dto.getCommonFlag())){
                dto.setCommonFlagList(Arrays.asList(dto.getCommonFlag().split(",")));
            }
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizMerchantDto>> paged = ResultObject.createInstance(bizMerchantDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizMerchantParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizMerchantDto insert(BizMerchantParam bizMerchantParam, UserInfoToken userInfo) {
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(bizMerchantParam);
        /**
         * 规范固定字段
         */
        // if (StringUtils.isEmpty(bizMerchantParam.getMerchantCode())) {
        //     throw new ErrorException(400, "客商编码不能为空！");
        // }
        if (StringUtils.isEmpty(bizMerchantParam.getMerchantNameCn())) {
            throw new ErrorException(400, "客商中文名称不能为空！");
        }
        String sid = UUID.randomUUID().toString();
        bizMerchant.setSid(sid);
        bizMerchant.setInsertUser(userInfo.getUserNo());
        bizMerchant.setInsertTime(new Date());
        bizMerchant.setTradeCode(userInfo.getCompany());
//        int check = bizMerchantMapper.checkKey(bizMerchant);
//        if(check>0){
//            throw new ErrorException(400, "客商编码已经存在！");
//        }
        int check = bizMerchantMapper.checkMerchantNameCn(bizMerchant);
        if(check>0){
            throw new ErrorException(400, "客商中文名称已经存在！");
        }

        // 获取当前客商信息的最大序号
        Integer maxSeqNo = bizMerchantMapper.getMaxSeqNo(userInfo.getCompany());
        String format = String.format("%04d", maxSeqNo);
        bizMerchant.setMerchantCode(format);
        bizMerchant.setSerialNo(maxSeqNo);

        if (CollectionUtils.isNotEmpty(bizMerchant.getCommonFlagList())){
            bizMerchant.setCommonFlag(bizMerchant.getCommonFlagList().stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
        }else {
            bizMerchant.setCommonFlag(null);
        }

        // 新增数据
        int insertStatus = bizMerchantMapper.insert(bizMerchant);
        return  insertStatus > 0 ? bizMerchantDtoMapper.toDto(bizMerchant) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizMerchantParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizMerchantDto update(BizMerchantParam bizMerchantParam, UserInfoToken userInfo) {
        if (StringUtils.isEmpty(bizMerchantParam.getMerchantCode())) {
            throw new ErrorException(400, "客商编码不能为空！");
        }
        if (StringUtils.isEmpty(bizMerchantParam.getMerchantNameCn())) {
            throw new ErrorException(400, "客商中文名称不能为空！");
        }
        BizMerchant bizMerchant = bizMerchantMapper.selectByPrimaryKey(bizMerchantParam.getSid());
        bizMerchantDtoMapper.updatePo(bizMerchantParam, bizMerchant);
        bizMerchant.setUpdateUser(userInfo.getUserNo());
        bizMerchant.setUpdateTime(new Date());
        // 更新数据
//        int check = bizMerchantMapper.checkKey(bizMerchant);
//        if(check>0){
//            throw new RuntimeException("客商编码已经存在！");
//        }
        int check = bizMerchantMapper.checkMerchantNameCn(bizMerchant);
        if(check>0){
            throw new ErrorException(400, "客商中文名称已经存在！");
        }
        if (CollectionUtils.isNotEmpty(bizMerchant.getCommonFlagList())){
            bizMerchant.setCommonFlag(bizMerchant.getCommonFlagList().stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
        }else {
            bizMerchant.setCommonFlag(null);
        }
        int update = bizMerchantMapper.updateByPrimaryKey(bizMerchant);
        return update > 0 ? bizMerchantDtoMapper.toDto(bizMerchant) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizMerchantMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizMerchantDto> selectAll(BizMerchantParam exportParam, UserInfoToken userInfo) {
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(exportParam);
         bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchantDto> bizMerchantDtos = new ArrayList<>();
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        if (CollectionUtils.isNotEmpty(bizMerchants)) {
            bizMerchantDtos = bizMerchants.stream().map(head -> {
                BizMerchantDto dto = bizMerchantDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizMerchantDtos;
    }

    public List<BizMaterialInformationKeyCode> selectPaclInfo(String company) {
        return  bizMerchantMapper.selectPaclInfo(company);
    }

    public List<BizMaterialInformationKeyCode> selectMerchandiseCategories(String company) {
        return bizMerchantMapper.selectMerchandiseCategories(company);
    }

    public String getMerchantCode(BizMerchantParam bizMerchantParam, UserInfoToken userInfo){
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(bizMerchantParam);
        bizMerchant.setTradeCode(userInfo.getCompany());
        Integer number = bizMerchantMapper.selectMerchantCode(bizMerchant);
        return String.format("%04d", number+1);
    }
    public BizMerchant getDefaultBuyer(BizMerchantParam bizMerchantParam, UserInfoToken userInfo){
        BizMerchant param = bizMerchantDtoMapper.toPo(bizMerchantParam);
        param.setTradeCode(userInfo.getCompany());
        param.setMerchantNameCn("中国烟草上海进出口有限责任公司");
        BizMerchant bizMerchant = bizMerchantMapper.getMerchantByName(param);
        return bizMerchant;
    }

    public Map<String, String> getMerchantMap(UserInfoToken userInfo) {
        //获取供应商
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        Map<String, String> merchantMap = createMerchantMap(bizMerchants);
        return merchantMap;
    }

    /**
     * 创建商户映射表，处理重复key的情况
     * @param bizMerchants 商户列表
     * @return 商户编码到中文名称的映射
     */
    public Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }


    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizMerchantParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizMerchantDto>> getForAggrSearch(BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(bizMerchantParam);
        bizMerchant.setTradeCode(userInfo.getCompany());
        Page<BizMerchant> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizMerchantMapper.getForAggrSearch(bizMerchant));
        List<BizMerchantDto> bizMerchantDtos = page.getResult().stream().map(head -> {
            BizMerchantDto dto = bizMerchantDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizMerchantDto>> paged = ResultObject.createInstance(bizMerchantDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizMerchantParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizMerchantDto>> getForAggrDomesticSearch(BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(bizMerchantParam);
        bizMerchant.setTradeCode(userInfo.getCompany());
        Page<BizMerchant> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizMerchantMapper.getForAggrDomesticSearch(bizMerchant));
        List<BizMerchantDto> bizMerchantDtos = page.getResult().stream().map(head -> {
            BizMerchantDto dto = bizMerchantDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizMerchantDto>> paged = ResultObject.createInstance(bizMerchantDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizMerchantParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizMerchantDto>> getForThirdPlanBuyerSearch(BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(bizMerchantParam);
        bizMerchant.setTradeCode(userInfo.getCompany());
        Page<BizMerchant> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizMerchantMapper.getForThirdPlanBuyerSearch(bizMerchant));
        List<BizMerchantDto> bizMerchantDtos = page.getResult().stream().map(head -> {
            BizMerchantDto dto = bizMerchantDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizMerchantDto>> paged = ResultObject.createInstance(bizMerchantDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizMerchantParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizMerchantDto>> getForThirdPlanSellerSearch(BizMerchantParam bizMerchantParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizMerchant bizMerchant = bizMerchantDtoMapper.toPo(bizMerchantParam);
        bizMerchant.setTradeCode(userInfo.getCompany());
        Page<BizMerchant> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizMerchantMapper.getForThirdPlanSellerSearch(bizMerchant));
        List<BizMerchantDto> bizMerchantDtos = page.getResult().stream().map(head -> {
            BizMerchantDto dto = bizMerchantDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizMerchantDto>> paged = ResultObject.createInstance(bizMerchantDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

}
