<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.ExpenseIHeadMapper">
    <sql id="Base_Column_List">
        h.sid
        ,h.BUSINESS_TYPE
        ,h.DOCUMENT_NUMBER
        ,h.PAYEE
        ,h.CURR
        ,h.ADVANCE_MARK
        ,h.SEND_UFIDA
        ,h.STATE
        ,h.CREATE_USER
        ,h.CREATE_USER_TIME
        ,h.CONFIRMATION_TIME
        ,h.TRADE_CODE
        ,h.INSERT_USER
        ,h.INSERT_TIME
        ,h.UPDATE_USER
        ,h.UPDATE_TIME
        ,h.REMARK
        ,h.ADVANCE_FLAG
        ,h.DEPT_NAME
    </sql>
    <sql id="condition">
        <if test="state != null and state != ''">
                and h.state =  #{state}
        </if>
        <if test="state == null or state == ''">
            and h.state != '2'
        </if>
        <if test="businessType != null and businessType != ''">
            and h.BUSINESS_TYPE =  #{businessType}
        </if>
        <if test="payee != null and payee != ''">
            and h.payee =  #{payee}
        </if>
        <if test="expenseType != null and expenseType != ''">
            and el.expense_type like concat(concat('%',#{expenseType}),'%')
        </if>
        <if test="contractNumber != null and contractNumber != ''">
            and el.CONTRACT_NUMBER like concat(concat('%',#{contractNumber}),'%')
        </if>
        <if test="orderNumber != null and orderNumber != ''">
            and el.ORDER_NUMBER like concat(concat('%',#{orderNumber}),'%')
        </if>
        <if test="createUserTimeFrom != null and createUserTimeFrom != ''">
            <![CDATA[ and h.CREATE_USER_TIME >= to_timestamp(SUBSTR(#{createUserTimeFrom},1,10),'yyyy-MM-dd')]]>
        </if>
        <if test="createUserTimeTo != null and createUserTimeTo != ''">
            <![CDATA[ and h.CREATE_USER_TIME < to_timestamp(SUBSTR(#{createUserTimeTo},1,10),'yyyy-MM-dd') + INTERVAL '1' DAY]]>
        </if>
        <if test="createUser != null and createUser != ''">
            and h.CREATE_USER like concat(concat('%',#{createUser}),'%')
        </if>
        <if test="sid != null and sid != ''">
            and h.SID = #{sid}
        </if>
    </sql>
    <update id="chargebackBySids">
        update T_BIZ_EXPENSE_I_HEAD
            set STATE = '0',
                CONFIRMATION_TIME = null
        where sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        update T_BIZ_EXPENSE_I_List
        set STATE = '0'
        where head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </update>
    <update id="chargeAffirmBySids">
        update T_BIZ_EXPENSE_I_HEAD
        set STATE = '1',
            CONFIRMATION_TIME = now()
        where sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        update T_BIZ_EXPENSE_I_LIST
        set STATE = '1'
        where head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </update>
    <delete id="deleteBySids">
        delete from T_BIZ_EXPENSE_I_HEAD  where sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        delete from T_BIZ_EXPENSE_I_LIST  where head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        delete from T_ATTACHED where HEAD_ID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </delete>
    <delete id="cancellationBySids">
        update T_BIZ_EXPENSE_I_HEAD
        set STATE = '2'
        where sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        update T_BIZ_EXPENSE_I_LIST
        set STATE = '2'
        where head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </delete>
    <select id="selectByDocumentNumber" resultType="com.dcjet.cs.bi.model.ExpenseIHead">
        select * from T_BIZ_EXPENSE_I_HEAD
        where trade_code = #{tradeCode}
        <if test="documentNumber != null and documentNumber != ''">
            and DOCUMENT_NUMBER like concat(concat('%',#{documentNumber}),'%')
        </if>
        order by CREATE_USER_TIME DESC
        limit 1;
    </select>
    <select id="getList" resultType="com.dcjet.cs.bi.model.ExpenseIHead">
        WITH ExpenseList AS (
        SELECT
        l.head_id,
        SUM(l.EXPENSE_AMOUNT) AS total_Amount,
        WM_CONCAT(DISTINCT l.EXPENSE_TYPE) AS EXPENSE_TYPE,
        WM_CONCAT(DISTINCT l.CONTRACT_NUMBER) AS CONTRACT_NUMBER,
        WM_CONCAT(DISTINCT l.purchase_number) AS ORDER_NUMBER
        FROM T_BIZ_EXPENSE_I_LIST l
        GROUP BY l.head_id
        )
        select
        <include refid="Base_Column_List"/>
        ,el.total_Amount
        ,el.EXPENSE_TYPE
        ,el.CONTRACT_NUMBER
        ,el.ORDER_NUMBER
        FROM T_BIZ_EXPENSE_I_HEAD h
        LEFT JOIN ExpenseList el ON h.sid = el.head_id
        <where>
            h.trade_code = #{tradeCode}
            <include refid="condition"></include>
        </where>
        order by INSERT_TIME desc
    </select>
    <update id="updateTaxReceiptListBySid">
        update t_biz_warehouse_receipt_list as t
        set TARIFF = (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                            left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%1%' and ct.TRADE_CODE = l.TRADE_CODE
                                                            left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                      where ct.COST_NAME like '%关税' and h.sid = #{sid} and l.PRODUCT_NAME = t.GOODS_NAME and l.trade_code = #{tradeCode} and rh.WAREHOUSE_RECEIPT_NUMBER = l.PURCHASE_NUMBER),
            CONSUMPTION_TAX = (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                                     left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%1%' and ct.TRADE_CODE = l.TRADE_CODE
                                                                     left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                               where ct.COST_NAME like '%消费税' and h.sid = #{sid} and l.PRODUCT_NAME = t.GOODS_NAME and l.trade_code = #{tradeCode} and rh.WAREHOUSE_RECEIPT_NUMBER = l.PURCHASE_NUMBER),
            VALUE_ADDED_TAX = (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                                     left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%1%' and ct.TRADE_CODE = l.TRADE_CODE
                                                                     left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                               where ct.COST_NAME like '%增值税' and h.sid = #{sid} and l.PRODUCT_NAME = t.GOODS_NAME and l.trade_code = #{tradeCode} and rh.WAREHOUSE_RECEIPT_NUMBER = l.PURCHASE_NUMBER),
            update_user = #{userNo},
            update_time = now()
            from T_BIZ_WAREHOUSE_RECEIPT_HEAD rh
        where rh.sid = t.parent_id and rh.WAREHOUSE_RECEIPT_NUMBER = (
            select distinct  PURCHASE_NUMBER from T_BIZ_EXPENSE_I_LIST where HEAD_ID = #{sid}
            );
    </update>
    <update id="updateBizStoreIHeadBysid">
        update t_biz_store_i_head as t
        set tariff_price = (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                                  left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%2%' and ct.TRADE_CODE = l.TRADE_CODE
                                                                  left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                            where ct.COST_NAME like '%关税' and h.sid = #{sid} and l.trade_code = #{tradeCode}),
            vat_price =  (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                                left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%2%' and ct.TRADE_CODE = l.TRADE_CODE
                                                                left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                          where ct.COST_NAME like '%增值税' and h.sid = #{sid}  and l.trade_code = #{tradeCode})
        where t.PURCHASE_NO_MARK in (
            select distinct  PURCHASE_NUMBER from T_BIZ_EXPENSE_I_LIST where HEAD_ID = #{sid}
            );
    </update>

</mapper>
