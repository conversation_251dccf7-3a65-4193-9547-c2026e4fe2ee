package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.ExpenseIHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ExpenseIHeadMapper extends Mapper<ExpenseIHead> {
    List<ExpenseIHead> selectByDocumentNumber(ExpenseIHead po);

    List<ExpenseIHead> getList(ExpenseIHead po);

    void deleteBySids(List<String> sids);

    void chargebackBySids(List<String> sids);

    void chargeAffirmBySids(List<String> sids);

    void cancellationBySids(List<String> sids);

    void updateTaxReceiptListBySid(@Param("sid") String sid, @Param("userNo") String userNo, @Param("tradeCode") String tradeCode);

    void updateBizStoreIHeadBysid(@Param("sid") String sid, @Param("userNo") String userNo, @Param("tradeCode") String tradeCode);
}
