package com.dcjet.cs.bi.service;
import com.dcjet.cs.bi.mapper.BizMaterialInformationDtoMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.dto.bi.BizMaterialInformationDto;
import com.dcjet.cs.dto.bi.BizMaterialInformationParam;
import com.dcjet.cs.dto.quo.BizQuotationDto;
import com.dcjet.cs.quo.model.BizQuotation;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-12
 */
@Service
public class BizMaterialInformationService extends BaseService<BizMaterialInformation> {
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    @Resource
    private BizMaterialInformationDtoMapper bizMaterialInformationDtoMapper;
    @Override
    public Mapper<BizMaterialInformation> getMapper() {
        return bizMaterialInformationMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizMaterialInformationParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizMaterialInformationDto>> getListPaged(BizMaterialInformationParam bizMaterialInformationParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizMaterialInformation bizMaterialInformation = bizMaterialInformationDtoMapper.toPo(bizMaterialInformationParam);
        bizMaterialInformation.setTradeCode(userInfo.getCompany());
        Page<BizMaterialInformation> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizMaterialInformationMapper.getList(bizMaterialInformation));
        List<BizMaterialInformationDto> bizMaterialInformationDtos = page.getResult().stream().map(head -> {
            BizMaterialInformationDto dto = bizMaterialInformationDtoMapper.toDto(head);
            if(null != head.getTaxRate()){
                dto.setTaxRate(head.getTaxRate().stripTrailingZeros().toPlainString());
            }
            if(null != head.getSupplierDiscountRate()){
                dto.setSupplierDiscountRate(head.getSupplierDiscountRate().stripTrailingZeros().toPlainString());
            }
            if (StringUtils.isNotEmpty(dto.getCommonMark())){
                dto.setCommonMarkList(Arrays.asList(dto.getCommonMark().split(",")));
            }
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizMaterialInformationDto>> paged = ResultObject.createInstance(bizMaterialInformationDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizMaterialInformationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizMaterialInformationDto insert(BizMaterialInformationParam bizMaterialInformationParam, UserInfoToken userInfo) {

        BizMaterialInformation bizMaterialInformation = bizMaterialInformationDtoMapper.toPo(bizMaterialInformationParam);
        bizMaterialInformation.setTradeCode(userInfo.getCompany());
        if(StringUtils.isNotBlank(bizMaterialInformation.getGname())){
            int i = bizMaterialInformationMapper.checkGName(bizMaterialInformation);
            if(i > 0){
                throw new ErrorException(400,"商品名称已存在！");
            }
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizMaterialInformation.setSid(sid);
        bizMaterialInformation.setInsertUser(userInfo.getUserNo());
        bizMaterialInformation.setInsertTime(new Date());
        bizMaterialInformation.setInsertUserName(userInfo.getUserName());
        if (CollectionUtils.isNotEmpty(bizMaterialInformation.getCommonMarkList())){
            bizMaterialInformation.setCommonMark(bizMaterialInformation.getCommonMarkList().stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
        }else {
            bizMaterialInformation.setCommonMark(null);
        }

        // 新增数据
        int insertStatus = bizMaterialInformationMapper.insert(bizMaterialInformation);
        return  insertStatus > 0 ? bizMaterialInformationDtoMapper.toDto(bizMaterialInformation) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizMaterialInformationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizMaterialInformationDto update(BizMaterialInformationParam bizMaterialInformationParam, UserInfoToken userInfo) {
        BizMaterialInformation bizMaterialInformation = bizMaterialInformationMapper.selectByPrimaryKey(bizMaterialInformationParam.getSid());
        bizMaterialInformationDtoMapper.updatePo(bizMaterialInformationParam, bizMaterialInformation);
        if(StringUtils.isNotBlank(bizMaterialInformation.getGname())){
            List<BizMaterialInformation> checkList = bizMaterialInformationMapper.checkGNameReturn(bizMaterialInformation);
            if(checkList.stream().anyMatch(x -> !x.getSid().equals(bizMaterialInformationParam.getSid()))){
                throw new ErrorException(400,"商品名称已存在！");
            }
        }
        bizMaterialInformation.setUpdateUser(userInfo.getUserNo());
        bizMaterialInformation.setUpdateTime(new Date());
        bizMaterialInformation.setUpdateUserName(userInfo.getUserName());
        if (CollectionUtils.isNotEmpty(bizMaterialInformation.getCommonMarkList())){
            bizMaterialInformation.setCommonMark(bizMaterialInformation.getCommonMarkList().stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
        }else {
            bizMaterialInformation.setCommonMark(null);
        }
        // 更新数据
        int update = bizMaterialInformationMapper.updateByPrimaryKey(bizMaterialInformation);
        return update > 0 ? bizMaterialInformationDtoMapper.toDto(bizMaterialInformation) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizMaterialInformationMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizMaterialInformationDto> selectAll(BizMaterialInformationParam exportParam, UserInfoToken userInfo) {
        BizMaterialInformation bizMaterialInformation = bizMaterialInformationDtoMapper.toPo(exportParam);
         bizMaterialInformation.setTradeCode(userInfo.getCompany());
        List<BizMaterialInformationDto> bizMaterialInformationDtos = new ArrayList<>();
        List<BizMaterialInformation> bizMaterialInformations = bizMaterialInformationMapper.getList(bizMaterialInformation);
        if (CollectionUtils.isNotEmpty(bizMaterialInformations)) {
            bizMaterialInformationDtos = bizMaterialInformations.stream().map(head -> {
                BizMaterialInformationDto dto = bizMaterialInformationDtoMapper.toDto(head);
                if(null != head.getTaxRate()){
                    dto.setTaxRate(head.getTaxRate().stripTrailingZeros().toPlainString()+"%");
                }
                if(null != head.getSupplierDiscountRate()){
                    dto.setSupplierDiscountRate(head.getSupplierDiscountRate().stripTrailingZeros().toPlainString()+"%");
                }
                if (StringUtils.isNotEmpty(dto.getCommonMark())){
                    dto.setCommonMarkList(Arrays.asList(dto.getCommonMark().split(",")));
                }
                return dto;
            }).collect(Collectors.toList());
        }
        return bizMaterialInformationDtos;
    }

    public Boolean cancel(List<String> sids, UserInfoToken userInfo) {
        if(ObjectUtils.isEmpty(sids)){
            throw new ErrorException(400,"作废数据不存在！");
        }
//        List<String> list = Arrays.asList(sids.split(","));
        for (String sid : sids) {
            BizMaterialInformation bizMaterialInformation = bizMaterialInformationMapper.selectByPrimaryKey(sid);
            if(!ObjectUtils.isEmpty(bizMaterialInformation)){
                BizMaterialInformation update = new BizMaterialInformation();
                update.setSid(sid);
                update.setDataState("1");
                update.setUpdateUser(userInfo.getUserNo());
                update.setUpdateTime(new Date());
                // 更新数据
                bizMaterialInformationMapper.updateByPrimaryKeySelective(update);
            }
        }
        return true;
    }

    /**
     * 根据交易代码获取物料计划信息
     * @param mat 物料信息
     * @return 物料信息列表
     */
    public List<BizMaterialInformationDto> getMatForPlan(BizMaterialInformationDto mat,UserInfoToken userInfo) {
        mat.setTradeCode(userInfo.getCompany());
        List<BizMaterialInformation> materialList = bizMaterialInformationMapper.getMatForPlan(mat);
        if (CollectionUtils.isEmpty(materialList)) {
            return new ArrayList<>();
        }
        return materialList.stream().map(material -> {
            BizMaterialInformationDto dto = bizMaterialInformationDtoMapper.toDto(material);
            return dto;
        }).collect(Collectors.toList());
    }


}
