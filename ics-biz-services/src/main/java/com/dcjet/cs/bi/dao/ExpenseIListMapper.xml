<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.ExpenseIListMapper">
    <sql id="Base_Column_List">
        SID
        ,HEAD_ID
        ,STATE
        ,CONTRACT_NUMBER
        ,INVOICE_NUMBER
        ,EXPENSE_TYPE
        ,QUANTITY
        ,TAX_AMOUNT
        ,NO_TAX_AMOUNT
        ,EXPENSE_AMOUNT
        ,TRADE_CODE
        ,INSERT_USER
        ,INSERT_TIME
        ,UPDATE_USER
        ,UPDATE_TIME
        ,PURCHASE_NUMBER
        ,PRODUCT_NAME
    </sql>
    <sql id="condition">

    </sql>
    <delete id="deleteBySids">
        delete from T_BIZ_EXPENSE_I_LIST l
        where l.CONTRACT_NUMBER in (select l.CONTRACT_NUMBER
        from T_BIZ_EXPENSE_I_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
        or l.purchase_number in (select l.purchase_number
        from T_BIZ_EXPENSE_I_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>);
        delete from T_BIZ_EXPENSE_I_LIST  where sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </delete>
    <select id="getList" resultType="com.dcjet.cs.bi.model.ExpenseIList">
        select
        <include refid="Base_Column_List"/>
            from T_BIZ_EXPENSE_I_LIST
        where head_id = #{headId}
    </select>
    <select id="getContractList" resultType="com.dcjet.cs.dto.bi.CostIContractParam">
        select
            l.sid as sid,
            l.PRODUCT_GRADE,
            l.CONTRACT_NO,
            h.ORDER_NO,
            h.PARTY_A,
            h.PARTY_B,
            l.CURR,
            l.DEC_TOTAL,
            l.QTY,
            l.UNIT,
            m.MERCHANDISE_CATEGORIES,
            rl.INVOICE_NUMBER,
            ph.purchase_order_no
        from T_BIZ_I_ORDER_LIST l
        left join T_BIZ_I_ORDER_HEAD h on h.sid = l.head_id
        left join T_BIZ_I_Purchase_HEAD ph on ph.HEAD_ID = h.SID
        left join T_BIZ_I_Purchase_LIST pl on pl.CONTRACT_LIST_ID = l.CONTRACT_LIST_ID  and ph.sid = pl.head_id
        left join t_biz_warehouse_receipt_list rl on rl.sid = pl.sid
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE  and m.DATA_STATE = '0'
        where
            l.trade_code = #{tradeCode} and
            pl.HEAD_ID = ph.SID and
            h.data_status  in ('1')
            <if test="contractNo != null and contractNo != ''">
                and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                and h.ORDER_NO   like concat(concat('%',#{orderNo}),'%')
            </if>
            <if test="sids != null and sids.size() > 0">
                and l.head_id in
                <foreach collection="sids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="getShippingOrderList" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select
            ol.sid as sid,
            h.purchase_order_no,
            ol.CONTRACT_NO,
            hh.ORDER_NO,
            hh.PARTY_A,
            hh.PARTY_B,
            l.PRODUCT_GRADE,
            l.CURR,
            l.DEC_TOTAL,
            l.DEC_PRICE,
            l.QTY,
            l.UNIT,
            m.MERCHANDISE_CATEGORIES,
            rl.INVOICE_NUMBER
        from T_BIZ_I_Purchase_LIST l
                 left join T_BIZ_I_Purchase_HEAD h  on h.sid = l.head_id
                 left join T_BIZ_I_ORDER_HEAD hh on hh.sid = h.HEAD_ID
                 left join t_biz_warehouse_receipt_list rl on rl.sid = l.sid
                 left join T_BIZ_I_ORDER_LIST ol on ol.contract_list_id = l.contract_list_id and ol.HEAD_ID = hh.sid
                 left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE and m.DATA_STATE = '0'
        where
        l.trade_code = #{tradeCode} and
        hh.data_status  in ('1') and
        h.data_status  in ('1')
        <if test="contractNo != null and contractNo != ''">
            and hh.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and hh.purchase_order_no  like concat(concat('%',#{orderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.PRODUCT_GRADE = #{productGrade}
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getSumDataByInvoiceSell" resultType="com.dcjet.cs.bi.model.ExpenseIList">
        select
            sum(EXPENSE_AMOUNT)  as EXPENSE_AMOUNT
        from T_BIZ_EXPENSE_I_LIST
        where HEAD_ID = #{headId};
    </select>
    <select id="getContractHead" resultType="com.dcjet.cs.dto.bi.CostIContractParam">
        <if test="businessType == '1'.toString()">
            select
            l.HEAD_ID as sid,
            max(ph.purchase_order_no) as purchase_order_no,
            max(h.insert_time) as insert_time,
            max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
            max(h.CONTRACT_NO) as CONTRACT_NO,
            max(h.ORDER_NO) as ORDER_NO,
            max(h.PARTY_A) as PARTY_A,
            max(h.PARTY_B) as PARTY_B,
            max(l.CURR) as CURR,
            sum(l.DEC_TOTAL) as DEC_TOTAL,
            sum(l.QTY) as QTY,
            max(l.UNIT) as UNIT,
            max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
            LISTAGG( DISTINCT
            pl.invoice_no, ',') WITHIN GROUP(ORDER BY pl.invoice_no) as INVOICE_NUMBER
            from T_BIZ_I_ORDER_HEAD h
            left join T_BIZ_I_ORDER_LIST l  on h.sid = l.head_id
            left join T_BIZ_I_Purchase_LIST pl on pl.CONTRACT_LIST_ID = l.CONTRACT_LIST_ID
            left join T_BIZ_I_Purchase_HEAD ph on ph.HEAD_ID = h.SID
            left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE and m.DATA_STATE = '0'
            where
            h.trade_code = #{tradeCode} and
            h.data_status in ('1') and pl.HEAD_ID = ph.SID and
            (select count(1)
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%1%'
            and t.TRADE_CODE =  #{tradeCode}
            and NOT EXISTS (
            SELECT 1
            FROM T_BIZ_EXPENSE_I_LIST el
            WHERE el.EXPENSE_TYPE = t.PARAM_CODE
            AND el.state IN ('0','1')
            AND el.purchase_number = h.purchase_order_no
            )) != 0
            <if test="contractNo != null and contractNo != ''">
                and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                and h.ORDER_NO   like concat(concat('%',#{orderNo}),'%')
            </if>
            group by l.HEAD_ID
            order by insert_time desc
        </if>
        <if test="businessType == '2'.toString()">
            SELECT
            ch.id as sid,
            ch.contract_no,
            ch.supplier_name as partyA,
            ch.currency as curr,
            cl_info.total_amount as decTotal,
            cl_info.total_quantity as qty,
            cl_info.unit,
            cl_info.product_category as merchandiseCategories
            FROM
            T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD ch
            LEFT JOIN (
            SELECT
            head_id,
            SUM(amount) AS total_amount,
            SUM(quantity) AS total_quantity,
            MAX(unit) AS unit,
            MAX(product_category) AS product_category
            FROM
            T_BIZ_I_AUXMAT_FOR_CONTRACT_List
            GROUP BY
            head_id
            ) cl_info ON cl_info.head_id = ch.id
            where ch.doc_status = '1'
            and
            (select count(1)
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%2%'
            and t.trade_code = ch.trade_code
            and NOT EXISTS (
            SELECT 1
            FROM T_BIZ_EXPENSE_I_LIST el
            WHERE el.EXPENSE_TYPE = t.PARAM_CODE
            AND el.state IN ('0','1')
            AND el.turnOVER_SID in (select id from T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST fl where fl.head_id = ch.id)
            )) != 0
            <if test="contractNo != null and contractNo != ''">
                and ch.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            order by ch.create_time desc
        </if>
    </select>
    <select id="getShippingOrderHead" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        <if test="businessType == '1'.toString()">
            select
            l.HEAD_ID as sid,
            max(h.purchase_order_no) as purchase_order_no,
            max(h.insert_time) as insert_time,
            max(hh.CONTRACT_NO) as CONTRACT_NO,
            max(hh.ORDER_NO) as ORDER_NO,
            max(hh.PARTY_A) as PARTY_A,
            max(hh.PARTY_B) as PARTY_B,
            max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
            max(l.CURR) as CURR,
            sum(l.DEC_TOTAL) as DEC_TOTAL,
            sum(l.DEC_PRICE) as DEC_PRICE,
            sum(l.QTY) as QTY,
            max(l.UNIT) as UNIT,
            max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
            LISTAGG( DISTINCT
            l.invoice_no, ',') WITHIN GROUP(ORDER BY l.invoice_no) as INVOICE_NUMBER
            from T_BIZ_I_Purchase_HEAD h
            left join T_BIZ_I_Purchase_LIST l  on h.sid = l.head_id
            left join T_BIZ_I_ORDER_HEAD hh on hh.sid = h.HEAD_ID
            left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE and m.DATA_STATE = '0'
            where
            h.trade_code = #{tradeCode} and
            hh.data_status in ('1') and
            h.data_status in ('1') and
            (select count(1)
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%1%'
            and t.TRADE_CODE =  #{tradeCode}
            and NOT EXISTS (
            SELECT 1
            FROM T_BIZ_EXPENSE_I_LIST el
            WHERE el.EXPENSE_TYPE = t.PARAM_CODE
            AND el.state IN ('0','1')
            AND el.purchase_number = h.purchase_order_no
            )) != 0
            <if test="contractNo != null and contractNo != ''">
                and hh.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
                and hh.purchase_order_no  like concat(concat('%',#{purchaseOrderNo}),'%')
            </if>
            <if test="productGrade != null and productGrade != ''">
                and l.PRODUCT_GRADE = #{productGrade}
            </if>
            group by l.HEAD_ID
            order by insert_time desc
        </if>
        <if test="businessType == '2'.toString()">
            SELECT
            gh.id as sid,
            gh.PURCHASE_NO as purchaseOrderNo,
            gh.CONTRACT_NO as contractNo,
            gh.supplier as partyA,
            gl_info.curr,
            gl_info.total_amount as decTotal,
            gl_info.total_quantity as qty,
            gl_info.unit,
            gl_info.merchandise_categories as merchandiseCategories,
            gl_info.invoice_no as invoiceNumber
            FROM
            T_BIZ_INCOMING_GOODS_HEAD gh
            LEFT JOIN (
            SELECT
            gl.head_id,
            MAX(gl.curr) AS curr,
            SUM(gl.amount) AS total_amount,
            SUM(gl.quantity) AS total_quantity,
            MAX(gl.unit) AS unit,
            MAX(m.merchandise_categories) AS merchandise_categories,
            MAX(gl.invoice_no) AS invoice_no
            FROM
            T_BIZ_INCOMING_GOODS_LIST gl
            LEFT JOIN
            T_BIZ_MATERIAL_INFORMATION m ON m.G_NAME = gl.goods_name AND m.DATA_STATE = '0'
            GROUP BY
            gl.head_id
            ) gl_info ON gl_info.head_id = gh.id
            where gh.DOCUMENT_STATUS = '1'
            and
            (select count(1)
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%2%'
            and t.trade_code = gh.trade_code
            and NOT EXISTS (
            SELECT 1
            FROM T_BIZ_EXPENSE_I_LIST el
            WHERE el.EXPENSE_TYPE = t.PARAM_CODE
            AND el.state IN ('0','1')
            AND el.turnOVER_SID in (select id from T_BIZ_INCOMING_GOODS_LIST il where il.head_id = gh.id)
            )) != 0
            AND gh.DATA_STATE != '2'
            <if test="contractNo != null and contractNo != ''">
                and gh.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
                and gh.PURCHASE_NO   like concat(concat('%',#{purchaseOrderNo}),'%')
            </if>
            order by gh.CREATE_TIME desc
        </if>
    </select>
    <select id="getContractHeadPayment" resultType="com.dcjet.cs.dto.bi.CostIContractParam">
        select
        l.HEAD_ID as sid,
        max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
        max(h.CONTRACT_NO) as CONTRACT_NO,
        max(h.ORDER_NO) as ORDER_NO,
        max(h.PARTY_A) as PARTY_A,
        max(h.PARTY_B) as PARTY_B,
        max(l.CURR) as CURR,
        sum(l.DEC_TOTAL) as DEC_TOTAL,
        sum(l.QTY) as QTY,
        max(l.UNIT) as UNIT,
        max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
        LISTAGG(DISTINCT rl.INVOICE_NUMBER, ',') WITHIN GROUP(ORDER BY rl.INVOICE_NUMBER) as INVOICE_NUMBER
        from T_BIZ_I_ORDER_HEAD h
        left join T_BIZ_I_ORDER_LIST l  on h.sid = l.head_id
        left join T_BIZ_I_Purchase_HEAD ph on ph.HEAD_ID = h.SID
        left join T_BIZ_I_Purchase_LIST pl on pl.CONTRACT_LIST_ID = l.CONTRACT_LIST_ID and ph.sid = pl.head_id
        left join t_biz_warehouse_receipt_list rl on rl.sid = pl.sid
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE and m.DATA_STATE = '0'
        where
        NOT EXISTS (
        SELECT 1
        FROM T_BIZ_PAYMENT_NOTIFY_LIST nl
        JOIN T_BIZ_PAYMENT_NOTIFY_HEAD nh ON nl.head_id = nh.sid
        left join T_BIZ_I_ORDER_LIST ol on ol.contract_list_id = nl.sid
        WHERE nh.DOC_STATUS IN ('0','1') AND nl.turnover_sid = l.sid
        )
        and
        h.trade_code = #{tradeCode} and
        h.data_status in ('1') and pl.HEAD_ID = ph.SID
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            and h.ORDER_NO   like concat(concat('%',#{orderNo}),'%')
        </if>
        group by l.HEAD_ID
        order by MAX(COALESCE(h.UPDATE_TIME,h.INSERT_TIME)) desc
    </select>
    <select id="getShippingOrderHeadPayment" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select
        l.HEAD_ID as sid,
        max(h.purchase_order_no) as purchase_order_no,
        max(hh.CONTRACT_NO) as CONTRACT_NO,
        max(hh.ORDER_NO) as ORDER_NO,
        max(hh.PARTY_A) as PARTY_A,
        max(hh.PARTY_B) as PARTY_B,
        max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
        max(l.CURR) as CURR,
        sum(l.DEC_TOTAL) as DEC_TOTAL,
        sum(l.DEC_PRICE) as DEC_PRICE,
        sum(l.QTY) as QTY,
        max(l.UNIT) as UNIT,
        max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
        LISTAGG(DISTINCT rl.INVOICE_NUMBER, ',') WITHIN GROUP(ORDER BY rl.INVOICE_NUMBER) as INVOICE_NUMBER
        from T_BIZ_I_Purchase_HEAD h
        left join T_BIZ_I_Purchase_LIST l  on h.sid = l.head_id
        left join T_BIZ_I_ORDER_HEAD hh on hh.sid = h.HEAD_ID
        left join T_BIZ_I_ORDER_LIST ol on ol.contract_list_id = l.contract_list_id and ol.HEAD_ID = hh.sid
        left join t_biz_warehouse_receipt_list rl on rl.sid = l.sid
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE and m.DATA_STATE = '0'
        where
        NOT EXISTS (
        SELECT 1
        FROM T_BIZ_PAYMENT_NOTIFY_LIST nl
        JOIN T_BIZ_PAYMENT_NOTIFY_HEAD nh ON nl.head_id = nh.sid
        left join T_BIZ_I_ORDER_LIST ool on ool.sid = nl.turnover_sid
        left join T_BIZ_I_Purchase_HEAD pph on pph.HEAD_ID = ool.HEAD_ID
        left join T_BIZ_I_Purchase_LIST pl on pl.contract_list_id = ool.contract_list_id and pph.sid = pl.HEAD_ID
        WHERE nh.DOC_STATUS IN ('0','1') AND nl.turnover_sid = ol.sid
        )
        AND
        h.trade_code = #{tradeCode} and
        hh.data_status in ('1') and
        h.data_status in ('1')
        <if test="contractNo != null and contractNo != ''">
            and hh.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.purchase_order_no  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.PRODUCT_GRADE = #{productGrade}
        </if>
        group by l.HEAD_ID
        order by MAX(COALESCE(h.UPDATE_TIME,h.INSERT_TIME)) desc
    </select>



    <select id="listPayment2" resultType="com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.id as sid,
        max(h.CONTRACT_NO) as contractNo,
        max(h.SUPPLIER) as partyB,
        max(l.CURR) as curr,
        sum(l.AMOUNT) as decTotal,
        sum(l.QUANTITY) as qty,
        max(l.UNIT) as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories,
        max(l.INVOICE_NO) as invoiceNumber
        from T_BIZ_INCOMING_GOODS_HEAD h
        left join T_BIZ_INCOMING_GOODS_LIST l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where contract_no not in (select distinct l.contract_no
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where l.trade_code = #{tradeCode}
        AND l.contract_no IS NOT NULL
        and h.DOC_STATUS != '2' and l.head_id = #{headId}) and
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.PURCHASE_NO  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.GOODS_NAME = #{productGrade}
        </if>
        group by h.PURCHASE_NO,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="getShippingOrderList2" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.CONTRACT_NO as contractNo,
        h.SUPPLIER as partyB,
        l.CURR as curr,
        l.AMOUNT as decTotal,
        l.QUANTITY as qty,
        l.UNIT as unit,
        m.MERCHANDISE_CATEGORIES as merchandiseCategories,
        l.INVOICE_NO as invoiceNumber,
        l.GOODS_NAME as productGrade
        from T_BIZ_INCOMING_GOODS_HEAD h
        left join T_BIZ_INCOMING_GOODS_LIST l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getShippingOrderList6" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.CONTRACT_NO as contractNo,
        h.SUPPLIER as partyB,
        (select DOMESTIC_PRINCIPAL from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD n where n.CONTRACT_NO = h.CONTRACT_NO and n.TRADE_CODE = h.TRADE_CODE limit 1) as domesticPrincipal,
        l.CURR as curr,
        l.AMOUNT as decTotal,
        l.QUANTITY as qty,
        l.UNIT as unit,
        m.MERCHANDISE_CATEGORIES as merchandiseCategories,
        l.INVOICE_NO as invoiceNumber,
        l.GOODS_NAME as productGrade
        from T_BIZ_NON_INCOMING_GOODS_HEAD h
        left join T_BIZ_NON_INCOMING_GOODS_LIST l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="listPayment6" resultType="com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.id as sid,
        max(h.CONTRACT_NO) as contractNo,
        max(h.SUPPLIER) as partyB,
        max(l.CURR) as curr,
        sum(l.AMOUNT) as decTotal,
        sum(l.QUANTITY) as qty,
        max(l.UNIT) as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories,
        max(l.INVOICE_NO) as invoiceNumber
        from T_BIZ_NON_INCOMING_GOODS_HEAD h
        left join T_BIZ_NON_INCOMING_GOODS_LIST l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where contract_no not in (select distinct l.contract_no
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where l.trade_code = #{tradeCode}
        AND l.contract_no IS NOT NULL
        and h.DOC_STATUS != '2' and l.head_id = #{headId}) and
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.PURCHASE_NO  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.GOODS_NAME = #{productGrade}
        </if>
        group by h.PURCHASE_NO,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>


    <select id="getCostType" resultType="com.dcjet.cs.dto.params.CostTypeDto">
        <if test="businessType == '1'.toString()">
            select PARAM_CODE,COST_NAME,customer_Supplier
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%1%'
            and t.TRADE_CODE =  #{tradeCode}
            and t.PARAM_CODE not in (select
            EXPENSE_TYPE
            from T_BIZ_EXPENSE_I_LIST l
            where l.state in ('0','1') and l.purchase_number = #{purchaseOrderNo})
        </if>
        <if test="businessType == '2'.toString()">
            select PARAM_CODE,COST_NAME,customer_Supplier
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%2%'
            and t.TRADE_CODE =  #{tradeCode}
            and t.PARAM_CODE not in (select
            EXPENSE_TYPE
            from T_BIZ_EXPENSE_I_LIST l
            where l.state in ('0','1') and l.turnOVER_SID in (
            <if test="type == '1'.toString()">
                select id from T_BIZ_I_AUXMAT_FOR_CONTRACT_LIST cl where head_id = #{sid})
            </if>
            <if test="type == '2'.toString()">
                select id from T_BIZ_INCOMING_GOODS_LIST cl where head_id = #{sid})
            </if>
            )
        </if>
        <if test="businessType == '6'.toString()">
            select PARAM_CODE,COST_NAME,customer_Supplier
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%6%'
            and t.TRADE_CODE =  #{tradeCode}
            and not exists (select 1 from T_BIZ_EXPENSE_I_LIST l
                           where l.state in ('0','1') and head_id = #{headId}
                           and t.PARAM_CODE = l.EXPENSE_TYPE
                           <if test="contractNo != null and contractNo != ''">
                           and l.CONTRACT_NUMBER = #{contractNo}
                           </if>
                           <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
                           and l.purchase_number = #{purchaseOrderNo}
                           </if>
            )
        </if>
        <if test="businessType == '3'.toString()">
            select PARAM_CODE,COST_NAME,customer_Supplier
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%3%'
            and t.TRADE_CODE =  #{tradeCode}
            and not exists (select 1 from T_BIZ_EXPENSE_I_LIST l
                           where l.state in ('0','1') and head_id = #{headId}
                           and t.PARAM_CODE = l.EXPENSE_TYPE
                           <if test="contractNo != null and contractNo != ''">
                           and l.CONTRACT_NUMBER = #{contractNo}
                           </if>
                           <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
                           and l.purchase_number = #{purchaseOrderNo}
                           </if>
            )
        </if>
        <if test="businessType == '7'.toString()">
            select PARAM_CODE,COST_NAME,customer_Supplier
            from T_BIZ_COST_TYPE t
            where t.COMMON_FLAG like '%7%'
            and t.TRADE_CODE =  #{tradeCode}
            and not exists (select 1 from T_BIZ_EXPENSE_I_LIST l
                           where l.state in ('0','1') and head_id = #{headId}
                           and t.PARAM_CODE = l.EXPENSE_TYPE
                           <if test="contractNo != null and contractNo != ''">
                           and l.CONTRACT_NUMBER = #{contractNo}
                           </if>
                           <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
                           and l.purchase_number = #{purchaseOrderNo}
                           </if>
            )
        </if>
    </select>

    <select id="getByHeadId" resultType="com.dcjet.cs.bi.model.ExpenseIList">
        select
        <include refid="Base_Column_List"/>
        , BAR_CODE
        , COST_NAME
        from (select * from T_BIZ_EXPENSE_I_LIST where HEAD_ID = #{headId}) EX_LIST
        left join (select * from
        (select G_NAME, BAR_CODE, row_number() over (partition by G_NAME order by G_NAME) as RN from
        T_BIZ_MATERIAL_INFORMATION where TRADE_CODE = #{tradeCode}) MAT
        where RN = 1) MAT_INFO on EX_LIST.PRODUCT_NAME = MAT_INFO.G_NAME
        left join (select * from
        (select PARAM_CODE, COST_NAME, row_number() over (partition by PARAM_CODE order by PARAM_CODE) as RN from
        T_BIZ_COST_TYPE where TRADE_CODE = #{tradeCode} and COMMON_FLAG like '%1%') COST
        where RN = 1) COST_TYPE on EX_LIST.EXPENSE_TYPE = COST_TYPE.PARAM_CODE
    </select>
    <select id="getContractListByBusinessType" resultType="com.dcjet.cs.dto.bi.CostIContractParam">
        <if test="businessType == '1'.toString()">
            select
            l.sid as sid,
            l.PRODUCT_GRADE,
            l.CONTRACT_NO,
            h.ORDER_NO,
            h.PARTY_A,
            h.PARTY_B,
            l.CURR,
            l.DEC_TOTAL,
            l.QTY,
            l.UNIT,
            m.MERCHANDISE_CATEGORIES,
            rl.INVOICE_NUMBER,
            ph.purchase_order_no
            from T_BIZ_I_ORDER_LIST l
            left join T_BIZ_I_ORDER_HEAD h on h.sid = l.head_id
            left join T_BIZ_I_Purchase_HEAD ph on ph.HEAD_ID = h.SID
            left join T_BIZ_I_Purchase_LIST pl on pl.CONTRACT_LIST_ID = l.CONTRACT_LIST_ID  and ph.sid = pl.head_id
            left join t_biz_warehouse_receipt_list rl on rl.sid = pl.sid
            left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE  and m.DATA_STATE = '0'
            where
            l.trade_code = #{tradeCode} and
            pl.HEAD_ID = ph.SID and
            h.data_status  in ('1')
            <if test="contractNo != null and contractNo != ''">
                and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                and h.ORDER_NO   like concat(concat('%',#{orderNo}),'%')
            </if>
            <if test="sids != null and sids.size() > 0">
                and l.head_id in
                <foreach collection="sids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="businessType == '2'.toString()">
            select
            cl.id as sid,
            ch.contract_no,
            cl.product_name as productGrade,
            ch.currency as curr,
            cl.amount as decTotal,
            cl.quantity as qty,
            cl.unit,
            cl.product_category as merchandiseCategories
            from T_BIZ_I_AUXMAT_FOR_CONTRACT_List cl
            left join T_BIZ_I_AUXMAT_FOR_CONTRACT_HEAD ch on ch.id = cl.head_id
            where ch.doc_status = '1'
            <if test="contractNo != null and contractNo != ''">
                and ch.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="sids != null and sids.size() > 0">
                and cl.head_id in
                <foreach collection="sids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>
    <select id="getShippingOrderListByBusinessType" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        <if test="businessType == '1'.toString()">
            select
            ol.sid as sid,
            h.purchase_order_no,
            ol.CONTRACT_NO,
            hh.ORDER_NO,
            hh.PARTY_A,
            hh.PARTY_B,
            l.PRODUCT_GRADE,
            l.CURR,
            l.DEC_TOTAL,
            l.DEC_PRICE,
            l.QTY,
            l.UNIT,
            m.MERCHANDISE_CATEGORIES,
            rl.INVOICE_NUMBER
            from T_BIZ_I_Purchase_LIST l
            left join T_BIZ_I_Purchase_HEAD h  on h.sid = l.head_id
            left join T_BIZ_I_ORDER_HEAD hh on hh.sid = h.HEAD_ID
            left join t_biz_warehouse_receipt_list rl on rl.sid = l.sid
            left join T_BIZ_I_ORDER_LIST ol on ol.contract_list_id = l.contract_list_id and ol.HEAD_ID = hh.sid
            left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE and m.DATA_STATE = '0'
            where
            l.trade_code = #{tradeCode} and
            hh.data_status  in ('1') and
            h.data_status  in ('1')
            <if test="contractNo != null and contractNo != ''">
                and hh.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
                and hh.purchase_order_no  like concat(concat('%',#{orderNo}),'%')
            </if>
            <if test="productGrade != null and productGrade != ''">
                and l.PRODUCT_GRADE = #{productGrade}
            </if>
            <if test="sids != null and sids.size() > 0">
                and l.head_id in
                <foreach collection="sids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="businessType == '2'.toString()">
            select
            gl.id as sid,
            gh.PURCHASE_NO as purchaseOrderNo,
            gh.CONTRACT_NO as contractNo,
            gh.supplier as partyA,
            gl.curr,
            gl.amount as decTotal,
            gl.quantity as qty,
            gl.unit,
            gl.goods_name as productGrade,
            gl.invoice_no as invoiceNumber
            from T_BIZ_INCOMING_GOODS_LIST gl
            left join T_BIZ_INCOMING_GOODS_HEAD gh on gh.id = gl.head_id
            where gh.DOCUMENT_STATUS = '1'
            <if test="contractNo != null and contractNo != ''">
                and gh.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
                and gh.PURCHASE_NO   like concat(concat('%',#{purchaseOrderNo}),'%')
            </if>
            <if test="sids != null and sids.size() > 0">
                and gl.head_id in
                <foreach collection="sids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>

    <select id="getAuxmatContractPayment"
            resultType="com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam">
        select h.CONTRACT_NO,
        h.SUPPLIER_NAME         as partyB,
        h.id         as sid,
        h.CURRENCY              as curr,
        sum(l.AMOUNT)           as decTotal,
        sum(l.QUANTITY)         as qty,
        max(l.UNIT)             as unit,
        max(l.PRODUCT_CATEGORY) as merchandiseCategories
        from t_biz_i_auxmat_for_contract_HEAD h
        left join T_BIZ_I_AUXMAT_FOR_CONTRACT_list l on l.HEAD_ID = h.ID
        where contract_no not in (select distinct l.contract_no
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where l.trade_code = #{tradeCode}
        AND l.contract_no IS NOT NULL
        and h.DOC_STATUS != '2' and l.head_id = #{headId})
        and h.DOC_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        group by h.CONTRACT_NO, h.SUPPLIER_NAME, h.CURRENCY,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="getAuxmatContractList" resultType="com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam">
        SELECT h.CONTRACT_NO,
        h.SUPPLIER_NAME    AS partyB,
        h.CURRENCY         AS curr,
        l.AMOUNT           AS decTotal,
        l.UNIT_PRICE           AS decPrice,
        l.product_name           AS productName,
        l.QUANTITY         AS qty,
        l.UNIT             AS unit,
        l.PRODUCT_CATEGORY AS merchandiseCategories
        FROM t_biz_i_auxmat_for_contract_HEAD h
        LEFT JOIN T_BIZ_I_AUXMAT_FOR_CONTRACT_list l ON l.HEAD_ID = h.ID
        where  h.DOC_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.product_name  like concat(concat('%',#{productName}),'%')
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAuxnonContractPayment"
            resultType="com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam">
        select h.CONTRACT_NO,
        h.SUPPLIER        as partyB,
        h.id         as sid,
        h.CURRENCY              as curr,
        sum(l.AMOUNT)           as decTotal,
        sum(l.QTY)         as qty,
        max(l.UNIT)             as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h
        left join T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l on l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME
        where contract_no not in (select distinct l.contract_no
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where l.trade_code = #{tradeCode}
        AND l.contract_no IS NOT NULL
        and h.DOC_STATUS != '2' and l.head_id = #{headId})
        and h.STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        group by h.CONTRACT_NO, h.SUPPLIER, h.CURRENCY,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="getAuxnonContractList"
            resultType="com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam">
        SELECT h.CONTRACT_NO,
        h.SUPPLIER    AS partyB,
        h.DOMESTIC_PRINCIPAL    AS domesticPrincipal,
        h.CURRENCY         AS curr,
        l.AMOUNT           AS decTotal,
        l.UNIT_PRICE           AS decPrice,
        l.GOODS_NAME           AS productName,
        l.qty         AS qty,
        l.UNIT             AS unit,
        m.MERCHANDISE_CATEGORIES as merchandiseCategories
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h
        left join T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l on l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME
        where  h.STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.GOODS_NAME  like concat(concat('%',#{productName}),'%') and l.UNIT_PRICE is not null
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getAuxnonContractPaymentCost"
            resultType="com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam">
        select h.CONTRACT_NO,
        h.SUPPLIER        as partyA,
        h.id         as sid,
        h.CURRENCY              as curr,
        sum(l.AMOUNT)           as decTotal,
        sum(l.QTY)         as qty,
        max(l.UNIT)             as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories
        from T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_HEAD h
        left join T_BIZ_I_NON_STATE_AUXMAT_AGGR_CONTRACT_LIST l on l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME
        where h.CONTRACT_NO not in (
            select distinct l.CONTRACT_NUMBER
            from T_BIZ_EXPENSE_I_LIST l
            left join T_BIZ_EXPENSE_I_HEAD eh on l.HEAD_ID = eh.SID
            where l.trade_code = #{tradeCode}
            and l.CONTRACT_NUMBER IS NOT NULL
            and eh.STATE != '2'
            and l.head_id = #{headId}
            group by l.CONTRACT_NUMBER
            having count(distinct l.EXPENSE_TYPE) = (
                select count(*) from T_BIZ_COST_TYPE ct
                where ct.COMMON_FLAG like '%6%'
                and ct.TRADE_CODE = #{tradeCode}
            )
        )
        and h.STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        group by h.CONTRACT_NO, h.SUPPLIER, h.CURRENCY,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="getContractHead6"
            resultType="com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.id as sid,
        max(h.CONTRACT_NO) as contractNo,
        max(h.SUPPLIER) as partyB,
        max(l.CURR) as curr,
        sum(l.AMOUNT) as decTotal,
        sum(l.QUANTITY) as qty,
        max(l.UNIT) as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories,
        max(l.INVOICE_NO) as invoiceNumber
        from T_BIZ_NON_INCOMING_GOODS_HEAD h
        left join T_BIZ_NON_INCOMING_GOODS_LIST l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where h.CONTRACT_NO not in (
            select distinct l.CONTRACT_NUMBER
            from T_BIZ_EXPENSE_I_LIST l
            left join T_BIZ_EXPENSE_I_HEAD eh on l.HEAD_ID = eh.SID
            where l.trade_code = #{tradeCode}
            and l.CONTRACT_NUMBER IS NOT NULL
            and eh.STATE != '2'
            and l.head_id = #{headId}
            group by l.CONTRACT_NUMBER
            having count(distinct l.EXPENSE_TYPE) = (
                select count(*) from T_BIZ_COST_TYPE ct
                where ct.COMMON_FLAG like '%6%'
                and ct.TRADE_CODE = #{tradeCode}
            )
        ) and
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.PURCHASE_NO  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        group by h.PURCHASE_NO,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="listContractPayment3" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        select h.CONTRACT_NO,
        h.SELLER         as partyB,
        h.id         as sid,
        h.CURR              as curr,
        sum(l.MONEY_AMOUNT)           as decTotal,
        sum(l.QTY)         as qty,
        max(l.UNIT)             as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD h
        left join T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST l on l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.G_NAME and m.DATA_STATE = '0'
        where contract_no not in (select distinct l.contract_no
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where l.trade_code = #{tradeCode}
        AND l.contract_no IS NOT NULL
        and h.DOC_STATUS != '2' and l.head_id = #{headId})
        and h.DATA_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        group by h.CONTRACT_NO, h.SELLER, h.CURR,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="getContractList3" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        SELECT h.CONTRACT_NO,
        h.SELLER    AS partyB,
        h.CURR         AS curr,
        l.MONEY_AMOUNT           AS decTotal,
        l.UNIT_PRICE           AS decPrice,
        l.G_NAME           AS productName,
        l.QTY         AS qty,
        l.UNIT             AS unit,
        m.MERCHANDISE_CATEGORIES AS merchandiseCategories
        FROM T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD h
        LEFT JOIN T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST l ON l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.G_NAME and m.DATA_STATE = '0'
        where  h.DATA_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.G_NAME  like concat(concat('%',#{productName}),'%')
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listPayment3" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.id as sid,
        max(h.CONTRACT_NO) as contractNo,
        max(h.SUPPLIER) as partyB,
        max(l.CURR) as curr,
        sum(l.AMOUNT) as decTotal,
        sum(l.QUANTITY) as qty,
        max(l.UNIT) as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories,
        max(l.INVOICE_NO) as invoiceNumber
        from t_biz_smoke_machine_incoming_goods_head h
        left join t_biz_smoke_machine_incoming_goods_list l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where contract_no not in (select distinct l.contract_no
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        left join T_BIZ_PAYMENT_NOTIFY_HEAD h on l.HEAD_ID = h.SID
        where l.trade_code = #{tradeCode}
        AND l.contract_no IS NOT NULL
        and h.DOC_STATUS != '2' and l.head_id = #{headId}) and
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.PURCHASE_NO  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.GOODS_NAME = #{productGrade}
        </if>
        group by h.PURCHASE_NO,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="getShippingOrderList3" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.CONTRACT_NO as contractNo,
        h.SUPPLIER as partyB,
        l.CURR as curr,
        l.UNIT_PRICE as decPrice,
        l.AMOUNT as decTotal,
        l.QUANTITY as qty,
        l.UNIT as unit,
        m.MERCHANDISE_CATEGORIES as merchandiseCategories,
        l.INVOICE_NO as invoiceNumber,
        l.GOODS_NAME as productGrade
        from t_biz_smoke_machine_incoming_goods_head h
        left join t_biz_smoke_machine_incoming_goods_list l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.GOODS_NAME  like concat(concat('%',#{productName}),'%') and l.UNIT_PRICE is not null
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="list3" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        select h.CONTRACT_NO,
        h.USING_MANUFACTURER         as partyA,
        h.id         as sid,
        h.CURR              as curr,
        sum(l.MONEY_AMOUNT)           as decTotal,
        sum(l.QTY)         as qty,
        max(l.UNIT)             as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories
        from T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD h
        left join T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST l on l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.G_NAME and m.DATA_STATE = '0'
        where h.CONTRACT_NO not in (
            select distinct l.CONTRACT_NUMBER
            from T_BIZ_EXPENSE_I_LIST l
            left join T_BIZ_EXPENSE_I_HEAD eh on l.HEAD_ID = eh.SID
            where l.trade_code = #{tradeCode}
            and l.CONTRACT_NUMBER IS NOT NULL
            and eh.STATE != '2'
            and l.head_id = #{headId}
            group by l.CONTRACT_NUMBER
            having count(distinct l.EXPENSE_TYPE) = (
                select count(*) from T_BIZ_COST_TYPE ct
                where ct.COMMON_FLAG like '%3%'
                and ct.TRADE_CODE = #{tradeCode}
            )
        )
        and h.DATA_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        group by h.CONTRACT_NO, h.USING_MANUFACTURER, h.CURR,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>

    <select id="getCostList3" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        SELECT h.CONTRACT_NO,
        h.USING_MANUFACTURER    AS partyA,
        h.CURR         AS curr,
        l.MONEY_AMOUNT           AS decTotal,
        l.UNIT_PRICE           AS decPrice,
        l.G_NAME           AS productName,
        l.QTY         AS qty,
        l.UNIT             AS unit,
        m.MERCHANDISE_CATEGORIES AS merchandiseCategories
        FROM T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD h
        LEFT JOIN T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_LIST l ON l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.G_NAME and m.DATA_STATE = '0'
        where  h.DATA_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.G_NAME  like concat(concat('%',#{productName}),'%')
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getCostShippingOrderList3" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.CONTRACT_NO as contractNo,
        h.CUSTOMER as partyA,
        l.CURR as curr,
        l.UNIT_PRICE as decPrice,
        l.AMOUNT as decTotal,
        l.QUANTITY as qty,
        l.UNIT as unit,
        m.MERCHANDISE_CATEGORIES as merchandiseCategories,
        l.INVOICE_NO as invoiceNumber,
        l.GOODS_NAME as productGrade
        from t_biz_smoke_machine_incoming_goods_head h
        left join t_biz_smoke_machine_incoming_goods_list l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.GOODS_NAME  like concat(concat('%',#{productName}),'%') and l.UNIT_PRICE is not null
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getContractHead3" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        select h.PURCHASE_NO as purchaseOrderNo,
        h.id as sid,
        max(h.CONTRACT_NO) as contractNo,
        max(h.CUSTOMER) as partyA,
        max(l.CURR) as curr,
        sum(l.AMOUNT) as decTotal,
        sum(l.QUANTITY) as qty,
        max(l.UNIT) as unit,
        max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories,
        max(l.INVOICE_NO) as invoiceNumber
        from t_biz_smoke_machine_incoming_goods_head h
        left join t_biz_smoke_machine_incoming_goods_list l on h.id = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.GOODS_NAME and m.DATA_STATE = '0'
        where h.CONTRACT_NO not in (
            select distinct l.CONTRACT_NUMBER
            from T_BIZ_EXPENSE_I_LIST l
            left join T_BIZ_EXPENSE_I_HEAD eh on l.HEAD_ID = eh.SID
            where l.trade_code = #{tradeCode}
            and l.CONTRACT_NUMBER IS NOT NULL
            and eh.STATE != '2'
            and l.head_id = #{headId}
            group by l.CONTRACT_NUMBER
            having count(distinct l.EXPENSE_TYPE) = (
                select count(*) from T_BIZ_COST_TYPE ct
                where ct.COMMON_FLAG like '%3%'
                and ct.TRADE_CODE = #{tradeCode}
            )
        ) and
        h.trade_code = #{tradeCode} and
        h.DATA_STATE ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.PURCHASE_NO  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        group by h.PURCHASE_NO,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>
    <select id="list7" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        select h.CONTRACT_NO,
               h.BUYER         as partyA,
               h.id         as sid,
               h.CURR              as curr,
               sum(l.MONEY_AMOUNT)           as decTotal,
               sum(l.QTY)         as qty,
               max(l.UNIT)             as unit,
               max(m.MERCHANDISE_CATEGORIES) as merchandiseCategories
        from T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD h
                 left join T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST l on l.HEAD_ID = h.ID
                 left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.G_NAME and m.DATA_STATE = '0'
        where h.CONTRACT_NO not in (
            select distinct l.CONTRACT_NUMBER
            from T_BIZ_EXPENSE_I_LIST l
            left join T_BIZ_EXPENSE_I_HEAD eh on l.HEAD_ID = eh.SID
            where l.trade_code = #{tradeCode}
            and l.CONTRACT_NUMBER IS NOT NULL
            and eh.STATE != '2'
            and l.head_id = #{headId}
            group by l.CONTRACT_NUMBER
            having count(distinct l.EXPENSE_TYPE) = (
                select count(*) from T_BIZ_COST_TYPE ct
                where ct.COMMON_FLAG like '%7%'
                and ct.TRADE_CODE = #{tradeCode}
            )
        )
        and h.DATA_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        group by h.CONTRACT_NO, h.BUYER, h.CURR,h.id
        order by MAX(COALESCE(h.UPDATE_TIME,h.CREATE_TIME)) desc
    </select>
    <select id="getContractHead7" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        SELECT *
        FROM (

                 SELECT
                     h.PURCHASE_ORDER_NO AS purchaseOrderNo,
                     h.sid AS sid,
                     max(h.CONTRACT_NO) AS contractNo,
                     max(h.CUSTOMER) AS partyA,
                     max(h.CURR) AS curr,
                     sum(l.AMOUNT) AS decTotal,
                     sum(l.QTY) AS qty,
                     max(l.UNIT) AS unit,
                     max(m.MERCHANDISE_CATEGORIES) AS merchandiseCategories,
                     null AS invoiceNumber,
                     MAX(COALESCE(h.UPDATE_TIME, h.CREATE_TIME)) AS orderTime
                 FROM
                     T_BIZ_DELIVERY_ORDER_HEAD h
                         LEFT JOIN
                     T_BIZ_DELIVERY_ORDER_LIST l ON h.sid = l.HEAD_ID
                         LEFT JOIN
                     T_BIZ_MATERIAL_INFORMATION m ON m.G_NAME = l.PRODUCT_NAME AND m.DATA_STATE = '0'
                 WHERE
                         h.CONTRACT_NO NOT IN (
                         SELECT DISTINCT
                             l.CONTRACT_NUMBER
                         FROM
                             T_BIZ_EXPENSE_I_LIST l
                                 LEFT JOIN
                             T_BIZ_EXPENSE_I_HEAD eh ON l.HEAD_ID = eh.SID
                         WHERE
                             l.trade_code = #{tradeCode}
                           AND l.CONTRACT_NUMBER IS NOT NULL
                           AND eh.STATE != '2'
                           AND l.head_id = #{headId}
                         GROUP BY l.CONTRACT_NUMBER
                         HAVING count(distinct l.EXPENSE_TYPE) = (
                             SELECT count(*) FROM T_BIZ_COST_TYPE ct
                             WHERE ct.COMMON_FLAG like '%7%'
                             AND ct.TRADE_CODE = #{tradeCode}
                         )
             )
            AND h.trade_code = #{tradeCode}
        AND h.STATUS = '1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.PURCHASE_NO  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        GROUP BY
            h.PURCHASE_ORDER_NO,
            h.sid

        UNION ALL


        SELECT
            h.PURCHASE_ORDER_NO AS purchaseOrderNo,
            h.sid AS sid,
            max(h.CONTRACT_NO) AS contractNo,
            max(h.CUSTOMER) AS partyA,
            max(h.CURR) AS curr,
            sum(l.AMOUNT) AS decTotal,
            sum(l.QTY) AS qty,
            max(l.UNIT) AS unit,
            max(m.MERCHANDISE_CATEGORIES) AS merchandiseCategories,
            null AS invoiceNumber,
            MAX(COALESCE(h.UPDATE_TIME, h.CREATE_TIME)) AS orderTime
        FROM
            t_biz_purchase_order_head h
                LEFT JOIN
            t_biz_purchase_order_list l ON h.sid = l.HEAD_ID
                LEFT JOIN
            T_BIZ_MATERIAL_INFORMATION m ON m.G_NAME = l.PRODUCT_NAME AND m.DATA_STATE = '0'
        WHERE
                h.CONTRACT_NO NOT IN (
                SELECT DISTINCT
                    l.CONTRACT_NUMBER
                FROM
                    T_BIZ_EXPENSE_I_LIST l
                        LEFT JOIN
                    T_BIZ_EXPENSE_I_HEAD eh ON l.HEAD_ID = eh.SID
                WHERE
                    l.trade_code = #{tradeCode}
                  AND l.CONTRACT_NUMBER IS NOT NULL
                  AND eh.STATE != '2'
                  AND l.head_id = #{headId}
                GROUP BY l.CONTRACT_NUMBER
                HAVING count(distinct l.EXPENSE_TYPE) = (
                    SELECT count(*) FROM T_BIZ_COST_TYPE ct
                    WHERE ct.COMMON_FLAG like '%7%'
                    AND ct.TRADE_CODE = #{tradeCode}
                )
            )
          AND h.trade_code = #{tradeCode}
          AND h.STATUS  = '1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.PURCHASE_NO  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        GROUP BY
            h.PURCHASE_ORDER_NO,
            h.sid
            ) AS CombinedResults
        ORDER BY
            orderTime DESC
    </select>
    <select id="getCostShippingOrderList7" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        SELECT * FROM (
        select h.PURCHASE_ORDER_NO as purchaseOrderNo,
        h.CONTRACT_NO as contractNo,
        h.CUSTOMER as partyA,
        h.CURR as curr,
        l.UNIT_PRICE as decPrice,
        l.AMOUNT as decTotal,
        l.QTY as qty,
        l.UNIT as unit,
        m.MERCHANDISE_CATEGORIES as merchandiseCategories,
        null as invoiceNumber,
        l.PRODUCT_NAME as productGrade
        from T_BIZ_DELIVERY_ORDER_HEAD h
        left join T_BIZ_DELIVERY_ORDER_LIST l on h.sid = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_NAME and m.DATA_STATE = '0'
        where
        h.trade_code = #{tradeCode} and
        h.STATUS  ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.PRODUCT_NAME  like concat(concat('%',#{productName}),'%') and l.UNIT_PRICE is not null
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        UNION ALL
        select h.PURCHASE_ORDER_NO as purchaseOrderNo,
        h.CONTRACT_NO as contractNo,
        h.CUSTOMER as partyA,
        h.CURR as curr,
        l.UNIT_PRICE as decPrice,
        l.AMOUNT as decTotal,
        l.QTY as qty,
        l.UNIT as unit,
        m.MERCHANDISE_CATEGORIES as merchandiseCategories,
        null as invoiceNumber,
        l.PRODUCT_NAME as productGrade
        from t_biz_purchase_order_head h
        left join t_biz_purchase_order_list l on h.sid = l.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_NAME and m.DATA_STATE = '0'
        where
        h.trade_code = #{tradeCode} and
        h.STATUS  ='1'
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.PRODUCT_NAME  like concat(concat('%',#{productName}),'%') and l.UNIT_PRICE is not null
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) AS topIn
    </select>
    <select id="getCostList7" resultType="com.dcjet.cs.dto.equipment.ForeignContractHeadParam">
        SELECT h.CONTRACT_NO,
        h.BUYER     AS partyA,
        h.CURR         AS curr,
        l.MONEY_AMOUNT           AS decTotal,
        l.UNIT_PRICE           AS decPrice,
        l.G_NAME           AS productName,
        l.QTY         AS qty,
        l.UNIT             AS unit,
        m.MERCHANDISE_CATEGORIES AS merchandiseCategories
        FROM T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD h
        LEFT JOIN T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST l ON l.HEAD_ID = h.ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.G_NAME and m.DATA_STATE = '0'
        where  h.DATA_STATUS = '1'
        and h.trade_code = #{tradeCode}
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="productName != null and productName != ''">
            and  l.G_NAME  like concat(concat('%',#{productName}),'%')
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
