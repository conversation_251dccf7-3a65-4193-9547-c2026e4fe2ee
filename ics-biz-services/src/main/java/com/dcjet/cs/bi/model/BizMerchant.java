package com.dcjet.cs.bi.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter
@Getter
@Table(name = "t_biz_merchant")
@Accessors(chain = true)
public class BizMerchant implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	@Id
	@Column(name = "id")
	private  String sid;
	/**
     * 
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 
     */
	@Column(name = "validate_status")
	private  String dataStatus;
	/**
     * 
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 
     */
	@Column(name = "create_by")
	private  String insertUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date insertTime;
	/**
     * 
     */
	@Column(name = "update_by")
	private  String updateUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 客商编码
     */
	@Column(name = "merchant_code")
	private  String merchantCode;
	/**
     * 客商中文名称
     */
	@Column(name = "merchant_name_cn")
	private  String merchantNameCn;
	/**
     * 客商英文名称
     */
	@Column(name = "merchant_name_en")
	private  String merchantNameEn;
	/**
     * 客商简称
     */
	@Column(name = "merchant_short")
	private  String merchantShort;
	/**
     * 客商地址
     */
	@Column(name = "merchant_address")
	private  String merchantAddress;
	/**
     * 收款银行
     */
	@Column(name = "receiving_bank")
	private  String receivingBank;
	/**
     * 收款方帐号
     */
	@Column(name = "receiver_account_num")
	private  String receiverAccountNum;
	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;

	/**
	 * 创建人部门编码
	 */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	@Column(name = "finance_code")
	private  String financeCode;


	/**
	 * 商品序号
	 */
	@Column(name = "serial_no")
	private Integer serialNo;

	/**
	 * 贸易国别
	 */
	@Column(name = "trade_country")
	private String tradeCountry;

	/**
	 * 装运人
	 */
	@Column(name = "shipper")
	private String shipper;

	/**
	 * 收货人
	 */
	@Column(name = "consignee")
	private String consignee;

	/**
	 * 通知人
	 */
	@Column(name = "notify_party")
	private String notifyParty;

	/**
	 * 仓储地址
	 */
	@Column(name = "warehouse_address")
	private String warehouseAddress;

	/**
	 * 联系人
	 */
	@Column(name = "contact_person")
	private String contactPerson;

	/**
	 * 联系电话
	 */
	@Column(name = "contact_phone")
	private String contactPhone;

	/**
	 * 客商类别
	 */
	@Column(name = "merchant_type")
	private String merchantType;

	/**
	 * 常用标识
	 */
	@Column(name = "common_flag")
	private String commonFlag;
	@Transient
	private List<String> commonFlagList;

	/**
	 * 传真
	 */
	@Column(name = "fax")
	private String fax;

	/**
	 * 邮件
	 */
	@Column(name = "email")
	private String email;

	/**
	 * 英文地址
	 */
	@Column(name = "en_address")
	private String enAddress;

	/**
	 * 贸易国别英文
	 */
	@Column(name = "trade_country_en")
	private String tradeCountryEn;

	/**
	 * 邮编
	 */
	@Column(name = "postcode")
	private String postcode;

	/**
	 * 开户行地址
	 */
	@Column(name = "bank_address")
	private String bankAddress;

	/**
	 * 支出账号
	 */
	@Column(name = "expend_account")
	private String expendAccount;

	/**
	 * 税号
	 */
	@Column(name = "tax_no")
	private String taxNo;

	/**
	 * 法人代表
	 */
	@Column(name = "legal_person")
	private String legalPerson;
}
