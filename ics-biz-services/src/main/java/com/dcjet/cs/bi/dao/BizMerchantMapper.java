package com.dcjet.cs.bi.dao;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.dto.bi.BizMaterialInformationKeyCode;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizMerchant
* <AUTHOR>
* @date: 2025-3-11
*/
public interface BizMerchantMapper extends Mapper<BizMerchant> {
    /**
     * 查询获取数据
     * @param bizMerchant
     * @return
     */
    List<BizMerchant> getList(BizMerchant bizMerchant);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkKey(BizMerchant merchantCode);

    List<BizMaterialInformationKeyCode> selectPaclInfo(@Param("company") String company);

    List<BizMaterialInformationKeyCode> selectMerchandiseCategories(@Param("company") String company);

    int checkMerchantNameCn(BizMerchant bizMerchant);

    Integer selectMerchantCode(BizMerchant bizMerchant);

    BizMerchant getMerchantByName(BizMerchant bizMerchant);


    /**
     * 获取当前企业最大序号
     * @param tradeCode 企业代码
     * @return 返回最大序号，为空返回1
     */
    Integer getMaxSeqNo(@Param("tradeCode") String tradeCode);

    BizMerchant getByMerchantCode(@Param("merchantCode") String merchantCode, @Param("company") String company);

    List<BizMerchant> getByMerchantCodes(@Param("merchantCodes") List<String> merchantCodes, @Param("company") String company);

    List<BizMerchant> getForAggrSearch (BizMerchant bizMerchant);

    List<BizMerchant> getForAggrDomesticSearch (BizMerchant bizMerchant);

    List<BizMerchant> getForThirdPlanBuyerSearch (BizMerchant bizMerchant);
    List<BizMerchant> getForThirdPlanSellerSearch (BizMerchant bizMerchant);

    List<BizMerchant> provideForOptions(@Param("businessType") String businessType, @Param("tradeCode") String tradeCode);

    /**
     * 根据客商编码和公司编码获取客商信息
     * @param code 客商编码
     * @param tradeCode 公司编码
     * @return 客商信息
     */
    BizMerchant getCustomerByCode(@Param("code") String code, @Param("tradeCode") String tradeCode);

    /**
     * 根据客商名称和公司编码获取客商编码
     * @param name 客商名称
     * @param tradeCode 公司编码
     * @return 客商编码
     */
    String getCodeByName(@Param("name") String name, @Param("tradeCode") String tradeCode);
}
