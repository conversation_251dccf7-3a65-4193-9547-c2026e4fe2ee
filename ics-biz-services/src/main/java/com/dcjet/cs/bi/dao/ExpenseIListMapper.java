package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.ExpenseIHead;
import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractHeadParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizINonStateAuxmatAggrContractHeadParam;
import com.dcjet.cs.dto.bi.CostIContractParam;
import com.dcjet.cs.dto.bi.CostIShippingOrderParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.dto.nonAuxiliaryMaterials.BizNonIncomingGoodsHeadParam;
import com.dcjet.cs.dto.params.CostTypeDto;
import com.dcjet.cs.dto.purchaseOrder.BizPurchaseOrderHeadParam;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import javax.validation.Valid;
import java.util.List;

public interface ExpenseIListMapper extends Mapper<ExpenseIList> {

    List<ExpenseIList> getList(ExpenseIList po);

    void deleteBySids(List<String> sids);

    List<CostIContractParam> getContractList(CostIContractParam costIContractParam);
    List<CostIContractParam> getContractListByBusinessType(CostIContractParam costIContractParam);

    List<CostIShippingOrderParam> getShippingOrderList(CostIShippingOrderParam costIShippingOrderParam);
    List<CostIShippingOrderParam> getShippingOrderListByBusinessType(CostIShippingOrderParam costIShippingOrderParam);

    ExpenseIList getSumDataByInvoiceSell(String headId);

    List<CostIContractParam> getContractHeadPayment(CostIContractParam costIContractParam);

    List<CostIShippingOrderParam> getShippingOrderHeadPayment(CostIShippingOrderParam costIShippingOrderParam);
    List<BizIncomingGoodsHeadParam> listPayment2(BizIncomingGoodsHeadParam bizIncomingGoodsHeadParam);
    List<BizNonIncomingGoodsHeadParam> listPayment6(BizNonIncomingGoodsHeadParam bizNonIncomingGoodsHeadParam);

    List<CostIContractParam> getContractHead(CostIContractParam costIContractParam);

    List<CostIShippingOrderParam> getShippingOrderHead(CostIShippingOrderParam costIShippingOrderParam);

    List<CostTypeDto> getCostType(CostIShippingOrderParam costIShippingOrderParam);

    List<ExpenseIList> getByHeadId(@Param("headId") String headId, @Param("tradeCode") String tradeCode);

    List<BizIAuxmatForContractHeadParam> getAuxmatContractPayment(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam);
    List<BizIAuxmatForContractHeadParam> getAuxmatContractList(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam);

    List<BizINonStateAuxmatAggrContractHeadParam>  getAuxnonContractPayment(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam);
    List<BizINonStateAuxmatAggrContractHeadParam>  getAuxnonContractPaymentCost(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam);
    List<BizINonStateAuxmatAggrContractHeadParam> getAuxnonContractList(BizINonStateAuxmatAggrContractHeadParam bizINonStateAuxmatAggrContractHeadParam);

    List<CostIShippingOrderParam> getShippingOrderList2(@Valid BizIncomingGoodsHeadParam bizIncomingGoodsHeadParam);

    List<CostIShippingOrderParam> getShippingOrderList6(@Valid BizNonIncomingGoodsHeadParam bizNonIncomingGoodsHeadParam);

    List<BizNonIncomingGoodsHeadParam>  getContractHead6(BizNonIncomingGoodsHeadParam param);
    List<ForeignContractHeadParam>  getContractHead3(ForeignContractHeadParam param);

    List<ForeignContractHeadParam> listContractPayment3(ForeignContractHeadParam foreignContractHeadParam);

    List<ForeignContractHeadParam> getContractList3(@Valid ForeignContractHeadParam foreignContractHeadParam);

    List<ForeignContractHeadParam> listPayment3(ForeignContractHeadParam foreignContractHeadParam);

    List<CostIShippingOrderParam> getShippingOrderList3(@Valid ForeignContractHeadParam foreignContractHeadParam);

    List<ForeignContractHeadParam> list3(ForeignContractHeadParam foreignContractHeadParam);
    List<ForeignContractHeadParam> getCostList3(ForeignContractHeadParam foreignContractHeadParam);

    List<CostIShippingOrderParam> getCostShippingOrderList3(ForeignContractHeadParam foreignContractHeadParam);

    List<ForeignContractHeadParam> list7(ForeignContractHeadParam foreignContractHeadParam);
    List<ForeignContractHeadParam>  getContractHead7(ForeignContractHeadParam param);

    List<CostIShippingOrderParam> getCostShippingOrderList7(BizPurchaseOrderHeadParam purchaseOrderHeadParam);

    List<ForeignContractHeadParam> getCostList7(ForeignContractHeadParam costIContractParam);
}
