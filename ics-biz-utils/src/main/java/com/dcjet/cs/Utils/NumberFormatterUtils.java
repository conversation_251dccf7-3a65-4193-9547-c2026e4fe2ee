package com.dcjet.cs.Utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class NumberFormatterUtils {

    // 中文数字
    private static final String[] CHINESE_NUMBERS = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
    // 中文单位
    private static final String[] CHINESE_UNITS = {"", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "万"};
    // 小数单位
    private static final String[] DECIMAL_UNITS = {"角", "分"};


    public static String formatNumber(BigDecimal value) {
        if (value == null) {
            return "";
        }
        // 判断小数部分是否为0
        if (value.stripTrailingZeros().scale() <= 0) {
            // 是整数，补两位小数，带千分位
            DecimalFormat df = new DecimalFormat("#,##0.00");
            return df.format(value);
        } else {
            // 不是整数，带千分位，原小数位保留
            // 先用 stripTrailingZeros() 保持真实小数位
            BigDecimal stripped = value.stripTrailingZeros();
            int scale = stripped.scale();
            StringBuilder pattern = new StringBuilder("#,##0");
            if (scale > 0) {
                pattern.append(".");
                for (int i = 0; i < scale; i++) {
                    pattern.append("0");
                }
            }
            DecimalFormat df = new DecimalFormat(pattern.toString());
            return df.format(stripped);
        }
    }

    /**
     * 将数字金额转换为中文金额
     * @param amount 金额数字
     * @return 中文金额字符串
     */
    public static String convertToChineseAmount(BigDecimal amount) {
        if (amount == null) {
            return "零元整";
        }
        
        // 处理负数
        boolean isNegative = amount.compareTo(BigDecimal.ZERO) < 0;
        if (isNegative) {
            amount = amount.abs();
        }
        
        // 分离整数部分和小数部分
        String amountStr = amount.toPlainString();
        String[] parts = amountStr.split("\\.");
        String integerPart = parts[0];
        String decimalPart = parts.length > 1 ? parts[1] : "";
        
        // 转换整数部分
        String chineseInteger = convertIntegerPart(integerPart);
        
        // 转换小数部分
        String chineseDecimal = convertDecimalPart(decimalPart);
        
        // 组合结果
        StringBuilder result = new StringBuilder();
        if (isNegative) {
            result.append("负");
        }
        
        if (chineseInteger.isEmpty() || "零".equals(chineseInteger)) {
            result.append("零元");
        } else {
            result.append(chineseInteger).append("元");
        }
        
        if (chineseDecimal.isEmpty()) {
            result.append("整");
        } else {
            result.append(chineseDecimal);
        }
        
        return result.toString();
    }
    
    /**
     * 转换整数部分
     */
    private static String convertIntegerPart(String integerStr) {
        if (integerStr == null || integerStr.isEmpty() || "0".equals(integerStr)) {
            return "零";
        }
        
        StringBuilder result = new StringBuilder();
        int length = integerStr.length();
        boolean hasZero = false; // 标记是否有零需要添加
        
        for (int i = 0; i < length; i++) {
            int digit = Character.getNumericValue(integerStr.charAt(i));
            int position = length - i - 1; // 从右往左的位置
            
            if (digit == 0) {
                // 如果是0，需要判断是否添加零
                if (position == 4 || position == 8) { // 万位或亿位
                    if (result.length() > 0 && !result.toString().endsWith("万") && !result.toString().endsWith("亿")) {
                        if (position == 4) {
                            result.append("万");
                        } else if (position == 8) {
                            result.append("亿");
                        }
                    }
                }
                hasZero = true;
            } else {
                // 如果前面有零，且当前不是最高位，添加零
                if (hasZero && result.length() > 0) {
                    result.append("零");
                }
                hasZero = false;
                
                // 添加数字
                result.append(CHINESE_NUMBERS[digit]);
                
                // 添加单位
                if (position > 0) {
                    if (position < CHINESE_UNITS.length) {
                        result.append(CHINESE_UNITS[position]);
                    }
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * 转换小数部分
     */
    private static String convertDecimalPart(String decimalStr) {
        if (decimalStr == null || decimalStr.isEmpty()) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        
        // 只处理角和分（前两位小数）
        for (int i = 0; i < Math.min(decimalStr.length(), 2); i++) {
            int digit = Character.getNumericValue(decimalStr.charAt(i));
            if (digit > 0) {
                result.append(CHINESE_NUMBERS[digit]).append(DECIMAL_UNITS[i]);
            }
        }
        
        return result.toString();
    }
}
