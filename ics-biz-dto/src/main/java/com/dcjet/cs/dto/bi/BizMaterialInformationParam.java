package com.dcjet.cs.dto.bi;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-3-12
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizMaterialInformationParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 
     */
	@XdoSize(max = 60, message = "长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String businessType;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String dataStatus;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String versionNo;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 40, message = "长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String parentId;
	/**
     * 创建人
     */
	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String insertUser;
	/**
     * 创建日期
     */
	@ApiModelProperty("创建日期")
	private  Date insertTime;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;
	/**
     * 商品名称
     */
	@XdoSize(max = 80, message = "商品名称长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	@NotEmpty(message = "商品名称不能为空")
	@JsonProperty("gName")
	private  String gname;
	/**
     * 中文简称
     */
	@XdoSize(max = 80, message = "中文简称长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("中文简称")
	private  String shortCn;
	/**
     * 开票名称
     */
	@XdoSize(max = 80, message = "开票名称长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("开票名称")
	private  String billingName;
	/**
     * 英文全称
     */
	@XdoSize(max = 100, message = "英文全称长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("英文全称")
	private  String fullEnName;
	/**
     * 英文简称
     */
	@XdoSize(max = 80, message = "英文简称长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("英文简称")
	private  String shortEnName;
	/**
     * 商品类别
     */
	@ApiModelProperty("商品类别")
	@NotEmpty(message = "商品类别不能为空")
	private  String merchandiseCategories;
	/**
     * 国家产品目录
     */
	@XdoSize(max = 100, message = "国家产品目录长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("国家产品目录")
	private  String nationalProductCatalogue;
	/**
     * 条形码
     */
	@XdoSize(max = 80, message = "条形码长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("条形码")
	private  String barCode;
	/**
     * 常用标志
     */
	@ApiModelProperty("常用标志")
	private  String commonMark;
	private List<String> commonMarkList;
	/**
     * 包装信息
     */
	@ApiModelProperty("包装信息")
	private  String packagingInformation;
	/**
     * 中烟MIS编码
     */
	@XdoSize(max = 80, message = "中烟MIS编码长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("中烟MIS编码")
	private  String misCode;
	/**
     * 统计名称
     */
	@XdoSize(max = 80, message = "统计名称长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("统计名称")
	private  String statisticalName;
	/**
     * 报送税务总局牌号名称方式
     */
	@ApiModelProperty("报送税务总局牌号名称方式")
	private  String nameMethod;
	/**
     * 国内不含税调拨价（RMB）
     */
	@Digits(integer = 13, fraction = 6, message = "国内不含税调拨价（RMB）必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("国内不含税调拨价（RMB）")
	private  BigDecimal taxExclusive;
	/**
     * 含税单价
     */
	@Digits(integer = 11, fraction = 8, message = "含税单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("含税单价")
	private  BigDecimal includingTax;
	/**
     * 税率
     */
	@Digits(integer = 13, fraction = 6, message = "税率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("税率")
	private  BigDecimal taxRate;
	/**
     * 不含税单价
     */
	@Digits(integer = 11, fraction = 8, message = "不含税单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("不含税单价")
	private  BigDecimal priceExcludingTax;
	/**
     * 备注
     */
	@XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 数据状态
     */
	@XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("数据状态")
	@NotEmpty(message = "数据状态不能为空")
	private  String dataState;
	/**
     * 供应商
     */
	@XdoSize(max = 200, message = "供应商长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	@NotEmpty(message = "供应商不能为空")
	private  String supplierCode;
	/**
     * 供应商折扣率
     */
	@Digits(integer = 15, fraction = 4, message = "供应商折扣率必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("供应商折扣率")
	private  BigDecimal supplierDiscountRate;
	/**
     * 进口单价
     */
	@Digits(integer = 11, fraction = 8, message = "进口单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("进口单价")
	private  BigDecimal importUnitPrice;
	/**
     * 币种
     */
	@XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	@NotEmpty(message = "币种不能为空")
	private  String curr;


	/**
	 * 创建人部门编码
	 */
	@ApiModelProperty("创建人部门编码")
	@XdoSize(max = 150, message = "创建人部门编码长度不能超过150位字节长度(一个汉字2位字节长度)!")
	private  String sysOrgCode;
}
