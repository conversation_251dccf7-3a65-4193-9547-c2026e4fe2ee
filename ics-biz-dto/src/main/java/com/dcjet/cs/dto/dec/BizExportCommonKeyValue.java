package com.dcjet.cs.dto.dec;


import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * 第 9 条线，非国营贸易-出口辅料 出货信息表头 - 通用查询对象
 */
@Getter
@Setter
@ApiModel(value = "第 9 条线，非国营贸易-出口辅料 出货信息表头 - 通用返回键值对")
public class BizExportCommonKeyValue<K,V> implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * 键
     */
    private K label;

    /**
     * 值
     */
    private V value;

    @Override
    public String toString() {
        return "{" +
                "label=" + label +
                ", value=" + value +
                '}';
    }
}