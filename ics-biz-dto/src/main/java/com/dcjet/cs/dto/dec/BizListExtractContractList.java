package com.dcjet.cs.dto.dec;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class BizListExtractContractList implements Serializable {
    //  ih.CONTRACT_NO,
    //  ih.TRADE_CODE,
    //  il.GOODS_NAME,
    //  SUM(il.QUANTITY) as USED_QUANTITY
    private static final long serialVersionUID = 1L;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 贸易代码
     */
    private String tradeCode;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 已使用数量
     */
    private BigDecimal usedQuantity;

    /**
     * 表头HEAD_ID
     */
    private String headId;


    /**
     * 可提取数量
     */
    private BigDecimal extractQuantity;

}
