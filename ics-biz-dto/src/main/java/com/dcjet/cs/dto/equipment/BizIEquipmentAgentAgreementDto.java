package com.dcjet.cs.dto.equipment;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-2
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIEquipmentAgentAgreementDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键id
      */
    @ApiModelProperty("主键id")
	private  String id;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 业务单号
      */
    @ApiModelProperty("业务单号")
	private  String tradeCode;
	/**
      * 组织机构代码
      */
    @ApiModelProperty("组织机构代码")
	private  String sysOrgCode;
	/**
      * 数据状态
      */
    @ApiModelProperty("数据状态")
	private  String dataState;
	/**
      * 版本号
      */
    @ApiModelProperty("版本号")
	private  String versionNo;
	/**
      * 父id
      */
    @ApiModelProperty("父id")
	private  String parentId;
	/**
      * 创建人账号
      */
    @ApiModelProperty("创建人账号")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 修改人账号
      */
    @ApiModelProperty("修改人账号")
	private  String updateBy;
	/**
      * 修改时间
      */
    @ApiModelProperty("修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 创建人姓名
      */
    @ApiModelProperty("创建人姓名")
	private  String insertUserName;
	/**
      * 修改人姓名
      */
    @ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
      * 扩展字段1
      */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
      * 扩展字段2
      */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
      * 扩展字段3
      */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
      * 扩展字段4
      */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
      * 扩展字段5
      */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
      * 扩展字段6
      */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
      * 扩展字段7
      */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
      * 扩展字段8
      */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
      * 扩展字段9
      */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
      * 扩展字段10
      */
    @ApiModelProperty("扩展字段10")
	private  String extend10;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 业务地点
      */
    @ApiModelProperty("业务地点")
	private  String businessPlace;
	/**
      * 协议类型
      */
    @ApiModelProperty("协议类型")
	private  String agreementType;
	/**
      * 协议编号
      */
    @ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
      * 客户
      */
    @ApiModelProperty("客户")
	private  String customer;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String supplier;
	/**
      * 签约日期
      */
    @ApiModelProperty("签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date signDate;
	/**
      * 签约地点
      */
    @ApiModelProperty("签约地点")
	private  String signPlace;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String currency;
	/**
      * 合同金额
      */
    @ApiModelProperty("合同金额")
	private  BigDecimal contractAmount;
	/**
      * 代理费率%
      */
    @ApiModelProperty("代理费率%")
	private  BigDecimal agencyRate;
	/**
      * 代理费用
      */
    @ApiModelProperty("代理费用")
	private  BigDecimal agencyFee;
	/**
      * 协议条款
      */
    @ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
      * 制单人
      */
    @ApiModelProperty("制单人")
	private  String makeBy;
	/**
      * 制单日期
      */
    @ApiModelProperty("制单日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date makeDate;
	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String billStatus;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 审批状态
      */
    @ApiModelProperty("审批状态")
	private  String approvalStatus;
}
