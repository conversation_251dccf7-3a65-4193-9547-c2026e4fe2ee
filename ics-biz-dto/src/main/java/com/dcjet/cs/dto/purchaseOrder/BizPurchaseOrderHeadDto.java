package com.dcjet.cs.dto.purchaseOrder;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-11
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizPurchaseOrderHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 最后修改人名称
      */
    @ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 拓展字段1
      */
    @ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
      * 拓展字段2
      */
    @ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
      * 拓展字段3
      */
    @ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
      * 拓展字段4
      */
    @ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
      * 拓展字段5
      */
    @ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
      * 拓展字段6
      */
    @ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
      * 拓展字段7
      */
    @ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
      * 拓展字段8
      */
    @ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
      * 拓展字段9
      */
    @ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
      * 拓展字段10
      */
    @ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 进货单号
      */
    @ApiModelProperty("进货单号")
	private  String purchaseOrderNo;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String supplier;
	/**
      * 客户
      */
    @ApiModelProperty("客户")
	private  String customer;
	/**
      * 客户地址
      */
    @ApiModelProperty("客户地址")
	private  String customerAddress;
	/**
      * 贸易国别
      */
    @ApiModelProperty("贸易国别")
	private  String tradeCountry;
	/**
      * 经营单位
      */
    @ApiModelProperty("经营单位")
	private  String businessEnterprise;
	/**
      * 目的地/港
      */
    @ApiModelProperty("目的地/港")
	private  String portOfDestination;
	/**
      * 付款方式
      */
    @ApiModelProperty("付款方式")
	private  String paymentMethod;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;
	/**
      * 总金额
      */
    @ApiModelProperty("总金额")
	private  BigDecimal totalAmount;
	/**
      * 运输方式
      */
    @ApiModelProperty("运输方式")
	private  String transportMode;
	/**
      * 价格条款
      */
    @ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
      * 价格条款对应港口
      */
    @ApiModelProperty("价格条款对应港口")
	private  String priceTermPort;
	/**
      * 发货单位
      */
    @ApiModelProperty("发货单位")
	private  String deliveryEnterprise;
	/**
      * 包装种类
      */
    @ApiModelProperty("包装种类")
	private  String wrapType;
	/**
      * 包装数量
      */
    @ApiModelProperty("包装数量")
	private  BigDecimal packNum;
	/**
      * 发货单位所在地
      */
    @ApiModelProperty("发货单位所在地")
	private  String deliveryEnterpriseAddress;
	/**
      * 总毛重
      */
    @ApiModelProperty("总毛重")
	private  BigDecimal totalNetWt;
	/**
      * 总净重
      */
    @ApiModelProperty("总净重")
	private  BigDecimal totalGrossWt;
	/**
      * 总皮重
      */
    @ApiModelProperty("总皮重")
	private  BigDecimal totalTare;
	/**
      * 发送报关
      */
    @ApiModelProperty("发送报关")
	private  String sendDeclare;
	/**
      * 业务日期
      */
    @ApiModelProperty("业务日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date businessDate;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 是否确认
      */
    @ApiModelProperty("是否确认")
	private  String isConfirm;
	/**
      * 是否保存
      */
    @ApiModelProperty("是否保存")
	private  String isSave;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String status;
	/**
      * 审核状态
      */
    @ApiModelProperty("审核状态")
	private  String apprStatus;
	/**
      * 发送财务系统
      */
    @ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
      * 是否红冲
      */
    @ApiModelProperty("是否红冲")
	private  String redFlush;
	/**
      * 外商合同、进货明细数据标记
      */
    @ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseMark;
	/**
      * 外商合同、进货明细数据标记
      */
    @ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseNoMark;
	/**
      * 发票号
      */
    @ApiModelProperty("发票号")
	private  String invoiceNo;
	/**
      * 启运港
      */
    @ApiModelProperty("启运港")
	private  String portOfDeparture;
	/**
      * 船名航次
      */
    @ApiModelProperty("船名航次")
	private  String vesselVoyage;
	/**
      * 开航日期
      */
    @ApiModelProperty("开航日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date sailingDate;
	/**
      * 作销日期
      */
    @ApiModelProperty("作销日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date salesDate;
	/**
      * 总数量
      */
    @ApiModelProperty("总数量")
	private  BigDecimal totalQuantity;

	private String createrBy;
	private String createrUserName;
	private Date createrTime;

	private Date declarationDate;
	private String entryNo;
}
