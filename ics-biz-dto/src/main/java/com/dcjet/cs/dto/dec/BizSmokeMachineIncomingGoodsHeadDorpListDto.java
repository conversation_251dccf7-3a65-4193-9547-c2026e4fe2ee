package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * （3）烟机设备-进货单-表头数据
 */
@Getter
@Setter
@ApiModel(value = "（3）烟机设备-进货单-表头数据-返回信息")
public class BizSmokeMachineIncomingGoodsHeadDorpListDto implements Serializable{

    private List<BizSmokeMachineIncomingGoodsHeadDto> list;


    /**
     * 当前数据中客户下拉列表
     */
    private List<Map<String,String>> customerList;


    /**
     * 当前数据中供应商下拉列表
     */
    private List<Map<String,String>> supplierList;


}