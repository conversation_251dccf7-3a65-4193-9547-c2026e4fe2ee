package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "信息表头-返回信息")
public class BizISellHeadDto implements Serializable{


    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    private String sid;

    private String headId;

    // purchaseOrderNumber: '', // 进货单号
    private String purchaseOrderNumber;

    //  purchasingUnit: '',      // 购货单位
    private String purchasingUnit;

    //  sellingUnit: '',        // 销货单位
    private String sellingUnit;

    //  taxRate: '',            // 税率%
    private BigDecimal taxRate;

    //  dateOfSale: '',         // 作销日期
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateOfSale;

    //  remark: '',             // 备注
    private String remark;

    //  salesDocumentStatus: '',// 销售单据状态
    private String salesDocumentStatus;

    //  salesDataConfirmationTime: '', // 销售数据确认时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date salesDataConfirmationTime;

    //  sendUFida: '',          // 发送用友
    private String sendUfida;

    //  drawer: '',             // 开票人
    private String drawer;

    //  businessDate: ''        // 业务日期
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;

    private String insertUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    private String insertUserName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateUser;

    private Date updateTime;

    private String updateUserName;

    private String tradeCode;


    //合同号
    private String contractNo;

    //客户
    private String customer;

    //发送财务系统
    private String sendFinancial;

    //是否冲红
    private String isFlushRed;

}