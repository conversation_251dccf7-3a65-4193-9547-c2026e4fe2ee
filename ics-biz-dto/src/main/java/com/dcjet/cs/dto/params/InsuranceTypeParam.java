package com.dcjet.cs.dto.params;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class InsuranceTypeParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;
	/**
     * 参数代码
     */
	@XdoSize(max = 30, message = "参数代码长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("参数代码")
	private  String paramCode;
	/**
     * 保险类别中文名称
     */
	@XdoSize(max = 200, message = "保险类别中文名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("保险类别中文名称")
	private  String insuranceTypeCn;
	/**
     * 保险类别英文名称
     */
	@XdoSize(max = 200, message = "保险类别英文名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("保险类别英文名称")
	private  String insuranceTypeEn;
	/**
     * 备注
     */
	@XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
}
