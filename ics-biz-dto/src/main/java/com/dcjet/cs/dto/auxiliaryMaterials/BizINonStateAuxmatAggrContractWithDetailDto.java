package com.dcjet.cs.dto.auxiliaryMaterials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 包含表头和表体数据的DTO
 * <AUTHOR>
 * @date: 2025-06-23
 */
@ApiModel(value = "非国营辅料合同表头和表体返回信息")
@Setter @Getter
public class BizINonStateAuxmatAggrContractWithDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 表头数据
     */
    @ApiModelProperty("表头数据")
    private BizINonStateAuxmatAggrContractHeadDto head;
    
    /**
     * 表体数据列表
     */
    @ApiModelProperty("表体数据列表")
    private List<BizINonStateAuxmatAggrContractListDto> details;
}
