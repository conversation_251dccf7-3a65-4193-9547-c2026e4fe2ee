package com.dcjet.cs.dto.nonAuxiliaryMaterials;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 进过管理-表体列表
 */
@Getter
@Setter
@ApiModel(value = "进过管理-表体列表-返回信息")
public class BizNonIncomingGoodsListDto implements Serializable{

    /**
     * 主键id
     * 数据库字段:id
     * 字符类型(80)
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(120)
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(20)
     */
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(20)
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(20)
     */
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(20)
     */
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * 字符类型(80)
     */
    @ApiModelProperty("父级id")
    private String parentId;

    /**
     * 创建人
     * 数据库字段:create_by
     * 字符类型(100)
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * 日期类型(6)
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private Date createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @ApiModelProperty("创建时间-结束时间")
    private Date createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(100)
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private Date updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(100)
     */
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(100)
     */
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 商品名称
     * 数据库字段:goods_name
     * 字符类型(160)
     */
    @ApiModelProperty("商品名称")
    private String goodsName;

    /**
     * 规格
     * 数据库字段:product_model
     * 字符类型(200)
     */
    @ApiModelProperty("规格")
    private String productModel;

    /**
     * 数量
     * 数据库字段:quantity
     * 数值类型(19,4)
     */
    @ApiModelProperty("数量")
    private BigDecimal quantity;

    /**
     * 单位
     * 数据库字段:unit
     * 字符类型(60)
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 单价
     * 数据库字段:unit_price
     * 数值类型(19,8)
     */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 金额
     * 数据库字段:amount
     * 数值类型(19,4)
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 交货日期
     * 数据库字段:delivery_date
     * 日期类型(6)
     */
    @ApiModelProperty("交货日期")
    private Date deliveryDate;
    /**
     * 交货日期-开始时间
     */
    @ApiModelProperty("交货日期-开始时间")
    private Date deliveryDateFrom;

    /**
     * 交货日期-结束时间
     */
    @ApiModelProperty("交货日期-结束时间")
    private Date deliveryDateTo;

    /**
     * 总价折美元
     * 数据库字段:total_usd
     * 数值类型(19,4)
     */
    @ApiModelProperty("总价折美元")
    private BigDecimal totalUsd;

    /**
     * 备注
     * 数据库字段:remarks
     * 字符类型(400)
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 表头head_id
     * 数据库字段:head_id
     * 字符类型(40)
     */
    @ApiModelProperty("表头head_id")
    private String headId;

    /**
     * 进口数量
     * 数据库字段:in_quantity
     * 数值类型(19,6)
     */
    @ApiModelProperty("进口数量")
    private BigDecimal inQuantity;

    /**
     * 进口单位
     * 数据库字段:in_unit
     * 字符类型(60)
     */
    @ApiModelProperty("进口单位")
    private String inUnit;

    /**
     * 币种
     * 数据库字段:curr
     * 字符类型(40)
     */
    @ApiModelProperty("币种")
    private String curr;

    /**
     * 进口发票号
     * 数据库字段:invoice_no
     * 字符类型(60)
     */
    @ApiModelProperty("进口发票号")
    private String invoiceNo;



    /**
     * 合同表体的ID
     * 字符类型(40)
     * 非必填
     */
    @ApiModelProperty("合同表体的ID")
    private String contractListId;


}