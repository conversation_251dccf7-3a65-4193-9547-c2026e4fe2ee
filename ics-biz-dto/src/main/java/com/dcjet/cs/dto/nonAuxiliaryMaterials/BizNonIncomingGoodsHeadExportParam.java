package com.dcjet.cs.dto.nonAuxiliaryMaterials;

import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 导出传入参数
 *
 * <AUTHOR>
 * @date 2025-05-22 15:28:59
 */
@Getter
@Setter
@ApiModel(description = "查询传入参数")
public class BizNonIncomingGoodsHeadExportParam extends ExcelExportParam {
    /**
     * 导出传入参数
     */
    @ApiModelProperty("查询参数")
    private BizNonIncomingGoodsHeadParam exportColumns;
}
