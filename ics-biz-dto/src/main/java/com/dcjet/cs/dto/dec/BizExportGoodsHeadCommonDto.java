package com.dcjet.cs.dto.dec;


import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * 第 9 条线，非国营贸易-出口辅料 出货信息表头 - 通用查询对象
 */
@Getter
@Setter
@ApiModel(value = "第 9 条线，非国营贸易-出口辅料 出货信息表头 - 通用查询对象")
public class BizExportGoodsHeadCommonDto implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * 客商信息列表
     */
    private List<Map<String,String>> customerList;

    /**
     * 币制列表
     */
    private List<Map<String,String>> currList;

    /**
     * 价格条款列表
     *   价格条款对应港口
     *   priceTermPort:[
     *     {
     *       label:'起运港',
     *       value:'0'
     *     },
     *     {
     *       label:'目的港',
     *       value:'1'
     *     }
     *   ],
     */
    private List<Map<String,String>> priceTermList;


    /**
     * 包装信息 【企业自定义参数-包装信息】
     */
    private List<Map<String,String>> packageList;


    /**
     * 城市信息 【企业自定义参数-城市】
     */
    private List<Map<String,String>> cityList;


    /**
     * 保险类型 【企业自定义参数-保险类别】
     */
    private List<Map<String,String>> insuranceTypeList;


    /**
     * 港口信息 - 自定义港口
     */
    private List<Map<String,String>> portList;


    /**
     * 出货信息 客户列表信息
     */
    private List<Map<String,String>> exportCustomerList;

    /**
     * 出货信息 供应商列表信息
     */
    private List<Map<String,String>> exportSupplierList;


    /**
     * 单位 企业参数库列表信息
     */
    private List<Map<String,String>> unitList;


}