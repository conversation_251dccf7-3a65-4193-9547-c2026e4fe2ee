package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import com.xdo.validation.annotation.XdoSize;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * （第7条线）出料加工进口薄片-分析单表表体
 */
@Getter
@Setter
@ApiModel(value = "（第7条线）出料加工进口薄片-分析单表表体-传入参数")
public class BizBpAnalyseOrderListParam implements Serializable{
    /**
     * 主键id
     * 数据库字段:id
     * 字符类型(160)
     */
    @NotNull(message = "主键id不能为空！")
    @XdoSize(max = 80, message = "主键id长度不能超过80位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(240)
     */
    @XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "数据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "版本号长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "企业10位编码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "组织机构代码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * 字符类型(160)
     */
    @XdoSize(max = 80, message = "父级id长度不能超过80位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("父级id")
    private String parentId;

    /**
     * 创建人
     * 数据库字段:create_by
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "创建人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * 日期类型(6)
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private String createTimeFrom;

    /**
    * 创建时间-结束时间
    */
    @ApiModelProperty("创建时间-结束时间")
    private String createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
    * 更新时间-结束时间
    */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "插入用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段1长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段2长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段3长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段4长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段5长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段6长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段7长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段8长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段9长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段10长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 商品名称
     * 数据库字段:product_name
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "商品名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 产品型号
     * 数据库字段:product_model
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "产品型号长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 单位
     * 数据库字段:unit
     * 字符类型(80)
     */
    @XdoSize(max = 40, message = "单位长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 数量
     * 数据库字段:quantity
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "数量必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("数量")
    private BigDecimal quantity;

    /**
     * 箱数
     * 数据库字段:box_count
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "箱数必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("箱数")
    private BigDecimal boxCount;

    /**
     * 毛重
     * 数据库字段:gross_weight
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "毛重必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("毛重")
    private BigDecimal grossWeight;

    /**
     * 单价
     * 数据库字段:unit_price
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "单价必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 金额
     * 数据库字段:amount
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "金额必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 备注
     * 数据库字段:remark
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remark;



    /**
     * 净重
     * 数据库字段:net_wt
     * 数值类型(19,4)
     */
    @ApiModelProperty("净重")
    @Digits(integer = 19, fraction = 4, message = "净重必须为数字,整数位最大19位,小数最大4位!")
    private BigDecimal netWt;



    /**
     * 体积
     * 数据库字段:volume
     * 数值类型(19,4)
     */
    @ApiModelProperty("体积")
    @Digits(integer = 19, fraction = 4, message = "体积必须为数字,整数位最大19位,小数最大4位!")
    private BigDecimal volume;


}