package com.dcjet.cs.dto.aeo;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 表单字段标记信息传入参数
 *
 * <AUTHOR>
 * @date 2024
 */
@Setter
@Getter
@ApiModel(value = "表单字段标记信息传入参数")
public class BizFieldMarkingParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 表单记录ID，关联具体的业务记录
     */
    @NotEmpty(message = "{表单记录ID不能为空！}")
    @XdoSize(max = 64, message = "{表单记录ID长度不能超过64位字节长度!}")
    @ApiModelProperty("表单记录ID")
    private String sid;

    /**
     * 表单类型，用于区分不同的表单页面
     */
    @XdoSize(max = 32, message = "{表单类型长度不能超过32位字节长度!}")
    @ApiModelProperty("表单类型")
    private String formType;

    /**
     * 字段标记数据，JSON格式存储fieldMarkings.value的完整内容
     */
    @ApiModelProperty("字段标记数据")
    private String fieldMarkings;

    /**
     * 备注信息，可用于存储额外的标记说明
     */
    @XdoSize(max = 500, message = "{备注信息长度不能超过500位字节长度!}")
    @ApiModelProperty("备注信息")
    private String remark;
}