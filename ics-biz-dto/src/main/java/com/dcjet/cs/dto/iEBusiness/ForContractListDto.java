package com.dcjet.cs.dto.iEBusiness;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外商合同列表DTO
 * 用于代理协议选择合同时显示
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Setter
@Getter
@ApiModel(value = "外商合同列表传输模型")
public class ForContractListDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 合同号
     */
    @ApiModelProperty("合同号")
    private String contractNo;

    /**
     * 买方
     */
    @ApiModelProperty("买方")
    private String buyer;

    /**
     * 卖方
     */
    @ApiModelProperty("卖方")
    private String seller;

    /**
     * 签约日期
     */
    @ApiModelProperty("签约日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    /**
     * 金额（汇总表体数据）
     */
    @ApiModelProperty("金额")
    private BigDecimal totalAmount;
}
