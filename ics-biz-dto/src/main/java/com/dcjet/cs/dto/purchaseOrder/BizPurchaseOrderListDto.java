package com.dcjet.cs.dto.purchaseOrder;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-14
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizPurchaseOrderListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 最后修改人名称
      */
    @ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 拓展字段1
      */
    @ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
      * 拓展字段2
      */
    @ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
      * 拓展字段3
      */
    @ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
      * 拓展字段4
      */
    @ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
      * 拓展字段5
      */
    @ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
      * 拓展字段6
      */
    @ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
      * 拓展字段7
      */
    @ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
      * 拓展字段8
      */
    @ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
      * 拓展字段9
      */
    @ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
      * 拓展字段10
      */
    @ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	private  String productName;
	/**
      * 产品型号
      */
    @ApiModelProperty("产品型号")
	private  String productModel;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private  String unit;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
      * 金额
      */
    @ApiModelProperty("金额")
	private  BigDecimal amount;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 箱数
      */
    @ApiModelProperty("箱数")
	private  BigDecimal containerNum;
	/**
      * 表头id
      */
    @ApiModelProperty("表头id")
	private  String headId;
	/**
      * 外商合同表体id
      */
    @ApiModelProperty("外商合同表体id")
	private  String contractListId;
	/**
      * 分析单表体id
      */
    @ApiModelProperty("分析单表体id")
	private  String analyseListId;
	/**
      * 毛重
      */
    @ApiModelProperty("毛重")
	private  BigDecimal grossWeight;
	/**
      * 净重
      */
    @ApiModelProperty("净重")
	private  BigDecimal netWeight;
}
