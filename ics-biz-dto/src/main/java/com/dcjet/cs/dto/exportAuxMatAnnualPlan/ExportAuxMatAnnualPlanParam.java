package com.dcjet.cs.dto.exportAuxMatAnnualPlan;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Setter
@Getter
@ApiModel(value = "出口辅料年度计划参数模型")
public class ExportAuxMatAnnualPlanParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 出口客户
     */
    @ApiModelProperty("出口客户")
    @NotNull(message = "出口客户不能为空")
    @XdoSize(max = 200, message = "出口客户长度不能超过200位字节长度(一个汉字2位字节长度)")
    private String exportCustomer;

    /**
     * 出口品类
     */
    @ApiModelProperty("出口品类")
    @NotNull(message = "出口品类不能为空")
    @XdoSize(max = 80, message = "出口品类长度不能超过80位字节长度(一个汉字2位字节长度)")
    private String exportCategory;

    /**
     * 年计划出口总量（万支，吨）
     */
    @ApiModelProperty("年计划出口总量（万支，吨）")
    @NotNull(message = "年计划出口总量不能为空")
    @Digits(integer = 13, fraction = 6, message = "年计划出口总量必须为数字,整数位最大13位,小数最大6位")
    private BigDecimal planExportAmount;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;
}