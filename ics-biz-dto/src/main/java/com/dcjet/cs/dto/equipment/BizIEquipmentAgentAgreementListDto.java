package com.dcjet.cs.dto.equipment;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@ApiModel(value = " DTO")
@Setter
@Getter
public class BizIEquipmentAgentAgreementListDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键id
    */
    @ApiModelProperty("主键id")
	private  String id;
    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型")
	private  String businessType;
    /**
    * 业务单号
    */
    @ApiModelProperty("业务单号")
	private  String tradeCode;
    /**
    * 组织机构代码
    */
    @ApiModelProperty("组织机构代码")
	private  String sysOrgCode;
    /**
    * 数据状态
    */
    @ApiModelProperty("数据状态")
	private  String dataState;
    /**
    * 版本号
    */
    @ApiModelProperty("版本号")
	private  String versionNo;
    /**
    * 父id
    */
    @ApiModelProperty("父id")
	private  String parentId;
    /**
    * 创建人账号
    */
    @ApiModelProperty("创建人账号")
	private  String createBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
	private  Date createTime;
    /**
    * 修改人账号
    */
    @ApiModelProperty("修改人账号")
	private  String updateBy;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
	private  Date updateTime;
    /**
    * 创建人姓名
    */
    @ApiModelProperty("创建人姓名")
	private  String insertUserName;
    /**
    * 修改人姓名
    */
    @ApiModelProperty("修改人姓名")
	private  String updateUserName;
    /**
    * 扩展字段1
    */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
    /**
    * 扩展字段2
    */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
    /**
    * 扩展字段3
    */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
    /**
    * 扩展字段4
    */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
    /**
    * 扩展字段5
    */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
    /**
    * 扩展字段6
    */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
    /**
    * 扩展字段7
    */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
    /**
    * 扩展字段8
    */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
    /**
    * 扩展字段9
    */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
    /**
    * 扩展字段10
    */
    @ApiModelProperty("扩展字段10")
	private  String extend10;
    /**
    * 商品名称
    */
    @ApiModelProperty("商品名称")
	private  String productName;
    /**
    * 产品型号
    */
    @ApiModelProperty("产品型号")
	private  String productModel;
    /**
    * 数量
    */
    @ApiModelProperty("数量")
	private  BigDecimal quantity;
    /**
    * 单位
    */
    @ApiModelProperty("单位")
	private  String unit;
    /**
    * 单价
    */
    @ApiModelProperty("单价")
	private  BigDecimal unitPrice;
    /**
    * 金额
    */
    @ApiModelProperty("金额")
	private  BigDecimal totalAmount;
    /**
    * 装运日期
    */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty("装运日期")
	private  Date shipDate;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
	private  String remark;
    /**
    * 表头ID
    */
    @ApiModelProperty("表头ID")
	private  String headId;
}
