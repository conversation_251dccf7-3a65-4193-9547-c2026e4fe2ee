package com.dcjet.cs.dto.warehouse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizStoreIHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 创建人
     */

	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */

	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@XdoSize(max = 50, message = "创建人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
     * 创建人
     */
	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String updateBy;
	/**
     * 最后修改人名称
     */
	@XdoSize(max = 50, message = "最后修改人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@XdoSize(max = 50, message = "企业编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@XdoSize(max = 50, message = "创建人部门编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@XdoSize(max = 200, message = "拓展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@XdoSize(max = 200, message = "拓展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@XdoSize(max = 200, message = "拓展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@XdoSize(max = 200, message = "拓展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@XdoSize(max = 200, message = "拓展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@XdoSize(max = 200, message = "拓展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@XdoSize(max = 200, message = "拓展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@XdoSize(max = 200, message = "拓展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@XdoSize(max = 200, message = "拓展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@XdoSize(max = 200, message = "拓展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
     * 入库回单编号
     */
	@XdoSize(max = 120, message = "入库回单编号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("入库回单编号")
	private  String storeINo;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 购销合同号
     */
	@XdoSize(max = 120, message = "购销合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("购销合同号")
	private  String purSaleContractNo;
	/**
     * 进货单号
     */
	@XdoSize(max = 120, message = "进货单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("进货单号")
	private  String purchaseOrderNo;
	/**
     * 发票号
     */
	@XdoSize(max = 120, message = "发票号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发票号")
	private  String invoiceNo;
	/**
     * 供应商
     */
	@XdoSize(max = 400, message = "供应商长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String merchantCode;
	/**
     * 卖出价（汇率）
     */
	@Digits(integer = 13, fraction = 6, message = "卖出价（汇率）必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("卖出价（汇率）")
	private  BigDecimal sellingRate;
	/**
     * 外币货价
     */
	@Digits(integer = 17, fraction = 2, message = "外币货价必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("外币货价")
	private  BigDecimal foreignCurrPrice;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 价格条款
     */
	@XdoSize(max = 40, message = "价格条款长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
     * 关税
     */
	@Digits(integer = 17, fraction = 2, message = "关税必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("关税")
	private  BigDecimal tariffPrice;
	/**
     * 增值税
     */
	@Digits(integer = 17, fraction = 2, message = "增值税必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("增值税")
	private  BigDecimal vatPrice;
	/**
     * 代理费用
     */
	@Digits(integer = 17, fraction = 2, message = "代理费用必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费用")
	private  BigDecimal agentFee;
	/**
     * 保险费用
     */
	@Digits(integer = 17, fraction = 2, message = "保险费用必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("保险费用")
	private  BigDecimal insuranceFee;
	/**
     * 保险费用(外币)
     */
	@Digits(integer = 17, fraction = 2, message = "保险费用(外币)必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("保险费用(外币)")
	private  BigDecimal insuranceFeeCurr;
	/**
     * 商品金额小计
     */
	@Digits(integer = 17, fraction = 2, message = "商品金额小计必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("商品金额小计")
	private  BigDecimal productAmountTotal;
	/**
     * 费用金额小计
     */
	@Digits(integer = 17, fraction = 2, message = "费用金额小计必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("费用金额小计")
	private  BigDecimal feeAmountTotal;
	/**
     * 税金金额小计
     */
	@Digits(integer = 17, fraction = 2, message = "税金金额小计必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("税金金额小计")
	private  BigDecimal taxAmountTotal;
	/**
     * 成本金额小计
     */
	@Digits(integer = 17, fraction = 2, message = "成本金额小计必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("成本金额小计")
	private  BigDecimal costAmountTotal;
	/**
     * 合计金额（RMB）
     */
	@Digits(integer = 17, fraction = 2, message = "合计金额（RMB）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("合计金额（RMB）")
	private  BigDecimal totalPrice;
	/**
     * 业务日期
     */
	@ApiModelProperty("业务日期")
	private  Date businessDate;
	/**
     * 发送财务系统
     */
	@XdoSize(max = 20, message = "发送财务系统长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
     * 是否红冲
     */
	@XdoSize(max = 20, message = "是否红冲长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否红冲")
	private  String redFlush;
	/**
     * 备注
     */
	@XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 单据状态
     */
	@XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;

	private  String isNext;
	private  String storeEStatus;
	private  Date confirmTime;
	private  String apprStatus;

	private String insertTimeFrom;
	private String insertTimeTo;
}
