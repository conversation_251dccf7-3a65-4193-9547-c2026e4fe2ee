package com.dcjet.cs.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ApiModel("外商合同选项传输模型")
@Accessors(chain = true)
public class ForeignContractOptionsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户选项
     */
    @ApiModelProperty("客户选项")
    private List<Map<String, String>> customerOptions = Collections.emptyList();

    /**
     * 供应商选项
     */
    @ApiModelProperty("供应商选项")
    private List<Map<String, String>> supplierOptions = Collections.emptyList();

    /**
     * 制单人选项
     */
    @ApiModelProperty("制单人选项")
    private List<Map<String, String>> makerOptions = Collections.emptyList();

    /**
     * 价格条款选项
     */
    @ApiModelProperty("价格条款选项")
    private List<Map<String, String>> priceTermsOptions = Collections.emptyList();

    /**
     * 城市选项
     */
    @ApiModelProperty("城市选项")
    private List<Map<String, String>> cityOptions = Collections.emptyList();

    /**
     * 商品类别选项
     */
    private List<Map<String, String>> productTypeOptions = Collections.emptyList();

    /**
     * 海关参数币种选项
     */
    @ApiModelProperty("海关参数币种选项")
    private List<Map<String, String>> currOptions = Collections.emptyList();

    /**
     * 海关参数港口选项
     */
    @ApiModelProperty("海关参数港口选项")
    private List<Map<String, String>> portOptions = Collections.emptyList();

    /**
     * 海关参数单位选项
     */
    @ApiModelProperty("海关参数单位选项")
    private List<Map<String, String>> unitOptions = Collections.emptyList();
}