package com.dcjet.cs.dto.auxiliaryMaterials;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class PurchaseOrderFormParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 合同表头id列表
     */
    private List<String> contractIds;

    /**
     * 打印类型
     */
    private String type;
}
