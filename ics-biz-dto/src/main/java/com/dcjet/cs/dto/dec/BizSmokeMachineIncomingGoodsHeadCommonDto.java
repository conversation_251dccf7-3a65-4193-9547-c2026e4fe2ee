package com.dcjet.cs.dto.dec;


import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * （3）烟机设备-进货单-表头数据
 */
@Getter
@Setter
@ApiModel(value = "（3）烟机设备-进货单-表头数据-通用查询对象 ")
public class BizSmokeMachineIncomingGoodsHeadCommonDto implements Serializable{

    private List<Map<String,String>> customerList;

    private List<Map<String,String>> supplierList;

    private List<Map<String,String>> createByList;

    private List<Map<String,String>> portList;

    private List<Map<String,String>> currList;
}