package com.dcjet.cs.dto.seven;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Setter
@Getter
@ApiModel("外商合同表体新增物料信息传输模型")
@Accessors(chain = true)
public class SevenForeignContractAddBodyMaterialDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @JsonProperty("gName")
    private String gName;

    /**
     * 英文名称
     */
    @ApiModelProperty("英文名称")
    private String fullEnName;

    /**
     * 商品类别
     */
    @ApiModelProperty("商品类别")
    private String merchandiseCategories;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplier;
}