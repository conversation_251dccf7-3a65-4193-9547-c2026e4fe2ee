package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 第9条线-非国营贸易出口辅料-外销发票表头
 */
@Getter
@Setter
@ApiModel(value = "第9条线-非国营贸易出口辅料-外销发票表头-返回信息")
public class BizExportGoodsSellHeadDto implements Serializable{

    /**
     * 主键id
     * 数据库字段:id
     * 字符类型(80)
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(240)
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(40)
     */
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(40)
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(40)
     */
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(40)
     */
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * 字符类型(80)
     */
    @ApiModelProperty("父级id")
    private String parentId;

    /**
     * 创建人
     * 数据库字段:create_by
     * 字符类型(200)
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * 日期类型(6)
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @ApiModelProperty("创建时间-结束时间")
    private String createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(200)
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(200)
     */
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(200)
     */
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 发票号
     * 数据库字段:invoice_no
     * 字符类型(120)
     */
    @ApiModelProperty("发票号")
    private String invoiceNo;

    /**
     * 出货单号
     * 数据库字段:export_no
     * 字符类型(120)
     */
    @ApiModelProperty("出货单号")
    private String exportNo;

    /**
     * 合同号
     * 数据库字段:contract_no
     * 字符类型(120)
     */
    @ApiModelProperty("合同号")
    private String contractNo;

    /**
     * 发票客户
     * 数据库字段:invoice_customer
     * 字符类型(400)
     */
    @ApiModelProperty("发票客户")
    private String invoiceCustomer;

    /**
     * 销售客户
     * 数据库字段:sales_customer
     * 字符类型(400)
     */
    @ApiModelProperty("销售客户")
    private String salesCustomer;

    /**
     * 信用证号
     * 数据库字段:lc_no
     * 字符类型(120)
     */
    @ApiModelProperty("信用证号")
    private String lcNo;

    /**
     * 币种
     * 数据库字段:currency
     * 字符类型(20)
     */
    @ApiModelProperty("币种")
    private String currency;

    /**
     * 合计金额
     * 数据库字段:total_amount
     * 数值类型(19,4)
     */
    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;

    /**
     * 唛头
     * 数据库字段:mark
     * 字符类型(600)
     */
    @ApiModelProperty("唛头")
    private String mark;

    /**
     * 代理费率%
     * 数据库字段:agent_rate
     * 数值类型(19,6)
     */
    @ApiModelProperty("代理费率%")
    private BigDecimal agentRate;

    /**
     * 代理费（外币）
     * 数据库字段:agent_fee_foreign
     * 数值类型(19,6)
     */
    @ApiModelProperty("代理费（外币）")
    private BigDecimal agentFeeForeign;

    /**
     * 预收款日期
     * 数据库字段:prepay_date
     * date
     */
    @ApiModelProperty("预收款日期")
    private Date prepayDate;

    /**
     * 作销日期
     * 数据库字段:cancel_date
     * date
     */
    @ApiModelProperty("作销日期")
    private Date cancelDate;

    /**
     * 发票日期
     * 数据库字段:invoice_date
     * date
     */
    @ApiModelProperty("发票日期")
    private Date invoiceDate;

    /**
     * 汇率
     * 数据库字段:exchange_rate
     * 数值类型(19,6)
     */
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    /**
     * 代理费（不含税金额）
     * 数据库字段:agent_fee_ex_tax
     * 数值类型(19,2)
     */
    @ApiModelProperty("代理费（不含税金额）")
    private BigDecimal agentFeeExTax;

    /**
     * 代理费税额
     * 数据库字段:agent_tax
     * 数值类型(19,2)
     */
    @ApiModelProperty("代理费税额")
    private BigDecimal agentTax;

    /**
     * 代理费（价税合计）
     * 数据库字段:agent_fee_total
     * 数值类型(19,6)
     */
    @ApiModelProperty("代理费（价税合计）")
    private BigDecimal agentFeeTotal;

    /**
     * 发送财务系统
     * 数据库字段:send_finance
     * 字符类型(20)
     */
    @ApiModelProperty("发送财务系统")
    private String sendFinance;

    /**
     * 备注
     * 数据库字段:remark
     * 字符类型(400)
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 是否红冲
     * 数据库字段:is_red_flush
     * 字符类型(20)
     */
    @ApiModelProperty("是否红冲")
    private String isRedFlush;

    /**
     * 单据状态
     * 数据库字段:bill_status
     * 字符类型(20)
     */
    @ApiModelProperty("单据状态")
    private String billStatus;

    /**
     * 确认时间
     * 数据库字段:confirm_time
     * 字符类型(36)
     */
    @ApiModelProperty("确认时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;


}