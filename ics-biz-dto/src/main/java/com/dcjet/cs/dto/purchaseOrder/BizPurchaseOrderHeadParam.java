package com.dcjet.cs.dto.purchaseOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizPurchaseOrderHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 创建人
     */
	@NotEmpty(message="创建人不能为空！")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */
	@NotNull(message="创建时间不能为空！")
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
    * 创建时间-开始
    */
	@ApiModelProperty("创建时间-开始")
	private String createTimeFrom;
	/**
    * 创建时间-结束
    */
	@ApiModelProperty("创建时间-结束")
    private String createTimeTo;
	/**
     * 创建人名称
     */
	@XdoSize(max = 50, message = "创建人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
     * 更新人
     */
	@XdoSize(max = 50, message = "更新人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("更新人")
	private  String updateBy;
	/**
     * 最后修改人名称
     */
	@XdoSize(max = 50, message = "最后修改人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@XdoSize(max = 50, message = "企业编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@XdoSize(max = 50, message = "创建人部门编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@XdoSize(max = 200, message = "拓展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@XdoSize(max = 200, message = "拓展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@XdoSize(max = 200, message = "拓展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@XdoSize(max = 200, message = "拓展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@XdoSize(max = 200, message = "拓展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@XdoSize(max = 200, message = "拓展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@XdoSize(max = 200, message = "拓展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@XdoSize(max = 200, message = "拓展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@XdoSize(max = 200, message = "拓展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@XdoSize(max = 200, message = "拓展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
     * 业务类型
     */
	@XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 进货单号
     */
	@XdoSize(max = 120, message = "进货单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("进货单号")
	private  String purchaseOrderNo;
	/**
     * 供应商
     */
	@ApiModelProperty("供应商")
	private  String supplier;
	/**
     * 客户
     */
	@XdoSize(max = 400, message = "客户长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String customer;
	/**
     * 客户地址
     */
	@XdoSize(max = 400, message = "客户地址长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户地址")
	private  String customerAddress;
	/**
     * 贸易国别
     */
	@XdoSize(max = 120, message = "贸易国别长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("贸易国别")
	private  String tradeCountry;
	/**
     * 经营单位
     */
	@XdoSize(max = 120, message = "经营单位长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("经营单位")
	private  String businessEnterprise;
	/**
     * 目的地/港
     */
	@XdoSize(max = 100, message = "目的地/港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("目的地/港")
	private  String portOfDestination;
	/**
     * 付款方式
     */
	@XdoSize(max = 40, message = "付款方式长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("付款方式")
	private  String paymentMethod;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 总金额
     */
	@Digits(integer = 15, fraction = 4, message = "总金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总金额")
	private  BigDecimal totalAmount;
	/**
     * 运输方式
     */
	@XdoSize(max = 100, message = "运输方式长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("运输方式")
	private  String transportMode;
	/**
     * 价格条款
     */
	@XdoSize(max = 20, message = "价格条款长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@XdoSize(max = 40, message = "价格条款对应港口长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款对应港口")
	private  String priceTermPort;
	/**
     * 发货单位
     */
	@XdoSize(max = 120, message = "发货单位长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发货单位")
	private  String deliveryEnterprise;
	/**
     * 包装种类
     */
	@XdoSize(max = 200, message = "包装种类长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("包装种类")
	private  String wrapType;
	/**
     * 包装数量
     */
	@Digits(integer = 15, fraction = 4, message = "包装数量必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("包装数量")
	private  BigDecimal packNum;
	/**
     * 发货单位所在地
     */
	@XdoSize(max = 400, message = "发货单位所在地长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发货单位所在地")
	private  String deliveryEnterpriseAddress;
	/**
     * 总毛重
     */
	@Digits(integer = 15, fraction = 4, message = "总毛重必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总毛重")
	private  BigDecimal totalNetWt;
	/**
     * 总净重
     */
	@Digits(integer = 15, fraction = 4, message = "总净重必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总净重")
	private  BigDecimal totalGrossWt;
	/**
     * 总皮重
     */
	@Digits(integer = 15, fraction = 4, message = "总皮重必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总皮重")
	private  BigDecimal totalTare;
	/**
     * 发送报关
     */
	@ApiModelProperty("发送报关")
	private  String sendDeclare;
	/**
     * 业务日期
     */
	@ApiModelProperty("业务日期")
	private  Date businessDate;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 是否确认
     */
	@XdoSize(max = 10, message = "是否确认长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否确认")
	private  String isConfirm;
	/**
     * 是否保存
     */
	@XdoSize(max = 10, message = "是否保存长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否保存")
	private  String isSave;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 单据状态
     */
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 审核状态
     */
	@XdoSize(max = 20, message = "审核状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审核状态")
	private  String apprStatus;
	/**
     * 发送财务系统
     */
	@XdoSize(max = 20, message = "发送财务系统长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
     * 是否红冲
     */
	@XdoSize(max = 20, message = "是否红冲长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否红冲")
	private  String redFlush;
	/**
     * 外商合同、进货明细数据标记
     */
	@XdoSize(max = 20, message = "外商合同、进货明细数据标记长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseMark;
	/**
     * 外商合同、进货明细数据标记
     */
	@XdoSize(max = 600, message = "外商合同、进货明细数据标记长度不能超过600位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseNoMark;
	/**
     * 发票号
     */
	@XdoSize(max = 120, message = "发票号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发票号")
	private  String invoiceNo;
	/**
     * 启运港
     */
	@XdoSize(max = 100, message = "启运港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("启运港")
	private  String portOfDeparture;
	/**
     * 船名航次
     */
	@XdoSize(max = 100, message = "船名航次长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("船名航次")
	private  String vesselVoyage;
	/**
     * 开航日期
     */
	@ApiModelProperty("开航日期")
	private  Date sailingDate;
	/**
     * 作销日期
     */
	@ApiModelProperty("作销日期")
	private  Date salesDate;
	/**
     * 总数量
     */
	@Digits(integer = 15, fraction = 4, message = "总数量必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总数量")
	private  BigDecimal totalQuantity;

	private String createrBy;

	private String createrUserName;

	private Date createrTime;

	private String fileType;
	private String partyB;
	private String partyA;
	private String headId;
	private BigDecimal decTotal;
	private BigDecimal decPrice;
	private BigDecimal qty;
	private String unit;
	private String merchandiseCategories;
	private String productName;
	private String productGrade;
	private List<String> sids;
	private String expenseType;
	//税额金额
	private BigDecimal taxAmount;
	//无税金额
	private BigDecimal noTaxAmount;
	//费用金额
	private String amount;
	//是否分摊
	private String apportionment;
	//分摊方式
	private String methodAllocation;

	private String type;
}
