package com.dcjet.cs.dto.bi;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-3-12
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizMaterialInformationDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String businessType;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String dataStatus;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String versionNo;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String parentId;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建日期
      */
    @ApiModelProperty("创建日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gname;
	/**
      * 中文简称
      */
    @ApiModelProperty("中文简称")
	private  String shortCn;
	/**
      * 开票名称
      */
    @ApiModelProperty("开票名称")
	private  String billingName;
	/**
      * 英文全称
      */
    @ApiModelProperty("英文全称")
	private  String fullEnName;
	/**
      * 英文简称
      */
    @ApiModelProperty("英文简称")
	private  String shortEnName;
	/**
      * 商品类别
      */
    @ApiModelProperty("商品类别")
	private  String merchandiseCategories;
	/**
      * 国家产品目录
      */
    @ApiModelProperty("国家产品目录")
	private  String nationalProductCatalogue;
	/**
      * 条形码
      */
    @ApiModelProperty("条形码")
	private  String barCode;
	/**
      * 常用标志
      */
    @ApiModelProperty("常用标志")
	private  String commonMark;
	private List<String> commonMarkList;
	/**
      * 包装信息
      */
    @ApiModelProperty("包装信息")
	private  String packagingInformation;
	/**
      * 中烟MIS编码
      */
    @ApiModelProperty("中烟MIS编码")
	private  String misCode;
	/**
      * 统计名称
      */
    @ApiModelProperty("统计名称")
	private  String statisticalName;
	/**
      * 报送税务总局牌号名称方式
      */
    @ApiModelProperty("报送税务总局牌号名称方式")
	private  String nameMethod;
	/**
      * 国内不含税调拨价（RMB）
      */
    @ApiModelProperty("国内不含税调拨价（RMB）")
	private  BigDecimal taxExclusive;
	/**
      * 含税单价
      */
    @ApiModelProperty("含税单价")
	private  BigDecimal includingTax;
	/**
      * 税率
      */
    @ApiModelProperty("税率")
//	private  String taxRateStr;
	private  String taxRate;
	/**
      * 不含税单价
      */
    @ApiModelProperty("不含税单价")
	private  BigDecimal priceExcludingTax;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 数据状态
      */
    @ApiModelProperty("数据状态")
	private  String dataState;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String supplierCode;
	/**
      * 供应商折扣率
      */
    @ApiModelProperty("供应商折扣率")
	private  String supplierDiscountRate;
//	private  String supplierDiscountRateStr;
	/**
      * 进口单价
      */
    @ApiModelProperty("进口单价")
	private  BigDecimal importUnitPrice;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;

	@ApiModelProperty("供应商中文名称")
	private String merchantNameCn;


	/**
	 * 创建人部门编码
	 */
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	@ApiModelProperty("商品类别名称")
	private String categoryName;
}
