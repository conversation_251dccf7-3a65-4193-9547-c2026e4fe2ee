package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import com.xdo.validation.annotation.XdoSize;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 第9条线-非国营贸易出口辅料-外销发票表头
 */
@Getter
@Setter
@ApiModel(value = "第9条线-非国营贸易出口辅料-外销发票表头-传入参数")
public class BizExportGoodsSellHeadParam implements Serializable{

    @XdoSize(max = 40, message = "主键id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(240)
     */
    @XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "数据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "版本号长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "企业10位编码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "组织机构代码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * 字符类型(80)
     */
    @XdoSize(max = 40, message = "父级id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("父级id")
    private String parentId;


    @XdoSize(max = 100, message = "创建人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * 日期类型(6)
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private String createTimeFrom;

    /**
    * 创建时间-结束时间
    */
    @ApiModelProperty("创建时间-结束时间")
    private String createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
    * 更新时间-结束时间
    */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "插入用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段1长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段2长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段3长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段4长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段5长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段6长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段7长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段8长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段9长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段10长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 发票号
     * 数据库字段:invoice_no
     * 字符类型(120)
     */
    @NotNull(message = "发票号不能为空！")
    @XdoSize(max = 60, message = "发票号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发票号")
    private String invoiceNo;

    /**
     * 出货单号
     * 数据库字段:export_no
     * 字符类型(120)
     */
    @NotNull(message = "出货单号不能为空！")
    @XdoSize(max = 60, message = "出货单号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("出货单号")
    private String exportNo;

    /**
     * 合同号
     * 数据库字段:contract_no
     * 字符类型(120)
     */
    @NotNull(message = "合同号不能为空！")
    @XdoSize(max = 60, message = "合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("合同号")
    private String contractNo;

    /**
     * 发票客户
     * 数据库字段:invoice_customer
     * 字符类型(400)
     */
    @NotNull(message = "发票客户不能为空！")
    @XdoSize(max = 200, message = "发票客户长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发票客户")
    private String invoiceCustomer;

    /**
     * 销售客户
     * 数据库字段:sales_customer
     * 字符类型(400)
     */
    @NotNull(message = "销售客户不能为空！")
    @XdoSize(max = 200, message = "销售客户长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("销售客户")
    private String salesCustomer;

    /**
     * 信用证号
     * 数据库字段:lc_no
     * 字符类型(120)
     */
    @XdoSize(max = 60, message = "信用证号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("信用证号")
    private String lcNo;

    /**
     * 币种
     * 数据库字段:currency
     * 字符类型(20)
     */
    @NotNull(message = "币种不能为空！")
    @XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币种")
    private String currency;

    /**
     * 合计金额
     * 数据库字段:total_amount
     * 数值类型(19,4)
     */
    @NotNull(message = "合计金额不能为空！")
    @Digits(integer = 19, fraction = 4, message = "合计金额必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;

    /**
     * 唛头
     * 数据库字段:mark
     * 字符类型(600)
     */
    @XdoSize(max = 300, message = "唛头长度不能超过300位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("唛头")
    private String mark;

    /**
     * 代理费率%
     * 数据库字段:agent_rate
     * 数值类型(19,6)
     */
    @NotNull(message = "代理费率%不能为空！")
    @Digits(integer = 19, fraction = 6, message = "代理费率%必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("代理费率%")
    private BigDecimal agentRate;

    /**
     * 代理费（外币）
     * 数据库字段:agent_fee_foreign
     * 数值类型(19,6)
     */
    @NotNull(message = "代理费（外币）不能为空！")
    @Digits(integer = 19, fraction = 6, message = "代理费（外币）必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("代理费（外币）")
    private BigDecimal agentFeeForeign;

    /**
     * 预收款日期
     * 数据库字段:prepay_date
     * date
     */
    @ApiModelProperty("预收款日期")
    private Date prepayDate;

    /**
     * 作销日期
     * 数据库字段:cancel_date
     * date
     */
    @ApiModelProperty("作销日期")
    private Date cancelDate;

    /**
     * 发票日期
     * 数据库字段:invoice_date
     * date
     */
    @ApiModelProperty("发票日期")
    private Date invoiceDate;

    /**
     * 汇率
     * 数据库字段:exchange_rate
     * 数值类型(19,6)
     */
    @NotNull(message = "汇率不能为空！")
    @Digits(integer = 19, fraction = 6, message = "汇率必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    /**
     * 代理费（不含税金额）
     * 数据库字段:agent_fee_ex_tax
     * 数值类型(19,2)
     */
    @NotNull(message = "代理费（不含税金额）不能为空！")
    @Digits(integer = 19, fraction = 2, message = "代理费（不含税金额）必须为数字,整数位最大19位,小数最大2位!")
    @ApiModelProperty("代理费（不含税金额）")
    private BigDecimal agentFeeExTax;

    /**
     * 代理费税额
     * 数据库字段:agent_tax
     * 数值类型(19,2)
     */
    @NotNull(message = "代理费税额不能为空！")
    @Digits(integer = 19, fraction = 2, message = "代理费税额必须为数字,整数位最大19位,小数最大2位!")
    @ApiModelProperty("代理费税额")
    private BigDecimal agentTax;

    /**
     * 代理费（价税合计）
     * 数据库字段:agent_fee_total
     * 数值类型(19,6)
     */
    @NotNull(message = "代理费（价税合计）不能为空！")
    @Digits(integer = 19, fraction = 6, message = "代理费（价税合计）必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("代理费（价税合计）")
    private BigDecimal agentFeeTotal;

    /**
     * 发送财务系统
     * 数据库字段:send_finance
     * 字符类型(20)
     */
    @NotNull(message = "发送财务系统不能为空！")
    @XdoSize(max = 10, message = "发送财务系统长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发送财务系统")
    private String sendFinance;

    /**
     * 备注
     * 数据库字段:remark
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 是否红冲
     * 数据库字段:is_red_flush
     * 字符类型(20)
     */
    @NotNull(message = "是否红冲不能为空！")
    @XdoSize(max = 10, message = "是否红冲长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("是否红冲")
    private String isRedFlush;

    /**
     * 单据状态
     * 数据库字段:bill_status
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "单据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单据状态")
    private String billStatus;


    @ApiModelProperty("确认时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;


}