package com.dcjet.cs.dto.auxiliaryMaterials;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@ApiModel("订货通知选择购销合同号传输模型")
public class OrderNoticeSelectBuyContractDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 购销合同号
     */
    @ApiModelProperty("购销合同号")
    private String psContractNo;

    /**
     * 购销年份
     */
    @ApiModelProperty("购销年份")
    private String psYear;
}
