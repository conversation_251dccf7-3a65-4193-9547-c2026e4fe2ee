package com.dcjet.cs.dto.customerAccount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-28
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizCustomerAccountSliceParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 业务类型
     */
	@XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 结算单号
     */
	@XdoSize(max = 120, message = "结算单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("结算单号")
	private  String accountNo;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 出口币种
     */
	@XdoSize(max = 20, message = "出口币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("出口币种")
	private  String currE;
	/**
     * 进口币种
     */
	@XdoSize(max = 20, message = "进口币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("进口币种")
	private  String currI;
	/**
     * 出口汇率
     */
	@Digits(integer = 13, fraction = 6, message = "出口汇率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("出口汇率")
	private  BigDecimal exchangeRateE;
	/**
     * 进口汇率
     */
	@Digits(integer = 13, fraction = 6, message = "进口汇率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("进口汇率")
	private  BigDecimal exchangeRateI;

	/**
     * 出口货款
     */
	@Digits(integer = 17, fraction = 2, message = "出口货款必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("出口货款")
	private  BigDecimal goodsPriceE;
	/**
     * 进口货款
     */
	@Digits(integer = 17, fraction = 2, message = "进口货款必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("进口货款")
	private  BigDecimal goodsPriceI;
	/**
     * 结算日期
     */
	@ApiModelProperty("结算日期")
	private  Date businessDate;

	/**
     * 发送财务系统
     */
	@XdoSize(max = 20, message = "发送财务系统长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
     * 单据状态
     */
	@XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 出口货款(人民币)
     */
	@Digits(integer = 17, fraction = 2, message = "出口货款(人民币)必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("出口货款(人民币)")
	private  BigDecimal goodsPriceERmb;
	/**
     * 进口货款(人民币)
     */
	@Digits(integer = 17, fraction = 2, message = "进口货款(人民币)必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("进口货款(人民币)")
	private  BigDecimal goodsPriceIRmb;
	/**
     * 共计金额(人民币)
     */
	@Digits(integer = 17, fraction = 2, message = "共计金额(人民币)必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("共计金额(人民币)")
	private  BigDecimal totalAmount;
	/**
     * 客户
     */
	@XdoSize(max = 400, message = "客户长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String customer;
	private  BigDecimal agentFeeRate;
	private  String purchaseOrderNo;
	private String fileType;
	private String insertTimeFrom;
	private String insertTimeTo;
}
