package com.dcjet.cs.dto.auxiliaryMaterials;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-6-18
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizINonStateAuxmatAggrContractListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String id;
	/**
	 * 表头ID
	 */
	@ApiModelProperty("表头ID")
	private  String headId;
	/**
     * 商品名称
     */
	@XdoSize(max = 160, message = "商品名称长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	private  String goodsName;
	/**
     * 商品描述
     */
	@XdoSize(max = 200, message = "商品描述长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品描述")
	private  String goodsDesc;
	/**
     * 数量
     */
	@Digits(integer = 13, fraction = 6, message = "数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
     * 单位
     */
	@XdoSize(max = 40, message = "单位长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单位")
	private  String unit;
	/**
     * 单价
     */
	@Digits(integer = 11, fraction = 8, message = "单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Digits(integer = 15, fraction = 4, message = "金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("金额")
	private  BigDecimal amount;
	/**
     * 交货日期
     */
	@ApiModelProperty("交货日期")
	private  Date deliveryDate;
	/**
     * 备注
     */
	@XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String remark;
	/**
     * 商品类别
     */
	@XdoSize(max = 160, message = "商品类别长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品类别")
	private  String goodsCategory;

	private String contractNo;
}
