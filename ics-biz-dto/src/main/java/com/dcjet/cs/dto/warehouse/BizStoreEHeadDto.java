package com.dcjet.cs.dto.warehouse;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-5-22
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizStoreEHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 最后修改人名称
      */
    @ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 拓展字段1
      */
    @ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
      * 拓展字段2
      */
    @ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
      * 拓展字段3
      */
    @ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
      * 拓展字段4
      */
    @ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
      * 拓展字段5
      */
    @ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
      * 拓展字段6
      */
    @ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
      * 拓展字段7
      */
    @ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
      * 拓展字段8
      */
    @ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
      * 拓展字段9
      */
    @ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
      * 拓展字段10
      */
    @ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
      * 出库回单编号
      */
    @ApiModelProperty("出库回单编号")
	private  String storeENo;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 进货单号
      */
    @ApiModelProperty("进货单号")
	private  String purchaseOrderNo;
	/**
      * 购销合同号
      */
    @ApiModelProperty("购销合同号")
	private  String purSaleContractNo;
	/**
      * 提货人
      */
    @ApiModelProperty("提货人")
	private  String consignee;
	/**
      * 出库日期
      */
    @ApiModelProperty("出库日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date deliveryDate;
	/**
      * 金额
      */
    @ApiModelProperty("金额")
	private  BigDecimal productAmountTotal;
	/**
      * 关税
      */
    @ApiModelProperty("关税")
	private  BigDecimal tariffPrice;
	/**
      * 保险费用
      */
    @ApiModelProperty("保险费用")
	private  BigDecimal insuranceFee;
	/**
      * 代理费用
      */
    @ApiModelProperty("代理费用")
	private  BigDecimal agentFee;
	/**
      * 业务日期
      */
    @ApiModelProperty("业务日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date businessDate;
	/**
      * 发送财务系统
      */
    @ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 出库数据状态
      */
    @ApiModelProperty("出库数据状态")
	private  String status;
	private  String headId;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	private  String redFlush;

	private String createrBy;
	private String createrUserName;
	private Date createrTime;
}
