package com.dcjet.cs.dto.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2025-3-7
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class RegistrationHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;

	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;

	/**
	 * 单据号
	 */
	@ApiModelProperty("单据号")
	private String documentNo;

	/**
	 * 业务类型
	 */
	@ApiModelProperty("业务类型")
	private String businessType;

	/**
	 * 预付标志
	 */
	@ApiModelProperty("预付标志")
	private String advanceFlag;

	/**
	 * 部门
	 */
	@ApiModelProperty("部门")
	private String department;

	/**
	 * 币种
	 */
	@ApiModelProperty("币种")
	private String currency;

	/**
	 * 付款客户
	 */
	@ApiModelProperty("付款客户")
	private String payerName;

	/**
	 * 委托单位
	 */
	@ApiModelProperty("委托单位")
	private String entrustCompany;

	/**
	 * 银行手续费
	 */
	@ApiModelProperty("银行手续费")
	private BigDecimal bankFee;

	/**
	 * 收款金额
	 */
	@ApiModelProperty("收款金额")
	private BigDecimal paymentAmount;

	/**
	 * 发送财务系统
	 */
	@ApiModelProperty("发送财务系统")
	private String financeFlag;

	/**
	 * 业务日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("业务日期")
	private Date businessDate;

	private Date confirmTime;

	/**
	 * 是否红冲
	 */
	@ApiModelProperty("是否红冲")
	private String reversalFlag;

	/**
	 * 单据状态
	 */
	@ApiModelProperty("单据状态")
	private String documentStatus;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;


	private String contractNo;
	private String curr;
	private BigDecimal decTotal;
	private BigDecimal qty;
	private String unit;


	private String orderNumber;

	/**
	 * 修改时间-开始
	 */
	@ApiModelProperty("修改时间-开始")
	private String updateTimeFrom;

	/**
	 * 修改时间-结束
	 */
	@ApiModelProperty("修改时间-结束")
	private String updateTimeTo;

}
