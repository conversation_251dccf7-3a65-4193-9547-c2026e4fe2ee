package com.dcjet.cs.dto.auxiliaryMaterials;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-8-6
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizENonStateAuxmatAggrContractHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 主键
     */
	@NotEmpty(message="主键不能为空！")
	@XdoSize(max = 80, message = "主键长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("主键")
	private  String id;
	/**
     * 企业编码
     */
	@NotEmpty(message="企业编码不能为空！")
	@XdoSize(max = 60, message = "企业编码长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 业务类型
     */
	@NotEmpty(message="业务类型不能为空！")
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 合同号
     */
	@NotEmpty(message="合同号不能为空！")
	@XdoSize(max = 60, message = "合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 客户
     */
	@NotEmpty(message="客户不能为空！")
	@XdoSize(max = 200, message = "客户长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String buyer;
	/**
     * 供应商
     */
	@NotEmpty(message="供应商不能为空！")
	@XdoSize(max = 200, message = "供应商长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String supplier;
	/**
     * 签约日期
     */
	@NotNull(message="签约日期不能为空！")
	@ApiModelProperty("签约日期")
	private  Date signingDate;
	/**
     * 装运期限
     */
	@XdoSize(max = 100, message = "装运期限长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装运期限")
	private  String shipmentDeadline;
	/**
     * 合同生效期
     */
	@ApiModelProperty("合同生效期")
	private  Date effectiveDate;
	/**
     * 合同有效期
     */
	@ApiModelProperty("合同有效期")
	private  Date expiryDate;
	/**
     * 签约地点(中文)
     */
	@XdoSize(max = 50, message = "签约地点(中文)长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点(中文)")
	private  String signingLocationCn;
	/**
     * 签约地点(英文)
     */
	@XdoSize(max = 50, message = "签约地点(英文)长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点(英文)")
	private  String signingLocationEn;
	/**
     * 装运港
     */
	@XdoSize(max = 50, message = "装运港长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装运港")
	private  String portOfLoading;
	/**
     * 目的港
     */
	@XdoSize(max = 50, message = "目的港长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("目的港")
	private  String portOfDestination;
	/**
     * 收汇方式
     */
	@XdoSize(max = 50, message = "收汇方式长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("收汇方式")
	private  String paymentMethod;
	/**
     * 币种
     */
	@NotEmpty(message="币种不能为空！")
	@XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 价格条款
     */
	@XdoSize(max = 20, message = "价格条款长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@XdoSize(max = 50, message = "价格条款对应港口长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款对应港口")
	private  String priceTermPort;
	/**
     * 建议授权签约人
     */
	@XdoSize(max = 30, message = "建议授权签约人长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("建议授权签约人")
	private  String suggestedSigner;
	/**
     * 短溢数%
     */
	@Digits(integer = 13, fraction = 6, message = "短溢数%必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("短溢数%")
	private  BigDecimal shortageOverflowPercent;
	/**
     * INCOTERMS 国际贸易术语
     */
	@XdoSize(max = 20, message = "INCOTERMS 国际贸易术语长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("INCOTERMS 国际贸易术语")
	private  String incoterms;
	/**
     * 唛头
     */
	@XdoSize(max = 250, message = "唛头长度不能超过250位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("唛头")
	private  String mark;
	/**
     * 合同条款
     */
	@XdoSize(max = 500, message = "合同条款长度不能超过500位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同条款")
	private  String contractTerms;
	/**
     * 备注
     */
	@XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String remarks;
	/**
     * 协议编号
     */
	@XdoSize(max = 60, message = "协议编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
     * 协议签约日期
     */
	@ApiModelProperty("协议签约日期")
	private  Date agreementSigningDate;
	/**
     * 总金额
     */
	@NotNull(message="总金额不能为空！")
	@Digits(integer = 17, fraction = 2, message = "总金额必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("总金额")
	private  BigDecimal agreementTotalAmount;
	/**
     * 代理费率%
     */
	@Digits(integer = 13, fraction = 6, message = "代理费率%必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("代理费率%")
	private  BigDecimal agreementAgentRate;
	/**
     * 代理费
     */
	@Digits(integer = 17, fraction = 2, message = "代理费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费")
	private  BigDecimal agreementAgentFee;
	/**
     * 建议授权签约人（协议相关）
     */
	@XdoSize(max = 30, message = "建议授权签约人（协议相关）长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("建议授权签约人（协议相关）")
	private  String agreementSuggestedSigner;
	/**
     * 协议条款
     */
	@XdoSize(max = 300, message = "协议条款长度不能超过300位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
     * 备注（协议相关）
     */
	@XdoSize(max = 200, message = "备注（协议相关）长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注（协议相关）")
	private  String agreementRemarks;
	/**
     * 版本号
     */
	@NotEmpty(message="版本号不能为空！")
	@XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
     * 单据状态
     */
	@NotEmpty(message="单据状态不能为空！")
	@XdoSize(max = 10, message = "单据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@XdoSize(max = 10, message = "审批状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
     * 创建人部门编码
     */
	@NotEmpty(message="创建人部门编码不能为空！")
	@XdoSize(max = 20, message = "创建人部门编码长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
     * 制单人
     */
	@NotEmpty(message="制单人不能为空！")
	@XdoSize(max = 100, message = "制单人长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("制单人")
	private  String createBy;
	/**
     * 最后修改人姓名
     */
	@NotEmpty(message="最后修改人姓名不能为空！")
	@XdoSize(max = 100, message = "最后修改人姓名长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人姓名")
	private  String createByUserName;
	/**
     * 制单日期
     */
	@NotNull(message="制单日期不能为空！")
	@ApiModelProperty("制单日期")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@NotEmpty(message="最后修改人不能为空！")
	@XdoSize(max = 100, message = "最后修改人长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
     * 最后修改人姓名
     */
	@NotEmpty(message="最后修改人姓名不能为空！")
	@XdoSize(max = 100, message = "最后修改人姓名长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人姓名")
	private  String updateByUserName;
	/**
     * 扩展字段1
     */
	@XdoSize(max = 200, message = "扩展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@XdoSize(max = 200, message = "扩展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@XdoSize(max = 200, message = "扩展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@XdoSize(max = 200, message = "扩展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@XdoSize(max = 200, message = "扩展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@XdoSize(max = 200, message = "扩展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@XdoSize(max = 200, message = "扩展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@XdoSize(max = 200, message = "扩展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@XdoSize(max = 200, message = "扩展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@XdoSize(max = 200, message = "扩展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段10")
	private  String extend10;
}
