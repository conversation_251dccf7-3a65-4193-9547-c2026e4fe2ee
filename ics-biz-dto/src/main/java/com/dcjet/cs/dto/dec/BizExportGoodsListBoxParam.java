package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import com.xdo.validation.annotation.XdoSize;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 第9条线-非国营贸易出口辅料-出货信息表体（商品信息）-装箱子表
 */
@Getter
@Setter
@ApiModel(value = "第9条线-非国营贸易出口辅料-出货信息表体（商品信息）-装箱子表-传入参数")
public class BizExportGoodsListBoxParam implements Serializable{

    @XdoSize(max = 40, message = "主键id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(240)
     */
    @XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "数据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "版本号长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "企业10位编码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "组织机构代码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * 字符类型(80)
     */
    @XdoSize(max = 40, message = "父级id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("父级id")
    private String parentId;


    @XdoSize(max = 100, message = "创建人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * 日期类型(6)
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private String createTimeFrom;

    /**
    * 创建时间-结束时间
    */
    @ApiModelProperty("创建时间-结束时间")
    private String createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
    * 更新时间-结束时间
    */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "插入用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段1长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段2长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段3长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段4长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段5长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段6长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段7长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段8长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段9长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段10长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 起始箱号
     * 数据库字段:start_box_no
     * 数值类型(10,0)
     */
    @NotNull(message = "起始箱号不能为空！")
    @Digits(integer = 10, fraction = 0, message = "起始箱号必须为数字,整数位最大10位,小数最大0位!")
    @ApiModelProperty("起始箱号")
    private BigDecimal startBoxNo;

    /**
     * 结束箱号
     * 数据库字段:end_box_no
     * 数值类型(10,0)
     */
    @NotNull(message = "结束箱号不能为空！")
    @Digits(integer = 10, fraction = 0, message = "结束箱号必须为数字,整数位最大10位,小数最大0位!")
    @ApiModelProperty("结束箱号")
    private BigDecimal endBoxNo;

    /**
     * 商品描述
     * 数据库字段:product_desc
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "商品描述长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品描述")
    private String productDesc;

    /**
     * 包装样式
     * 数据库字段:package_style
     * 字符类型(100)
     */
    @NotNull(message = "包装样式不能为空！")
    @XdoSize(max = 50, message = "包装样式长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("包装样式")
    private String packageStyle;

    /**
     * 毛重(kg)
     * 数据库字段:gross_weight_box
     * 数值类型(19,4)
     */
    @NotNull(message = "毛重(kg)不能为空！")
    @Digits(integer = 19, fraction = 4, message = "毛重(kg)必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("毛重(kg)")
    private BigDecimal grossWeightBox;

    /**
     * 净重(kg)
     * 数据库字段:net_weight_box
     * 数值类型(19,4)
     */
    @NotNull(message = "净重(kg)不能为空！")
    @Digits(integer = 19, fraction = 4, message = "净重(kg)必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("净重(kg)")
    private BigDecimal netWeightBox;

    /**
     * 皮重(kg)
     * 数据库字段:tare_weight_box
     * 数值类型(19,4)
     */
    @NotNull(message = "皮重(kg)不能为空！")
    @Digits(integer = 19, fraction = 4, message = "皮重(kg)必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("皮重(kg)")
    private BigDecimal tareWeightBox;

    /**
     * 长(m)
     * 数据库字段:length_m
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "长(m)必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("长(m)")
    private BigDecimal lengthM;

    /**
     * 宽(m)
     * 数据库字段:width_m
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "宽(m)必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("宽(m)")
    private BigDecimal widthM;

    /**
     * 高(m)
     * 数据库字段:height_m
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "高(m)必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("高(m)")
    private BigDecimal heightM;

    /**
     * 进货单表体SID
     * 数据库字段:parent_list_id
     * 字符类型(80)
     */
    @XdoSize(max = 40, message = "进货单表体SID长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("进货单表体SID")
    private String parentListId;


}