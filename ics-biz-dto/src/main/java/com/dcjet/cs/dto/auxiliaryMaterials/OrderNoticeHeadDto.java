package com.dcjet.cs.dto.auxiliaryMaterials;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ApiModel("订货通知表头传输模型")
public class OrderNoticeHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 父ID
     */
    @ApiModelProperty("父ID")
    private String parentId;

    /**
     * 购销合同表头ids
     */
    @ApiModelProperty("购销合同表头ids")
    private String buyHeadIds;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 订货编号
     */
    @ApiModelProperty("订货编号")
    private String orderNo;

    /**
     * 订货日期
     */
    @ApiModelProperty("订货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    /**
     * 客户
     */
    @ApiModelProperty("客户")
    private String customer;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 单据状态 0编制 1确认 2作废
     */
    @ApiModelProperty("单据状态")
    private String dataStatus;

    /**
     * 购销合同号
     */
    @ApiModelProperty("购销合同号")
    private String purchaseSalesContractNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 确认时间
     */
    @ApiModelProperty("确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    /**
     * 审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回
     */
    @ApiModelProperty("审批状态")
    private String apprStatus;

    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 制单人
     */
    @ApiModelProperty("制单人")
    private String documentMaker;

    /**
     * 制单人名称
     */
    @ApiModelProperty("制单人名称")
    private String documentMakerName;

    /**
     * 制单时间
     */
    @ApiModelProperty("制单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date documentMakeTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String updateByName;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;
}