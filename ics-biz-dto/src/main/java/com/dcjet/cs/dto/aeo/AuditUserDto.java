package com.dcjet.cs.dto.aeo;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter @Getter
@ApiModel(value = "内审差错统计返回信息")
public class AuditUserDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
	 * 审批类型
	 */
	@ApiModelProperty("审批类型")
	private  String apprType;
	/**
	 * 总制单票数
	 */
	@ApiModelProperty("总制单票数")
	private  String allCount;
	/**
	 * 无差错票数
	 */
	@ApiModelProperty("无差错票数")
	private  String noErrorCount;

	/**
	 * 内审退回票数
	 */
	@ApiModelProperty("内审退回票数")
	private  String rollBackCount;
	/**
	 * 总内审通过票数
	 */
	@ApiModelProperty("总内审通过票数")
	private  String passCount;

	/**
	 * 制单准确率
	 */
	@ApiModelProperty("制单准确率")
	private String percent;

	public String getPercent() {
		return this.percent==null?"":this.percent+"%";
	}
}
