package com.dcjet.cs.dto.customerAccount;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2025-6-16
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizCustomerAccountCTobaccoParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 创建时间
     */
	@NotNull(message="创建时间不能为空！")
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
    * 创建时间-开始
    */
	@ApiModelProperty("创建时间-开始")
	private String createTimeFrom;
	/**
    * 创建时间-结束
    */
	@ApiModelProperty("创建时间-结束")
    private String createTimeTo;
	/**
     * 企业编码
     */
	@XdoSize(max = 50, message = "企业编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 结算单号
     */
	@XdoSize(max = 120, message = "结算单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("结算单号")
	private  String accountNo;
	/**
     * 进货单号
     */
	@XdoSize(max = 120, message = "进货单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("进货单号")
	private  String purchaseOrderNo;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 客户
     */
	@XdoSize(max = 400, message = "客户长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String customer;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 汇率
     */
	@Digits(integer = 13, fraction = 6, message = "汇率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
     * 货款
     */
	@Digits(integer = 14, fraction = 5, message = "货款必须为数字,整数位最大14位,小数最大5位!")
	@ApiModelProperty("货款")
	private  BigDecimal goodsPrice;
	/**
     * 代理费率%
     */
	@Digits(integer = 13, fraction = 6, message = "代理费率%必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("代理费率%")
	private  BigDecimal agentFeeRate;
	/**
     * 代理费（不含税金额）
     */
	@Digits(integer = 17, fraction = 2, message = "代理费（不含税金额）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费（不含税金额）")
	private  BigDecimal agentFee;
	/**
     * 代理费税额
     */
	@Digits(integer = 17, fraction = 2, message = "代理费税额必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费税额")
	private  BigDecimal agentTaxFee;
	/**
     * 代理费（价税合计）
     */
	@Digits(integer = 17, fraction = 2, message = "代理费（价税合计）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费（价税合计）")
	private  BigDecimal agentFeeTotal;
	/**
     * 业务日期
     */
	@ApiModelProperty("业务日期")
	private  Date businessDate;
	/**
     * 商品名称
     */
	@XdoSize(max = 160, message = "商品名称长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	private  String businessType;
	/**
     * 发送财务系统
     */
	@XdoSize(max = 20, message = "发送财务系统长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
     * 商品与数量
     */
	@XdoSize(max = 400, message = "商品与数量长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品与数量")
	private  String producrSome;
	/**
     * 备注
     */
	@XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 是否红冲
     */
	@XdoSize(max = 20, message = "是否红冲长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否红冲")
	private  String redFlush;
	/**
     * 单据状态
     */
	@XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 审核状态
     */
	@XdoSize(max = 20, message = "审核状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审核状态")
	private  String apprStatus;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 是否确认
     */
	@XdoSize(max = 10, message = "是否确认长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否确认")
	private  String isConfirm;
	/**
     * 外商合同、进货明细数据标记
     */
	@XdoSize(max = 20, message = "外商合同、进货明细数据标记长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseMark;
	/**
     * 外商合同、进货明细数据标记
     */
	@XdoSize(max = 600, message = "外商合同、进货明细数据标记长度不能超过600位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseNoMark;
	private  String versionNo;
	private String fileType;
	private String insertTimeFrom;
	private String insertTimeTo;
}
