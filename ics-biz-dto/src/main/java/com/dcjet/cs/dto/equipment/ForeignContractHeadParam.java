package com.dcjet.cs.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@ApiModel("外商合同表头参数模型")
public class ForeignContractHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;
    private String sid;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
//    @NotNull(message = "业务类型不能为空")
    private String businessType;

    /**
     * 合同号
     */
    @ApiModelProperty("合同号")
//    @NotNull(message = "合同号不能为空")
    private String contractNo;

    /**
     * 业务地点
     */
    @ApiModelProperty("业务地点")
//    @NotNull(message = "业务地点不能为空")
    private String businessPlace;

    /**
     * 买方
     */
    @ApiModelProperty("BUYER")
//    @NotNull(message = "买家不能为空")
    private String buyer;

    /**
     * 卖方
     */
    @ApiModelProperty("SELLER")
//    @NotNull(message = "卖家不能为空")
    private String seller;

    /**
     * 使用厂家
     */
    @ApiModelProperty("使用厂家")
    private String usingManufacturer;

    /**
     * 国内委托方
     */
    @ApiModelProperty("国内委托方")
    private String domesticClient;

    /**
     * 签约日期
     */
    @ApiModelProperty("签约日期")
    private Date signDate;

    /**
     * 签约地点(中文)
     */
    @ApiModelProperty("签约地点(中文)")
    private String signPlaceCn;

    /**
     * 签约地点(英文)
     */
    @ApiModelProperty("签约地点(英文)")
    private String signPlaceEn;

    /**
     * 合同生效期
     */
    @ApiModelProperty("合同生效期")
    private Date contractEffectiveDate;

    /**
     * 合同有效期
     */
    @ApiModelProperty("合同有效期")
    private Date contractValidityDate;

    /**
     * 运输方式 0海运 1空运 2陆运
     */
    @ApiModelProperty("运输方式")
    private String transportMode;

    /**
     * 装运港
     */
    @ApiModelProperty("装运港")
    private String shippingPort;

    /**
     * 目的港
     */
    @ApiModelProperty("目的港")
    private String destPort;

    /**
     * 报关口岸
     */
    @ApiModelProperty("报关口岸")
    private String customsDeclarationPort;

    /**
     * 付款方式 0付款交单 1即期信用证 2电汇 3预付款
     */
    @ApiModelProperty("付款方式")
    private String paymentMethod;

    /**
     * 币种
     */
    @ApiModelProperty("币种")
    private String curr;

    /**
     * 价格条款
     */
    @ApiModelProperty("价格条款")
    private String priceTerm;

    /**
     * 价格条款对应港口 0起运港 1目的港
     */
    @ApiModelProperty("价格条款对应港口")
    private String priceTermPort;

    /**
     * 建议授权签约人
     */
    @ApiModelProperty("建议授权签约人")
    private String suggestAuthorSignatory;

    /**
     * 短溢数%
     */
    @ApiModelProperty("短溢数%")
    private BigDecimal shortOverflowNumber;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 制单人
     */
    @ApiModelProperty("制单人")
    private String documentMaker;

    /**
     * 制单人编号
     */
    @ApiModelProperty("制单人")
    private String documentMakerNo;

    /**
     * 制单日期
     */
    @ApiModelProperty("制单日期")
    private Date documentMakeDate;

    /**
     * 单据状态 0编制 1确认 2作废
     */
    @ApiModelProperty("单据状态")
    private String dataStatus;

    /**
     * 确认时间
     */
    @ApiModelProperty("确认时间")
    private Date confirmTime;

    /**
     * 审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回
     */
    @ApiModelProperty("审批状态")
    private String apprStatus;

    /**
     * 创建人部门编码
     */
    @ApiModelProperty("创建人部门编码")
    private String sysOrgCode;

    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 制单时间开始
     */
    @ApiModelProperty("制单时间开始")
    private String documentMakeDateFrom;

    /**
     * 制单时间结束
     */
    @ApiModelProperty("制单时间结束")
    private String documentMakeDateTo;




    private String partyB;
    private String partyA;
    private String headId;
    private BigDecimal decTotal;
    private BigDecimal decPrice;
    private BigDecimal qty;
    private String unit;
    private String merchandiseCategories;
    private String productName;
    private String purchaseOrderNo;
    private String productGrade;
    private List<String> sids;
    private String expenseType;
    //税额金额
    private BigDecimal taxAmount;
    //无税金额
    private BigDecimal noTaxAmount;
    //费用金额
    private String amount;
    //是否分摊
    private String apportionment;
    //分摊方式
    private String methodAllocation;

    private String type;

    /**
     * 主键列表
     */
    @ApiModelProperty("主键列表")
    private List<String> ids;
}