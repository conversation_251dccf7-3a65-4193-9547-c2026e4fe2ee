package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-8-6
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizENonStateAuxmatAggrContractHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String id;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 客户
      */
    @ApiModelProperty("客户")
	private  String buyer;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String supplier;
	/**
      * 签约日期
      */
    @ApiModelProperty("签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date signingDate;
	/**
      * 装运期限
      */
    @ApiModelProperty("装运期限")
	private  String shipmentDeadline;
	/**
      * 合同生效期
      */
    @ApiModelProperty("合同生效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date effectiveDate;
	/**
      * 合同有效期
      */
    @ApiModelProperty("合同有效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date expiryDate;
	/**
      * 签约地点(中文)
      */
    @ApiModelProperty("签约地点(中文)")
	private  String signingLocationCn;
	/**
      * 签约地点(英文)
      */
    @ApiModelProperty("签约地点(英文)")
	private  String signingLocationEn;
	/**
      * 装运港
      */
    @ApiModelProperty("装运港")
	private  String portOfLoading;
	/**
      * 目的港
      */
    @ApiModelProperty("目的港")
	private  String portOfDestination;
	/**
      * 收汇方式
      */
    @ApiModelProperty("收汇方式")
	private  String paymentMethod;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;
	/**
      * 价格条款
      */
    @ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
      * 价格条款对应港口
      */
    @ApiModelProperty("价格条款对应港口")
	private  String priceTermPort;
	/**
      * 建议授权签约人
      */
    @ApiModelProperty("建议授权签约人")
	private  String suggestedSigner;
	/**
      * 短溢数%
      */
    @ApiModelProperty("短溢数%")
	private  BigDecimal shortageOverflowPercent;
	/**
      * INCOTERMS 国际贸易术语
      */
    @ApiModelProperty("INCOTERMS 国际贸易术语")
	private  String incoterms;
	/**
      * 唛头
      */
    @ApiModelProperty("唛头")
	private  String mark;
	/**
      * 合同条款
      */
    @ApiModelProperty("合同条款")
	private  String contractTerms;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String remarks;
	/**
      * 协议编号
      */
    @ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
      * 协议签约日期
      */
    @ApiModelProperty("协议签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date agreementSigningDate;
	/**
      * 总金额
      */
    @ApiModelProperty("总金额")
	private  BigDecimal agreementTotalAmount;
	/**
      * 代理费率%
      */
    @ApiModelProperty("代理费率%")
	private  BigDecimal agreementAgentRate;
	/**
      * 代理费
      */
    @ApiModelProperty("代理费")
	private  BigDecimal agreementAgentFee;
	/**
      * 建议授权签约人（协议相关）
      */
    @ApiModelProperty("建议授权签约人（协议相关）")
	private  String agreementSuggestedSigner;
	/**
      * 协议条款
      */
    @ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
      * 备注（协议相关）
      */
    @ApiModelProperty("备注（协议相关）")
	private  String agreementRemarks;
	/**
      * 版本号
      */
    @ApiModelProperty("版本号")
	private  String versionNo;
	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String status;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 审批状态
      */
    @ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 制单人
      */
    @ApiModelProperty("制单人")
	private  String createBy;
	/**
      * 最后修改人姓名
      */
    @ApiModelProperty("最后修改人姓名")
	private  String createByUserName;
	/**
      * 制单日期
      */
    @ApiModelProperty("制单日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 最后修改人
      */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
      * 最后修改人姓名
      */
    @ApiModelProperty("最后修改人姓名")
	private  String updateByUserName;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 扩展字段1
      */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
      * 扩展字段2
      */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
      * 扩展字段3
      */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
      * 扩展字段4
      */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
      * 扩展字段5
      */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
      * 扩展字段6
      */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
      * 扩展字段7
      */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
      * 扩展字段8
      */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
      * 扩展字段9
      */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
      * 扩展字段10
      */
    @ApiModelProperty("扩展字段10")
	private  String extend10;
	private  String domesticPrincipal;


	/**
	 * 表体总数量
	 */
	@ApiModelProperty("表体总数量")
	private BigDecimal totalQty;
}
