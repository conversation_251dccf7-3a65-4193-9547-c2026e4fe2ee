package com.dcjet.cs.dto.auxiliaryMaterials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@ApiModel(value = "订货通知选项数据传输模型")
public class OrderNoticeOptionsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户选项
     */
    @ApiModelProperty("客户选项")
    private List<Map<String, String>> customerOptions = Collections.emptyList();

    /**
     * 供应商选项
     */
    @ApiModelProperty("供应商选项")
    private List<Map<String, String>> supplierOptions = Collections.emptyList();

    /**
     * 港口选项
     */
    private List<Map<String, String>> portOptions = Collections.emptyList();
}
