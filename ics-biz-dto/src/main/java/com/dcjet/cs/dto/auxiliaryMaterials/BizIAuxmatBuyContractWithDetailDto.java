package com.dcjet.cs.dto.auxiliaryMaterials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 包含表头和表体数据的DTO
 * <AUTHOR>
 * @date: 2025-01-15
 */
@ApiModel(value = "辅料采购合同表头和表体返回信息")
@Setter @Getter
public class BizIAuxmatBuyContractWithDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 表头数据
     */
    @ApiModelProperty("表头数据")
    private BizIAuxmatBuyContractDto head;
    
    /**
     * 表体数据列表
     */
    @ApiModelProperty("表体数据列表")
    private List<BizIAuxmatBuyContractListDto> details;
}
