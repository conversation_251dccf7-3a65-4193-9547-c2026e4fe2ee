package com.dcjet.cs.dto.dec;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class GenerateYJTBParams implements Serializable {

    /**
     * 类型
     */
    @NotNull(message = "类型不能为空")
    private String type;


    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private String id;


    /**
     * 表头ID
     */
    @NotNull(message = "表头ID不能为空")
    private String headId;


    /**
     * 文件名称
     */
    private String name;
}
