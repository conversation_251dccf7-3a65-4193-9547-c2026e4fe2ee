package com.dcjet.cs.dto.equipment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIEquipmentContainerInfoParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String id;
	/**
	 * 关联划款参数id
	 */
	@ApiModelProperty("关联划款参数id")
	private String headId;
	/**
     * 箱型（如20GP,40HQ等）
     */
	@NotEmpty(message="箱型不能为空！")
	@XdoSize(max = 200, message = "箱型长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("箱型")
	private  String containerType;
	/**
     * 单价合计
     */
	@NotNull(message="单价合计不能为空！")
	@Digits(integer = 13, fraction = 6, message = "单价合计必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("单价合计")
	private  BigDecimal unitPriceTotal;
	/**
     * 箱数
     */
	@NotNull(message="箱数不能为空！")
	@Digits(integer = 13, fraction = 6, message = "箱数必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("箱数")
	private  BigDecimal containerCount;
	/**
     * 金额
     */
	@Digits(integer = 13, fraction = 6, message = "金额必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("金额")
	private  BigDecimal amount;
}
