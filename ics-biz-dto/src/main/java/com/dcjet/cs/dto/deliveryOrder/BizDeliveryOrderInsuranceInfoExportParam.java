package com.dcjet.cs.dto.deliveryOrder;
import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
/**
  * <AUTHOR>
  * @date: 2025-7-7
 */
@Getter
@Setter
@ApiModel(description = "进出口表头查询传入参数")
public class BizDeliveryOrderInsuranceInfoExportParam extends ExcelExportParam {
    /**
     * 导出传入参数
     */
    @ApiModelProperty("查询参数")
    private BizDeliveryOrderInsuranceInfoParam exportColumns;
}
