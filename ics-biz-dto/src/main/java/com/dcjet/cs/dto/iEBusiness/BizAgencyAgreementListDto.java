package com.dcjet.cs.dto.iEBusiness;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@ApiModel(value = " DTO")
@Setter
@Getter
public class BizAgencyAgreementListDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    @ApiModelProperty("主键")
	private  String id;
    /**
    * 商品名称
    */
    @ApiModelProperty("商品名称")
	private  String goodsName;
    /**
    * 产品型号
    */
    @ApiModelProperty("产品型号")
	private  String productModel;
    /**
    * 数量
    */
    @ApiModelProperty("数量")
	private  BigDecimal quantity;
    /**
    * 单位
    */
    @ApiModelProperty("单位")
	private  String unit;
    /**
    * 单价
    */
    @ApiModelProperty("单价")
	private  BigDecimal unitPrice;
    /**
    * 金额
    */
    @ApiModelProperty("金额")
	private  BigDecimal amount;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
	private  String remark;
    /**
    * 扩展字段1
    */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
    /**
    * 扩展字段2
    */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
    /**
    * 扩展字段3
    */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
    /**
    * 扩展字段4
    */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
    /**
    * 扩展字段5
    */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
    /**
    * 扩展字段6
    */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
    /**
    * 扩展字段7
    */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
    /**
    * 扩展字段8
    */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
    /**
    * 扩展字段9
    */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
    /**
    * 扩展字段10
    */
    @ApiModelProperty("扩展字段10")
	private  String extend10;
    /**
    * 表头id
    */
    @ApiModelProperty("表头id")
	private  String headId;
    /**
    * 上游id
    */
    @ApiModelProperty("上游id")
	private  String parentId;
    /**
    * 进出口标识
    */
    @ApiModelProperty("进出口标识")
	@JsonProperty("iEMark")
	private  String iEMark;
    /**
    * 企业编码
    */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
    /**
    * 创建人部门编码
    */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
	private  String createBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
	private  Date createTime;
    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
    /**
    * 最后修改时间
    */
    @ApiModelProperty("最后修改时间")
	private  Date updateTime;
}
