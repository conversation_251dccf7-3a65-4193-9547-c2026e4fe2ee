package com.dcjet.cs.dto.seven;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ApiModel("外商合同打印会签单参数")
public class SevenForeignContractPrintCountersignSheetParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键列表
     */
    @ApiModelProperty("主键列表")
    private List<String> ids;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private String type;
}
