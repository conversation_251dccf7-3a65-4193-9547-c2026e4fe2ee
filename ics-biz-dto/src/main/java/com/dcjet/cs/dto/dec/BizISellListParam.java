package com.dcjet.cs.dto.dec;


import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "销售表头-传入参数")
public class BizISellListParam implements Serializable{


    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    private String sid;

    private String headId;

    private List<String> sids;

    @XdoSize(max = 60, message = "销售合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    private String salesContractNumber;

    @XdoSize(max = 60, message = "销售发票号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    private String salesInvoiceNumber;

    private String tradeName;

    private String unit;

    private BigDecimal quantity;

    private BigDecimal unitPriceExcludingTax;

    @NotNull(message = "税额不能为空!")
    @Digits(integer = 17, fraction = 2, message = "税额必须为数字,整数位最大17位,小数最大2位!")
    private BigDecimal amountOfTax;
    @NotNull(message = "不含税金额不能为空!")
    @Digits(integer = 17, fraction = 2, message = "不含税金额必须为数字,整数位最大17位,小数最大2位!")
    private BigDecimal taxNotIncluded;

    private BigDecimal totalValueTax;


    private String insertUser;

    private Date insertTime;

    private String insertUserName;

    private String updateUser;

    private Date updateTime;

    private String updateUserName;

    private String tradeCode;

    //进口数量
    private BigDecimal iCount;
    //进口单位
    private String iUnit;

}