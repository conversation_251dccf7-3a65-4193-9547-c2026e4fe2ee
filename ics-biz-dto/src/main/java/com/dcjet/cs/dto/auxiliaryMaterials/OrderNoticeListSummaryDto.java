package com.dcjet.cs.dto.auxiliaryMaterials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("订货通知表体汇总信息传输模型")
@Accessors(chain = true)
public class OrderNoticeListSummaryDto {
    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private BigDecimal totalQty;
}
