package com.dcjet.cs.dto.seven;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@ApiModel("外商合同新增参数模型")
public class SevenForeignContractAddParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表头参数
     */
    @ApiModelProperty("表头参数")
    @Valid
    private SevenForeignContractHeadParam head;

    /**
     * 出料加工表体列表参数
     */
    @ApiModelProperty("出料加工表体列表参数")
    private List<SevenForeignContractListParam> processBodyList;

    /**
     * 进口薄片表体列表参数
     */
    @ApiModelProperty("进口薄片表体列表参数")
    private List<SevenForeignContractListParam> sliceBodyList;
}