package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-6-18
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizINonStateAuxmatAggrContractListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String id;
	/**
      * 表头ID
      */
    @ApiModelProperty("表头ID")
	private  String headId;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      *  修改人
      */
    @ApiModelProperty(" 修改人")
	private  String updateBy;
	/**
      * 修改时间
      */
    @ApiModelProperty("修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	private  String goodsName;
	/**
      * 商品描述
      */
    @ApiModelProperty("商品描述")
	private  String goodsDesc;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private  String unit;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
      * 金额
      */
    @ApiModelProperty("金额")
	private  BigDecimal amount;
	/**
      * 交货日期
      */
    @ApiModelProperty("交货日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date deliveryDate;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String remark;
	/**
      * 商品类别
      */
    @ApiModelProperty("商品类别")
	private  String goodsCategory;
	/**
      * 插入用户名
      */
    @ApiModelProperty("插入用户名")
	private  String insertUserName;
	/**
      * 更新用户名
      */
    @ApiModelProperty("更新用户名")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
}
