package com.dcjet.cs.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@ApiModel("外商合同新增参数模型")
public class ForeignContractAddParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表头参数
     */
    @ApiModelProperty("表头参数")
    @Valid
    private ForeignContractHeadParam head;

    /**
     * 表体列表参数
     */
    @ApiModelProperty("表体列表参数")
    private List<ForeignContractListParam> bodyList;
}