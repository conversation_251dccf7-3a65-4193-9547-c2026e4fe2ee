package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * （3）烟机设备-进货单-表头数据
 */
@Getter
@Setter
@ApiModel(value = "（3）烟机设备-进货单-表头数据-汇总返回信息")
public class BizSmokeHeadTotalDto implements Serializable{

    /**
     * 总金额
     */
    private BigDecimal amount;

}