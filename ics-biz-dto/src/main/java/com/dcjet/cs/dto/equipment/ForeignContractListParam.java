package com.dcjet.cs.dto.equipment;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel("外商合同表体参数模型")
public class ForeignContractListParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 表头id
     */
    @ApiModelProperty("表头id")
    private String headId;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @JsonProperty("gName")
    private String gName;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @JsonProperty("gModel")
    private String gModel;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal moneyAmount;

    /**
     * 交货日期
     */
    @ApiModelProperty("交货日期")
    private Date deliveryDate;

    /**
     * 总价折美元
     */
    @ApiModelProperty("总价折美元")
    private BigDecimal convertedTotalDollars;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 单据状态 0编制 1确认 2作废
     */
    @ApiModelProperty("单据状态")
    private String dataStatus;

    /**
     * 确认时间
     */
    @ApiModelProperty("确认时间")
    private Date confirmTime;

    /**
     * 创建人部门编码
     */
    @ApiModelProperty("创建人部门编码")
    private String sysOrgCode;

    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;

    /**
     * 制单用户
     */
    @ApiModelProperty("制单用户")
    private String createBy;

    /**
     * 制单时间
     */
    @ApiModelProperty("制单时间")
    private Date createTime;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;
}