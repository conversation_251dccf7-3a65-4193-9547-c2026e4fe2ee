package com.dcjet.cs.dto.bi;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizMerchantParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 
     */
	@XdoSize(max = 60, message = "长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String businessType;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String dataStatus;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String versionNo;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 40, message = "长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String parentId;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;
	/**
     * 客商编码
     */
	@XdoSize(max = 60, message = "客商编码长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客商编码")
	private  String merchantCode;
	/**
     * 客商中文名称
     */
	@XdoSize(max = 200, message = "客商中文名称长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客商中文名称")
	private  String merchantNameCn;
	/**
     * 客商英文名称
     */
	@XdoSize(max = 400, message = "客商英文名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客商英文名称")
	private  String merchantNameEn;
	/**
     * 客商简称
     */
	@XdoSize(max = 60, message = "客商简称长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客商简称")
	private  String merchantShort;
	/**
     * 客商地址
     */
	@XdoSize(max = 400, message = "客商地址长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客商地址")
	private  String merchantAddress;
	/**
     * 收款银行
     */
	@XdoSize(max = 160, message = "收款银行长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("收款银行")
	private  String receivingBank;
	/**
     * 收款方帐号
     */
	@XdoSize(max = 160, message = "收款方帐号长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("收款方帐号")
	private  String receiverAccountNum;
	/**
     * 备注
     */
	@XdoSize(max = 400, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;


	/**
	 * 创建人部门编码
	 */
	@ApiModelProperty("创建人部门编码")
	@XdoSize(max = 150, message = "创建人部门编码长度不能超过150位字节长度(一个汉字2位字节长度)!")
	private  String sysOrgCode;
	@ApiModelProperty("财务系统编码")
	@XdoSize(max = 60, message = "财务系统编码长度不能超过60位字节长度(一个汉字2位字节长度)!")
	private  String financeCode;


	/**
	 * 序号
	 */
	@ApiModelProperty("序号")
	@Digits(integer = 6, fraction = 0, message = "序号必须为数字,整数位最大6位,小数最大0位!")
	private Integer serialNo;

	/**
	 * 贸易国别
	 */
	@XdoSize(max = 60, message = "贸易国别长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("贸易国别")
	private String tradeCountry;

	/**
	 * 装运人
	 */
	@XdoSize(max = 300, message = "装运人长度不能超过300位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装运人")
	private String shipper;

	/**
	 * 收货人
	 */
	@XdoSize(max = 300, message = "收货人长度不能超过300位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("收货人")
	private String consignee;

	/**
	 * 通知人
	 */
	@XdoSize(max = 300, message = "通知人长度不能超过300位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("通知人")
	private String notifyParty;

	/**
	 * 仓储地址
	 */
	@XdoSize(max = 300, message = "仓储地址长度不能超过300位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("仓储地址")
	private String warehouseAddress;

	/**
	 * 联系人
	 */
	@XdoSize(max = 20, message = "联系人长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("联系人")
	private String contactPerson;

	/**
	 * 联系电话
	 */
	@XdoSize(max = 20, message = "联系电话长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("联系电话")
	private String contactPhone;

	/**
	 * 客商类别
	 */
	@XdoSize(max = 20, message = "客商类别长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客商类别")
	private String merchantType;

	/**
	 * 常用标识
	 */
	@XdoSize(max = 60, message = "常用标识长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("常用标识")
	private String commonFlag;

	private List<String> commonFlagList;

	/**
	 * 传真
	 */
	@XdoSize(max = 40, message = "传真长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("传真")
	private String fax;

	/**
	 * 邮件
	 */
	@XdoSize(max = 400, message = "邮件长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("邮件")
	private String email;

	/**
	 * 英文地址
	 */
	@XdoSize(max = 400, message = "英文地址长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("英文地址")
	private String enAddress;

	/**
	 * 贸易国别英文
	 */
	@XdoSize(max = 100, message = "贸易国别英文长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("贸易国别英文")
	private String tradeCountryEn;

	/**
	 * 邮编
	 */
	@XdoSize(max = 20, message = "邮编长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("邮编")
	private String postcode;

	/**
	 * 开户行地址
	 */
	@XdoSize(max = 400, message = "开户行地址长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("开户行地址")
	private String bankAddress;

	/**
	 * 支出账号
	 */
	@XdoSize(max = 100, message = "支出账号长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("支出账号")
	private String expendAccount;

	/**
	 * 税号
	 */
	@XdoSize(max = 100, message = "税号长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("税号")
	private String taxNo;

	/**
	 * 法人代表
	 */
	@XdoSize(max = 40, message = "法人代表长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("法人代表")
	private String legalPerson;
}
