package com.dcjet.cs.dto.quo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-5-20
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizQuotationDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
      * 最后修改人
      */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 最后修改人名称
      */
    @ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 拓展字段1
      */
    @ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
      * 拓展字段2
      */
    @ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
      * 拓展字段3
      */
    @ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
      * 拓展字段4
      */
    @ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
      * 拓展字段5
      */
    @ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
      * 拓展字段6
      */
    @ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
      * 拓展字段7
      */
    @ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
      * 拓展字段8
      */
    @ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
      * 拓展字段9
      */
    @ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
      * 拓展字段10
      */
    @ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
      * 商品类别
      */
    @ApiModelProperty("商品类别")
	private  String merchandiseCategories;
	/**
      * 产品型号
      */
    @ApiModelProperty("产品型号")
	private  String productModel;
	/**
      * 规格
      */
    @ApiModelProperty("规格")
	private  String specifications;
	/**
      * 克重
      */
    @ApiModelProperty("克重")
	private  BigDecimal grammage;
	/**
      * 材料编号
      */
    @ApiModelProperty("材料编号")
	private  String materialNo;
	/**
      * 计量单位
      */
    @ApiModelProperty("计量单位")
	private  String unit;
	/**
      * 进口计量单位
      */
    @ApiModelProperty("进口计量单位")
	private  String unitI;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String merchantCode;
	/**
      * 价格条款
      */
    @ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
      * 指运港
      */
    @ApiModelProperty("指运港")
	private  String destinationPort;
	/**
      * 进口单价
      */
    @ApiModelProperty("进口单价")
	private  BigDecimal importUnitPrice;
	/**
      * 单价/盘（USD）
      */
    @ApiModelProperty("单价/盘（USD）")
	private  BigDecimal unitTrayPrice;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;
	/**
      * 汇率
      */
    @ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
      * 关税率%
      */
    @ApiModelProperty("关税率%")
	private  BigDecimal tariffRate;
	/**
      * 关税金额
      */
    @ApiModelProperty("关税金额")
	private  BigDecimal tariffPrice;
	/**
      * 增值税率%
      */
    @ApiModelProperty("增值税率%")
	private  BigDecimal vat;
	/**
      * 总公司代理费率%
      */
    @ApiModelProperty("总公司代理费率%")
	private  BigDecimal headAgencyFeeRate;
	/**
      * 总公司代理费
      */
    @ApiModelProperty("总公司代理费")
	private  BigDecimal headAgencyFeePrice;
	/**
      * 货代费单价
      */
    @ApiModelProperty("货代费单价")
	private  BigDecimal freightForwardingFeePrice;
	/**
      * 货代费用
      */
    @ApiModelProperty("货代费用")
	private  BigDecimal freightForwardingFee;
	/**
      * 保险费
      */
    @ApiModelProperty("保险费")
	private  BigDecimal insuranceFee;
	/**
      * 购进成本
      */
    @ApiModelProperty("购进成本")
	private  BigDecimal purchaseCost;
	/**
      * 仓储运输及税额
      */
    @ApiModelProperty("仓储运输及税额")
	private  BigDecimal storageTransportTax;
	/**
      * 毛利
      */
    @ApiModelProperty("毛利")
	private  BigDecimal grossMargin;
	/**
      * 不含税单价
      */
    @ApiModelProperty("不含税单价")
	private  BigDecimal priceExcludingTax;
	/**
      * 人民币单价（含税）
      */
    @ApiModelProperty("人民币单价（含税）")
	private  BigDecimal priceRmb;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 数据状态
      */
    @ApiModelProperty("数据状态")
	private  String status;
	private String createrBy;
	private String createrUserName;
	private Date createrTime;
	private String merchantNameCn;
}
