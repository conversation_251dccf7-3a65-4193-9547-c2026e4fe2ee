package com.dcjet.cs.dto.equipment;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@ApiModel(value = " DTO")
@Setter
@Getter
public class BizIEquipmentPlanPayNotifyDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    @ApiModelProperty("主键")
	private  String id;
    /**
    * 表头id
    */
    @ApiModelProperty("表头id")
	private  String headId;
    /**
    * 序号
    */
    @ApiModelProperty("序号")
	private  String serialNo;
    /**
    * 款项类型（多复选框）
    */
    @ApiModelProperty("款项类型（多复选框）")
	private  String paymentType;
    /**
    * 合同金额
    */
    @ApiModelProperty("合同金额")
	private  BigDecimal contractAmount;
    /**
    * 汇率
    */
    @ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
    /**
    * 进出口公司代理费率%
    */
    @ApiModelProperty("进出口公司代理费率%")
	private  BigDecimal importExportAgentRate;
    /**
    * 进出口公司代理费
    */
    @ApiModelProperty("进出口公司代理费")
	private  BigDecimal importExportAgentFee;
    /**
    * 总公司代理费率%
    */
    @ApiModelProperty("总公司代理费率%")
	private  BigDecimal headOfficeAgentRate;
    /**
    * 总公司代理费
    */
    @ApiModelProperty("总公司代理费")
	private  BigDecimal headOfficeAgentFee;
    /**
    * 计费箱数
    */
    @ApiModelProperty("计费箱数")
	private  String chargeContainerCount;
    /**
    * 通关费
    */
    @ApiModelProperty("通关费")
	private  BigDecimal customsClearanceFee;
    /**
    * 验柜服务费
    */
    @ApiModelProperty("验柜服务费")
	private  BigDecimal containerInspectionFee;
    /**
    * 货代费用
    */
    @ApiModelProperty("货代费用")
	private  BigDecimal freightForwardingFee;
    /**
    * 保险费率
    */
    @ApiModelProperty("保险费率")
	private  BigDecimal insuranceRate;
    /**
    * 保险费
    */
    @ApiModelProperty("保险费")
	private  BigDecimal insuranceFee;
    /**
    * 划款金额（RMB）
    */
    @ApiModelProperty("划款金额（RMB）")
	private  BigDecimal remittanceAmountRmb;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
	private  String remark;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
	private  String createBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
	private  Date createTime;
    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
    /**
    * 最后修改时间
    */
    @ApiModelProperty("最后修改时间")
	private  Date updateTime;
    /**
    * 创建人部门编码
    */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
    /**
    * 企业编码
    */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
    /**
    * 数据状态
    */
    @ApiModelProperty("数据状态")
	private  String status;
    /**
    * 版本号
    */
    @ApiModelProperty("版本号")
	private  String versionNo;
    /**
    * 父ID
    */
    @ApiModelProperty("父ID")
	private  String parentId;
    /**
    * 创建人姓名
    */
    @ApiModelProperty("创建人姓名")
	private  String createUserName;
    /**
    * 最后修改人姓名
    */
    @ApiModelProperty("最后修改人姓名")
	private  String updateUserName;
    /**
    * 扩展字段1
    */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
    /**
    * 扩展字段2
    */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
    /**
    * 扩展字段3
    */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
    /**
    * 扩展字段4
    */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
    /**
    * 扩展字段5
    */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
    /**
    * 扩展字段6
    */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
    /**
    * 扩展字段7
    */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
    /**
    * 扩展字段8
    */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
    /**
    * 扩展字段9
    */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
    /**
    * 扩展字段10
    */
    @ApiModelProperty("扩展字段10")
	private  String extend10;
}
