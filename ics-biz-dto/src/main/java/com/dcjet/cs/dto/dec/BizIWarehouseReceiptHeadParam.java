package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 进口管理-订单信息表头
 */
@Getter
@Setter
@ApiModel(value = "进口管理-入库回单-返回信息")
public class BizIWarehouseReceiptHeadParam implements Serializable{

    /**
     * 主建sid
     * 数据库字段:sid
     * 字符类型(50)
     */
    @ApiModelProperty("主建sid")
    private String sid;

    /**
     * 制单人
     * 数据库字段:insert_user
     * 字符类型(50)
     */
    @ApiModelProperty("制单人")
    private String insertUser;

    /**
     * 订单制单时间
     * 数据库字段:insert_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("订单制单时间")
    private Date insertTime;
    /**
     * 订单制单时间-开始时间
     */
    @ApiModelProperty("订单制单时间-开始时间")
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @ApiModelProperty("订单制单时间-结束时间")
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("创建人姓名")
    private String insertUserName;

    /**
     * 更新人
     * 数据库字段:update_user
     * 字符类型(50)
     */
    @ApiModelProperty("更新人")
    private String updateUser;

    /**
     * 更新时间
     * 数据库字段:update_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    /**
     * 企业代码
     * 数据库字段:trade_code
     * 字符类型(50)
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(60)
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 版本号

     * 数据库字段:version_no
     * 字符类型(10)
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 数据状态
     * 数据库字段:data_status
     * 字符类型(10)
     */
    @ApiModelProperty("数据状态")
    private String dataStatus;

    /**
     * 拓展字段1
     * 数据库字段:extend1
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段1")
    private String extend1;

    /**
     * 拓展字段2
     * 数据库字段:extend2
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段2")
    private String extend2;

    /**
     * 拓展字段3
     * 数据库字段:extend3
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段3")
    private String extend3;

    /**
     * 拓展字段4
     * 数据库字段:extend4
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段4")
    private String extend4;

    /**
     * 拓展字段5
     * 数据库字段:extend5
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段5")
    private String extend5;

    /**
     * 拓展字段6
     * 数据库字段:extend6
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段6")
    private String extend6;

    /**
     * 拓展字段7
     * 数据库字段:extend7
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段7")
    private String extend7;

    /**
     * 拓展字段8
     * 数据库字段:extend8
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段8")
    private String extend8;

    /**
     * 拓展字段9
     * 数据库字段:extend9
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段9")
    private String extend9;

    /**
     * 拓展字段10
     * 数据库字段:extend10
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段10")
    private String extend10;

    /**
     */
    private String parentId;




    /**
     *入库回单编号
     */
    private String warehouseReceiptNumber;
    /**
     *合同号
     */
    private String contractNumber;

    /**
     *订单号
     */
    private String orderNo;

    /**
     *进仓编号
     */
    @XdoSize(max = 60, message = "进仓编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    private String warehouseEntryNumber;

    /**
     *提货单号
     */
    private String ladingNumber;
    /**
     *提货单位
     */
    private String ladingDepartment;
    /**
     *进口发票号码
     */
    private String invoiceNumber;
    /**
     *供应商
     */
    private String supplier;
    /**
     *仓库
     */
    private String warehouse;
    /**
     *币种
     */
    private String curr;
    /**
     *卖出价（汇率）
     */
    @Digits(integer = 13, fraction = 6, message = "卖出价（汇率）必须为数字,整数位最大13位,小数最大6位!")
    private BigDecimal sellingRate;

    /**
     *汇率
     */
    @Digits(integer = 13, fraction = 6, message = "汇率必须为数字,整数位最大13位,小数最大6位!")
    private BigDecimal rate;
    /**
     *税单日期
     */
    private Date taxInvoiceDate;
    /**
     *进仓日期
     */
    private Date entryDate;
    /**
     *出库日期
     */
    private Date outdate;

    /**
     *客户折扣率
     */
    private BigDecimal discountRate;
    /**
     *发送用友
     */
    private String sendToYongyou;
    /**
     *备注
     */
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String note;
    /**
     *入库单据状态
     */
    private String status;


    /**
     * 是否流入下一个节点
     */
    private String isNext;

    /**
     * 打印文件类型pdf,excel
     */
    private String type;


    /**
     * 业务日期
     */
    @ApiModelProperty("业务日期")
    private Date businessDate;

    private List<BizIWarehouseReceiptListParam> warehouseReceiptListParams;


}