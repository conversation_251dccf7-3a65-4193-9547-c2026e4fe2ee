package com.dcjet.cs.dto.deliveryOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizDeliveryOrderInsuranceInfoParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 编号
     */
	@XdoSize(max = 120, message = "编号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("编号")
	private  String purchaseOrderNo;
	/**
     * 保险公司
     */
	@XdoSize(max = 400, message = "保险公司长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("保险公司")
	private  String insuranceCompany;
	/**
     * 被保险人
     */
	@XdoSize(max = 400, message = "被保险人长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("被保险人")
	private  String insurant;
	/**
     * 发票抬头
     */
	@XdoSize(max = 400, message = "发票抬头长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发票抬头")
	private  String invoiceTitle;
	/**
     * 运输工具名称
     */
	@XdoSize(max = 400, message = "运输工具名称长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("运输工具名称")
	private  String trafName;
	/**
     * 开航日期
     */
	@ApiModelProperty("开航日期")
	private  Date startShipmentDate;
	/**
     * 运输路线自
     */
	@XdoSize(max = 400, message = "运输路线自长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("运输路线自")
	private  String trafRouteFrom;
	/**
     * 经
     */
	@XdoSize(max = 400, message = "经长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("经")
	private  String trafRoutePass;
	/**
     * 至
     */
	@XdoSize(max = 400, message = "至长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("至")
	private  String trafRouteTo;
	/**
     * 投保险别
     */
	@XdoSize(max = 400, message = "投保险别长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("投保险别")
	private  String insuranceType;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 投保加成%
     */
	@Digits(integer = 15, fraction = 4, message = "投保加成%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("投保加成%")
	private  BigDecimal insuranceMarkup;
	/**
     * 保险金额
     */
	@Digits(integer = 15, fraction = 4, message = "保险金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("保险金额")
	private  BigDecimal insuranceAmount;
	/**
     * 保险费率
     */
	@Digits(integer = 15, fraction = 4, message = "保险费率必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("保险费率")
	private  BigDecimal insuranceRate;
	/**
     * 保费
     */
	@Digits(integer = 15, fraction = 4, message = "保费必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("保费")
	private  BigDecimal insuranceFee;
	/**
     * 投保日期
     */
	@ApiModelProperty("投保日期")
	private  Date insuranceDate;
	/**
     * 投保日期
     */
	@Digits(integer = 15, fraction = 4, message = "投保日期必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("投保日期")
	private  BigDecimal freightFee;
	/**
     * 投保日期
     */
	@XdoSize(max = 20, message = "投保日期长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("投保日期")
	private  String freightCurr;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	private  String headId;
}
