package com.dcjet.cs.dto.equipment;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-8
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIEquipmentPlanHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String id;
	/**
      * 计划书单号
      */
    @ApiModelProperty("计划书单号")
	private  String planNo;
	/**
      * 业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）
      */
    @ApiModelProperty("业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）")
	private  String businessType;
	/**
      * 合同号（弹窗选择，允许修改）
      */
    @ApiModelProperty("合同号（弹窗选择，允许修改）")
	private  String contractNo;
	/**
      * 业务地点（从外商合同带入，不允许修改）
      */
    @ApiModelProperty("业务地点（从外商合同带入，不允许修改）")
	private  String businessLocation;
	/**
      * 买家（从外商合同带入，不允许修改）
      */
    @ApiModelProperty("买家（从外商合同带入，不允许修改）")
	private  String buyer;
	/**
      * 卖家（从外商合同带入，不允许修改）
      */
    @ApiModelProperty("卖家（从外商合同带入，不允许修改）")
	private  String seller;
	/**
      * 使用厂家（从外商合同带入）
      */
    @ApiModelProperty("使用厂家（从外商合同带入）")
	private  String manufacturer;
	/**
      * 国内委托方（从外商合同带入）
      */
    @ApiModelProperty("国内委托方（从外商合同带入）")
	private  String domesticClient;
	/**
      * 预计收款时间（用户录入，当收款状态为未收款时触发预警）
      */
    @ApiModelProperty("预计收款时间（用户录入，当收款状态为未收款时触发预警）")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date estReceiveDate;
	/**
      * 收款状态（0未收款，1已收款。默认未收款）
      */
    @ApiModelProperty("收款状态（0未收款，1已收款。默认未收款）")
	private  String receiveStatus;
	/**
      * 预计付款时间（用户录入，当付款状态为未付款时触发预警）
      */
    @ApiModelProperty("预计付款时间（用户录入，当付款状态为未付款时触发预警）")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date estPaymentDate;
	/**
      * 付款状态（0未付款，1已付款。默认未付款）
      */
    @ApiModelProperty("付款状态（0未付款，1已付款。默认未付款）")
	private  String paymentStatus;
	/**
      * 预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）
      */
    @ApiModelProperty("预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date estArbitrationDate;
	/**
      * 预裁定状态（0未裁定，1已裁定。默认未裁定）
      */
    @ApiModelProperty("预裁定状态（0未裁定，1已裁定。默认未裁定）")
	private  String arbitrationStatus;
	/**
      * 预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）
      */
    @ApiModelProperty("预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date estLicenseDate;
	/**
      * 许可证状态（0未办理，1已办理。默认未办理）
      */
    @ApiModelProperty("许可证状态（0未办理，1已办理。默认未办理）")
	private  String licenseStatus;
	/**
      * 预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）
      */
    @ApiModelProperty("预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date estTransportCertDate;
	/**
      * 准运证状态（0未办理，1已办理。默认未办理）
      */
    @ApiModelProperty("准运证状态（0未办理，1已办理。默认未办理）")
	private  String transportCertStatus;
	/**
      * 预计保险申办时间（用户录入，当保险状态为未办理时触发预警）
      */
    @ApiModelProperty("预计保险申办时间（用户录入，当保险状态为未办理时触发预警）")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date estInsuranceDate;
	/**
      * 保险状态（0未办理，1已办理。默认未办理）
      */
    @ApiModelProperty("保险状态（0未办理，1已办理。默认未办理）")
	private  String insuranceStatus;
	/**
      * 报关状态
      */
    @ApiModelProperty("报关状态")
	private  String entryStatus;
	/**
      * 预计装箱信息（用户录入，预估箱型及箱数）
      */
    @ApiModelProperty("预计装箱信息（用户录入，预估箱型及箱数）")
	private  String estPackingInfo;
	/**
      * 许可证号
      */
    @ApiModelProperty("许可证号")
	private  String licenseNo;
	/**
      * 许可证申请日期
      */
    @ApiModelProperty("许可证申请日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date licenseApplyDate;
	/**
      * 许可证有效期
      */
    @ApiModelProperty("许可证有效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date licenseValidityDate;
	/**
      * 许可证备注
      */
    @ApiModelProperty("许可证备注")
	private  String licenseRemark;
	/**
      * 备注（用户录入）
      */
    @ApiModelProperty("备注（用户录入）")
	private  String remark;
	/**
      * 数据状态
      */
    @ApiModelProperty("数据状态")
	private  String status;
	/**
      * 审批状态
      */
    @ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 版本号
      */
    @ApiModelProperty("版本号")
	private  String versionNo;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 父级ID
      */
    @ApiModelProperty("父级ID")
	private  String parentId;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 最后修改人
      */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 创建人姓名
      */
    @ApiModelProperty("创建人姓名")
	private  String createUserName;
	/**
      * 最后修改人姓名
      */
    @ApiModelProperty("最后修改人姓名")
	private  String updateUserName;
	/**
      * 扩展字段1
      */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
      * 扩展字段2
      */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
      * 扩展字段3
      */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
      * 扩展字段4
      */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
      * 扩展字段5
      */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
      * 扩展字段6
      */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
      * 扩展字段7
      */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
      * 扩展字段8
      */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
      * 扩展字段9
      */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
      * 扩展字段10
      */
    @ApiModelProperty("扩展字段10")
	private  String extend10;

	List<BizIEquipmentPlanListDto> listData;

	private String insertTime;
}
