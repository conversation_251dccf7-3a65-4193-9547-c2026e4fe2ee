package com.dcjet.cs.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@ApiModel("外商合同表体新增参数模型")
@Accessors(chain = true)
public class ForeignContractAddBodyParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表头id
     */
    @ApiModelProperty("表头id")
    private String headId;

    /**
     * 物料id列表
     */
    @ApiModelProperty("物料id列表")
    private List<String> materialIds;
}