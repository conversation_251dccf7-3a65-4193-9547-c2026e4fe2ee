package com.dcjet.cs.dto.auxiliaryMaterials;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel("订货通知新增参数模型")
public class OrderNoticeAddParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;

    /**
     * 购销合同id列表
     */
    @ApiModelProperty("购销合同sid列表")
    private List<String> contractIds;

    /**
     * 购销合同表体id列表
     */
    @ApiModelProperty("购销合同表体id列表")
    private List<String> contractListIds;

    /**
     * 订货编号
     */
    @NotNull(message = "订货编号不能为空！")
    @XdoSize(max = 60, message = "订货编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("订货编号")
    private String orderNo;

    /**
     * 订货日期
     */
    @ApiModelProperty("订货日期")
    @NotNull(message = "订货日期不能为空！")
    private Date orderDate;

    /**
     * 客户
     */
    @ApiModelProperty("客户")
    @NotNull(message = "客户不能为空！")
    private String customer;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String note;

    /**
     * 表体列表
     */
    @ApiModelProperty("表体列表")
    private List<OrderNoticeListParam> bodyList;
}
