package com.dcjet.cs.dto.dec;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 出货信息表体汇总信息
 */
@Getter
@Setter
public class BizExportGoodsListSummaryDto implements Serializable {


    /**
     * 数量汇总
     */
    private BigDecimal qtyTotal;

    /**
     * 金额汇总
     */
    private BigDecimal totalAmount;

    /**
     * 毛重汇总
     */
    private BigDecimal grossTotal;

    /**
     * 净重汇总
     */
    private BigDecimal netTotal;

    /**
     * 皮重
     */
    private BigDecimal traeTotal;



}
