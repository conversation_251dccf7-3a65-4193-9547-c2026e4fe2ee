package com.dcjet.cs.dto.aeo;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter @Getter
@ApiModel(value = "内审差错统计输入信息")
public class AuditUserParam implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 审批类型
	 */
	@ApiModelProperty("审批类型")
	private  String apprType;
	/**
     * 内审员
     */
	@XdoSize(max = 50, message = "{内审员长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("内审员")
	private  String insertUser;
	/**
    * 创建时间-开始
    */
	@ApiModelProperty("创建时间-开始")
	private String insertTimeFrom;
	/**
    * 创建时间-结束
    */
	@ApiModelProperty("创建时间-结束")
    private String insertTimeTo;


}
