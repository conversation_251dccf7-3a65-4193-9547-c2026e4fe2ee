package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@ApiModel(value = "进口费用信息传入参数")
public class CostIContractParam implements Serializable {
    private static final long serialVersionUID = 1L;
    //出库单号
    private String purchaseOrderNo;
    //合同号
    private String contractNo;
    //商品名称
    private String productGrade;
    //订单号
    private String orderNo;
    //客户名称
    private String partyA;
    private String partyB;
    //币种
    private String curr;
    //金额
    private BigDecimal decTotal;
    //数量
    private BigDecimal qty;
    //单位
    private String unit;
    //商品类别
    private String merchandiseCategories;
    //sid
    private String sid;

    private String tradeCode;
    //----------------------------------------------------\
    private String businessType;

    //--------------------------------------------------
    //费用类型
    private String expenseType;
    //发票号
    private String invoiceNumber;
    //开票客户
    private String invoiceUser;
    //税额金额
    private BigDecimal taxAmount;
    //无税金额
    private BigDecimal noTaxAmount;
    //费用金额
    private String amount;
    //是否分摊
    private String apportionment;
    //分摊方式
    private String methodAllocation;
    //sids
    private List<String> sids;
    //headid
    private String headId;


}
