package com.dcjet.cs.dto.deliveryOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizDeliveryOrderListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 商品名称
     */
	@XdoSize(max = 200, message = "商品名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	private  String productName;
	/**
     * 产品型号
     */
	@XdoSize(max = 200, message = "产品型号长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("产品型号")
	private  String productModel;
	/**
     * 单位
     */
	@XdoSize(max = 40, message = "单位长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单位")
	private  String unit;
	/**
     * 数量
     */
	@Digits(integer = 13, fraction = 6, message = "数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
     * 单价
     */
	@Digits(integer = 11, fraction = 8, message = "单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@Digits(integer = 15, fraction = 4, message = "金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("金额")
	private  BigDecimal amount;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 箱数
     */
	@Digits(integer = 13, fraction = 6, message = "箱数必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("箱数")
	private  BigDecimal containerNum;

	private  String headId;
	private  BigDecimal grossWeight;
	private  BigDecimal netWeight;
}
