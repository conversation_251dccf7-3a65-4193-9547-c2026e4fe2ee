package com.dcjet.cs.dto.auxiliaryMaterials;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.math.BigDecimal;

/**
 * 包含表头和表体数据的参数类
 * <AUTHOR>
 * @date: 2025-06-23
 */
@Setter @Getter
@ApiModel(value = "非国营辅料合同表头和表体传入参数")
public class BizINonStateAuxmatAggrContractWithDetailParam implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;
    
    /**
     * 业务类型
     */
    @NotEmpty(message="业务类型不能为空！")
    @XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;
    
    /**
     * 合同号
     */
    @NotEmpty(message="合同号不能为空！")
    @XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("合同号")
    private String contractNo;
    
    /**
     * 买方
     */
    @XdoSize(max = 400, message = "买方长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("买方")
    private String buyer;
    
    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplier;
    
    /**
     * 签约日期
     */
    @ApiModelProperty("签约日期")
    private Date signingDate;
    
    /**
     * 国内委托方
     */
    @ApiModelProperty("国内委托方")
    private String domesticPrincipal;
    
    /**
     * 运输方式
     */
    @XdoSize(max = 20, message = "运输方式长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运输方式")
    private String transportMode;
    
    /**
     * 合同生效期
     */
    @ApiModelProperty("合同生效期")
    private Date effectiveDate;
    
    /**
     * 合同有效期
     */
    @ApiModelProperty("合同有效期")
    private Date expiryDate;
    
    /**
     * 签约地点(中文)
     */
    @XdoSize(max = 200, message = "签约地点(中文)长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("签约地点(中文)")
    private String signingLocationCn;
    
    /**
     * 签约地点(英文)
     */
    @XdoSize(max = 200, message = "签约地点(英文)长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("签约地点(英文)")
    private String signingLocationEn;
    
    /**
     * 装运港
     */
    @XdoSize(max = 200, message = "装运港长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("装运港")
    private String portOfLoading;
    
    /**
     * 目的港
     */
    @XdoSize(max = 200, message = "目的港长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("目的港")
    private String portOfDestination;
    
    /**
     * 报关口岸
     */
    @XdoSize(max = 200, message = "报关口岸长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("报关口岸")
    private String customsPort;
    
    /**
     * 付款方式
     */
    @XdoSize(max = 200, message = "付款方式长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("付款方式")
    private String paymentMethod;
    
    /**
     * 币种
     */
    @XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币种")
    private String currency;
    
    /**
     * 价格条款
     */
    @XdoSize(max = 200, message = "价格条款长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("价格条款")
    private String priceTerm;
    
    /**
     * 价格条款对应港口
     */
    @XdoSize(max = 200, message = "价格条款对应港口长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("价格条款对应港口")
    private String priceTermPort;
    
    /**
     * 建议授权签约人
     */
    @XdoSize(max = 200, message = "建议授权签约人长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("建议授权签约人")
    private String suggestedSigner;
    
    /**
     * 短溢数%
     */
    @ApiModelProperty("短溢数%")
    private BigDecimal shortageOverflowPercent;
    
    /**
     * 备注
     */
    @XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remarks;
    
    /**
     * 版本号
     */
    @NotEmpty(message="版本号不能为空！")
    @XdoSize(max = 20, message = "版本号长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;
    
    /**
     * 单据状态
     */
    @NotEmpty(message="单据状态不能为空！")
    @ApiModelProperty("单据状态")
    private String status;
    
    /**
     * 确认时间
     */
    @ApiModelProperty("确认时间")
    private Date confirmTime;
    
    /**
     * 审批状态
     */
    @XdoSize(max = 20, message = "审批状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("审批状态")
    private String apprStatus;
    
    /**
     * 制单日期
     */
    @NotNull(message="制单日期不能为空！")
    @ApiModelProperty("制单日期")
    private Date createTime;

    /**
     * 人民币划款金额
     */
    @ApiModelProperty("人民币划款金额")
    private BigDecimal paymentAmount;
    /**
     * 合同金额
     */
    @ApiModelProperty("合同金额")
    private BigDecimal contractAmount;

    /**
     * 汇率
     */
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    /**
     * 关税税率
     */
    @ApiModelProperty("关税税率")
    private BigDecimal tariffRate;

    /**
     * 关税金额
     */
    @ApiModelProperty("关税金额")
    private BigDecimal tariffAmount;

    /**
     * 消费税率
     */
    @ApiModelProperty("消费税率")
    private BigDecimal consumptionTaxRate;

    /**
     * 消费税金额
     */
    @ApiModelProperty("消费税金额")
    private BigDecimal consumptionTaxAmount;

    /**
     * 增值税税率
     */
    @ApiModelProperty("增值税税率")
    private BigDecimal vatRate;

    /**
     * 增值税金额
     */
    @ApiModelProperty("增值税金额")
    private BigDecimal vatAmount;

    /**
     * 进出口代理费率
     */
    @ApiModelProperty("进出口代理费率")
    private BigDecimal importExportAgencyRate;

    /**
     * 进出口代理费
     */
    @ApiModelProperty("进出口代理费")
    private BigDecimal importExportAgencyFee;

    /**
     * 总部代理费率
     */
    @ApiModelProperty("总部代理费率")
    private BigDecimal headquartersAgencyRate;

    /**
     * 总部代理费
     */
    @ApiModelProperty("总部代理费")
    private BigDecimal headquartersAgencyFee;

    /**
     * 合同数量
     */
    @ApiModelProperty("合同数量")
    private BigDecimal contractQuantity;

    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal billingWeight;

    /**
     * 清关费
     */
    @ApiModelProperty("清关费")
    private BigDecimal customsClearanceFee;

    /**
     * 集装箱检验费
     */
    @ApiModelProperty("集装箱检验费")
    private BigDecimal containerInspectionFee;

    /**
     * 货运代理费
     */
    @ApiModelProperty("货运代理费")
    private BigDecimal freightForwarderFee;

    /**
     * 保险费率
     */
    @ApiModelProperty("保险费率")
    private BigDecimal insuranceRate;

    /**
     * 保险费
     */
    @ApiModelProperty("保险费")
    private BigDecimal insuranceFee;
    /**
     * 是否划款通知保存过
     */
    @ApiModelProperty("是否划款通知保存过")
    private String isTransferNotice;
    
    /**
     * 表体数据列表
     */
    @ApiModelProperty("表体数据列表")
    private List<BizINonStateAuxmatAggrContractListParam> details;
}
