package com.dcjet.cs.dto.exportAuxMatAnnualPlan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@ApiModel(value = "出口辅料年度计划选项数据传输模型")
public class ExportAuxMatAnnualPlanOptionsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户选项
     */
    @ApiModelProperty("客户选项")
    private List<Map<String, String>> customerOptions = Collections.emptyList();

    /**
     * 商品类别选项
     */
    @ApiModelProperty("商品类别选项")
    private List<Map<String, String>> productTypeOptions = Collections.emptyList();
}