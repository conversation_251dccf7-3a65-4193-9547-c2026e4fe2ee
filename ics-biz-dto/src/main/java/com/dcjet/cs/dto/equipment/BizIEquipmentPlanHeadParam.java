package com.dcjet.cs.dto.equipment;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIEquipmentPlanHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String id;
	/**
     * 计划书单号
     */
	@XdoSize(max = 60, message = "计划书单号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("计划书单号")
	private  String planNo;
	/**
     * 业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）
     */
	@XdoSize(max = 60, message = "业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型（默认3-国营贸易进口烟机设备，置灰，不允许修改）")
	private  String businessType;
	/**
     * 合同号（弹窗选择，允许修改）
     */
	@XdoSize(max = 60, message = "合同号（弹窗选择，允许修改）长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号（弹窗选择，允许修改）")
	private  String contractNo;
	/**
     * 合同号列表（用于从外商合同生成计划数据）
     */
	@ApiModelProperty("合同号列表（用于从外商合同生成计划数据）")
	private  List<String> forContractIdList;
	/**
     * 业务地点（从外商合同带入，不允许修改）
     */
	@XdoSize(max = 20, message = "业务地点（从外商合同带入，不允许修改）长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务地点（从外商合同带入，不允许修改）")
	private  String businessLocation;
	/**
     * 买家（从外商合同带入，不允许修改）
     */
	@ApiModelProperty("买家（从外商合同带入，不允许修改）")
	private  String buyer;
	/**
     * 卖家（从外商合同带入，不允许修改）
     */
	@ApiModelProperty("卖家（从外商合同带入，不允许修改）")
	private  String seller;
	/**
     * 使用厂家（从外商合同带入）
     */
	@XdoSize(max = 200, message = "使用厂家（从外商合同带入）长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("使用厂家（从外商合同带入）")
	private  String manufacturer;
	/**
     * 国内委托方（从外商合同带入）
     */
	@XdoSize(max = 200, message = "国内委托方（从外商合同带入）长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("国内委托方（从外商合同带入）")
	private  String domesticClient;
	/**
     * 预计收款时间（用户录入，当收款状态为未收款时触发预警）
     */
	@ApiModelProperty("预计收款时间（用户录入，当收款状态为未收款时触发预警）")
	private  Date estReceiveDate;
	/**
     * 收款状态（0未收款，1已收款。默认未收款）
     */
	@XdoSize(max = 20, message = "收款状态（0未收款，1已收款。默认未收款）长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("收款状态（0未收款，1已收款。默认未收款）")
	private  String receiveStatus;
	/**
     * 预计付款时间（用户录入，当付款状态为未付款时触发预警）
     */
	@ApiModelProperty("预计付款时间（用户录入，当付款状态为未付款时触发预警）")
	private  Date estPaymentDate;
	/**
     * 付款状态（0未付款，1已付款。默认未付款）
     */
	@XdoSize(max = 20, message = "付款状态（0未付款，1已付款。默认未付款）长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("付款状态（0未付款，1已付款。默认未付款）")
	private  String paymentStatus;
	/**
     * 预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）
     */
	@ApiModelProperty("预计裁定时间（用户录入，当预裁定状态为未裁定时触发预警）")
	private  Date estArbitrationDate;
	/**
     * 预裁定状态（0未裁定，1已裁定。默认未裁定）
     */
	@XdoSize(max = 20, message = "预裁定状态（0未裁定，1已裁定。默认未裁定）长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("预裁定状态（0未裁定，1已裁定。默认未裁定）")
	private  String arbitrationStatus;
	/**
     * 预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）
     */
	@ApiModelProperty("预计许可证申办时间（用户录入，当许可证状态为未办理时触发预警）")
	private  Date estLicenseDate;
	/**
     * 许可证状态（0未办理，1已办理。默认未办理）
     */
	@XdoSize(max = 20, message = "许可证状态（0未办理，1已办理。默认未办理）长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("许可证状态（0未办理，1已办理。默认未办理）")
	private  String licenseStatus;
	/**
     * 预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）
     */
	@ApiModelProperty("预计准运证申办时间（用户录入，当准运证状态为未办理时触发预警）")
	private  Date estTransportCertDate;
	/**
     * 准运证状态（0未办理，1已办理。默认未办理）
     */
	@XdoSize(max = 20, message = "准运证状态（0未办理，1已办理。默认未办理）长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("准运证状态（0未办理，1已办理。默认未办理）")
	private  String transportCertStatus;
	/**
     * 预计保险申办时间（用户录入，当保险状态为未办理时触发预警）
     */
	@ApiModelProperty("预计保险申办时间（用户录入，当保险状态为未办理时触发预警）")
	private  Date estInsuranceDate;
	/**
     * 保险状态（0未办理，1已办理。默认未办理）
     */
	@XdoSize(max = 20, message = "保险状态（0未办理，1已办理。默认未办理）长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("保险状态（0未办理，1已办理。默认未办理）")
	private  String insuranceStatus;
	/**
     * 预计装箱信息（用户录入，预估箱型及箱数）
     */
	@XdoSize(max = 200, message = "预计装箱信息（用户录入，预估箱型及箱数）长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("预计装箱信息（用户录入，预估箱型及箱数）")
	private  String estPackingInfo;
	/**
     * 备注（用户录入）
     */
	@XdoSize(max = 200, message = "备注（用户录入）长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注（用户录入）")
	private  String remark;
	/**
     * 数据状态
     */
	@ApiModelProperty("数据状态")
	private  String status;
	/**
     * 创建人
     */

	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */

	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
    * 创建时间-开始
    */
	@ApiModelProperty("创建时间-开始")
	private String createTimeFrom;
	/**
    * 创建时间-结束
    */
	@ApiModelProperty("创建时间-结束")
    private String createTimeTo;
	/**
	 * 许可证号
	 */
	@ApiModelProperty("许可证号")
	private  String licenseNo;
	/**
	 * 许可证申请日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty("许可证申请日期")
	private  Date licenseApplyDate;
	/**
	 * 许可证有效期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty("许可证有效期")
	private  Date licenseValidityDate;
	/**
	 * 许可证备注
	 */
	@ApiModelProperty("许可证备注")
	private  String licenseRemark;

	List<BizIEquipmentPlanListParam> listData;
}
