package com.dcjet.cs.dto.dec;

import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Getter
@Setter
public class BizBatchUpdateInvoiceNoParams implements Serializable {

    @NotNull(message = "发票号不能为空!")
    @XdoSize(message = "发票号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    private String invoiceNo;



    @NotNull(message = "ID集合和不能为空!")
    private List<String> ids;
}
