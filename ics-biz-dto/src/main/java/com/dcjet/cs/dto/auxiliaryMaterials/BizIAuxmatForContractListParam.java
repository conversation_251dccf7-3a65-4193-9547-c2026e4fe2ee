package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class BizIAuxmatForContractListParam implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
    * 主键
    */
    @ApiModelProperty("主键")
    private String sid;
	/**
    * 关联字段
    */
	@ApiModelProperty("表头关联字段")
	@NotEmpty(message="表头关联字段不能为空！")
	private String headId;
	/**
    * 主键ID
    */
	@NotEmpty(message="主键ID不能为空！")
	@XdoSize(max = 40, message = "主键ID长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("主键ID")
	private  String id;
	/**
    * 商品名称
    */
	@XdoSize(max = 160, message = "商品名称长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	private  String productName;
	/**
    * 进口数量
    */
	@NotNull(message="进口数量不能为空！")
	@Digits(integer = 13, fraction = 6, message = "进口数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("进口数量")
	private  BigDecimal importQuantity;
	/**
    * 进口计量单位
    */
	@XdoSize(max = 60, message = "进口计量单位长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("进口计量单位")
	private  String importUnit;
	/**
    * 数量
    */
	@NotNull(message="数量不能为空！")
	@Digits(integer = 13, fraction = 6, message = "数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("数量")
	private  BigDecimal quantity;
	/**
    * 单位
    */
	@XdoSize(max = 60, message = "单位长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单位")
	private  String unit;
	/**
    * 单价
    */
	@Digits(integer = 11, fraction = 8, message = "单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
    * 金额
    */
	@Digits(integer = 15, fraction = 4, message = "金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("金额")
	private  BigDecimal amount;
	/**
    * 发货日期
    */
	@ApiModelProperty("发货日期")
	private  Date deliveryDate;
	/**
    * 总值折美元
    */
	@Digits(integer = 15, fraction = 4, message = "总值折美元必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总值折美元")
	private  BigDecimal usdTotal;
	/**
    * 规格
    */
	@XdoSize(max = 400, message = "规格长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("规格")
	private  String specification;
	/**
    * 商品类别
    */
	@XdoSize(max = 160, message = "商品类别长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品类别")
	private  String productCategory;
	/**
    * 数据状态
    */
	@XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("数据状态")
	private  String dataState;
	/**
    * 版本号
    */
	@XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
    * 业务编码
    */
	@XdoSize(max = 10, message = "业务编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务编码")
	private  String tradeCode;
	/**
    * 所属机构编码
    */
	@XdoSize(max = 10, message = "所属机构编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("所属机构编码")
	private  String sysOrgCode;
	/**
    * 创建人
    */
	@NotEmpty(message="创建人不能为空！")
	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
    * 创建时间
    */
	@NotNull(message="创建时间不能为空！")
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
    * 修改人
    */
	@XdoSize(max = 50, message = "修改人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("修改人")
	private  String updateBy;
	/**
    * 创建人姓名
    */
	@XdoSize(max = 50, message = "创建人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人姓名")
	private  String insertUserName;
	/**
    * 修改人姓名
    */
	@XdoSize(max = 50, message = "修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
    * 扩展字段1
    */
	@XdoSize(max = 200, message = "扩展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
    * 扩展字段2
    */
	@XdoSize(max = 200, message = "扩展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
    * 扩展字段3
    */
	@XdoSize(max = 200, message = "扩展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
    * 扩展字段4
    */
	@XdoSize(max = 200, message = "扩展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
    * 扩展字段5
    */
	@XdoSize(max = 200, message = "扩展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
    * 扩展字段6
    */
	@XdoSize(max = 200, message = "扩展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
    * 扩展字段7
    */
	@XdoSize(max = 200, message = "扩展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
    * 扩展字段8
    */
	@XdoSize(max = 200, message = "扩展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
    * 扩展字段9
    */
	@XdoSize(max = 200, message = "扩展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
    * 扩展字段10
    */
	@XdoSize(max = 200, message = "扩展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段10")
	private  String extend10;

	private String orderNo;

	private List<String> sids;
}
