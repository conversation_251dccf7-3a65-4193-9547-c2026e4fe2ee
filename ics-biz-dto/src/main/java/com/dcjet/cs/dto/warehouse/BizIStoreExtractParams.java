package com.dcjet.cs.dto.warehouse;

import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 入库回单提取数据
 */
@Getter
@Setter
public class BizIStoreExtractParams implements Serializable {

    @XdoSize(max = 200, message = "合同号长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String contractNo;


    private List<String> sids;

    @XdoSize(max = 200, message = "订货单号长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String purchaseOrderNo;

}
