package com.dcjet.cs.dto.deliveryOrder;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-7
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizDeliveryOrderContainerListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 最后修改人名称
      */
    @ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 拓展字段1
      */
    @ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
      * 拓展字段2
      */
    @ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
      * 拓展字段3
      */
    @ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
      * 拓展字段4
      */
    @ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
      * 拓展字段5
      */
    @ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
      * 拓展字段6
      */
    @ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
      * 拓展字段7
      */
    @ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
      * 拓展字段8
      */
    @ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
      * 拓展字段9
      */
    @ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
      * 拓展字段10
      */
    @ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 起始箱号
      */
    @ApiModelProperty("起始箱号")
	private  String boxNoStart;
	/**
      * 结束箱号
      */
    @ApiModelProperty("结束箱号")
	private  String boxNoEnd;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	private  String productName;
	/**
      * 包装样式
      */
    @ApiModelProperty("包装样式")
	private  String packageStyle;
	/**
      * 毛重(KG)
      */
    @ApiModelProperty("毛重(KG)")
	private  BigDecimal grossWt;
	/**
      * 净重(KG)
      */
    @ApiModelProperty("净重(KG)")
	private  BigDecimal netWt;
	/**
      * 皮重(KG)
      */
    @ApiModelProperty("皮重(KG)")
	private  BigDecimal tareWt;
	/**
      * 长(M)
      */
    @ApiModelProperty("长(M)")
	private  BigDecimal longer;
	/**
      * 宽(M)
      */
    @ApiModelProperty("宽(M)")
	private  BigDecimal whither;
	/**
      * 高(M)
      */
    @ApiModelProperty("高(M)")
	private  BigDecimal higher;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 箱数
      */
    @ApiModelProperty("件数")
	private  BigDecimal containerNum;
	/**
      * 表头id
      */
    @ApiModelProperty("表头id")
	private  String headId;
	/**
      * 外商合同表体id
      */
    @ApiModelProperty("外商合同表体id")

	private  String contractListId;
	@ApiModelProperty("数量")
	private  BigDecimal qty;
}
