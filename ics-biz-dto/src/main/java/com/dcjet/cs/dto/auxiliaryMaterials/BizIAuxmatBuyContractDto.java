package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-5-28
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIAuxmatBuyContractDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String id;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 购销合同号
      */
    @ApiModelProperty("购销合同号")
	private  String contractNo;
	/**
      * 购销年份
      */
    @ApiModelProperty("购销年份")
	private  String contractYear;
	/**
      * 业务区分
      */
    @ApiModelProperty("业务区分")
	private  String businessDistinction;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String remark;
	/**
      * 版本号
      */
    @ApiModelProperty("版本号")
	private  String versionNo;
	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String status;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 审批状态
      */
    @ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private  String createByName;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 最后修改人
      */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
	 * 最后修改人
	 */
	@ApiModelProperty("最后修改人")
	private  String updateByName;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	//updateUser不为空取updateUser,否则取insertUser
	@ApiModelProperty("制单人")
	private String createrUser;

	//updateTime不为空取updateUser,否则取insertTime
	@ApiModelProperty("制单时间")
	private Date createrTime;

    /**
     * 合同金额
     */
    @ApiModelProperty("合同金额")
    private BigDecimal contractAmount;

    /**
     * 汇率
     */
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    /**
     * 关税税率
     */
    @ApiModelProperty("关税税率")
    private BigDecimal tariffRate;

    /**
     * 关税金额
     */
    @ApiModelProperty("关税金额")
    private BigDecimal tariffAmount;

    /**
     * 消费税率
     */
    @ApiModelProperty("消费税率")
    private BigDecimal consumptionTaxRate;

    /**
     * 消费税金额
     */
    @ApiModelProperty("消费税金额")
    private BigDecimal consumptionTaxAmount;

    /**
     * 增值税税率
     */
    @ApiModelProperty("增值税税率")
    private BigDecimal vatRate;

    /**
     * 增值税金额
     */
    @ApiModelProperty("增值税金额")
    private BigDecimal vatAmount;

    /**
     * 进出口代理费率
     */
    @ApiModelProperty("进出口代理费率")
    private BigDecimal importExportAgencyRate;

    /**
     * 进出口代理费
     */
    @ApiModelProperty("进出口代理费")
    private BigDecimal importExportAgencyFee;

    /**
     * 总部代理费率
     */
    @ApiModelProperty("总部代理费率")
    private BigDecimal headquartersAgencyRate;

    /**
     * 总部代理费
     */
    @ApiModelProperty("总部代理费")
    private BigDecimal headquartersAgencyFee;

    /**
     * 合同数量
     */
    @ApiModelProperty("合同数量")
    private BigDecimal contractQuantity;

    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal billingWeight;

    /**
     * 清关费
     */
    @ApiModelProperty("清关费")
    private BigDecimal customsClearanceFee;

    /**
     * 集装箱检验费
     */
    @ApiModelProperty("集装箱检验费")
    private BigDecimal containerInspectionFee;

    /**
     * 货运代理费
     */
    @ApiModelProperty("货运代理费")
    private BigDecimal freightForwarderFee;

    /**
     * 保险费率
     */
    @ApiModelProperty("保险费率")
    private BigDecimal insuranceRate;

    /**
     * 保险费
     */
    @ApiModelProperty("保险费")
    private BigDecimal insuranceFee;

    /**
     * 人民币划款金额
     */
    @ApiModelProperty("人民币划款金额")
    private BigDecimal paymentAmount;

    /**
     * 是否划款通知保存过
     */
    @ApiModelProperty("是否划款通知保存过")
    private String isTransferNotice;

    private String hasHeadNotice;
    private String isCopy;;
}
