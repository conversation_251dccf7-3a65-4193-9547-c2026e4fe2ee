package com.dcjet.cs.dto.dec;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * （第7条线）出料加工进口薄片-分析单表头
 */
@Getter
@Setter
@ApiModel(value = "（第7条线）出料加工进口薄片-分析单表头-返回信息")
public class BizBpAnalyseOrderHeadDto implements Serializable{

    /**
     * 主键id
     * 数据库字段:id
     * 字符类型(160)
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(240)
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(40)
     */
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(40)
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(40)
     */
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(40)
     */
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * 字符类型(160)
     */
    @ApiModelProperty("父级id")
    private String parentId;

    /**
     * 创建人
     * 数据库字段:create_by
     * 字符类型(200)
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * 日期类型(6)
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private String createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @ApiModelProperty("创建时间-结束时间")
    private String createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(200)
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(200)
     */
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(200)
     */
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(800)
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 收货人
     * 数据库字段:receiver
     * 字符类型(2000)
     */
    @ApiModelProperty("收货人")
    private String receiver;

    /**
     * 合同号
     * 数据库字段:contract_no
     * 字符类型(120)
     */
    @ApiModelProperty("合同号")
    private String contractNo;

    /**
     * 分析单号
     * 数据库字段:analysis_no
     * 字符类型(120)
     */
    @ApiModelProperty("分析单号")
    private String analysisNo;

    /**
     * 贸易国别
     * 数据库字段:trade_country
     * 字符类型(100)
     */
    @ApiModelProperty("贸易国别")
    private String tradeCountry;

    /**
     * 目的地
     * 数据库字段:destination
     * 字符类型(200)
     */
    @ApiModelProperty("目的地")
    private String destination;

    /**
     * 消费国别
     * 数据库字段:consume_country
     * 字符类型(100)
     */
    @ApiModelProperty("消费国别")
    private String consumeCountry;

    /**
     * 是否转运
     * 数据库字段:is_transit
     * 字符类型(20)
     */
    @ApiModelProperty("是否转运")
    private String isTransit;

    /**
     * 装运期限
     * 数据库字段:shipment_deadline
     * date
     */
    @ApiModelProperty("装运期限")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shipmentDeadline;

    /**
     * 装运期限-开始时间
     */
    @ApiModelProperty("装运期限-开始")
    private String shipmentDeadlineFrom;

    /**
     * 装运期限-结束时间
     */
    @ApiModelProperty("装运结束")
    private String shipmentDeadlineTo;

    /**
     * shipper
     * 数据库字段:shipper
     * 字符类型(1000)
     */
    @ApiModelProperty("shipper")
    private String shipper;

    /**
     * consignee
     * 数据库字段:consignee
     * 字符类型(1000)
     */
    @ApiModelProperty("consignee")
    private String consignee;

    /**
     * notify party
     * 数据库字段:notify_party
     * 字符类型(1000)
     */
    @ApiModelProperty("notify party")
    private String notifyParty;

    /**
     * freight
     * 数据库字段:freight
     * 字符类型(1000)
     */
    @ApiModelProperty("freight")
    private String freight;

    /**
     * 出境加工账册编号
     * 数据库字段:process_account_no
     * 字符类型(100)
     */
    @ApiModelProperty("出境加工账册编号")
    private String processAccountNo;

    /**
     * 装箱时间
     * 数据库字段:packing_time
     * date
     */
    @ApiModelProperty("装箱时间")
    private Date packingTime;

    /**
     * 至
     * 数据库字段:packing_time_to
     * date
     */
    @ApiModelProperty("至")
    private Date packingTimeTo;

    /**
     * 仓库地址
     * 数据库字段:warehouse_address
     * 字符类型(1000)
     */
    @ApiModelProperty("仓库地址")
    private String warehouseAddress;

    /**
     * 船名航次
     * 数据库字段:ship_name_voyage
     * 字符类型(200)
     */
    @ApiModelProperty("船名航次")
    private String shipNameVoyage;

    /**
     * 提单编号
     * 数据库字段:bill_no
     * 字符类型(200)
     */
    @ApiModelProperty("提单编号")
    private String billNo;

    /**
     * 联系人及电话
     * 数据库字段:contact_phone
     * 字符类型(200)
     */
    @ApiModelProperty("联系人及电话")
    private String contactPhone;

    /**
     * 提单日期
     * 数据库字段:bill_date
     * date
     */
    @ApiModelProperty("提单日期")
    private Date billDate;

    /**
     * 审核状态
     * 数据库字段:appr_status
     * 字符类型(20)
     */
    @ApiModelProperty("审核状态")
    private String apprStatus;

    /**
     * 币制
     * 数据库字段:curr
     * 字符类型(20)
     */
    @ApiModelProperty("币制")
    private String curr;

    /**
     * 确认时间
     * 数据库字段:confirm_time
     * timestamp
     */
    @ApiModelProperty("确认时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;
    /**
     * 确认时间-开始时间
     */
    @ApiModelProperty("确认时间-开始时间")
    private String confirmTimeFrom;

    /**
     * 确认时间-结束时间
     */
    @ApiModelProperty("确认时间-结束时间")
    private String confirmTimeTo;

    /**
     * 表体金额汇总
     * 数据库字段:total
     * 数值类型(19,4)
     */
    @ApiModelProperty("表体金额汇总")
    private BigDecimal total;


    /**
     * 客户代码
     * 数据库字段:customer_code
     * 字符类型(200)
     */
    @ApiModelProperty("客户代码")
    private String customerCode;



    @ApiModelProperty("外商合同表体总金额")
    private String totalMoney;

    @ApiModelProperty("签约日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;


}