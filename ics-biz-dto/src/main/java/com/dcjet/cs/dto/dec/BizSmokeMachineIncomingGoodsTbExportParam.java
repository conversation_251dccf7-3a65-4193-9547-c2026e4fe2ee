package com.dcjet.cs.dto.dec;

import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 导出传入参数
 *
 * <AUTHOR>
 * @date 2025-07-04 21:29:36
 */
@Getter
@Setter
@ApiModel(description = "查询传入参数")
public class  BizSmokeMachineIncomingGoodsTbExportParam extends ExcelExportParam {
    /**
     * 导出传入参数
     */
    @ApiModelProperty("查询参数")
    private  BizSmokeMachineIncomingGoodsTbParam exportColumns;
}
