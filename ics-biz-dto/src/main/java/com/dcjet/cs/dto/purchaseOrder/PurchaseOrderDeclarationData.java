package com.dcjet.cs.dto.purchaseOrder;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 通关申报数据实体类
 */
@Getter
@Setter
public class PurchaseOrderDeclarationData {
    
    /**
     * 单据号
     */
    @JsonProperty("billNo")
    private String billNo;
    
    /**
     * 单据序号
     */
    @JsonProperty("billSerialNo")
    private Integer billSerialNo;
    
    /**
     * 保完税标志
     */
    @JsonProperty("bondMark")
    private String bondMark;
    
    /**
     * 物料类型
     */
    @JsonProperty("gMark")
    private String gMark;
    
    /**
     * 企业料号
     */
    @JsonProperty("facGNo")
    private String facGNo;
    
    /**
     * 数量
     */
    @JsonProperty("qtyErp")
    private BigDecimal qtyErp;
    
    /**
     * 单价
     */
    @JsonProperty("decPrice")
    private BigDecimal decPrice;
    
    /**
     * 总价
     */
    @JsonProperty("decTotal")
    private BigDecimal decTotal;
    
    /**
     * ERP交易单位
     */
    @JsonProperty("unitErp")
    private String unitErp;
    
    /**
     * 币制
     */
    @JsonProperty("curr")
    private String curr;
    
    /**
     * 净重
     */
    @JsonProperty("netWt")
    private BigDecimal netWt;
    
    /**
     * 毛重
     */
    @JsonProperty("grossWet")
    private String grossWet;

    /**
     * 原产国
     */
    @JsonProperty("originCountry")
    private String originCountry;
    /**
     * 发票号
     */
    @JsonProperty("invNo")
    private String invNo;

    /**
     * 发票日期
     */
    @JsonProperty("POInvDate")
    private String POInvDate;

    /**
     * 采购订单号
     */
    @JsonProperty("PONumber")
    private String PONumber;

     /**
     * 采购订单行号
     */
    @JsonProperty("POLineNumber")
    private String POLineNumber;

     /**
     * 采购订单日期
     */
    @JsonProperty("PODate")
    private String PODate;
    /**
     * 采购数量
     */
    @JsonProperty("POQty")
    private String POQty;

     /**
     * 供应商代码
     */
    @JsonProperty("supplierCode")
    private String supplierCode;
    /**
     * 出口方式
     */
    @JsonProperty("ieMode")
    private String ieMode;

    /**
     * 法定数量
     */
    @JsonProperty("firstQty")
    private String firstQty;

    /**
     * 法定第二数量
     */
    @JsonProperty("secondQty")
    private String secondQty;

    /**
     * 备注
     */
    @JsonProperty("note")
    private String note;

    /**
     * ERP创建时间
     */
    @JsonProperty("lastUpdateTime")
    private String lastUpdateTime;

    /**
     * 传输批次号
     */
    @JsonProperty("tempOwner")
    private String tempOwner;

    /**
     * 征免方式
     */
    @JsonProperty("dutyMode")
    private String dutyMode;
    /**
     * 境内目的地
     */
    @JsonProperty("districtCode")
    private String districtCode;

    /**
     * 境内目的地行政区划
     */
    @JsonProperty("districtPostCode")
    private String districtPostCode;
    /**
     * 最终目的国
     */
    @JsonProperty("destinationCountry")
    private String destinationCountry;

    /**
     * 报关单归并序号
     */
    @JsonProperty("entryGNo")
    private String entryGNo;

    /**
     * 备注1
     */
    @JsonProperty("remark1")
    private String remark1;

    /**
     * 备注2
     */
    @JsonProperty("remark2")
    private String remark2;

    /**
     * 备注3
     */
    @JsonProperty("remark3")
    private String remark3;

    /**
     * 入库关联单号
     */
    @JsonProperty("inOutRelNo")
    private String inOutRelNo;

}
