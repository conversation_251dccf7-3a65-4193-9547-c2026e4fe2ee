package com.dcjet.cs.dto.deliveryOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizDeliveryOrderContainerListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 起始箱号
     */
	@XdoSize(max = 200, message = "起始箱号长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("起始箱号")
	private  String boxNoStart;
	/**
     * 结束箱号
     */
	@XdoSize(max = 200, message = "结束箱号长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("结束箱号")
	private  String boxNoEnd;
	/**
     * 商品名称
     */
	@XdoSize(max = 200, message = "商品名称长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	private  String productName;
	/**
     * 包装样式
     */
	@XdoSize(max = 200, message = "包装样式长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("包装样式")
	private  String packageStyle;
	/**
     * 毛重(KG)
     */
	@Digits(integer = 15, fraction = 4, message = "毛重(KG)必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("毛重(KG)")
	private  BigDecimal grossWt;
	/**
     * 净重(KG)
     */
	@Digits(integer = 15, fraction = 4, message = "净重(KG)必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("净重(KG)")
	private  BigDecimal netWt;
	/**
     * 皮重(KG)
     */
	@Digits(integer = 15, fraction = 4, message = "皮重(KG)必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("皮重(KG)")
	private  BigDecimal tareWt;
	/**
     * 长(M)
     */
	@Digits(integer = 15, fraction = 4, message = "长(M)必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("长(M)")
	private  BigDecimal longer;
	/**
     * 宽(M)
     */
	@Digits(integer = 15, fraction = 4, message = "宽(M)必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("宽(M)")
	private  BigDecimal whither;
	/**
     * 高(M)
     */
	@Digits(integer = 15, fraction = 4, message = "高(M)必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("高(M)")
	private  BigDecimal higher;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 箱数
     */
	@Digits(integer = 15, fraction = 4, message = "件数必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("件数")
	private  BigDecimal containerNum;

	private  String headId;
	private  BigDecimal qty;
}
