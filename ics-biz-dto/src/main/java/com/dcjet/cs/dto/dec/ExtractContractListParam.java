package com.dcjet.cs.dto.dec;

import com.dcjet.cs.dto.auxiliaryMaterials.BizENonStateAuxmatAggrContractListParam;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ExtractContractListParam implements Serializable {
    private static final long serialVersionUID = 1L;


    @NotNull(message = "请选择需要提取数据")
    private List<BizENonStateAuxmatAggrContractListParam> contractList;


    @NotNull(message = "表头ID不能为空")
    private String parentId;
}
