package com.dcjet.cs.dto.iEBusiness;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizAgencyAgreementParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 主键
     */
	@NotEmpty(message="主键不能为空！")
	@XdoSize(max = 40, message = "主键长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("主键")
	private  String id;
	/**
     * 业务类型
     */
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 合同号
     */
	@XdoSize(max = 60, message = "合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 协议编号
     */
	@XdoSize(max = 60, message = "协议编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
     * 客户
     */
	@XdoSize(max = 200, message = "客户长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String customer;
	/**
     * 签约日期
     */
	@ApiModelProperty("签约日期")
	private  Date signingDate;
	/**
    * 签约日期-开始
    */
	@ApiModelProperty("签约日期-开始")
	private String signingDateFrom;
	/**
    * 签约日期-结束
    */
	@ApiModelProperty("签约日期-结束")
    private String signingDateTo;
	/**
     * 签约地点
     */
	@XdoSize(max = 100, message = "签约地点长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点")
	private  String signingPlace;
	/**
     * 币种
     */
	@XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String currency;
	/**
     * 合同金额
     */
	@Digits(integer = 15, fraction = 4, message = "合同金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("合同金额")
	private  BigDecimal contractAmount;
	/**
     * 代理费率%
     */
	@Digits(integer = 17, fraction = 2, message = "代理费率%必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费率%")
	private  BigDecimal agencyRate;
	/**
     * 代理费用
     */
	@Digits(integer = 17, fraction = 2, message = "代理费用必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费用")
	private  BigDecimal agencyFee;
	/**
     * 建议授权签约人
     */
	@XdoSize(max = 60, message = "建议授权签约人长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("建议授权签约人")
	private  String suggestedSignatory;
	/**
     * 协议条款
     */
	@XdoSize(max = 1000, message = "协议条款长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
     * 制单人
     */
	@XdoSize(max = 10, message = "制单人长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("制单人")
	private  String maker;
	/**
     * 制单日期
     */
	@ApiModelProperty("制单日期")
	private  Date makeDate;
	/**
    * 制单日期-开始
    */
	@ApiModelProperty("制单日期-开始")
	private String makeDateFrom;
	/**
    * 制单日期-结束
    */
	@ApiModelProperty("制单日期-结束")
    private String makeDateTo;
	/**
     * 单据状态
     */
	@XdoSize(max = 10, message = "单据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String billStatus;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
    * 确认时间-开始
    */
	@ApiModelProperty("确认时间-开始")
	private String confirmTimeFrom;
	/**
    * 确认时间-结束
    */
	@ApiModelProperty("确认时间-结束")
    private String confirmTimeTo;
	/**
     * 审批状态
     */
	@XdoSize(max = 10, message = "审批状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审批状态")
	private  String approvalStatus;
	/**
     * 扩展字段1
     */
	@XdoSize(max = 200, message = "扩展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@XdoSize(max = 200, message = "扩展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@XdoSize(max = 200, message = "扩展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@XdoSize(max = 200, message = "扩展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@XdoSize(max = 200, message = "扩展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@XdoSize(max = 200, message = "扩展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@XdoSize(max = 200, message = "扩展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@XdoSize(max = 200, message = "扩展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@XdoSize(max = 200, message = "扩展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@XdoSize(max = 200, message = "扩展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段10")
	private  String extend10;
	/**
     * 父节点
     */
	@XdoSize(max = 200, message = "父节点长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("父节点")
	private  String parentId;
	/**
     * 企业编码
     */
	@NotEmpty(message="企业编码不能为空！")
	@XdoSize(max = 10, message = "企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@NotEmpty(message="创建人部门编码不能为空！")
	@XdoSize(max = 10, message = "创建人部门编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
     * 创建人
     */
	@NotEmpty(message="创建人不能为空！")
	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */
	@NotNull(message="创建时间不能为空！")
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@XdoSize(max = 50, message = "最后修改人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人")
	private  String updateBy;

	private List<String> ids;
}
