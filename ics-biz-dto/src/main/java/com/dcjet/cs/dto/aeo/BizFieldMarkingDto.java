package com.dcjet.cs.dto.aeo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 表单字段标记信息返回信息
 *
 * <AUTHOR>
 * @date 2024
 */
@ApiModel(value = "表单字段标记信息返回信息")
@Setter
@Getter
public class BizFieldMarkingDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 表单记录ID，关联具体的业务记录
     */
    @ApiModelProperty("表单记录ID")
    private String sid;

    /**
     * 表单类型，用于区分不同的表单页面
     */
    @ApiModelProperty("表单类型")
    private String formType;

    /**
     * 字段标记数据，JSON格式存储fieldMarkings.value的完整内容
     */
    @ApiModelProperty("字段标记数据")
    private String fieldMarkings;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private String createUser;

    /**
     * 更新用户
     */
    @ApiModelProperty("更新用户")
    private String updateUser;

    /**
     * 备注信息，可用于存储额外的标记说明
     */
    @ApiModelProperty("备注信息")
    private String remark;
}