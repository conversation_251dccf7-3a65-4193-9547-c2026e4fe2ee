package com.dcjet.cs.dto.quo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-5-20
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizQuotationParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;

	/**
     * 创建人
     */

	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@XdoSize(max = 50, message = "创建人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
     * 最后修改人
     */
	@XdoSize(max = 50, message = "最后修改人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
     * 最后修改人名称
     */
	@XdoSize(max = 50, message = "最后修改人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@XdoSize(max = 50, message = "企业编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@XdoSize(max = 50, message = "创建人部门编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@XdoSize(max = 200, message = "拓展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@XdoSize(max = 200, message = "拓展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@XdoSize(max = 200, message = "拓展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@XdoSize(max = 200, message = "拓展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@XdoSize(max = 200, message = "拓展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@XdoSize(max = 200, message = "拓展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@XdoSize(max = 200, message = "拓展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@XdoSize(max = 200, message = "拓展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@XdoSize(max = 200, message = "拓展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@XdoSize(max = 200, message = "拓展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
     * 业务类型
     */
	@XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 商品名称
     */
	@XdoSize(max = 160, message = "商品名称长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 商品类别
     */
	@XdoSize(max = 160, message = "商品类别长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品类别")
	private  String merchandiseCategories;
	/**
     * 产品型号
     */
	@XdoSize(max = 160, message = "产品型号长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("产品型号")
	private  String productModel;
	/**
     * 规格
     */
	@XdoSize(max = 400, message = "规格长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("规格")
	private  String specifications;
	/**
     * 克重
     */
	@Digits(integer = 14, fraction = 5, message = "克重必须为数字,整数位最大14位,小数最大5位!")
	@ApiModelProperty("克重")
	private  BigDecimal grammage;
	/**
     * 材料编号
     */
	@XdoSize(max = 100, message = "材料编号长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("材料编号")
	private  String materialNo;
	/**
     * 计量单位
     */
	@XdoSize(max = 40, message = "计量单位长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("计量单位")
	private  String unit;
	/**
     * 进口计量单位
     */
	@XdoSize(max = 40, message = "进口计量单位长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("进口计量单位")
	private  String unitI;
	/**
     * 供应商
     */
	@XdoSize(max = 400, message = "供应商长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String merchantCode;
	/**
     * 价格条款
     */
	@XdoSize(max = 40, message = "价格条款长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
     * 指运港
     */
	@XdoSize(max = 100, message = "指运港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("指运港")
	private  String destinationPort;
	/**
     * 进口单价
     */
	@Digits(integer = 11, fraction = 8, message = "进口单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("进口单价")
	private  BigDecimal importUnitPrice;
	/**
     * 单价/盘（USD）
     */
	@Digits(integer = 11, fraction = 8, message = "单价/盘（USD）必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("单价/盘（USD）")
	private  BigDecimal unitTrayPrice;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 汇率
     */
	@Digits(integer = 13, fraction = 6, message = "汇率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
     * 关税率%
     */
	@Digits(integer = 13, fraction = 6, message = "关税率%必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("关税率%")
	private  BigDecimal tariffRate;
	/**
     * 关税金额
     */
	@Digits(integer = 17, fraction = 2, message = "关税金额必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("关税金额")
	private  BigDecimal tariffPrice;
	/**
     * 增值税率%
     */
	@Digits(integer = 13, fraction = 6, message = "增值税率%必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("增值税率%")
	private  BigDecimal vat;
	/**
     * 总公司代理费率%
     */
	@Digits(integer = 15, fraction = 4, message = "总公司代理费率%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总公司代理费率%")
	private  BigDecimal headAgencyFeeRate;
	/**
     * 总公司代理费
     */
	@Digits(integer = 17, fraction = 2, message = "总公司代理费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("总公司代理费")
	private  BigDecimal headAgencyFeePrice;
	/**
     * 货代费单价
     */
	@Digits(integer = 15, fraction = 4, message = "货代费单价必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("货代费单价")
	private  BigDecimal freightForwardingFeePrice;
	/**
     * 货代费用
     */
	@Digits(integer = 17, fraction = 2, message = "货代费用必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("货代费用")
	private  BigDecimal freightForwardingFee;
	/**
     * 保险费
     */
	@Digits(integer = 17, fraction = 2, message = "保险费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("保险费")
	private  BigDecimal insuranceFee;
	/**
     * 购进成本
     */
	@Digits(integer = 17, fraction = 2, message = "购进成本必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("购进成本")
	private  BigDecimal purchaseCost;
	/**
     * 仓储运输及税额
     */
	@Digits(integer = 17, fraction = 2, message = "仓储运输及税额必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("仓储运输及税额")
	private  BigDecimal storageTransportTax;
	/**
     * 毛利
     */
	@Digits(integer = 17, fraction = 2, message = "毛利必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("毛利")
	private  BigDecimal grossMargin;
	/**
     * 不含税单价
     */
	@Digits(integer = 17, fraction = 2, message = "不含税单价必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("不含税单价")
	private  BigDecimal priceExcludingTax;
	/**
     * 人民币单价（含税）
     */
	@Digits(integer = 17, fraction = 2, message = "人民币单价（含税）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("人民币单价（含税）")
	private  BigDecimal priceRmb;
	/**
     * 备注
     */
	@XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 数据状态
     */
	@XdoSize(max = 20, message = "数据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("数据状态")
	private  String status;
	private  String stype;

	private String insertTimeFrom;
	private String insertTimeTo;

	List<String> sids;
}
