package com.dcjet.cs.dto.params;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Setter
@Getter
@ApiModel(value = "城市参数模型")
public class CityParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;

    /**
     * 参数代码
     */
    @ApiModelProperty("参数代码")
    private String paramCode;

    /**
     * 城市中文名称
     */
    @ApiModelProperty("城市中文名称")
    @NotNull(message = "城市中文名称不能为空！")
    @XdoSize(max = 50, message = "城市中文名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
    private String cityCnName;

    /**
     * 城市英文名称
     */
    @ApiModelProperty("城市英文名称")
    @XdoSize(max = 80, message = "城市英文名称长度不能超过80位字节长度(一个汉字2位字节长度)!")
    private String cityEnName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String note;

    /**
     * 扩展字段1
     */
    @XdoSize(max = 50, message = "国别中文长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @XdoSize(max = 50, message = "国别英文长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;
}
