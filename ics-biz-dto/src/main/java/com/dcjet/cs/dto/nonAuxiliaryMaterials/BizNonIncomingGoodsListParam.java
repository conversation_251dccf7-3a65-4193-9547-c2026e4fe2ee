package com.dcjet.cs.dto.nonAuxiliaryMaterials;


import com.dcjet.cs.dto.utils.ValidatorUtil;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 进过管理-表体列表
 */
@Getter
@Setter
@ApiModel(value = "进过管理-表体列表-传入参数")
public class BizNonIncomingGoodsListParam implements Serializable{
    /**
     * 主键id
     * 数据库字段:id
     * 字符类型(80)
     */
    @NotNull(message = "主键id不能为空！")
    @XdoSize(max = 40, message = "主键id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主键id")
    private String id;

    private List<String> sids;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(120)
     */
    @XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "企业10位编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "组织机构代码长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * 字符类型(80)
     */
    @XdoSize(max = 40, message = "父级id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("父级id")
    private String parentId;

    /**
     * 创建人
     * 数据库字段:create_by
     * 字符类型(100)
     */
    @NotNull(message = "创建人不能为空！")
    @XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * 日期类型(6)
     */
    @NotNull(message = "创建时间不能为空！")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private Date createTimeFrom;

    /**
    * 创建时间-结束时间
    */
    @ApiModelProperty("创建时间-结束时间")
    private Date createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "更新人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private Date updateTimeFrom;

    /**
    * 更新时间-结束时间
    */
    @ApiModelProperty("更新时间-结束时间")
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "插入用户名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "更新用户名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "扩展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 商品名称
     * 数据库字段:goods_name
     * 字符类型(160)
     */
    @XdoSize(max = 80, message = "商品名称长度不能超过80位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品名称")
    private String goodsName;

    /**
     * 规格
     * 数据库字段:product_model
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "规格长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("规格")
    private String productModel;

    /**
     * 数量
     * 数据库字段:quantity
     * 数值类型(19,4)
     */
    @Digits(integer = 13, fraction = 6, message = "数量必须为数字,整数位最大13位,小数最大6位!")
    @ApiModelProperty("数量")
    private BigDecimal quantity;

    /**
     * 单位
     * 数据库字段:unit
     * 字符类型(60)
     */
    @XdoSize(max = 20, message = "单位长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 单价
     * 数据库字段:unit_price
     * 数值类型(19,8)
     */
    @Digits(integer = 14, fraction = 5, message = "单价必须为数字,整数位最大14位,小数最大5位!")
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 金额
     * 数据库字段:amount
     * 数值类型(19,4)
     */
    @Digits(integer = 17, fraction = 2, message = "金额必须为数字,整数位最大17位,小数最大2位!")
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 交货日期
     * 数据库字段:delivery_date
     * 日期类型(6)
     */
    @ApiModelProperty("交货日期")
    private Date deliveryDate;
    /**
     * 交货日期-开始时间
     */
    @ApiModelProperty("交货日期-开始时间")
    private Date deliveryDateFrom;

    /**
    * 交货日期-结束时间
    */
    @ApiModelProperty("交货日期-结束时间")
    private Date deliveryDateTo;

    /**
     * 总价折美元
     * 数据库字段:total_usd
     * 数值类型(19,4)
     */
    @Digits(integer = 15, fraction = 4, message = "总价折美元必须为数字,整数位最大15位,小数最大4位!")
    @ApiModelProperty("总价折美元")
    private BigDecimal totalUsd;

    /**
     * 备注
     * 数据库字段:remarks
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 表头head_id
     * 数据库字段:head_id
     * 字符类型(40)
     */
    @XdoSize(max = 40, message = "表头head_id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("表头head_id")
    private String headId;

    /**
     * 进口数量
     * 数据库字段:in_quantity
     * 数值类型(19,6)
     */
    @Digits(integer = 13, fraction = 6, message = "进口数量必须为数字,整数位最大13位,小数最大6位!")
    @ApiModelProperty("进口数量")
    private BigDecimal inQuantity;

    /**
     * 进口单位
     * 数据库字段:in_unit
     * 字符类型(60)
     */
    @XdoSize(max = 30, message = "进口单位长度不能超过30位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("进口单位")
    private String inUnit;

    /**
     * 币种
     * 数据库字段:curr
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币种")
    private String curr;

    /**
     * 进口发票号
     * 数据库字段:invoice_no
     * 字符类型(60)
     */
    @XdoSize(max = 60, message = "进口发票号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("进口发票号")
    private String invoiceNo;



    /**
     * 合同表体的ID
     * 字符类型(40)
     * 非必填
     */
    @ApiModelProperty("合同表体的ID")
    private String contractListId;





    /**
     * 能否提交
     * @return
     */
    public String canSubmit(int index) {
        return ValidatorUtil.validation(index,this);
    }

}