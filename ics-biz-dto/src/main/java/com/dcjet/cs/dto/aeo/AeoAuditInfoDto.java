package com.dcjet.cs.dto.aeo;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-18
 */
@ApiModel(value = "AEO审核信息返回信息")
@Setter
@Getter
public class AeoAuditInfoDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;

    /**
     * 审批类型
     */
    @ApiModelProperty("审批类型")
    private String apprType;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private String status;

    /**
     * 内审员
     */
    @ApiModelProperty("内审员")
    private String apprUser;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date apprDate;

    /**
     * 审核意见
     */
    @ApiModelProperty("审核意见")
    private String apprNote;

    /**
     * 审批单据SID
     */
    @ApiModelProperty("审批单据SID")
    private String businessSid;

    /**
     * 企业代码
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;

    /**
     * 审批类型
     */
    @ApiModelProperty("审批类型")
    private String apprTypeName;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private String StatusName;

    @ApiModelProperty("单据内部编号")
    private String emsListNo;

    @ApiModelProperty("进口提单制单员")
    private String erpInsertUser;

    @ApiModelProperty("进口提单制单员")
    private String erpInsertUserName;

    @ApiModelProperty("录入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date erpInsertTime;

    @ApiModelProperty("报关单号")
    private String entryNo;

    @ApiModelProperty("自定义参数名")
    private String apprStatusName;

    public String getErpInsertUser() {
        return (this.erpInsertUser == null ? "" : this.erpInsertUser) + (this.erpInsertUserName == null ? "" : "(" + this.erpInsertUserName + ")");
    }

    /**
     * 合同协议号
     */
    @ApiModelProperty("合同协议号")
    private String contrNo;

    /**
     * 备案料号
     */
    @ApiModelProperty("备案料号")
    private String copGNo;

    /**
     * 企业料号
     */
    @ApiModelProperty("企业料号")
    private String facGNo;

	/**
	 * 企业料号
	 */
	@ApiModelProperty("申报单位编码")
	private String declareCode;

	/**
	 * 企业料号
	 */
	@ApiModelProperty("申报单位名称")
	private String declareName;

	/**
	 * 企业料号
	 */
	@ApiModelProperty("受托单位统一社会信用代码")
	private String entrustedCoScc;

	/**
	 * 提运单号
	 */
	@ApiModelProperty("提运单号")
	private String hawb;
}
