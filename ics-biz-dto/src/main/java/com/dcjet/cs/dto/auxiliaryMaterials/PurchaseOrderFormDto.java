package com.dcjet.cs.dto.auxiliaryMaterials;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

public class PurchaseOrderFormDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表头
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Head {

        /**
         * 当前日期
         */
        private String currentDate;

        /**
         * 制单人
         */
        private String createByName;

        /**
         * 制单日期
         */
        private String createDate;

        /**
         * 订货编号
         */
        private String orderNo;
    }

    /**
     * 表体
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Body {
        /**
         * 品名
         */
        private String productName;

        /**
         * 供应商
         */
        private String supplier;

        /**
         * 型号
         */
        private String productModel;

        /**
         * 规格
         */
        private String specifications;

        /**
         * 克重
         */
        private String weight;

        /**
         * 单位
         */
        private String unit;

        /**
         * 数量
         */
        private String qty;

        /**
         * 运输方式
         */
        private String transportMode;

        /**
         * 到货时间
         */
        private String arrivedTime;

        /**
         * 单价
         */
        private String unitPrice;

        /**
         * 港口
         */
        private String port;

        /**
         * 最终用户
         */
        private String finalUser;

        /**
         * 材料编号
         */
        private String materialNo;

        /**
         * 申请公司
         */
        private String applicantCompany;

        /**
         * 订货编号
         */
        private String orderNo;

        /**
         * 是否新品
         */
        private String isNew;
    }
}
