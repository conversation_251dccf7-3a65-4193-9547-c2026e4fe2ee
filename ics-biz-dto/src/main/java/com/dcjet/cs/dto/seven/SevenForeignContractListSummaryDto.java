package com.dcjet.cs.dto.seven;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("外商合同表体汇总信息传输模型")
@Accessors(chain = true)
public class SevenForeignContractListSummaryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private BigDecimal totalQty;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalMoneyAmount;

    /**
     * 总箱数
     */
    @ApiModelProperty("总箱数")
    private BigDecimal totalBoxNum;

    /**
     * 总毛重
     */
    @ApiModelProperty("总毛重")
    private BigDecimal totalGrossWeight;
}