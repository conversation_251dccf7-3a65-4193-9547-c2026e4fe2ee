package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * （第7条线）出料加工进口薄片-装箱列表
 */
@Getter
@Setter
@ApiModel(value = "（第7条线）出料加工进口薄片-装箱列表-新增传入参数")
public class BizBpAnalyseOrderListBoxAddParam implements Serializable{

    /**
     * 表头Id
     */
    @NotNull(message = "表头Id不能为空")
    private String parentId;


    /**
     * 集装箱规格
     * 数据库字段:container_spec
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "集装箱规格长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("集装箱规格")
    private String containerSpec;

    /**
     * 集装箱数
     * 数据库字段:container_count
     * 数值类型(19,0)
     */
    @Digits(integer = 19, fraction = 0, message = "集装箱数必须为数字,整数位最大19位,小数最大0位!")
    @ApiModelProperty("集装箱数")
    private BigDecimal containerCount;

    /**
     * 集装箱号
     * 数据库字段:container_no
     * 字符类型(1000)
     */
    @XdoSize(max = 500, message = "集装箱号长度不能超过500位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("集装箱号")
    private String containerNo;


    /**
     * 集装箱列表
     */
    @NotNull(message = "表体商品信息不能为空")
    private List<BizBpAnalyseOrderListBoxParam> list;

}