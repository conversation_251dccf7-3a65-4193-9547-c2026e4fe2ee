package com.dcjet.cs.dto.equipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("外商合同表体汇总信息传输模型")
@Accessors(chain = true)
public class ForeignContractListSummaryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private BigDecimal totalQty;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalMoneyAmount;

    /**
     * 总价折美元
     */
    @ApiModelProperty("总价折美元")
    private BigDecimal totalConvertedDollars;
}