package com.dcjet.cs.dto.deliveryOrder;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-7
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizDeliveryOrderInsuranceInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 最后修改人名称
      */
    @ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 拓展字段1
      */
    @ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
      * 拓展字段2
      */
    @ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
      * 拓展字段3
      */
    @ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
      * 拓展字段4
      */
    @ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
      * 拓展字段5
      */
    @ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
      * 拓展字段6
      */
    @ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
      * 拓展字段7
      */
    @ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
      * 拓展字段8
      */
    @ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
      * 拓展字段9
      */
    @ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
      * 拓展字段10
      */
    @ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 编号
      */
    @ApiModelProperty("编号")
	private  String purchaseOrderNo;
	/**
      * 保险公司
      */
    @ApiModelProperty("保险公司")
	private  String insuranceCompany;
	/**
      * 被保险人
      */
    @ApiModelProperty("被保险人")
	private  String insurant;
	/**
      * 发票抬头
      */
    @ApiModelProperty("发票抬头")
	private  String invoiceTitle;
	/**
      * 运输工具名称
      */
    @ApiModelProperty("运输工具名称")
	private  String trafName;
	/**
      * 开航日期
      */
    @ApiModelProperty("开航日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date startShipmentDate;
	/**
      * 运输路线自
      */
    @ApiModelProperty("运输路线自")
	private  String trafRouteFrom;
	/**
      * 经
      */
    @ApiModelProperty("经")
	private  String trafRoutePass;
	/**
      * 至
      */
    @ApiModelProperty("至")
	private  String trafRouteTo;
	/**
      * 投保险别
      */
    @ApiModelProperty("投保险别")
	private  String insuranceType;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;
	/**
      * 投保加成%
      */
    @ApiModelProperty("投保加成%")
	private  BigDecimal insuranceMarkup;
	/**
      * 保险金额
      */
    @ApiModelProperty("保险金额")
	private  BigDecimal insuranceAmount;
	/**
      * 保险费率
      */
    @ApiModelProperty("保险费率")
	private  BigDecimal insuranceRate;
	/**
      * 保费
      */
    @ApiModelProperty("保费")
	private  BigDecimal insuranceFee;
	/**
      * 投保日期
      */
    @ApiModelProperty("投保日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insuranceDate;
	/**
      * 投保日期
      */
    @ApiModelProperty("投保日期")
	private  BigDecimal freightFee;
	/**
      * 投保日期
      */
    @ApiModelProperty("投保日期")
	private  String freightCurr;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 表头id
      */
    @ApiModelProperty("表头id")
	private  String headId;
	/**
      * 分析单id
      */
    @ApiModelProperty("分析单id")
	private  String analysisId;
	private  BigDecimal exchangeRate;
}
