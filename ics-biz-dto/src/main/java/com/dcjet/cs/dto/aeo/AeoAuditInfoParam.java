package com.dcjet.cs.dto.aeo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter
@Getter
@ApiModel(value = "AEO审核信息传入参数")
public class AeoAuditInfoParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;

    /**
     * 审批类型
     */
    @NotEmpty(message = "{审批类型不能为空！}")
    @XdoSize(max = 2, message = "{审批类型长度不能超过2位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审批类型")
    private String apprType;

    /**
     * 审批状态
     */
    @XdoSize(max = 2, message = "{审批状态长度不能超过2位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审批状态")
    private String status;

    /**
     * 内审员
     */
    @XdoSize(max = 50, message = "{内审员长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("内审员")
    private String apprUser;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date apprDate;

    /**
     * 审批时间-开始
     */
    @ApiModelProperty("审批时间-开始")
    private String apprDateFrom;

    /**
     * 审批时间-结束
     */
    @ApiModelProperty("审批时间-结束")
    private String apprDateTo;

    /**
     * 审核意见
     */
    @XdoSize(max = 255, message = "{审核意见长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审核意见")
    private String apprNote;

    /**
     * 审批单据SID
     */
    @XdoSize(max = 255, message = "{审批单据SID长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("审批单据SID")
    private String businessSid;

    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    private String insertTimeFrom;

    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    private String insertTimeTo;

    /**
     * 更新时间-开始
     */
    @ApiModelProperty("更新时间-开始")
    private String updateTimeFrom;

    /**
     * 更新时间-结束
     */
    @ApiModelProperty("更新时间-结束")
    private String updateTimeTo;

    @ApiModelProperty("单据内部编号")
    private String emsListNo;

    @ApiModelProperty("提单制单员")
    private String erpInsertUser;

    @ApiModelProperty("录入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date erpInsertTime;

    /**
     * 创建时间-开始
     */
    @ApiModelProperty("录入日期-开始")
    private String erpInsertTimeFrom;

    /**
     * 创建时间-结束
     */
    @ApiModelProperty("录入日期-结束")
    private String erpInsertTimeTo;

    @ApiModelProperty("报关单号")
    private String entryNo;

    /**
     * 合同协议号
     */
    @ApiModelProperty("合同协议号")
    private String contrNo;

    /**
     * 备案料号
     */
    @ApiModelProperty("备案料号")
    private String copGNo;

    /**
     * 企业料号
     */
    @ApiModelProperty("企业料号")
    private String facGNo;

    /**
     * 单据sid数组
     */
    @ApiModelProperty("单据sid数组")
    private List<String> sids;

    /**
     * 审核方式
     */
    @ApiModelProperty("审核方式")
    private String auditType;
    /**
     * 审核位置 0 列表 1 表体
     */
    @ApiModelProperty("审核位置")
    private String from;
}
