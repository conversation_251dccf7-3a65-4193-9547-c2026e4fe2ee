package com.dcjet.cs.dto.iEBusiness;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-7
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizAgencyAgreementDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String id;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 协议编号
      */
    @ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
      * 客户
      */
    @ApiModelProperty("客户")
	private  String customer;
	/**
      * 签约日期
      */
    @ApiModelProperty("签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date signingDate;
	/**
      * 签约地点
      */
    @ApiModelProperty("签约地点")
	private  String signingPlace;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String currency;
	/**
      * 合同金额
      */
    @ApiModelProperty("合同金额")
	private  BigDecimal contractAmount;
	/**
      * 代理费率%
      */
    @ApiModelProperty("代理费率%")
	private  BigDecimal agencyRate;
	/**
      * 代理费用
      */
    @ApiModelProperty("代理费用")
	private  BigDecimal agencyFee;
	/**
      * 建议授权签约人
      */
    @ApiModelProperty("建议授权签约人")
	private  String suggestedSignatory;
	/**
      * 协议条款
      */
    @ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
      * 制单人
      */
    @ApiModelProperty("制单人")
	private  String maker;
	/**
      * 制单日期
      */
    @ApiModelProperty("制单日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date makeDate;
	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String billStatus;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 审批状态
      */
    @ApiModelProperty("审批状态")
	private  String approvalStatus;
	/**
      * 扩展字段1
      */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
      * 扩展字段2
      */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
      * 扩展字段3
      */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
      * 扩展字段4
      */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
      * 扩展字段5
      */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
      * 扩展字段6
      */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
      * 扩展字段7
      */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
      * 扩展字段8
      */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
      * 扩展字段9
      */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
      * 扩展字段10
      */
    @ApiModelProperty("扩展字段10")
	private  String extend10;
	/**
      * 父节点
      */
    @ApiModelProperty("父节点")
	private  String parentId;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 最后修改人
      */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
}
