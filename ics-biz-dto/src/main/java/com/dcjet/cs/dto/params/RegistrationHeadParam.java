package com.dcjet.cs.dto.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class RegistrationHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;

	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;


	@XdoSize(max = 50, message = "贸易条款文本长度不能超过50位字节长度(一个汉字2位字节长度)!")
	private  String extend1;

	/**
	 * 单据号
	 */
	@XdoSize(max = 60, message = "单据号长度不能超过60位字节长度!")
	@ApiModelProperty("单据号")
	private String documentNo;

	/**
	 * 业务类型
	 */
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度!")
	@ApiModelProperty("业务类型")
	private String businessType;

	/**
	 * 预付标志
	 */
	@XdoSize(max = 10, message = "预付标志长度不能超过60位字节长度!")
	@ApiModelProperty("预付标志")
	private String advanceFlag;

	/**
	 * 付款客户
	 */
	@XdoSize(max = 200, message = "付款客户长度不能超过200位字节长度!")
	@ApiModelProperty("付款客户")
	private String payerName;

	/**
	 * 单据状态
	 */
	@XdoSize(max = 10, message = "单据状态长度不能超过10位字节长度!")
	@ApiModelProperty("单据状态")
	private String documentStatus;

	/**
	 * 银行手续费
	 */
	@Digits(integer = 17, fraction = 2, message = "手续费整数最多17位，小数最多2位")
	@ApiModelProperty("银行手续费")
	private BigDecimal bankFee;



	/**
	 * 部门
	 */
	@ApiModelProperty("部门")
	private String department;

	/**
	 * 币种
	 */
	@ApiModelProperty("币种")
	private String currency;



	/**
	 * 委托单位
	 */
	@ApiModelProperty("委托单位")
	private String entrustCompany;



	/**
	 * 收款金额
	 */
	@ApiModelProperty("收款金额")
	private BigDecimal paymentAmount;

	/**
	 * 发送财务系统
	 */
	@ApiModelProperty("发送财务系统")
	private String financeFlag;

	/**
	 * 业务日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("业务日期")
	private Date businessDate;

	/**
	 * 是否红冲
	 */
	@ApiModelProperty("是否红冲")
	private String reversalFlag;


	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;


	private String contractNo;
	private String orderNumber;

	/**
	 * 修改时间-开始
	 */
	@ApiModelProperty("修改时间-开始")
	private String updateTimeFrom;

	/**
	 * 修改时间-结束
	 */
	@ApiModelProperty("修改时间-结束")
	private String updateTimeTo;
	private String updateUserName;
}
