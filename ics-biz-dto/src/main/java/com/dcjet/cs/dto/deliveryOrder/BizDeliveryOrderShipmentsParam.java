package com.dcjet.cs.dto.deliveryOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizDeliveryOrderShipmentsParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 装运人SHIPPER
     */
	@XdoSize(max = 1000, message = "装运人SHIPPER长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装运人SHIPPER")
	private  String shipper;
	/**
     * 收件人CONSIGNEE
     */
	@XdoSize(max = 1000, message = "收件人CONSIGNEE长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("收件人CONSIGNEE")
	private  String consignee;
	/**
     * 通知人NOTIFY PARTY
     */
	@XdoSize(max = 1000, message = "通知人NOTIFY PARTY长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("通知人NOTIFY PARTY")
	private  String notifyParty;
	/**
     * 装运港
     */
	@XdoSize(max = 100, message = "装运港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装运港")
	private  String portOfShipment;
	/**
     * 目的地/港
     */
	@XdoSize(max = 100, message = "目的地/港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("目的地/港")
	private  String portOfDestination;
	/**
     * 装运期限
     */
	@ApiModelProperty("装运期限")
	private  Date destinationDate;
	/**
     * 仓库地址
     */
	@XdoSize(max = 1000, message = "仓库地址长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("仓库地址")
	private  String warehouseAddress;
	/**
     * 联系人
     */
	@XdoSize(max = 200, message = "联系人长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("联系人")
	private  String contactPerson;
	/**
     * 电话
     */
	@XdoSize(max = 200, message = "电话长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("电话")
	private  String contactPhone;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	private  String headId;
	private  String analysisId;
}
