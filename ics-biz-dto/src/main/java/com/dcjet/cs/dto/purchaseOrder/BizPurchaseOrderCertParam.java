package com.dcjet.cs.dto.purchaseOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizPurchaseOrderCertParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 准运证编号
     */
	@XdoSize(max = 200, message = "准运证编号长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("准运证编号")
	private  String transportPermit;
	/**
     * 出货确认日期
     */
	@ApiModelProperty("出货确认日期")
	private  Date shipmentConfirmDate;
	/**
     * 报关单号
     */
	@XdoSize(max = 40, message = "报关单号长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("报关单号")
	private  String entryNo;
	/**
     * 申报日期
     */
	@ApiModelProperty("申报日期")
	private  Date declarationDate;
	/**
     * 放行日期
     */
	@ApiModelProperty("放行日期")
	private  Date releaseDate;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;

	private  String headId;
}
