package com.dcjet.cs.dto.dec;


import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * （3）烟机设备-进货单-表头数据
 */
@Getter
@Setter
@ApiModel(value = "（3）烟机设备-进货单-表体数据-通用查询对象 ")
public class BizSmokeMachineIncomingGoodsListCommonDto implements Serializable{


    /**
     * 单位列表
     */
    private List<Map<String,String>> unitList;


    /**
     * 币制列表
     */
    private List<Map<String,String>> currList;


    /**
     * 客户列表
     */
    private List<Map<String,String>> customerList;


    /**
     * 国家信息
     */
    private List<Map<String,String>> countryList;


    /**
     * 投保险别
     */
    private List<Map<String,String>> insuranceCategoryList;


}