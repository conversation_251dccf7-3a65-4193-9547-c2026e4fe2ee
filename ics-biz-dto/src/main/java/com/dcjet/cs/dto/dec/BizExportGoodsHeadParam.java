package com.dcjet.cs.dto.dec;


import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.dto.aeo.AuditUserParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import com.xdo.validation.annotation.XdoSize;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 第9条线-非国营贸易出口辅料-出货信息表头
 */
@Getter
@Setter
@ApiModel(value = "第9条线-非国营贸易出口辅料-出货信息表头-传入参数")
public class BizExportGoodsHeadParam extends ApprovalFlowParam implements Serializable{

    @ApiModelProperty("主键id")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(240)
     */
    @XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "数据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "版本号长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "企业10位编码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "组织机构代码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级id
     * 数据库字段:parent_id
     * uuid
     */
    @ApiModelProperty("父级id")
    private String parentId;


    @XdoSize(max = 100, message = "创建人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String createBy;


    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private String createTimeFrom;

    /**
    * 创建时间-结束时间
    */
    @ApiModelProperty("创建时间-结束时间")
    private String createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新人长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
    * 更新时间-结束时间
    */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "插入用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "更新用户名长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段1长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段2长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段3长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段4长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段5长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段6长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段7长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段8长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段9长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(800)
     */
    @XdoSize(max = 400, message = "扩展字段10长度不能超过400位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 出货单号
     * 数据库字段:export_no
     * 字符类型(120)
     */
    @NotNull(message = "出货单号不能为空！")
    @XdoSize(max = 60, message = "出货单号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("出货单号")
    private String exportNo;

    /**
     * 合同号
     * 数据库字段:contract_no
     * 字符类型(120)
     */
    @NotNull(message = "合同号不能为空！")
    @XdoSize(max = 60, message = "合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("合同号")
    private String contractNo;

    /**
     * 客户
     * 数据库字段:customer
     * 字符类型(400)
     */
    @NotNull(message = "客户不能为空！")
    @XdoSize(max = 200, message = "客户长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("客户")
    private String customer;

    /**
     * 客户地址
     * 数据库字段:customer_address
     * 字符类型(120)
     */
    @NotNull(message = "客户地址不能为空！")
    @XdoSize(max = 60, message = "客户地址长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("客户地址")
    private String customerAddress;

    /**
     * 供应商
     * 数据库字段:supplier
     * 字符类型(400)
     */
    @NotNull(message = "供应商不能为空！")
    @XdoSize(max = 200, message = "供应商长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("供应商")
    private String supplier;

    /**
     * 贸易国别
     * 数据库字段:trade_country
     * 字符类型(120)
     */
    @NotNull(message = "贸易国别不能为空！")
    @XdoSize(max = 60, message = "贸易国别长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("贸易国别")
    private String tradeCountry;

    /**
     * 经营单位
     * 数据库字段:manage_unit
     * 字符类型(400)
     */
    @NotNull(message = "经营单位不能为空！")
    @XdoSize(max = 200, message = "经营单位长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("经营单位")
    private String manageUnit;

    /**
     * 付款方式
     * 数据库字段:payment_type
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "付款方式长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("付款方式")
    private String paymentType;

    /**
     * 币种
     * 数据库字段:currency
     * 字符类型(20)
     */
    @NotNull(message = "币种不能为空！")
    @XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币种")
    private String currency;

    /**
     * 运输方式
     * 数据库字段:transport_type
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "运输方式长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运输方式")
    private String transportType;

    /**
     * 价格条款
     * 数据库字段:price_terms
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "价格条款长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("价格条款")
    private String priceTerms;

    /**
     * 价格条款对应港口
     * 数据库字段:price_terms_port
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "价格条款对应港口长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("价格条款对应港口")
    private String priceTermsPort;

    /**
     * 发货单位
     * 数据库字段:delivery_unit
     * 字符类型(120)
     */
    @NotNull(message = "发货单位不能为空！")
    @XdoSize(max = 60, message = "发货单位长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发货单位")
    private String deliveryUnit;

    /**
     * 包装种类
     * 数据库字段:package_type
     * 字符类型(60)
     */
    @XdoSize(max = 30, message = "包装种类长度不能超过30位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("包装种类")
    private String packageType;

    /**
     * 包装数量
     * 数据库字段:package_num
     * 数值类型(10,0)
     */
    @Digits(integer = 10, fraction = 0, message = "包装数量必须为数字,整数位最大10位,小数最大0位!")
    @ApiModelProperty("包装数量")
    private BigDecimal packageNum;

    /**
     * 发货单位所在地
     * 数据库字段:delivery_unit_location
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "发货单位所在地长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发货单位所在地")
    private String deliveryUnitLocation;

    /**
     * 装运人shipper
     * 数据库字段:shipper
     * 字符类型(600)
     */
    @XdoSize(max = 300, message = "装运人shipper长度不能超过300位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("装运人shipper")
    private String shipper;

    /**
     * 收货人consignee
     * 数据库字段:consignee
     * 字符类型(600)
     */
    @XdoSize(max = 300, message = "收货人consignee长度不能超过300位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("收货人consignee")
    private String consignee;

    /**
     * 通知人notify party
     * 数据库字段:notify_party
     * 字符类型(600)
     */
    @XdoSize(max = 300, message = "通知人notify party长度不能超过300位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("通知人notify party")
    private String notifyParty;

    /**
     * 总毛重
     * 数据库字段:gross_weight
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "总毛重必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("总毛重")
    private BigDecimal grossWeight;

    /**
     * 总净重
     * 数据库字段:net_weight
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "总净重必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("总净重")
    private BigDecimal netWeight;

    /**
     * 总皮重
     * 数据库字段:tare_weight
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "总皮重必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("总皮重")
    private BigDecimal tareWeight;

    /**
     * 唛头
     * 数据库字段:mark
     * 字符类型(600)
     */
    @XdoSize(max = 300, message = "唛头长度不能超过300位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("唛头")
    private String mark;

    /**
     * 装运港
     * 数据库字段:port_of_shipment
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "装运港长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("装运港")
    private String portOfShipment;

    /**
     * 目的地/港
     * 数据库字段:port_of_destination
     * 字符类型(100)
     */
    @XdoSize(max = 50, message = "目的地/港长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("目的地/港")
    private String portOfDestination;

    /**
     * 装运期限
     * 数据库字段:shipment_date
     * date
     */
    @ApiModelProperty("装运期限")
    private Date shipmentDate;

    /**
     * 险别
     * 数据库字段:insurance_type
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "险别长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("险别")
    private String insuranceType;

    /**
     * 保费币种
     * 数据库字段:insurance_currency
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "保费币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("保费币种")
    private String insuranceCurrency;

    /**
     * 投保加成%
     * 数据库字段:insurance_add_rate
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "投保加成%必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("投保加成%")
    private BigDecimal insuranceAddRate;

    /**
     * 保费费率(%)
     * 数据库字段:insurance_rate
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "保费费率(%)必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("保费费率(%)")
    private BigDecimal insuranceRate;

    /**
     * 保险费
     * 数据库字段:insurance_fee
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "保险费必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("保险费")
    private BigDecimal insuranceFee;

    /**
     * 投保人
     * 数据库字段:insurer
     * 字符类型(400)
     */
    @NotNull(message = "投保人不能为空！")
    @XdoSize(max = 200, message = "投保人长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("投保人")
    private String insurer;

    /**
     * 运费
     * 数据库字段:freight
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "运费必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("运费")
    private BigDecimal freight;

    /**
     * 运费币种
     * 数据库字段:freight_currency
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "运费币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("运费币种")
    private String freightCurrency;

    /**
     * 仓储地址
     * 数据库字段:warehouse_address
     * 字符类型(600)
     */
    @XdoSize(max = 300, message = "仓储地址长度不能超过300位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("仓储地址")
    private String warehouseAddress;

    /**
     * 联系人
     * 数据库字段:contact_person
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "联系人长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("联系人")
    private String contactPerson;

    /**
     * 联系电话
     * 数据库字段:contact_phone
     * 字符类型(40)
     */
    @XdoSize(max = 20, message = "联系电话长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /**
     * 备注
     * 数据库字段:remark
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 发送报关
     * 数据库字段:send_customs
     * 字符类型(20)
     */
    @NotNull(message = "发送报关不能为空！")
    @XdoSize(max = 10, message = "发送报关长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发送报关")
    private String sendCustoms;

    /**
     * 确认时间
     * 数据库字段:confirm_time
     * 日期类型(6)
     */
    @ApiModelProperty("确认时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;
    /**
     * 确认时间-开始时间
     */
    @ApiModelProperty("确认时间-开始时间")
    private String confirmTimeFrom;

    /**
    * 确认时间-结束时间
    */
    @ApiModelProperty("确认时间-结束时间")
    private String confirmTimeTo;

    /**
     * 准运证编号，用户录入
     * 数据库字段:transport_permit_no
     * 字符类型(200)
     */
    @XdoSize(max = 100, message = "准运证编号长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("准运证编号")
    private String transportPermitNo;

    /**
     * 准运证申办日期，用户录入
     * 数据库字段:transport_permit_apply_date
     * 日期类型(0)
     */
    @ApiModelProperty("准运证申办日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transportPermitApplyDate;
    /**
     * 准运证申办日期，用户录入-开始时间
     */
    @ApiModelProperty("准运证申办日期-开始时间")
    private String transportPermitApplyDateFrom;

    /**
    * 准运证申办日期，用户录入-结束时间
    */
    @ApiModelProperty("准运证申办日期-结束时间")
    private String transportPermitApplyDateTo;

    /**
     * 到货确认日期，用户录入
     * 数据库字段:arrival_confirm_date
     * 日期类型(0)
     */
    @ApiModelProperty("到货确认日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arrivalConfirmDate;
    /**
     * 到货确认日期，用户录入-开始时间
     */
    @ApiModelProperty("到货确认日期-开始时间")
    private String arrivalConfirmDateFrom;

    /**
    * 到货确认日期，用户录入-结束时间
    */
    @ApiModelProperty("到货确认日期-结束时间")
    private String arrivalConfirmDateTo;




    /**
     * 外销发票确认时间 确认时间
     * 字符类型(36)
     */
    @ApiModelProperty("确认时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date invoiceConfirmTime;


    /**
     * 外销发票状态
     */
    @ApiModelProperty("导出发票状态")
    @XdoSize(max = 10, message = "导出发票状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    private String invoiceDataState;


    /**
     * 外销发票 是否红冲
     */
    @ApiModelProperty("外销发票是否红冲")
    @XdoSize(max = 10, message = "导出发票是否红冲长度不能超过10位字节长度(一个汉字2位字节长度)!")
    private String invoiceIsRedFlush;


    /**
     * 外销发票 发送财务系统
     */
    @ApiModelProperty("外销发票 发送财务系统")
    @XdoSize(max = 10, message = "导出发票发送财务系统长度不能超过10位字节长度(一个汉字2位字节长度)!")
    private String invoiceSendFinance;



    /**
     * 表体金额汇总
     */
    @ApiModelProperty("表体金额汇总")
    private BigDecimal listTotal;


    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String approvalStatus;


    /**
     * 装箱说明
     */
    @ApiModelProperty("装箱说明")
    @XdoSize(max = 200, message = "装箱说明长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String boxDesc;
    /**
     * 运输工具名称
     */
    @ApiModelProperty("运输工具名称")
    @XdoSize(max = 50, message = "运输工具名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
    private String transportationToolsName;

    /**
     * 开航日期
     */
    @ApiModelProperty("开航日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sailingDate;

    /**
     * 开航日期-开始时间
     */
    @ApiModelProperty("开航日期-开始时间")
    private String sailingDateFrom;

    /**
    * 开航日期-结束时间
    */
    @ApiModelProperty("开航日期-结束时间")
    private String sailingDateTo;



    /**
     * 序号
     */
    @ApiModelProperty("序号")
    @Digits(integer = 10, fraction = 0, message = "序号必须为数字,整数位最大10位,小数最大0位!")
    private BigDecimal serialNo;



}