package com.dcjet.cs.dto.aeo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @date: 2019-4-18
 */
@ApiModel(value = "AEO审核信息HS校验返回信息")
@Setter @Getter
public class AeoMessageDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
      * 企业料号
      */
    @ApiModelProperty("企业料号")
	private  String facGNo;

	/**
	 * 错误信息
	 */
	@ApiModelProperty("错误信息")
	private  String errMsg;

}
