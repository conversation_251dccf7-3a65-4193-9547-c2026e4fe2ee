package com.dcjet.cs.dto.deliveryOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizDeliveryOrderHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 业务类型
     */
	@XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 出货单号
     */
	@XdoSize(max = 120, message = "出货单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("出货单号")
	private  String purchaseOrderNo;
	/**
     * 供应商
     */
	@XdoSize(max = 400, message = "供应商长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String supplier;
	/**
     * 客户
     */
	@XdoSize(max = 400, message = "客户长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String customer;
	/**
     * 客户地址
     */
	@XdoSize(max = 400, message = "客户地址长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户地址")
	private  String customerAddress;
	/**
     * 贸易国别
     */
	@XdoSize(max = 120, message = "贸易国别长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("贸易国别")
	private  String tradeCountry;
	/**
     * 经营单位
     */
	@XdoSize(max = 120, message = "经营单位长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("经营单位")
	private  String businessEnterprise;
	/**
     * 目的地/港
     */
	@XdoSize(max = 100, message = "目的地/港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("目的地/港")
	private  String portOfDestination;
	/**
     * 付款方式
     */
	@XdoSize(max = 40, message = "付款方式长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("付款方式")
	private  String paymentMethod;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 总金额
     */
	@Digits(integer = 15, fraction = 4, message = "总金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总金额")
	private  BigDecimal totalAmount;
	/**
     * 运输方式
     */
	@XdoSize(max = 100, message = "运输方式长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("运输方式")
	private  String transportMode;
	/**
     * 价格条款
     */
	@XdoSize(max = 20, message = "价格条款长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款")
	private  String priceTerm;
	/**
     * 价格条款对应港口
     */
	@XdoSize(max = 40, message = "价格条款对应港口长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款对应港口")
	private  String priceTermPort;
	/**
     * 发货单位
     */
	@XdoSize(max = 120, message = "发货单位长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发货单位")
	private  String deliveryEnterprise;
	/**
     * 包装种类
     */
	@XdoSize(max = 200, message = "包装种类长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("包装种类")
	private  String wrapType;
	/**
     * 包装数量
     */
	@Digits(integer = 15, fraction = 4, message = "包装数量必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("包装数量")
	private  BigDecimal packNum;
	/**
     * 发货单位所在地
     */
	@XdoSize(max = 400, message = "发货单位所在地长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发货单位所在地")
	private  String deliveryEnterpriseAddress;
	/**
     * 总毛重
     */
	@Digits(integer = 15, fraction = 4, message = "总毛重必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总毛重")
	private  BigDecimal totalNetWt;
	/**
     * 总净重
     */
	@Digits(integer = 15, fraction = 4, message = "总净重必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总净重")
	private  BigDecimal totalGrossWt;
	/**
     * 总皮重
     */
	@Digits(integer = 15, fraction = 4, message = "总皮重必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总皮重")
	private  BigDecimal totalTare;
	/**
     * 发送报关
     */
	@XdoSize(max = 20, message = "发送报关长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发送报关")
	private  String sendDeclare;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 单据状态
     */
	@XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;
	private String destinationDateFrom;
	private String destinationDateTo;
	private String insertTimeFrom;
	private String insertTimeTo;
	private String createrBy;

	private String createrUserName;

	private Date createrTime;
	private  String isSave;
	private String fileType;
}
